using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Controls.Shapes;
using Avalonia.Media;
using Avalonia.Threading;
using Avalonia.VisualTree;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Presentation.Messages;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Views.Dashboard
{
    public partial class MqttStatusIndicator : UserControl, IRecipient<MqttConnectionStatusChangedMessage>
    {
        private readonly Ellipse _statusIndicator;
        private string _statusText = "未连接";

        public static readonly StyledProperty<string> StatusTextProperty =
            AvaloniaProperty.Register<MqttStatusIndicator, string>(nameof(StatusText));

        public string StatusText
        {
            get => GetValue(StatusTextProperty);
            set => SetValue(StatusTextProperty, value);
        }

        public MqttStatusIndicator()
        {
            InitializeComponent();
            _statusIndicator = this.Find<Ellipse>("StatusIndicator");
            
            // 设置初始状态
            UpdateStatus(ConnectionStatus.Disconnected);
            DataContext = this;
            
            // 注册消息接收
            WeakReferenceMessenger.Default.Register<MqttConnectionStatusChangedMessage>(this);
        }

        public void Receive(MqttConnectionStatusChangedMessage message)
        {
            // 确保在UI线程上更新UI
            Dispatcher.UIThread.Post(() =>
            {
                UpdateStatus(message.NewStatus, message.Message);
            });
        }

        private void UpdateStatus(ConnectionStatus status, string message = null)
        {
            switch (status)
            {
                case ConnectionStatus.Connected:
                    _statusIndicator.Fill = new SolidColorBrush(Colors.Green);
                    StatusText = "已连接";
                    break;
                case ConnectionStatus.Connecting:
                    _statusIndicator.Fill = new SolidColorBrush(Colors.Yellow);
                    StatusText = "连接中...";
                    break;
                case ConnectionStatus.Reconnecting:
                    _statusIndicator.Fill = new SolidColorBrush(Colors.Orange);
                    StatusText = "重连中...";
                    break;
                case ConnectionStatus.Error:
                    _statusIndicator.Fill = new SolidColorBrush(Colors.DarkRed);
                    StatusText = "连接错误";
                    break;
                case ConnectionStatus.Disconnected:
                default:
                    _statusIndicator.Fill = new SolidColorBrush(Colors.Red);
                    StatusText = "未连接";
                    break;
            }

            // 如果有详细消息，作为工具提示显示
            if (!string.IsNullOrEmpty(message))
            {
                ToolTip.SetTip(this, message);
            }
        }
    }
}