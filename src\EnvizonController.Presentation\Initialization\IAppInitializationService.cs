namespace EnvizonController.Presentation.Initialization
{
    /// <summary>
    /// 定义应用程序初始化服务的接口
    /// </summary>
    public interface IAppInitializationService
    {
        /// <summary>
        /// 在初始化进度变化时发生
        /// </summary>
        event EventHandler<InitializationProgressEventArgs> ProgressChanged;

        /// <summary>
        /// 获取当前初始化状态
        /// </summary>
        InitializationProgress CurrentState { get; }

        /// <summary>
        /// 异步执行应用程序初始化
        /// </summary>
        /// <param name="progress">用于报告进度的接口</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的任务</returns>
        Task InitializeAsync(IProgress<InitializationProgress> progress = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 重置初始化状态，允许重新初始化
        /// </summary>
        void Reset();
    }
}