﻿using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Domain.Services;

/// <summary>
///     协议服务接口
/// </summary>
public interface IProtocolService
{

    /// <summary>
    ///     添加协议
    /// </summary>
    /// <param name="protocol">协议</param>
    Task<Protocol> AddProtocolAsync(Protocol protocol);

    /// <summary>
    ///     更新协议
    /// </summary>
    /// <param name="protocol">协议</param>
    /// <returns>是否成功更新</returns>
    Task<Protocol> UpdateProtocolAsync(Protocol protocol);
}