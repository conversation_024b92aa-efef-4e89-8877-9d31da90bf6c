using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Mqtt.Client.Handlers;
using EnvizonController.Mqtt.Client.Services;
using EnvizonController.Presentation.Messages;
using Serilog;
using System.Linq;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Handlers
{
    /// <summary>
    /// 设备采集详情消息处理器
    /// </summary>
    public class DeviceCollectionDetailsMessageHandler : IMessageHandler
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger _logger;

        public DeviceCollectionDetailsMessageHandler(
            INotificationService notificationService,
            ILogger logger = null)
        {
            _notificationService = notificationService;
            _logger = logger ?? Log.ForContext<DeviceCollectionDetailsMessageHandler>();
        }

        public bool CanHandle(string topic)
        {
            return topic.StartsWith("device/data", StringComparison.OrdinalIgnoreCase);
        }

        public Task HandleMessageAsync(string topic, string message)
        {
            try
            {
                _logger.Debug("收到设备采集详情消息: Topic={Topic}", topic);

                // 处理批量消息
                if (topic.EndsWith("/batch", StringComparison.OrdinalIgnoreCase))
                {
                    return HandleBatchMessageAsync(message);
                }
                
                // 处理单个消息
                return HandleSingleMessageAsync(message);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "处理设备采集详情消息时发生错误");
                return Task.FromException(ex);
            }
        }

        /// <summary>
        /// 处理单个设备采集详情消息
        /// </summary>
        private Task HandleSingleMessageAsync(string message)
        {
            var deviceCollectionDetails = JsonSerializer.Deserialize<DeviceCollectionDetailsDTO>(message);
            if (deviceCollectionDetails == null)
            {
                _logger.Warning("无法解析设备采集详情消息: {Message}", message);
                return Task.CompletedTask;
            }

            // 发布通知
            _notificationService.Publish(deviceCollectionDetails);

            return Task.CompletedTask;
        }

        /// <summary>
        /// 处理批量设备采集详情消息
        /// </summary>
        private Task HandleBatchMessageAsync(string message)
        {
            try
            {
                // 解析批量数据包
                var batchData = JsonSerializer.Deserialize<BatchDeviceCollectionDetails>(message);
                if (batchData == null || batchData.Items == null || !batchData.Items.Any())
                {
                    _logger.Warning("无法解析批量设备采集详情消息或消息为空: {Message}", message);
                    return Task.CompletedTask;
                }

                _logger.Debug("收到批量设备采集详情消息, 包含 {BatchSize} 个设备数据", batchData.BatchSize);

                _notificationService.Publish(batchData);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "处理批量设备采集详情消息时发生错误");
                return Task.FromException(ex);
            }
        }
    }

    /// <summary>
    /// 批量设备采集详情数据结构
    /// </summary>
    public class BatchDeviceCollectionDetails
    {


        /// <summary>
        /// 批次类型
        /// </summary>
        public string BatchType { get; set; } = string.Empty;
        
        /// <summary>
        /// 批次大小
        /// </summary>
        public int BatchSize { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 批量设备采集数据
        /// </summary>
        public List<DeviceCollectionDetailsReceivedDTO> Items { get; set; } = new List<DeviceCollectionDetailsReceivedDTO>();
    }
}