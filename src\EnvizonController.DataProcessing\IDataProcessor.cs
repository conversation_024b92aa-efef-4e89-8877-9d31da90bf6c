using System.Threading.Tasks;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 数据处理器接口
    /// </summary>
    public interface IDataProcessor
    {
        /// <summary>
        /// 获取处理器名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 获取处理器描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context);
        
        /// <summary>
        /// 判断是否可以处理指定上下文
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>是否可以处理</returns>
        bool CanProcess(IDataProcessingContext context);
    }
}
