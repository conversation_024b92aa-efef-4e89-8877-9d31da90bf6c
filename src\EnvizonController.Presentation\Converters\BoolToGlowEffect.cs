using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    public class BoolToGlowEffect : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isImportant && isImportant)
            {
                // 重要项使用强发光效果
                return new DropShadowEffect
                {
                    BlurRadius = 18,
                    OffsetX = 0,
                    OffsetY = 0,
                    Opacity = 0.6,
                    Color = Color.Parse("#0DF0FF")
                };
            }
            
            // 不重要项使用轻微发光效果
            return new DropShadowEffect
            {
                BlurRadius = 12,
                OffsetX = 0,
                OffsetY = 0,
                Opacity = 0.3,
                Color = Color.Parse("#0DF0FF")
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 