using System.Collections.ObjectModel;
using System.Diagnostics;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.Services;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.ViewModels.Dashboard;

/// <summary>
/// 设备列表项 ViewModel，封装 DeviceDto 并添加 UI 相关属性
/// </summary>
public partial class DeviceListItemViewModel : ObservableObject
{
    private readonly DeviceDto _deviceDto;
    
    [ObservableProperty]
    private bool _isSelected;
    
    public DeviceListItemViewModel(DeviceDto deviceDto)
    {
        _deviceDto = deviceDto;
    }
    
    // 设备原始DTO对象
    public DeviceDto DeviceDto => _deviceDto;
    
    // 设备基本信息属性
    public long Id => _deviceDto.Id;
    public string Name => _deviceDto.Name;
    public EnvizonController.Shared.Enums.DeviceStatus Status => _deviceDto.Status;
    public ConnectionStatus ConnectionStatus => _deviceDto.ConnectionStatus;
    public string ConnectionType => _deviceDto.ConnectionType;
    public string TransportType => _deviceDto.TransportType;
    public bool AutoStart => _deviceDto.AutoStart;
    
    // 连接状态相关的辅助属性
    public bool IsConnected => _deviceDto.ConnectionStatus == ConnectionStatus.Connected;
    public bool IsDisconnected => _deviceDto.ConnectionStatus == ConnectionStatus.Disconnected;
    public bool IsConnecting => _deviceDto.ConnectionStatus == ConnectionStatus.Connecting;
}

/// <summary>
/// API设备列表 ViewModel，用于在 DashboardView 左侧面板显示设备列表
/// </summary>
public partial class ApiDevicesViewModel : ViewModelBase
{
    private readonly IDeviceService _deviceService;
    
    [ObservableProperty]
    private ObservableCollection<DeviceListItemViewModel> _devices = new();
    
    [ObservableProperty]
    private DeviceListItemViewModel _selectedDevice;
    
    [ObservableProperty]
    private bool _isLoading;
    
    [ObservableProperty]
    private DateTime? _lastRefreshTime;
    
    [ObservableProperty]
    private string _statusMessage = string.Empty;
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="deviceService">设备服务</param>
    public ApiDevicesViewModel(IDeviceService deviceService)
    {
        _deviceService = deviceService;
        
        // 初始化时加载设备列表
        _ = LoadDevicesAsync();
    }
    
    /// <summary>
    /// 从API加载设备列表
    /// </summary>
    [RelayCommand]
    public async Task LoadDevicesAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载设备列表...";
            
            var result = await _deviceService.GetDevicesAsync();
            
            if (result.IsSuccess && result.Data != null)
            {
                // 清空现有设备列表
                Devices.Clear();
                
                // 将 DeviceDto 转换为 DeviceListItemViewModel
                foreach (var deviceDto in result.Data.Items)
                {
                    Devices.Add(new DeviceListItemViewModel(deviceDto));
                }
                
                LastRefreshTime = DateTime.Now;
                StatusMessage = $"已加载 {Devices.Count} 个设备";
                
                // 默认选择之前选中的设备，或者第一个设备
                var deviceToSelect = Devices.FirstOrDefault(d => 
                    SelectedDevice != null && d.Id == SelectedDevice.Id) 
                    ?? Devices.FirstOrDefault();
                
                if (deviceToSelect != null)
                {
                    SelectDevice(deviceToSelect);
                }
            }
            else
            {
                StatusMessage = $"加载设备列表失败: {result.ErrorMessage}";
                Debug.WriteLine(StatusMessage);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载设备列表时发生异常: {ex.Message}";
            Debug.WriteLine(StatusMessage);
        }
        finally
        {
            IsLoading = false;
        }
    }
    
    /// <summary>
    /// 选择设备
    /// </summary>
    [RelayCommand]
    public void SelectDevice(DeviceListItemViewModel device)
    {
        if (device == null) return;
        
        // 如果点击已选中的设备，则保持选中状态不变
        if (device.IsSelected)
            return;
        
        // 清除所有设备的选中状态
        foreach (var deviceItem in Devices)
        {
            deviceItem.IsSelected = false;
        }
        
        // 设置当前设备为选中状态
        device.IsSelected = true;
        SelectedDevice = device;
        
        // 发送设备选择变更消息
        WeakReferenceMessenger.Default.Send(new CurrentDeviceDtoChangedMessage(device.DeviceDto));
    }
    
    /// <summary>
    /// 刷新设备列表
    /// </summary>
    [RelayCommand]
    public async Task RefreshAsync()
    {
        await LoadDevicesAsync();
    }
}