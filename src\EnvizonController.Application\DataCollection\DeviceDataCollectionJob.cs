using EnvizonController.Domain.Aggregates;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Serilog;

namespace EnvizonController.Application.DataCollection;

/// <summary>
///     设备数据采集作业
/// </summary>
[DisallowConcurrentExecution]
public class DeviceDataCollectionJob(
    IDataCollectionCoordinator coordinator,
    ILogger logger)
    : IJob
{
    public async Task Execute(IJobExecutionContext context)
    {
        // 从作业数据获取设备信息
        var dataMap = context.MergedJobDataMap;
        var device = (Device)dataMap["Device"];

        try
        {
            // 执行数据采集
            await coordinator.CollectDataAsync(device);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "设备 {DeviceId} 数据采集作业执行失败：{Message}", device.Id, ex.Message);
        }
    }
}