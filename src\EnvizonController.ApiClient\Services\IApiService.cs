using EnvizonController.ApiClient.Results;
using System.Threading.Tasks;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// API服务接口基础定义
    /// </summary>
    public interface IApiService
    {
        /// <summary>
        /// API服务基础URL
        /// </summary>
        string BaseUrl { get; }
        
        /// <summary>
        /// 初始化API服务
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// 检查API服务状态
        /// </summary>
        /// <returns>API服务健康状态结果</returns>
        Task<Result<bool>> CheckHealthAsync();
    }
}