# AutoMapper 迁移方案：从 Extensions.DI 到纯 AutoMapper

## 项目当前状态分析

通过对代码的分析，我发现以下情况：

1. 项目当前使用 AutoMapper 14.0.0
2. 在 `EnvizonController.Application` 项目中通过 `services.AddAutoMapper(typeof(MappingProfile).Assembly)` 扩展方法注册 AutoMapper
3. `Program.cs` 中有被注释的 `services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies())` 代码
4. 已有 `MappingProfile` 类定义了部分实体映射（`Alarm -> AlarmDTO` 和 `TestItem -> TestItemDTO`）
5. `Device -> DeviceDto` 的映射在 `DevicesController` 中通过手动代码实现
6. 部分服务类（如 `TestItemAppService` 和 `AlarmAppService`）已经正确使用 `IMapper` 接口进行对象映射

## 迁移原因

由于 `AutoMapper.Extensions.Microsoft.DependencyInjection` 包已被标记为弃用，需要迁移到纯 AutoMapper 的最新解决方案。

## 详细迁移方案

### 1. 版本评估

**结论**：保持使用 AutoMapper 14.0.0 版本。这是最新的稳定版本，符合项目需求。

### 2. 新的 AutoMapper 配置和注册方式

在 AutoMapper 14.0 中，推荐的注册方式是直接使用 `MapperConfiguration` 和 `IMapper` 接口。

**实施步骤**：

1. 修改 `EnvizonController.Application` 项目中的 `DependencyInjection.cs` 文件：

```csharp
// 将此代码：
services.AddAutoMapper(typeof(MappingProfile).Assembly);

// 替换为：
var mapperConfig = new MapperConfiguration(cfg => 
{
    cfg.AddProfile<MappingProfile>();
    // 可以添加其他 Profile
});

// 开发环境下验证配置
if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
{
    mapperConfig.AssertConfigurationIsValid();
}

// 创建 IMapper 实例并注册为单例
IMapper mapper = mapperConfig.CreateMapper();
services.AddSingleton(mapper);
```

2. 删除 `Program.cs` 中被注释的 `AddAutoMapper` 代码行

### 3. 完善 MappingProfile 映射配置

需要扩展 `MappingProfile` 类，添加 `Device` 和各种 `DeviceDto` 之间的映射关系。

```csharp
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // 现有映射
        CreateMap<Alarm, AlarmDTO>()
            .ForMember(dest => dest.Level, opt => opt.MapFrom(src => (AlarmSeverity)src.Level))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (AlarmStatus)src.Status));

        CreateMap<TestItem, TestItemDTO>();

        // 添加 Device 相关映射
        
        // Device -> DeviceDto
        CreateMap<Device, DeviceDto>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (SharedEnums.DeviceStatus)(int)src.Status))
            .ForMember(dest => dest.ConnectionStatus, opt => opt.MapFrom(src => (SharedEnums.ConnectionStatus)(int)src.ConnectionStatus));

        // CreateDeviceDto -> Device
        CreateMap<CreateDeviceDto, Device>()
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => DomainEnums.DeviceStatus.Normal))
            .ForMember(dest => dest.ConnectionStatus, opt => opt.MapFrom(src => DomainEnums.ConnectionStatus.Disconnected))
            .ForMember(dest => dest.PortName, opt => opt.MapFrom(src => src.PortName ?? string.Empty))
            .ForMember(dest => dest.BaudRate, opt => opt.MapFrom(src => src.BaudRate ?? 9600))
            .ForMember(dest => dest.DataBits, opt => opt.MapFrom(src => src.DataBits ?? 8))
            .ForMember(dest => dest.Parity, opt => opt.MapFrom(src => src.Parity ?? 0))
            .ForMember(dest => dest.StopBits, opt => opt.MapFrom(src => src.StopBits ?? 1))
            .ForMember(dest => dest.HostAddress, opt => opt.MapFrom(src => src.HostAddress ?? string.Empty))
            .ForMember(dest => dest.Port, opt => opt.MapFrom(src => src.Port ?? 502));

        // UpdateDeviceDto -> Device
        CreateMap<UpdateDeviceDto, Device>()
            .ForMember(dest => dest.LastUpdatedAt, opt => opt.MapFrom(src => DateTime.Now))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (DomainEnums.DeviceStatus)(int)src.Status))
            .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
    }
}
```

### 4. 修改 DevicesController 使用 AutoMapper

修改 `DevicesController` 类，使用 `IMapper` 接口进行对象映射：

```csharp
public class DevicesController : ControllerBase
{
    private readonly IDeviceAppService _deviceService;
    private readonly ILogger<DevicesController> _logger;
    private readonly IMapper _mapper; // 添加 IMapper 字段

    public DevicesController(
        IDeviceAppService deviceService, 
        ILogger<DevicesController> logger,
        IMapper mapper) // 在构造函数中注入 IMapper
    {
        _deviceService = deviceService;
        _logger = logger;
        _mapper = mapper;
    }

    // 获取所有设备
    [HttpGet]
    [ProducesResponseType(typeof(PagedResultDto<DeviceDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResultDto<DeviceDto>>> GetDevices(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var devices = await _deviceService.GetAllDevicesAsync();
            var devicesList = devices.ToList();
            
            // 使用 AutoMapper 映射
            var deviceDtos = devicesList
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(d => _mapper.Map<DeviceDto>(d))
                .ToList();

            return Ok(new PagedResultDto<DeviceDto>
            {
                Items = deviceDtos,
                TotalCount = devicesList.Count,
                Page = page,
                PageSize = pageSize
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备列表时出错");
            return StatusCode(StatusCodes.Status500InternalServerError, "获取设备列表时发生错误");
        }
    }

    // 获取单个设备
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DeviceDto>> GetDevice(long id)
    {
        try
        {
            var device = await _deviceService.GetDeviceByIdAsync(id);
            if (device == null)
            {
                return NotFound($"ID为{id}的设备不存在");
            }

            // 使用 AutoMapper 映射
            var deviceDto = _mapper.Map<DeviceDto>(device);
            
            return Ok(deviceDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 时出错", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "获取设备详情时发生错误");
        }
    }

    // 创建设备
    [HttpPost]
    [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<DeviceDto>> CreateDevice(CreateDeviceDto deviceDto)
    {
        try
        {
            // 使用 AutoMapper 映射到 Domain 实体
            var device = _mapper.Map<Device>(deviceDto);

            // 创建设备
            var createdDevice = await _deviceService.CreateDeviceAsync(device);

            // 使用 AutoMapper 映射回 DTO
            var createdDeviceDto = _mapper.Map<DeviceDto>(createdDevice);

            return CreatedAtAction(nameof(GetDevice), new { id = createdDevice.Id }, createdDeviceDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备时出错");
            return StatusCode(StatusCodes.Status500InternalServerError, "创建设备时发生错误");
        }
    }

    // 更新设备
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<DeviceDto>> UpdateDevice(long id, UpdateDeviceDto deviceDto)
    {
        try
        {
            // 检查设备是否存在
            var existingDevice = await _deviceService.GetDeviceByIdAsync(id);
            if (existingDevice == null)
            {
                return NotFound($"ID为{id}的设备不存在");
            }

            // 使用 AutoMapper 更新实体
            _mapper.Map(deviceDto, existingDevice);
            
            // 更新设备
            var updatedDevice = await _deviceService.UpdateDeviceAsync(existingDevice);

            // 使用 AutoMapper 映射回 DTO
            var updatedDeviceDto = _mapper.Map<DeviceDto>(updatedDevice);

            return Ok(updatedDeviceDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备 {DeviceId} 时出错", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "更新设备时发生错误");
        }
    }
}
```

### 5. 迁移步骤清单

1. 移除项目中对 AutoMapper.Extensions.Microsoft.DependencyInjection 包的引用（如果有）
2. 修改 `EnvizonController.Application` 项目中的 `DependencyInjection.cs` 文件，替换 AutoMapper 注册方式
3. 在 `MappingProfile` 类中添加 Device 相关的映射配置
4. 修改 `DevicesController`，注入 `IMapper` 并使用它替换手动映射代码
5. 测试所有 API 端点，确保映射正常工作
6. 检查其他可能使用 AddAutoMapper 扩展方法的地方并进行修改

### 6. 兼容性风险评估

1. **版本风险**：低
   - 保持使用 AutoMapper 14.0.0，避免版本变更引起的问题

2. **行为风险**：中
   - 自动映射与手动映射可能会有细微差别，特别是在处理空值、默认值时
   - 解决方案：使用详细的映射配置，处理特殊情况，并进行全面测试

3. **调用者风险**：低
   - API 接口不变，对外部调用者无影响
   - 内部代码修改范围有限，主要是控制器中的映射逻辑

4. **其他风险**：低
   - 需要确保所有 DTO 和实体之间的映射关系都已正确配置
   - 复杂映射可能需要特别注意，比如集合映射、深层嵌套对象等

## 结论和建议

1. **推荐的包版本**：保持使用 AutoMapper 14.0.0

2. **新的注册方式**：
   ```csharp
   // 配置 AutoMapper
   var mapperConfig = new MapperConfiguration(cfg => 
   {
       cfg.AddProfile<MappingProfile>();
   });
   
   // 可选：验证配置
   mapperConfig.AssertConfigurationIsValid();
   
   // 创建 IMapper 并注册
   IMapper mapper = mapperConfig.CreateMapper();
   services.AddSingleton(mapper);
   ```

3. **最佳实践建议**：
   - 考虑按模块或功能区域将映射配置分离到不同的 Profile 类
   - 为复杂映射编写单元测试，确保行为符合预期
   - 使用 AutoMapper 的调试和验证功能排查映射问题

4. **性能优化**：
   - AutoMapper 14.0 在性能方面已有很大改进
   - 对于频繁调用的映射，考虑使用 ProjectTo 方法优化数据库查询

通过这个迁移方案，项目可以平稳地从依赖 AutoMapper.Extensions.Microsoft.DependencyInjection 过渡到使用纯 AutoMapper，同时改进现有代码的质量和一致性。