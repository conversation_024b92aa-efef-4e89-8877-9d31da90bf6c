using EnvizonController.Application.Devices;
using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Client;
using EnvizonController.Modbus.Protocol.Enums;
using Serilog;
using System.Reactive.Subjects;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using EnvizonController.Modbus.Abstractions.Enums;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Enums;
using System;
using EnvizonController.Modbus.Client.Extensions;

namespace EnvizonController.Infrastructure.Services.Devices
{
    /// <summary>
    /// Modbus设备服务实现
    /// </summary>
    public class ModbusDeviceService : IModbusDeviceService, IDisposable
    {
        private readonly ILogger _logger;
        private readonly Device _device;
        private readonly IModbusChannelFactory _channelFactory;
        private IModbusClient? _modbusClient;
        private IModbusChannel? _modbusChannel;
        private readonly BehaviorSubject<DeviceModel> _deviceStateSubject;
        private readonly DeviceModel _deviceState;
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private CancellationTokenSource? _reconnectCts;
        private bool _isDisposed;

        // 默认的重试和重连设置
        private readonly int _retryCount = 3;
        private readonly int _retryDelayMilliseconds = 100;
        private readonly int _responseTimeout = 1000;
        private readonly bool _autoReconnect = true;
        private readonly int _autoReconnectDelayMilliseconds = 5000;
        private readonly int _autoReconnectMaxAttempts = 3;

        /// <summary>
        /// 获取设备状态流
        /// </summary>
        public IObservable<DeviceModel> DeviceStateStream => _deviceStateSubject;

        public IModbusClient ModbusClient => _modbusClient;
        /// <summary>
        /// 获取设备是否已连接
        /// </summary>
        public bool IsConnected => _modbusClient?.IsConnected ?? false;

        private IServiceProvider _serviceProvider;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="device">设备配置</param>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="channelFactory">Modbus通道工厂</param>
        public ModbusDeviceService(
            ILogger logger, 
            Device device,
            IServiceProvider serviceProvider,
            IModbusChannelFactory channelFactory)
        {
            _serviceProvider = serviceProvider;
            _logger = logger ?? Log.ForContext<ModbusDeviceService>();
            _device = device ?? throw new ArgumentNullException(nameof(device));
            _channelFactory = channelFactory;
            
            // 初始化设备状态
            _deviceState = new DeviceModel
            {
                DeviceId = device.Id.ToString(),
                DeviceName = device.Name,
                ConnectionStatus = Application.Devices.ConnectionStatus.Disconnected,
                StatusMessage = "未连接"
            };
            
            _deviceStateSubject = new BehaviorSubject<DeviceModel>(_deviceState);
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功连接</returns>
        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected)
                    return true;

                // 更新设备状态
                UpdateDeviceState(Application.Devices.ConnectionStatus.Connecting, "正在连接...");

                try
                {
                    // 创建通信通道
                    _modbusChannel = CreateChannel();
                    
                    // 创建Modbus客户端
                    _modbusClient = ModbusClientFactory.CreateClient(
                        _modbusChannel,
                        GetTransportType(),
                        _retryCount,
                        _retryDelayMilliseconds,
                        _responseTimeout);
                    
                    // 连接到设备
                    await _modbusClient.ConnectAsync(cancellationToken);
                    
                    // 更新设备状态
                    UpdateDeviceState(Application.Devices.ConnectionStatus.Connected, "已连接");
                    
                    // 读取设备信息
                    //await ReadDeviceInfoAsync(cancellationToken);
                    
                    // 启动自动重连
                    if (_autoReconnect)
                    {
                        StartAutoReconnect();
                    }
                    
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "连接到Modbus设备时出错");
                    UpdateDeviceState(Application.Devices.ConnectionStatus.Error, $"连接错误: {ex.Message}");
                    
                    // 清理资源
                    _modbusClient?.Dispose();
                    _modbusClient = null;
                    _modbusChannel?.Dispose();
                    _modbusChannel = null;
                    
                    return false;
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 断开与设备的连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功断开连接</returns>
        public async Task<bool> DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected)
                    return true;

                // 停止自动重连
                StopAutoReconnect();

                // 更新设备状态
                UpdateDeviceState(Application.Devices.ConnectionStatus.Disconnected, "正在断开连接...");

                try
                {
                    // 断开连接
                    await _modbusClient!.DisconnectAsync(cancellationToken);
                    
                    // 清理资源
                    _modbusClient.Dispose();
                    _modbusClient = null;
                    _modbusChannel?.Dispose();
                    _modbusChannel = null;
                    
                    // 更新设备状态
                    UpdateDeviceState(Application.Devices.ConnectionStatus.Disconnected, "已断开连接");
                    
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "断开Modbus设备连接时出错");
                    UpdateDeviceState(Application.Devices.ConnectionStatus.Error, $"断开连接错误: {ex.Message}");
                    return false;
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public async Task RefreshDeviceStatusAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                return;

            try
            {
                // 读取设备信息
                await ReadDeviceInfoAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "刷新设备状态时出错");
                UpdateDeviceState(_deviceState.ConnectionStatus, $"刷新状态错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 读取设备参数
        /// </summary>
        /// <param name="parameterAddress">参数地址</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>参数值</returns>
        public async Task<ushort> ReadParameterAsync(ushort parameterAddress, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || _modbusClient == null)
                throw new InvalidOperationException("设备未连接");

            try
            {
                // 读取单个寄存器
                ushort[] values = await _modbusClient.ReadHoldingRegistersAsync(
                    _device.SlaveId,
                    parameterAddress,
                    1,
                    cancellationToken);
                
                return values[0];
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "读取设备参数时出错: 地址={Address}", parameterAddress);
                throw;
            }
        }

        /// <summary>
        /// 写入设备参数
        /// </summary>
        /// <param name="parameterAddress">参数地址</param>
        /// <param name="value">参数值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        public async Task<bool> WriteParameterAsync(ushort parameterAddress, ushort value, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || _modbusClient == null)
                throw new InvalidOperationException("设备未连接");

            try
            {
                // 写入单个寄存器
                return await _modbusClient.WriteSingleRegisterAsync(
                    _device.SlaveId,
                    parameterAddress,
                    value,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "写入设备参数时出错: 地址={Address}, 值={Value}", parameterAddress, value);
                throw;
            }
        }

        /// <summary>
        /// 读取多个设备参数
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="count">参数数量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>参数值数组</returns>
        public async Task<ushort[]> ReadParametersAsync(ushort startAddress, ushort count, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || _modbusClient == null)
                throw new InvalidOperationException("设备未连接");

            try
            {
                // 读取多个寄存器
                return await _modbusClient.ReadHoldingRegistersAsync(
                    _device.SlaveId,
                    startAddress,
                    count,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "读取多个设备参数时出错: 起始地址={StartAddress}, 数量={Count}", startAddress, count);
                throw;
            }
        }

        /// <summary>
        /// 写入多个设备参数
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">参数值数组</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        public async Task<bool> WriteParametersAsync(ushort startAddress, ushort[] values, CancellationToken cancellationToken = default)
        {
            if (!IsConnected || _modbusClient == null)
                throw new InvalidOperationException("设备未连接");

            try
            {
                // 写入多个寄存器
                return await _modbusClient.WriteMultipleRegistersAsync(
                    _device.SlaveId,
                    startAddress,
                    values,
                    cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "写入多个设备参数时出错: 起始地址={StartAddress}, 数量={Count}", startAddress, values.Length);
                throw;
            }
        }

        /// <summary>
        /// 创建通信通道
        /// </summary>
        /// <returns>通信通道</returns>
        private IModbusChannel CreateChannel()
        {
            return GetConnectionType() switch
            {
                ModbusConnectionType.Serial => CreateSerialChannel(),
                ModbusConnectionType.Network => CreateNetworkChannel(),
                ModbusConnectionType.Usb => throw new NotImplementedException("USB通道尚未实现"),
                _ => throw new ArgumentException($"不支持的连接类型: {_device.ConnectionType}")
            };
        }

        /// <summary>
        /// 获取连接类型枚举
        /// </summary>
        private ModbusConnectionType GetConnectionType()
        {
            if (Enum.TryParse<ModbusConnectionType>(_device.ConnectionType, out var connectionType))
            {
                return connectionType;
            }
            return ModbusConnectionType.Serial; // 默认返回串口连接
        }

        /// <summary>
        /// 获取传输类型枚举
        /// </summary>
        private ModbusTransportType GetTransportType()
        {
            if (Enum.TryParse<ModbusTransportType>(_device.TransportType, out var transportType))
            {
                return transportType;
            }
            return ModbusTransportType.Rtu; // 默认返回RTU模式
        }

        /// <summary>
        /// 创建串口通道
        /// </summary>
        /// <returns>串口通道</returns>
        private IModbusChannel CreateSerialChannel()
        {
            try
            {
                return _channelFactory.CreateSerialChannel(
                    _device.PortName,
                    _device.BaudRate,
                    _device.DataBits,
                    (Parity)_device.Parity,
                    (StopBits)_device.StopBits,
                    _device.ReadTimeout,
                    _device.WriteTimeout);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "创建串口通道失败");
                throw new InvalidOperationException($"创建串口通道失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建网络通道
        /// </summary>
        /// <returns>网络通道</returns>
        private IModbusChannel CreateNetworkChannel()
        {
            try
            {
                return _channelFactory.CreateNetworkChannel(
                    _device.HostAddress,
                    _device.Port,
                    _device.ConnectionTimeout);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "创建网络通道失败");
                throw new InvalidOperationException($"创建网络通道失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 读取设备信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        private async Task ReadDeviceInfoAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected || _modbusClient == null)
                return;

            try
            {
                var value = await _modbusClient.ReadFloatAsync(5, 41214, EndianHelper.WordOrder.BigEndian, cancellationToken);

                // 这里的实现取决于具体设备的寄存器映射
                // 以下仅为示例，实际应用中需要根据具体设备进行调整

                // 读取设备型号
                ushort[] modelRegisters = await _modbusClient.ReadHoldingRegistersAsync(
                    5,
                    0x0000, // 设备型号寄存器地址
                    5, // 寄存器数量
                    cancellationToken);
                
                // 读取固件版本
                ushort[] versionRegisters = await _modbusClient.ReadHoldingRegistersAsync(
                    _device.SlaveId,
                    0x0010, // 固件版本寄存器地址
                    2, // 寄存器数量
                    cancellationToken);
                
                // 读取设备状态
                ushort[] statusRegisters = await _modbusClient.ReadHoldingRegistersAsync(
                    _device.SlaveId,
                    0x0020, // 设备状态寄存器地址
                    1, // 寄存器数量
                    cancellationToken);
                
                // 更新设备信息
                string modelName = ConvertRegistersToString(modelRegisters);
                string firmwareVersion = $"{versionRegisters[0]}.{versionRegisters[1]}";
                Application.Devices.DeviceStatus deviceStatus = ConvertToDeviceStatus(statusRegisters[0]);
                
                _deviceState.ModelName = modelName;
                _deviceState.FirmwareVersion = firmwareVersion;
                _deviceState.DeviceStatus = deviceStatus;
                _deviceState.LastUpdated = DateTime.Now;
                
                // 更新Domain实体
                _device.Status = MapToDeviceStatus(deviceStatus);
                _device.LastUpdatedAt = DateTime.Now;
                
                // 通知状态变更
                _deviceStateSubject.OnNext(_deviceState);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "读取设备信息时出错");
                throw;
            }
        }

        /// <summary>
        /// 将寄存器值转换为字符串
        /// </summary>
        /// <param name="registers">寄存器值</param>
        /// <returns>字符串</returns>
        private static string ConvertRegistersToString(ushort[] registers)
        {
            // 将每个寄存器转换为两个ASCII字符
            char[] chars = new char[registers.Length * 2];
            for (int i = 0; i < registers.Length; i++)
            {
                chars[i * 2] = (char)(registers[i] >> 8);
                chars[i * 2 + 1] = (char)(registers[i] & 0xFF);
            }
            
            // 移除尾部的空字符
            return new string(chars).TrimEnd('\0');
        }

        /// <summary>
        /// 将寄存器值转换为设备状态
        /// </summary>
        /// <param name="statusRegister">状态寄存器值</param>
        /// <returns>设备状态</returns>
        private static Application.Devices.DeviceStatus ConvertToDeviceStatus(ushort statusRegister)
        {
            // 这里的实现取决于具体设备的状态定义
            // 以下仅为示例，实际应用中需要根据具体设备进行调整
            return statusRegister switch
            {
                0 => Application.Devices.DeviceStatus.Normal,
                1 => Application.Devices.DeviceStatus.Warning,
                2 => Application.Devices.DeviceStatus.Fault,
                _ => Application.Devices.DeviceStatus.Normal
            };
        }

        /// <summary>
        /// 更新设备状态
        /// </summary>
        /// <param name="connectionStatus">连接状态</param>
        /// <param name="statusMessage">状态消息</param>
        private void UpdateDeviceState(Application.Devices.ConnectionStatus connectionStatus, string statusMessage)
        {
            _deviceState.ConnectionStatus = connectionStatus;
            _deviceState.StatusMessage = statusMessage;
            _deviceState.LastUpdated = DateTime.Now;
            
            // 通知状态变更
            _deviceStateSubject.OnNext(_deviceState);
            
            _logger.Information("设备状态已更新: {Status} - {Message}", connectionStatus, statusMessage);
            
            // 更新设备状态到Domain实体
            _device.ConnectionStatus = MapToDeviceConnectionStatus(connectionStatus);
            _device.LastUpdatedAt = DateTime.Now;
            
            // 如果已连接，则更新最后连接时间
            if (connectionStatus == Application.Devices.ConnectionStatus.Connected)
            {
                _device.LastConnectedAt = DateTime.Now;
            }
        }
        
        /// <summary>
        /// 将应用层连接状态映射到领域层连接状态
        /// </summary>
        /// <param name="status">应用层连接状态</param>
        /// <returns>领域层连接状态</returns>
        private Domain.Enums.ConnectionStatus MapToDeviceConnectionStatus(Application.Devices.ConnectionStatus status)
        {
            return status switch
            {
                Application.Devices.ConnectionStatus.Connected => Domain.Enums.ConnectionStatus.Connected,
                Application.Devices.ConnectionStatus.Connecting => Domain.Enums.ConnectionStatus.Connecting,
                Application.Devices.ConnectionStatus.Disconnected => Domain.Enums.ConnectionStatus.Disconnected,
                Application.Devices.ConnectionStatus.Error => Domain.Enums.ConnectionStatus.Error,
                Application.Devices.ConnectionStatus.Reconnecting => Domain.Enums.ConnectionStatus.Reconnecting,
                _ => Domain.Enums.ConnectionStatus.Disconnected
            };
        }
        
        /// <summary>
        /// 将应用层设备状态映射到领域层设备状态
        /// </summary>
        /// <param name="status">应用层设备状态</param>
        /// <returns>领域层设备状态</returns>
        private Domain.Enums.DeviceStatus MapToDeviceStatus(Application.Devices.DeviceStatus status)
        {
            return status switch
            {
                Application.Devices.DeviceStatus.Normal => Domain.Enums.DeviceStatus.Normal,
                Application.Devices.DeviceStatus.Warning => Domain.Enums.DeviceStatus.Warning,
                Application.Devices.DeviceStatus.Fault => Domain.Enums.DeviceStatus.Fault,
                _ => Domain.Enums.DeviceStatus.Normal
            };
        }

        /// <summary>
        /// 启动自动重连
        /// </summary>
        private void StartAutoReconnect()
        {
            // 停止现有的重连任务
            StopAutoReconnect();
            
            // 创建新的取消令牌
            _reconnectCts = new CancellationTokenSource();
            
            // 启动重连任务
            _ = Task.Run(async () =>
            {
                int attempts = 0;
                
                while (!_reconnectCts.Token.IsCancellationRequested && attempts < _autoReconnectMaxAttempts)
                {
                    try
                    {
                        // 等待一段时间
                        await Task.Delay(_autoReconnectDelayMilliseconds, _reconnectCts.Token);
                        
                        // 检查连接状态
                        if (_modbusClient == null || !_modbusClient.IsConnected)
                        {
                            _logger.Information("尝试自动重连 (尝试 {Attempt}/{MaxAttempts})", attempts + 1, _autoReconnectMaxAttempts);
                            
                            // 更新设备状态
                            UpdateDeviceState(Application.Devices.ConnectionStatus.Reconnecting, $"正在重新连接 (尝试 {attempts + 1}/{_autoReconnectMaxAttempts})...");
                            
                            // 尝试重新连接
                            bool success = await ConnectAsync(_reconnectCts.Token);
                            
                            if (success)
                            {
                                _logger.Information("自动重连成功");
                                break;
                            }
                            
                            attempts++;
                        }
                        else
                        {
                            // 连接正常，重置尝试次数
                            attempts = 0;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "自动重连时出错");
                        attempts++;
                    }
                }
                
                if (attempts >= _autoReconnectMaxAttempts)
                {
                    _logger.Warning("自动重连失败，已达到最大尝试次数");
                    UpdateDeviceState(Application.Devices.ConnectionStatus.Error, "自动重连失败，已达到最大尝试次数");
                }
            }, _reconnectCts.Token);
        }

        /// <summary>
        /// 停止自动重连
        /// </summary>
        private void StopAutoReconnect()
        {
            _reconnectCts?.Cancel();
            _reconnectCts?.Dispose();
            _reconnectCts = null;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 停止自动重连
                StopAutoReconnect();
                
                // 断开连接
                if (IsConnected)
                {
                    DisconnectAsync().GetAwaiter().GetResult();
                }
                
                // 释放资源
                _modbusClient?.Dispose();
                _modbusChannel?.Dispose();
                _semaphore.Dispose();
                _deviceStateSubject.Dispose();
            }

            _isDisposed = true;
        }
    }
}
