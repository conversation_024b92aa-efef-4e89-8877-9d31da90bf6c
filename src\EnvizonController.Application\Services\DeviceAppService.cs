using EnvizonController.Application.DataCollection;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Shared.Enums;
using Microsoft.Extensions.Logging;
using ConnectionStatus = EnvizonController.Domain.Enums.ConnectionStatus;
using DeviceStatus = EnvizonController.Domain.Enums.DeviceStatus;

namespace EnvizonController.Application.Services
{
    /// <summary>
    /// 设备应用服务实现
    /// </summary>
    public class DeviceAppService : IDeviceAppService
    {
        private readonly IDeviceRepository _deviceRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DeviceAppService> _logger;
        private readonly IQuartzDeviceCollectionService _quartzDeviceCollectionService;
        private readonly IProtocolRepository _protocolRepository; // 新增协议仓储

        public DeviceAppService(
            IDeviceRepository deviceRepository,
            IUnitOfWork unitOfWork,
            ILogger<DeviceAppService> logger,
            IQuartzDeviceCollectionService quartzDeviceCollectionService,
            IProtocolRepository protocolRepository)
        {
            _deviceRepository = deviceRepository;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _quartzDeviceCollectionService = quartzDeviceCollectionService;
            _protocolRepository = protocolRepository;
        }

        /// <summary>
        /// 获取所有自动启动设备，并为每个设备赋值协议（不保存到数据库）
        /// </summary>
        public async Task<IEnumerable<Device>> GetAllAutoStartDevicesWithProtocolAsync()
        {
            var devices = await _deviceRepository.GetAllAsync();
            var autoStartDevices = devices.Where(d => d.AutoStart).ToList();

            foreach (var device in autoStartDevices)
            {
                if (device.ProtocolId != 0)
                {
                    device.Protocol = await _protocolRepository.GetByIdAsync(device.ProtocolId);
                }
            }

            return autoStartDevices;
        }

        /// <summary>
        /// 获取所有设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetAllDevicesAsync()
        {
            return await _deviceRepository.GetAllAsync();
        }

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        public async Task<Device?> GetDeviceByIdAsync(long id)
        {
            return await _deviceRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// 创建设备
        /// </summary>
        public async Task<Device> CreateDeviceAsync(Device device)
        {
            await _deviceRepository.AddAsync(device);
            await _unitOfWork.SaveChangesAsync();

            // 如果设备配置为自动启动，则创建采集任务
            if (device.AutoStart)
            {
                await _quartzDeviceCollectionService.ScheduleDeviceCollectionTask(device);
                _logger.LogInformation("已为设备 {DeviceId} 创建数据采集任务", device.Id);
            }

            return device;
        }

        /// <summary>
        /// 更新设备
        /// </summary>
        public async Task<Device> UpdateDeviceAsync(Device device)
        {
            var existingDevice = await _deviceRepository.GetByIdAsync(device.Id);
            bool wasAutoStart = existingDevice?.AutoStart ?? false;

            _deviceRepository.Update(device);
            await _unitOfWork.SaveChangesAsync();

            // 处理采集任务变更
            if (device.AutoStart)
            {
                // 更新或创建采集任务
                await _quartzDeviceCollectionService.UpdateDeviceCollectionTask(device);
                _logger.LogInformation("已更新设备 {DeviceId} 的数据采集任务", device.Id);
            }
            else if (wasAutoStart && !device.AutoStart)
            {
                // 取消采集任务
                await _quartzDeviceCollectionService.UnscheduleDeviceCollectionTask(device.Id);
                _logger.LogInformation("已取消设备 {DeviceId} 的数据采集任务", device.Id);
            }

            return device;
        }

        /// <summary>
        /// 删除设备
        /// </summary>
        public async Task DeleteDeviceAsync(long id)
        {
            // 确保删除对应的采集任务
            await _quartzDeviceCollectionService.UnscheduleDeviceCollectionTask(id);
            _logger.LogInformation("已取消设备 {DeviceId} 的数据采集任务", id);

            await _deviceRepository.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();
        }

        /// <summary>
        /// 初始化默认设备（如果不存在）
        /// </summary>
        public async Task<Device?> InitializeDefaultDeviceAsync()
        {
            try
            {
                var devices = await _deviceRepository.GetAllAsync();

                // 如果没有设备，创建一个默认设备
                if (!devices.Any())
                {
                    _logger.LogInformation("数据库中没有设备，创建默认设备");

                    // 使用Create方法创建设备
                    var defaultDevice = Device.Create(
                        name: "默认测试设备",
                        slaveId: 5,
                        protocolId: 1,
                        connectionType: "Serial",
                        transportType: "RTU");

                    // 设置其他属性
                    defaultDevice.TestId = 1;
                    defaultDevice.PortName = "COM11";
                    defaultDevice.BaudRate = 38400;
                    defaultDevice.DataBits = 8;
                    defaultDevice.Parity = 0;
                    defaultDevice.StopBits = 1;
                    defaultDevice.ConnectionTimeout = 5000;
                    defaultDevice.ReadTimeout = 1000;
                    defaultDevice.WriteTimeout = 1000;
                    defaultDevice.Status = DeviceStatus.Normal;
                    defaultDevice.ConnectionStatus = ConnectionStatus.Disconnected;
                    defaultDevice.OperatingStatus = DeviceOperatingStatus.Stopped;
                    defaultDevice.Remarks = "系统自动创建的默认设备";
                    defaultDevice.AutoStart = true;
                    defaultDevice.CollectionIntervalMs = 1000;  // 默认采集间隔为1秒

                    await _deviceRepository.AddAsync(defaultDevice);
                    var d = await _deviceRepository.GetAllAsync();
                    await _unitOfWork.SaveChangesAsync();
                    var d2 = await _deviceRepository.GetAllAsync();
                    _logger.LogInformation($"已创建默认设备，ID: {defaultDevice.Id}, 名称: {defaultDevice.Name}");

                    // 为默认设备创建采集任务
                    if (defaultDevice.AutoStart)
                    {
                        await _quartzDeviceCollectionService.ScheduleDeviceCollectionTask(defaultDevice);
                        _logger.LogInformation("已为默认设备 {DeviceId} 创建数据采集任务", defaultDevice.Id);
                    }

                    return defaultDevice;
                }

                _logger.LogInformation($"数据库中已存在 {devices.Count()} 个设备，无需创建默认设备");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化默认设备时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 启动设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        public async Task<Device?> StartDeviceAsync(long id)
        {
            var device = await _deviceRepository.GetByIdAsync(id);
            if (device == null)
            {
                _logger.LogWarning("尝试启动不存在的设备，ID: {DeviceId}", id);
                return null;
            }

            // 检查设备当前状态，只有在停止或暂停状态下才能启动
            if (device.OperatingStatus != DeviceOperatingStatus.Stopped &&
                device.OperatingStatus != DeviceOperatingStatus.Paused)
            {
                _logger.LogWarning("设备当前状态不允许启动操作，设备ID: {DeviceId}, 当前状态: {Status}",
                    id, device.OperatingStatus);
                return device;
            }

            // 更新设备运行状态为"运行"
            device.OperatingStatus = DeviceOperatingStatus.Running;
            device.LastUpdatedAt = DateTime.Now;

            _deviceRepository.Update(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("设备 {DeviceId} 已启动运行", id);
            return device;
        }

        /// <summary>
        /// 停止设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        public async Task<Device?> StopDeviceAsync(long id)
        {
            var device = await _deviceRepository.GetByIdAsync(id);
            if (device == null)
            {
                _logger.LogWarning("尝试停止不存在的设备，ID: {DeviceId}", id);
                return null;
            }

            // 只有在运行或暂停状态下才能停止
            if (device.OperatingStatus != DeviceOperatingStatus.Running &&
                device.OperatingStatus != DeviceOperatingStatus.Paused)
            {
                _logger.LogWarning("设备当前状态不允许停止操作，设备ID: {DeviceId}, 当前状态: {Status}",
                    id, device.OperatingStatus);
                return device;
            }

            // 更新设备运行状态为"停止"
            device.OperatingStatus = DeviceOperatingStatus.Stopped;
            device.LastUpdatedAt = DateTime.Now;

            _deviceRepository.Update(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("设备 {DeviceId} 已停止运行", id);
            return device;
        }

        /// <summary>
        /// 暂停设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        public async Task<Device?> PauseDeviceAsync(long id)
        {
            var device = await _deviceRepository.GetByIdAsync(id);
            if (device == null)
            {
                _logger.LogWarning("尝试暂停不存在的设备，ID: {DeviceId}", id);
                return null;
            }

            // 只有在运行状态下才能暂停
            if (device.OperatingStatus != DeviceOperatingStatus.Running)
            {
                _logger.LogWarning("设备当前状态不允许暂停操作，设备ID: {DeviceId}, 当前状态: {Status}",
                    id, device.OperatingStatus);
                return device;
            }

            // 更新设备运行状态为"暂停"
            device.OperatingStatus = DeviceOperatingStatus.Paused;
            device.LastUpdatedAt = DateTime.Now;

            _deviceRepository.Update(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("设备 {DeviceId} 已暂停运行", id);
            return device;
        }

        /// <summary>
        /// 继续设备操作
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>更新后的设备</returns>
        public async Task<Device?> ContinueDeviceAsync(long id)
        {
            var device = await _deviceRepository.GetByIdAsync(id);
            if (device == null)
            {
                _logger.LogWarning("尝试继续不存在的设备，ID: {DeviceId}", id);
                return null;
            }

            // 只有在暂停状态下才能继续
            if (device.OperatingStatus != DeviceOperatingStatus.Paused)
            {
                _logger.LogWarning("设备当前状态不允许继续操作，设备ID: {DeviceId}, 当前状态: {Status}",
                    id, device.OperatingStatus);
                return device;
            }

            // 更新设备运行状态为"运行"
            device.OperatingStatus = DeviceOperatingStatus.Running;
            device.LastUpdatedAt = DateTime.Now;

            _deviceRepository.Update(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("设备 {DeviceId} 已继续运行", id);
            return device;
        }
    }
}
