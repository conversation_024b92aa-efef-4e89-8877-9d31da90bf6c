using EnvizonController.Mqtt.Server.Models;
using EnvizonController.Mqtt.Server.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace EnvizonController.Mqtt.Server.Handlers
{
    public class DeviceStatusMessageHandler : IMessageHandler
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<DeviceStatusMessageHandler> _logger;
        private readonly Regex _topicRegex;

        public DeviceStatusMessageHandler(INotificationService notificationService, ILogger<DeviceStatusMessageHandler> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
            _topicRegex = new Regex(@"^devices/(.+)/status$", RegexOptions.Compiled);
        }

        public bool CanHandle(string topic)
        {
            return _topicRegex.IsMatch(topic);
        }

        public void HandleMessage(string topic, string message, string clientId)
        {
            try
            {
                var match = _topicRegex.Match(topic);
                if (match.Success)
                {
                    var deviceId = match.Groups[1].Value;

                    var status = JsonSerializer.Deserialize<DeviceStatusNotification>(message);
                    if (status == null)
                    {
                        _logger.LogWarning($"无法解析设备状态消息: {message}");
                        return;
                    }

                    // 确保设备ID与主题匹配
                    if (string.IsNullOrEmpty(status.DeviceId))
                    {
                        status.DeviceId = deviceId;
                    }

                    _logger.LogInformation($"处理设备状态消息: 设备={status.DeviceId}, 状态={status.Status}");
                    
                    // 发布通知
                    _notificationService.Publish(status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备状态消息时发生错误");
            }
        }
    }
} 