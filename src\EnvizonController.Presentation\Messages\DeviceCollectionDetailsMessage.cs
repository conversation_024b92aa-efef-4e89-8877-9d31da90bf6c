using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 设备采集详情消息
/// </summary>
public class DeviceCollectionDetailsMessage : ValueChangedMessage<DeviceCollectionDetailsDTO>
{
    /// <summary>
    /// 设备采集详情
    /// </summary>
    public DeviceCollectionDetailsDTO Details => Value;

    /// <summary>
    /// 创建设备采集详情消息
    /// </summary>
    /// <param name="details">设备采集详情</param>
    public DeviceCollectionDetailsMessage(DeviceCollectionDetailsDTO details) : base(details)
    {
    }
} 