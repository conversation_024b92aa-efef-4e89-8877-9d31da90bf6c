using Avalonia.Data.Converters;
using Avalonia.Media;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警级别转换为对应的颜色画刷
    /// </summary>
    public class LevelToBrushConverter : IValueConverter
    {
        /// <summary>
        /// 将报警级别转换为对应的颜色画刷
        /// </summary>
        /// <param name="value">报警级别</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>对应的颜色画刷</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not AlarmSeverity level) return new SolidColorBrush(Colors.Gray);

            return level switch
            {
                AlarmSeverity.High => new SolidColorBrush(Color.Parse("#E53935")),    // 红色
                AlarmSeverity.Medium => new SolidColorBrush(Color.Parse("#FFA000")),  // 橙色
                AlarmSeverity.Low => new SolidColorBrush(Color.Parse("#4CAF50")),     // 绿色
                _ => new SolidColorBrush(Colors.Gray)
            };
        }

        /// <summary>
        /// 将颜色画刷转换回报警级别（不支持）
        /// </summary>
        /// <param name="value">颜色画刷</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 