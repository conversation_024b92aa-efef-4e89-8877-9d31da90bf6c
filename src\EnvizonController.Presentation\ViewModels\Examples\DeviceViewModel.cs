using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace EnvizonController.Presentation.ViewModels.Examples
{
    /// <summary>
    /// 这是一个示例ViewModel，演示如何在Presentation层使用API服务
    /// </summary>
    public class DeviceViewModel : ViewModelBase
    {
        private readonly IDeviceApiService _deviceApiService;
        private ObservableCollection<DeviceDto> _devices;

        /// <summary>
        /// 设备集合
        /// </summary>
        public ObservableCollection<DeviceDto> Devices
        {
            get => _devices;
            set => SetProperty(ref _devices, value);
        }

        /// <summary>
        /// 构造函数 - 通过依赖注入获取DeviceApiService
        /// </summary>
        /// <param name="deviceApiService">设备API服务</param>
        public DeviceViewModel(IDeviceApiService deviceApiService)
        {
            _deviceApiService = deviceApiService;
            Devices = new ObservableCollection<DeviceDto>();
        }

        /// <summary>
        /// 加载设备列表
        /// </summary>
        public async Task LoadDevicesAsync()
        {
            var result = await _deviceApiService.GetDevicesAsync();
            
            if (result.IsSuccess)
            {
                Devices.Clear();
                foreach (var device in result.Data.Items)
                {
                    Devices.Add(device);
                }
            }
            else
            {
                // 处理错误
                Debug.WriteLine($"加载设备失败: {result.ErrorMessage}");
            }
        }
    }

    // =========== 以下是示例模型类 ===========
    // 注意: 这些类仅供示例使用，实际应用中应引用相应的项目命名空间

    /// <summary>
    /// 示例：设备API服务接口
    /// </summary>
    public interface IDeviceApiService
    {
        Task<ApiResult<PagedResultDto<DeviceDto>>> GetDevicesAsync(int page = 1, int pageSize = 20);
        Task<ApiResult<DeviceDto>> GetDeviceAsync(long id);
        Task<ApiResult<DeviceDto>> CreateDeviceAsync(DeviceDto device);
        Task<ApiResult<DeviceDto>> UpdateDeviceAsync(long id, DeviceDto device);
        Task<ApiResult<bool>> DeleteDeviceAsync(long id);
    }

    /// <summary>
    /// 设备DTO示例 - 仅用于演示
    /// </summary>
    public class DeviceDto
    {
        public long Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUpdatedAt { get; set; }
    }

    /// <summary>
    /// 分页结果DTO示例 - 仅用于演示
    /// </summary>
    public class PagedResultDto<T>
    {
        public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (TotalCount + PageSize - 1) / PageSize;
    }

    /// <summary>
    /// API结果包装器示例 - 仅用于演示
    /// </summary>
    public class ApiResult<T>
    {
        public bool IsSuccess { get; private set; }
        public string ErrorMessage { get; private set; }
        public int StatusCode { get; private set; }
        public T Data { get; private set; }

        private ApiResult(bool isSuccess, T data, string errorMessage, int statusCode)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            StatusCode = statusCode;
        }

        public static ApiResult<T> Success(T data, int statusCode = 200)
        {
            return new ApiResult<T>(true, data, null, statusCode);
        }

        public static ApiResult<T> Failure(string errorMessage, int statusCode)
        {
            return new ApiResult<T>(false, default, errorMessage, statusCode);
        }
    }
}