namespace EnvizonController.ApiClient.Results
{
    /// <summary>
    /// API结果实现
    /// </summary>
    public class Result<T> : IResult<T>
    {
        public bool IsSuccess { get; private set; }
        public string ErrorMessage { get; private set; }
        /// <summary>
        /// HTTP 状态码，表示API请求的结果状态。
        /// 常见值包括：
        /// <list type="bullet">
        /// <item>200: 请求成功 (OK)</item>
        /// <item>201: 资源创建成功 (Created)</item>
        /// <item>400: 客户端请求错误 (Bad Request)</item>
        /// <item>401: 未授权 (Unauthorized)</item>
        /// <item>404: 资源未找到 (Not Found)</item>
        /// <item>500: 服务器内部错误 (Internal Server Error)</item>
        /// </list>
        /// </summary>
        public int StatusCode { get; private set; }
        public T Data { get; private set; }

        private Result(bool isSuccess, T data, string errorMessage, int statusCode)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            StatusCode = statusCode;
        }

        public static Result<T> Success(T data, int statusCode = 200)
        {
            return new Result<T>(true, data, null, statusCode);
        }

        public static Result<T> Failure(string errorMessage, int statusCode)
        {
            return new Result<T>(false, default, errorMessage, statusCode);
        }
    }
}