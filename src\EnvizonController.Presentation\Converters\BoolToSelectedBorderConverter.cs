using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为选中/非选中状态的边框色
    /// </summary>
    public class BoolToSelectedBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected 
                    ? new SolidColorBrush(Color.Parse("#0DF0FF")) // 选中状态的边框色（青色）
                    : new SolidColorBrush(Color.Parse("#2A384A")); // 非选中状态的边框色（深灰蓝色）
            }

            return new SolidColorBrush(Color.Parse("#2A384A"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}