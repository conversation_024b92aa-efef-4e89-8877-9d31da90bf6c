using EnvizonController.ApiClient.Services;

namespace EnvizonController.ApiClient.Factories
{
    /// <summary>
    /// API客户端工厂接口
    /// </summary>
    public interface IApiClientFactory
    {
        IDeviceApiService CreateDeviceApiService();
        IAlarmApiService CreateAlarmApiService();
        IDataPointApiService CreateDataPointApiService();
        ITestItemApiService CreateTestItemApiService();
        IDeviceTestApiService CreateDeviceTestApiService();
    }
}