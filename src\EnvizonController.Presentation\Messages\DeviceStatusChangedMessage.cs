﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;
 using EnvizonController.Shared.DTOs;

 namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 设备状态变更消息
/// </summary>
public class DeviceStatusChangedMessage : ValueChangedMessage<DeviceDto>
{
    /// <summary>
    /// 设备
    /// </summary>
    public DeviceDto Device => Value;

    /// <summary>
    /// 创建设备状态变更消息
    /// </summary>
    /// <param name="device">设备</param>
    public DeviceStatusChangedMessage(DeviceDto device) : base(device)
    {
    }
}
