# MQTT服务架构设计

## 概述

本文档描述了EnvizonController项目中MQTT服务的新架构设计，旨在将MQTT连接从UI初始化流程中分离出来，实现独立的MQTT服务。

## 当前实现分析

当前的MQTT连接实现存在以下主要问题：

1. **紧耦合于初始化流程** - MQTT连接在`AppInitializationService`的`ConnectMqttAsync`方法中实现，作为应用程序启动过程的一部分
2. **缺乏状态反馈** - 虽然实现了基本的连接功能，但没有向用户提供连接状态的实时反馈
3. **简单的重连机制** - 当前实现在`HandleDisconnectedAsync`方法中有基础的重连逻辑，但缺乏更复杂的重试策略
4. **阻塞UI线程** - 当前实现可能会在连接过程中阻塞UI线程
5. **没有独立服务设计** - MQTT功能没有作为一个独立的服务运行，而是嵌入在初始化流程中

## 新MQTT服务架构设计

### 1. 整体架构

```mermaid
classDiagram
    class IMqttConnectionManager {
        <<interface>>
        +ConnectionStatus Status
        +ConnectionStatusChangedEvent
        +ConnectAsync()
        +DisconnectAsync()
        +GetConnectionConfiguration()
        +UpdateConnectionConfiguration()
    }
    
    class MqttConnectionManager {
        -IMqttClientService _mqttClient
        -IMessageHandlerRegistry _handlerRegistry
        -ILogger _logger
        -ConnectionStatus _status
        -CancellationTokenSource _reconnectCts
        -ReconnectPolicy _reconnectPolicy
        -ConnectionConfiguration _config
        +ConnectionStatus Status
        +ConnectionStatusChangedEvent
        +ConnectAsync()
        +DisconnectAsync()
        +GetConnectionConfiguration()
        +UpdateConnectionConfiguration()
        -HandleConnectionStatusChanged()
        -StartReconnectionLoop()
        -StopReconnectionLoop()
    }
    
    class ReconnectPolicy {
        +int MaxAttempts
        +TimeSpan InitialDelay
        +TimeSpan MaxDelay
        +TimeSpan BackoffMultiplier
        +CalculateDelay(attemptCount)
    }
    
    class ConnectionConfiguration {
        +string BrokerAddress
        +int Port
        +bool UseTls
        +string Username
        +string Password
        +string ClientId
        +TimeSpan KeepAliveInterval
        +MqttQualityOfServiceLevel DefaultQoS
    }
    
    class MqttConnectionStatusChangedMessage {
        +ConnectionStatus OldStatus
        +ConnectionStatus NewStatus
        +string Message
        +Exception Error
    }
    
    class MqttTopicSubscriptionManager {
        -IMqttClientService _mqttClient
        -IMessageHandlerRegistry _handlerRegistry
        -ILogger _logger
        -Dictionary<string, MqttQualityOfServiceLevel> _subscriptions
        +SubscribeAsync(topic, handler, qos)
        +UnsubscribeAsync(topic, handler)
        +GetActiveSubscriptions()
    }
    
    IMqttConnectionManager <|.. MqttConnectionManager
    MqttConnectionManager --> ReconnectPolicy
    MqttConnectionManager --> ConnectionConfiguration
    MqttConnectionManager --> IMqttClientService
    MqttConnectionManager --> IMessageHandlerRegistry
    MqttTopicSubscriptionManager --> IMqttClientService
    MqttTopicSubscriptionManager --> IMessageHandlerRegistry
```

所有MQTT连接管理相关的组件都将在`EnvizonController.Presentation`命名空间中实现。

### 2. 连接状态管理和通知机制

```mermaid
stateDiagram-v2
    [*] --> Disconnected
    Disconnected --> Connecting: ConnectAsync()
    Connecting --> Connected: 连接成功
    Connecting --> Error: 连接失败
    Connected --> Disconnected: DisconnectAsync()
    Connected --> Reconnecting: 连接中断
    Error --> Reconnecting: 自动重试
    Error --> Disconnected: 放弃重试
    Reconnecting --> Connected: 重连成功
    Reconnecting --> Error: 重连失败
```

### 3. 错误处理和重连策略

```mermaid
flowchart TD
    A[检测到连接断开] --> B{是否启用自动重连?}
    B -->|是| C[开始重连过程]
    B -->|否| D[保持断开状态]
    C --> E[计算重连延迟]
    E --> F[等待延迟时间]
    F --> G[尝试重新连接]
    G --> H{连接成功?}
    H -->|是| I[恢复正常状态]
    H -->|否| J{已达最大重试次数?}
    J -->|否| E
    J -->|是| K[进入永久错误状态]
```

### 4. 组件详细说明

#### IMqttConnectionManager 接口

MQTT连接管理的主要接口，包含以下功能：
- 提供连接状态属性和状态变更事件
- 连接和断开连接方法
- 获取和更新连接配置

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接管理器接口，负责管理MQTT连接的生命周期和状态
    /// </summary>
    public interface IMqttConnectionManager
    {
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        ConnectionStatus Status { get; }

        /// <summary>
        /// 当连接状态变化时发生
        /// </summary>
        event EventHandler<MqttConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 异步连接到MQTT服务器
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        Task ConnectAsync();

        /// <summary>
        /// 异步断开与MQTT服务器的连接
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        Task DisconnectAsync();

        /// <summary>
        /// 获取当前连接配置
        /// </summary>
        /// <returns>连接配置</returns>
        ConnectionConfiguration GetConnectionConfiguration();

        /// <summary>
        /// 更新连接配置
        /// </summary>
        /// <param name="configuration">新的连接配置</param>
        void UpdateConnectionConfiguration(ConnectionConfiguration configuration);
    }
}
```

#### MqttConnectionManager 实现类

管理MQTT连接的生命周期：
- 跟踪连接状态并发布状态变更通知
- 实现智能重连机制
- 非阻塞操作，在后台线程执行

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接管理器实现，负责管理MQTT连接的生命周期和状态
    /// </summary>
    public class MqttConnectionManager : IMqttConnectionManager
    {
        private readonly IMqttClientService _mqttClient;
        private readonly IMessageHandlerRegistry _handlerRegistry;
        private readonly ILogger _logger;
        private ConnectionStatus _status;
        private CancellationTokenSource _reconnectCts;
        private readonly ReconnectPolicy _reconnectPolicy;
        private ConnectionConfiguration _config;

        public ConnectionStatus Status => _status;

        public event EventHandler<MqttConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        public MqttConnectionManager(
            IMqttClientService mqttClient,
            IMessageHandlerRegistry handlerRegistry,
            ReconnectPolicy reconnectPolicy,
            ConnectionConfiguration initialConfig,
            ILogger logger)
        {
            _mqttClient = mqttClient;
            _handlerRegistry = handlerRegistry;
            _reconnectPolicy = reconnectPolicy;
            _config = initialConfig;
            _logger = logger;
            _status = ConnectionStatus.Disconnected;

            // 注册断开连接事件处理
            _mqttClient.DisconnectedAsync += HandleMqttDisconnectedAsync;
        }

        public async Task ConnectAsync()
        {
            if (_status == ConnectionStatus.Connected || 
                _status == ConnectionStatus.Connecting ||
                _status == ConnectionStatus.Reconnecting)
            {
                _logger.Debug("已连接或正在连接到MQTT服务器，忽略连接请求");
                return;
            }

            try
            {
                UpdateStatus(ConnectionStatus.Connecting, "正在连接到MQTT服务器...");
                
                await _mqttClient.ConnectAsync();
                
                UpdateStatus(ConnectionStatus.Connected, "已成功连接到MQTT服务器");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接MQTT服务器失败");
                UpdateStatus(ConnectionStatus.Error, $"连接失败: {ex.Message}", ex);
                
                // 如果启用了自动重连，开始重连过程
                if (_reconnectPolicy.AutoReconnect)
                {
                    StartReconnectionLoop();
                }
            }
        }

        public async Task DisconnectAsync()
        {
            // 停止任何正在进行的重连尝试
            StopReconnectionLoop();
            
            if (_status == ConnectionStatus.Disconnected)
            {
                return;
            }

            try
            {
                await _mqttClient.DisconnectAsync();
                UpdateStatus(ConnectionStatus.Disconnected, "已断开与MQTT服务器的连接");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开MQTT连接时发生错误");
                // 即使出现异常，我们也将状态设置为已断开
                UpdateStatus(ConnectionStatus.Disconnected, "断开连接过程中发生错误，但已断开");
            }
        }

        public ConnectionConfiguration GetConnectionConfiguration()
        {
            return _config;
        }

        public void UpdateConnectionConfiguration(ConnectionConfiguration configuration)
        {
            _config = configuration;
            _logger.Information("MQTT连接配置已更新");
            
            // 如果当前已连接，可以选择重新连接以应用新配置
            // 这里我们简单地记录日志，实际实现中可以根据需求决定是否重新连接
            if (_status == ConnectionStatus.Connected)
            {
                _logger.Information("配置已更新，但需要重新连接才能应用新设置");
            }
        }

        private async Task HandleMqttDisconnectedAsync(MqttClientDisconnectedEventArgs args)
        {
            // 如果是正常断开（DisconnectAsync方法调用），不启动重连
            if (_status == ConnectionStatus.Disconnected)
            {
                return;
            }

            _logger.Warning($"与MQTT服务器的连接已断开: {args.Reason}");
            UpdateStatus(ConnectionStatus.Reconnecting, $"连接断开: {args.Reason}");

            // 启动重连过程（如果启用）
            if (_reconnectPolicy.AutoReconnect)
            {
                StartReconnectionLoop();
            }
            else
            {
                UpdateStatus(ConnectionStatus.Disconnected, "连接已断开且自动重连已禁用");
            }
        }

        private void UpdateStatus(ConnectionStatus newStatus, string message, Exception error = null)
        {
            var oldStatus = _status;
            _status = newStatus;
            
            var args = new MqttConnectionStatusChangedEventArgs
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message,
                Error = error
            };
            
            ConnectionStatusChanged?.Invoke(this, args);
            
            // 发布状态变更消息，以便UI和其他组件能够接收
            // 使用你的消息发布机制，例如：
            _messagePublisher.Publish(new MqttConnectionStatusChangedMessage
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message,
                Error = error
            });
        }

        private void StartReconnectionLoop()
        {
            StopReconnectionLoop();
            
            _reconnectCts = new CancellationTokenSource();
            
            // 在后台线程中启动重连循环
            Task.Run(async () => await ReconnectionLoopAsync(_reconnectCts.Token));
        }

        private void StopReconnectionLoop()
        {
            _reconnectCts?.Cancel();
            _reconnectCts?.Dispose();
            _reconnectCts = null;
        }

        private async Task ReconnectionLoopAsync(CancellationToken cancellationToken)
        {
            int attemptCount = 0;
            
            while (!cancellationToken.IsCancellationRequested && attemptCount < _reconnectPolicy.MaxAttempts)
            {
                attemptCount++;
                
                // 计算延迟时间（使用指数退避）
                var delay = _reconnectPolicy.CalculateDelay(attemptCount);
                
                _logger.Information($"将在 {delay.TotalSeconds} 秒后尝试重新连接 (尝试 {attemptCount}/{_reconnectPolicy.MaxAttempts})");
                UpdateStatus(ConnectionStatus.Reconnecting, $"重新连接尝试 {attemptCount}/{_reconnectPolicy.MaxAttempts}，等待 {delay.TotalSeconds} 秒");
                
                try
                {
                    await Task.Delay(delay, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break; // 重连过程被取消
                }
                
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }
                
                try
                {
                    _logger.Information($"尝试重新连接 (尝试 {attemptCount}/{_reconnectPolicy.MaxAttempts})");
                    UpdateStatus(ConnectionStatus.Reconnecting, $"正在尝试重新连接 (尝试 {attemptCount}/{_reconnectPolicy.MaxAttempts})");
                    
                    await _mqttClient.ConnectAsync();
                    
                    // 重新连接成功
                    _logger.Information("重新连接成功");
                    UpdateStatus(ConnectionStatus.Connected, "重新连接成功");
                    return; // 退出重连循环
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"重新连接尝试 {attemptCount} 失败");
                    // 继续循环尝试下一次重连
                }
            }
            
            // 如果达到最大尝试次数仍未成功
            if (attemptCount >= _reconnectPolicy.MaxAttempts && !cancellationToken.IsCancellationRequested)
            {
                _logger.Warning($"达到最大重连尝试次数 ({_reconnectPolicy.MaxAttempts})，放弃重连");
                UpdateStatus(ConnectionStatus.Error, $"重连失败：已达到最大尝试次数 ({_reconnectPolicy.MaxAttempts})");
            }
        }
    }
}
```

#### ReconnectPolicy 类

定义重连策略：
- 最大尝试次数、初始延迟时间等
- 使用指数退避算法计算重连延迟
- 可通过配置自定义策略

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT重连策略
    /// </summary>
    public class ReconnectPolicy
    {
        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// 最大重连尝试次数
        /// </summary>
        public int MaxAttempts { get; set; } = 5;

        /// <summary>
        /// 初始重连延迟时间
        /// </summary>
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(2);

        /// <summary>
        /// 最大重连延迟时间
        /// </summary>
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// 退避算法倍增因子
        /// </summary>
        public double BackoffMultiplier { get; set; } = 1.5;

        /// <summary>
        /// 计算重连延迟时间
        /// </summary>
        /// <param name="attemptCount">当前尝试次数</param>
        /// <returns>应等待的延迟时间</returns>
        public TimeSpan CalculateDelay(int attemptCount)
        {
            if (attemptCount <= 0)
            {
                return InitialDelay;
            }

            // 使用指数退避算法计算延迟时间
            double factor = Math.Pow(BackoffMultiplier, attemptCount - 1);
            var delay = TimeSpan.FromMilliseconds(InitialDelay.TotalMilliseconds * factor);

            // 限制最大延迟时间
            if (delay > MaxDelay)
            {
                return MaxDelay;
            }

            return delay;
        }
    }
}
```

#### ConnectionConfiguration 类

存储MQTT连接的配置：
- 连接参数（服务器地址、端口等）
- 支持运行时配置更新
- 包含连接验证和安全设置

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接配置
    /// </summary>
    public class ConnectionConfiguration
    {
        /// <summary>
        /// MQTT代理服务器地址
        /// </summary>
        public string BrokerAddress { get; set; } = "localhost";

        /// <summary>
        /// MQTT代理服务器端口
        /// </summary>
        public int Port { get; set; } = 1883;

        /// <summary>
        /// 是否使用TLS加密
        /// </summary>
        public bool UseTls { get; set; } = false;

        /// <summary>
        /// 用户名（如果需要认证）
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码（如果需要认证）
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = $"EnvizonController_{Guid.NewGuid()}";

        /// <summary>
        /// 保持连接时间间隔
        /// </summary>
        public TimeSpan KeepAliveInterval { get; set; } = TimeSpan.FromSeconds(60);

        /// <summary>
        /// 默认的服务质量级别
        /// </summary>
        public MqttQualityOfServiceLevel DefaultQoS { get; set; } = MqttQualityOfServiceLevel.AtMostOnce;

        /// <summary>
        /// 克隆当前配置
        /// </summary>
        /// <returns>新的配置对象</returns>
        public ConnectionConfiguration Clone()
        {
            return new ConnectionConfiguration
            {
                BrokerAddress = this.BrokerAddress,
                Port = this.Port,
                UseTls = this.UseTls,
                Username = this.Username,
                Password = this.Password,
                ClientId = this.ClientId,
                KeepAliveInterval = this.KeepAliveInterval,
                DefaultQoS = this.DefaultQoS
            };
        }
    }
}
```

#### MqttConnectionStatusChangedMessage 类 

定义连接状态变更的消息结构：
- 包含旧状态、新状态、描述信息和异常信息
- 用于通知UI和其他组件连接状态变化

```csharp
namespace EnvizonController.Presentation.Messages
{
    /// <summary>
    /// MQTT连接状态变化消息
    /// </summary>
    public class MqttConnectionStatusChangedMessage
    {
        /// <summary>
        /// 旧连接状态
        /// </summary>
        public ConnectionStatus OldStatus { get; set; }

        /// <summary>
        /// 新连接状态
        /// </summary>
        public ConnectionStatus NewStatus { get; set; }

        /// <summary>
        /// 状态变化描述
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 如果发生错误，包含相关异常
        /// </summary>
        public Exception Error { get; set; }
    }
}
```

#### MqttTopicSubscriptionManager 类

管理主题订阅和消息处理：
- 管理主题订阅和消息处理器
- 在连接恢复后自动重新订阅主题
- 提供活动订阅的查询功能

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT主题订阅管理器
    /// </summary>
    public class MqttTopicSubscriptionManager
    {
        private readonly IMqttClientService _mqttClient;
        private readonly IMessageHandlerRegistry _handlerRegistry;
        private readonly ILogger _logger;
        private readonly Dictionary<string, MqttQualityOfServiceLevel> _subscriptions = new();
        private readonly IMqttConnectionManager _connectionManager;

        public MqttTopicSubscriptionManager(
            IMqttClientService mqttClient,
            IMessageHandlerRegistry handlerRegistry,
            IMqttConnectionManager connectionManager,
            ILogger logger)
        {
            _mqttClient = mqttClient;
            _handlerRegistry = handlerRegistry;
            _connectionManager = connectionManager;
            _logger = logger;

            // 订阅连接状态变更事件，在重新连接后恢复订阅
            _connectionManager.ConnectionStatusChanged += OnConnectionStatusChanged;
        }

        /// <summary>
        /// 订阅MQTT主题并注册消息处理器
        /// </summary>
        /// <param name="topic">要订阅的主题</param>
        /// <param name="handler">消息处理器</param>
        /// <param name="qos">服务质量级别</param>
        /// <returns>表示异步操作的任务</returns>
        public async Task SubscribeAsync(string topic, IMessageHandler handler, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            // 注册消息处理器
            _handlerRegistry.RegisterHandler(topic, handler);

            // 如果当前已连接，订阅主题
            if (_connectionManager.Status == ConnectionStatus.Connected)
            {
                try
                {
                    await _mqttClient.SubscribeAsync(topic, qos);
                    _logger.Information($"已订阅主题: {topic}");
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"订阅主题失败: {topic}");
                    throw;
                }
            }

            // 记录订阅，以便在重新连接时恢复
            _subscriptions[topic] = qos;
        }

        /// <summary>
        /// 取消订阅MQTT主题并解除消息处理器注册
        /// </summary>
        /// <param name="topic">要取消订阅的主题</param>
        /// <param name="handler">消息处理器</param>
        /// <returns>表示异步操作的任务</returns>
        public async Task UnsubscribeAsync(string topic, IMessageHandler handler)
        {
            // 解除消息处理器注册
            _handlerRegistry.UnregisterHandler(topic, handler);

            // 检查是否还有其他处理器订阅此主题
            var remainingHandlers = _handlerRegistry.GetHandlers(topic);
            if (!remainingHandlers.Any())
            {
                // 如果没有其他处理器，取消订阅主题
                if (_connectionManager.Status == ConnectionStatus.Connected)
                {
                    try
                    {
                        await _mqttClient.UnsubscribeAsync(topic);
                        _logger.Information($"已取消订阅主题: {topic}");
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"取消订阅主题失败: {topic}");
                        // 不抛出异常，因为这不应该阻止处理器解除注册
                    }
                }

                // 从订阅记录中移除
                _subscriptions.Remove(topic);
            }
        }

        /// <summary>
        /// 获取所有活动订阅
        /// </summary>
        /// <returns>主题和服务质量级别的字典</returns>
        public IReadOnlyDictionary<string, MqttQualityOfServiceLevel> GetActiveSubscriptions()
        {
            return _subscriptions;
        }

        private async void OnConnectionStatusChanged(object sender, MqttConnectionStatusChangedEventArgs e)
        {
            // 当连接恢复时，重新订阅所有主题
            if (e.NewStatus == ConnectionStatus.Connected && 
                (e.OldStatus == ConnectionStatus.Disconnected || 
                 e.OldStatus == ConnectionStatus.Reconnecting || 
                 e.OldStatus == ConnectionStatus.Error))
            {
                await ResubscribeAllAsync();
            }
        }

        private async Task ResubscribeAllAsync()
        {
            _logger.Information("重新建立连接后恢复所有主题订阅");

            foreach (var subscription in _subscriptions)
            {
                try
                {
                    await _mqttClient.SubscribeAsync(subscription.Key, subscription.Value);
                    _logger.Information($"已恢复订阅主题: {subscription.Key}");
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"恢复订阅主题失败: {subscription.Key}");
                    // 继续尝试订阅其他主题
                }
            }
        }
    }
}
```

### 5. 与UI层的集成方式

#### 在ViewModel中使用MQTT连接管理器

```csharp
namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// MQTT连接状态视图模型
    /// </summary>
    public class MqttConnectionStatusViewModel : ViewModelBase
    {
        private readonly IMqttConnectionManager _connectionManager;
        private ConnectionStatus _status;
        private string _statusMessage;
        private bool _isConnecting;

        public ConnectionStatus Status
        {
            get => _status;
            private set => SetProperty(ref _status, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            private set => SetProperty(ref _statusMessage, value);
        }

        public bool IsConnecting
        {
            get => _isConnecting;
            private set => SetProperty(ref _isConnecting, value);
        }

        public ICommand ConnectCommand { get; }
        public ICommand DisconnectCommand { get; }

        public MqttConnectionStatusViewModel(
            IMqttConnectionManager connectionManager,
            IMessenger messenger)
        {
            _connectionManager = connectionManager;
            Status = _connectionManager.Status;

            // 订阅状态变更消息
            messenger.Register<MqttConnectionStatusChangedMessage>(this, OnConnectionStatusChanged);

            // 连接命令
            ConnectCommand = new AsyncRelayCommand(
                async () => await ConnectAsync(),
                () => Status != ConnectionStatus.Connected && 
                      Status != ConnectionStatus.Connecting && 
                      Status != ConnectionStatus.Reconnecting);

            // 断开连接命令
            DisconnectCommand = new AsyncRelayCommand(
                async () => await DisconnectAsync(),
                () => Status == ConnectionStatus.Connected);
        }

        private async Task ConnectAsync()
        {
            IsConnecting = true;
            try
            {
                await _connectionManager.ConnectAsync();
            }
            finally
            {
                IsConnecting = false;
            }
        }

        private async Task DisconnectAsync()
        {
            await _connectionManager.DisconnectAsync();
        }

        private void OnConnectionStatusChanged(MqttConnectionStatusChangedMessage message)
        {
            Status = message.NewStatus;
            StatusMessage = message.Message;

            // 更新命令是否可执行
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
```

#### MQTT状态指示器控件

创建一个XAML控件来显示MQTT连接状态：

```xml
<UserControl x:Class="EnvizonController.Presentation.Views.Controls.MqttStatusIndicator"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:enums="clr-namespace:EnvizonController.Shared.Enums"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="50">
    
    <UserControl.Resources>
        <DataTemplate x:Key="ConnectedTemplate">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="10" Height="10" Fill="Green" Margin="0,0,5,0" />
                <TextBlock Text="已连接" VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>
        
        <DataTemplate x:Key="DisconnectedTemplate">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="10" Height="10" Fill="Red" Margin="0,0,5,0" />
                <TextBlock Text="未连接" VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>
        
        <DataTemplate x:Key="ConnectingTemplate">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="10" Height="10" Fill="Yellow" Margin="0,0,5,0" />
                <TextBlock Text="连接中..." VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>
        
        <DataTemplate x:Key="ReconnectingTemplate">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="10" Height="10" Fill="Orange" Margin="0,0,5,0" />
                <TextBlock Text="重连中..." VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>
        
        <DataTemplate x:Key="ErrorTemplate">
            <StackPanel Orientation="Horizontal">
                <Ellipse Width="10" Height="10" Fill="DarkRed" Margin="0,0,5,0" />
                <TextBlock Text="连接错误" VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>
    </UserControl.Resources>
    
    <Grid>
        <ContentControl Content="{Binding Status}">
            <ContentControl.ContentTemplate>
                <ContentTemplateSelector>
                    <DataTemplate DataType="{x:Type enums:ConnectionStatus.Connected}">
                        <ContentControl ContentTemplate="{StaticResource ConnectedTemplate}" />
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type enums:ConnectionStatus.Disconnected}">
                        <ContentControl ContentTemplate="{StaticResource DisconnectedTemplate}" />
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type enums:ConnectionStatus.Connecting}">
                        <ContentControl ContentTemplate="{StaticResource ConnectingTemplate}" />
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type enums:ConnectionStatus.Reconnecting}">
                        <ContentControl ContentTemplate="{StaticResource ReconnectingTemplate}" />
                    </DataTemplate>
                    <DataTemplate DataType="{x:Type enums:ConnectionStatus.Error}">
                        <ContentControl ContentTemplate="{StaticResource ErrorTemplate}" />
                    </DataTemplate>
                </ContentTemplateSelector>
            </ContentControl.ContentTemplate>
        </ContentControl>
        
        <ToolTip IsVisible="{Binding !!StatusMessage}">
            <TextBlock Text="{Binding StatusMessage}" />
        </ToolTip>
    </Grid>
</UserControl>
```

### 6. 从初始化流程中分离的实现方案

1. 在DI容器中注册MQTT服务组件：

```csharp
// AppBootstrapper.cs 或其他启动配置类
public static void ConfigureServices(IServiceCollection services)
{
    // 已有服务注册...
    
    // MQTT连接配置
    services.AddSingleton(sp => 
    {
        // 从配置中加载MQTT设置
        var configManager = sp.GetRequiredService<IConfigurationManager>();
        return configManager.GetMqttConfiguration() ?? new ConnectionConfiguration();
    });
    
    // 重连策略
    services.AddSingleton<ReconnectPolicy>();
    
    // MQTT连接和主题订阅管理
    services.AddSingleton<IMqttConnectionManager, MqttConnectionManager>();
    services.AddSingleton<MqttTopicSubscriptionManager>();
    
    // 已有其他服务注册...
}
```

2. 修改`AppInitializationService`，移除`ConnectMqttAsync`方法：

```csharp
public class AppInitializationService : IAppInitializationService
{
    // ...现有代码...
    
    public async Task InitializeAsync(IProgress<InitializationProgress> progress = null, CancellationToken cancellationToken = default)
    {
        // ...现有代码...
        
        try
        {
            _logger.Information("开始应用程序初始化");
            
            // 更新进度状态并报告
            UpdateProgressState(new InitializationProgress(
                0, 
                "开始初始化...", 
                InitializationStage.NotStarted));
            
            // 加载配置
            await LoadConfigurationAsync(cancellationToken);
            if (cancellationToken.IsCancellationRequested) return;
            
            // 注册服务
            await RegisterServicesAsync(cancellationToken);
            if (cancellationToken.IsCancellationRequested) return;
            
            // 连接MQTT的步骤已移除
            
            // 完成初始化
            _logger.Information($"应用程序初始化完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
            UpdateProgressState(InitializationProgress.Completed());
        }
        // ...现有代码...
    }
    
    // 移除ConnectMqttAsync方法
}
```

3. 增加MQTT选项，允许应用程序启动后自动连接MQTT：

```csharp
namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT选项配置
    /// </summary>
    public class MqttOptions
    {
        /// <summary>
        /// 应用启动后是否自动连接MQTT
        /// </summary>
        public bool AutoConnectOnStartup { get; set; } = true;
        
        /// <summary>
        /// 连接失败后最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 5;
        
        /// <summary>
        /// 连接超时时间（秒）
        /// </summary>
        public int ConnectionTimeoutSeconds { get; set; } = 10;
    }
}
```

4. 在应用程序主ViewModel中启动MQTT连接：

```csharp
namespace EnvizonController.Presentation.ViewModels
{
    public class MainViewModel : ViewModelBase
    {
        private readonly IMqttConnectionManager _mqttConnectionManager;
        private readonly MqttOptions _mqttOptions;
        
        // ...现有代码...
        
        public MainViewModel(
            // ...现有依赖...
            IMqttConnectionManager mqttConnectionManager,
            MqttOptions mqttOptions)
        {
            _mqttConnectionManager = mqttConnectionManager;
            _mqttOptions = mqttOptions;
            
            // ...现有初始化代码...
            
            // 应用启动后初始化MQTT
            InitializeMqttAsync();
        }
        
        private async void InitializeMqttAsync()
        {
            if (_mqttOptions.AutoConnectOnStartup)
            {
                await _mqttConnectionManager.ConnectAsync();
            }
        }
        
        // ...现有代码...
    }
}
```

## 总结

这个新的MQTT服务架构解决了当前实现中存在的问题：

1. **独立服务** - MQTT连接管理被设计为一个独立的服务，与初始化流程完全分离
2. **状态反馈** - 提供了完善的连接状态管理和通知机制
3. **健壮的错误处理** - 实现了智能重连策略，能更好地处理网络问题
4. **非阻塞操作** - MQTT连接在后台线程中执行，不会阻塞UI
5. **灵活配置** - 支持连接参数的动态配置和更新

这个设计不仅解决了当前的问题，还提供了更好的可扩展性和可维护性，使MQTT功能成为应用程序中更健壮、更独立的一部分。

## 后续实现步骤

1. 在`EnvizonController.Presentation`命名空间中创建`Mqtt`文件夹
2. 实现`IMqttConnectionManager`接口和`MqttConnectionManager`类
3. 实现`ReconnectPolicy`和`ConnectionConfiguration`类
4. 实现`MqttTopicSubscriptionManager`类
5. 创建`MqttConnectionStatusChangedMessage`类
6. 实现UI状态指示器控件
7. 修改`AppInitializationService`移除MQTT连接步骤
8. 更新DI容器配置
9. 在主视图模型中添加MQTT自动连接逻辑