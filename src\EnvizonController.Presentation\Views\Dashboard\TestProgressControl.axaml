<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.TestProgressControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels.Dashboard"
    d:DesignHeight="200"
    d:DesignWidth="300"
    x:DataType="viewModels:TestProgressViewModel"
    mc:Ignorable="d">
    <Panel>
        <Border Classes="cyber-noGlow-border card" />
        <Grid Classes="card-in" RowDefinitions="Auto,*">
            <StackPanel Grid.Row="0" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,6,16"
                    VerticalAlignment="Center"
                    Classes="h4 primary font-icon"
                    Text="&#xf0ae;" />
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="h4 primary bottom"
                    Text="测试进度" />
            </StackPanel>

            <Grid Grid.Row="1" RowDefinitions="Auto,Auto,*,Auto">
                <StackPanel Grid.Row="0">
                    <Panel>
                        <!--  恒温恒湿循环测试  快速温变循环测试  -->
                        <TextBlock
                            Name="Title"
                            Margin="0,0,0,3"
                            Classes="gray h4"
                            Text="{Binding TestName}" />
                        <TextBlock
                            Margin="0,0,0,3"
                            HorizontalAlignment="Right"
                            Classes="primary h4"
                            Text="{Binding CurrentStage}" />
                    </Panel>
                    <ProgressBar
                        Height="21"
                        Margin="-6"
                        Classes="custom-gradient-glow"
                        Maximum=""
                        Value="{Binding TotalProgressPercentage}" />
                    <Grid ColumnDefinitions="Auto,*">
                        <TextBlock
                            Grid.Column="0"
                            Margin="0,5,0,0"
                            Classes="gray2 small"
                            Text="{Binding TotalProgressPercentage, StringFormat='{}{0:F1}% 完成'}" />
                        <TextBlock
                            Name="Time1"
                            Grid.Column="1"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Right"
                            Classes="gray2 small"
                            Text="{Binding TotalRemainingTime, StringFormat='剩余时间: {0}'}" />
                    </Grid>
                </StackPanel>
                <StackPanel Grid.Row="1" Margin="0,9,0,0">
                    <Panel>
                        <TextBlock
                            Margin="0,0,0,3"
                            Classes="gray h4"
                            Text="{Binding CurrentStage, StringFormat='{}阶段运行时间'}" />

                    </Panel>
                    <ProgressBar Height="10" Value="{Binding StageProgressPercentage}">
                        <ProgressBar.Foreground>
                            <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                                <GradientStop Offset="0" Color="#0044FF" />
                                <GradientStop Offset="1" Color="#AA00FF" />
                            </LinearGradientBrush>
                        </ProgressBar.Foreground>
                    </ProgressBar>
                    <Grid ColumnDefinitions="Auto,*">
                        <TextBlock
                            Grid.Column="0"
                            Margin="0,5,0,0"
                            Classes="gray2 small"
                            Text="{Binding StageProgressPercentage, StringFormat='{}{0:F1}% 完成'}" />
                        <TextBlock
                            Name="Time2"
                            Grid.Column="1"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Right"
                            Classes="gray2 small"
                            Text="{Binding StageRemainingTime, StringFormat='剩余时间：{0}'}" />
                    </Grid>
                </StackPanel>


                <Grid
                    Grid.Row="2"
                    Margin="0,20,0,0"
                    ColumnDefinitions="*,*">
                    <Border Grid.Column="0" Classes="cyber-border card">
                        <Grid RowDefinitions="Auto,*,Auto">
                            <TextBlock Classes="gray small" Text="开始时间" />
                            <TextBlock
                                Name="StartTime"
                                Grid.Row="1"
                                Margin="0,3"
                                VerticalAlignment="Center"
                                Classes="white font-cyber"
                                Text="{Binding StartTimeDate}"
                                TextWrapping="WrapWithOverflow" />
                            <TextBlock
                                Name="StartTime2"
                                Grid.Row="2"
                                Classes="primary font-value"
                                Text="{Binding StartTimeTime}" />
                        </Grid>
                    </Border>

                    <Border Grid.Column="1" Classes="cyber-border card">
                        <Grid RowDefinitions="Auto,*,Auto">
                            <TextBlock Classes="gray small" Text="预计结束" />
                            <TextBlock
                                Name="EndTime"
                                Grid.Row="1"
                                Margin="0,3"
                                VerticalAlignment="Center"
                                Classes="white font-cyber"
                                Text="{Binding EstimatedEndTimeDate}"
                                TextWrapping="WrapWithOverflow" />
                            <TextBlock
                                Name="EndTime2"
                                Grid.Row="2"
                                Classes="primary font-value"
                                Text="{Binding EstimatedEndTimeTime}" />
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Grid>
    </Panel>
</UserControl>
