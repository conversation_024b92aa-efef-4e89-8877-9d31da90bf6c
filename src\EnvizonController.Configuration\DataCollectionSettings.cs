namespace EnvizonController.Configuration
{
    /// <summary>
    /// 数据采集配置
    /// </summary>
    public class DataCollectionSettings
    {
        /// <summary>
        /// 采集间隔（毫秒）
        /// </summary>
        public int CollectionIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 是否自动启动采集
        /// </summary>
        public bool AutoStart { get; set; } = true;

        /// <summary>
        /// 采集参数配置列表
        /// </summary>
        public List<CollectionParameterConfig> Parameters { get; set; } = new List<CollectionParameterConfig>();
    }

    /// <summary>
    /// 采集参数配置
    /// </summary>
    public class CollectionParameterConfig
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 参数地址
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public string DataType { get; set; } = "UInt16";

        /// <summary>
        /// 缩放因子
        /// </summary>
        public double ScaleFactor { get; set; } = 1.0;

        /// <summary>
        /// 偏移量
        /// </summary>
        public double Offset { get; set; } = 0.0;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
