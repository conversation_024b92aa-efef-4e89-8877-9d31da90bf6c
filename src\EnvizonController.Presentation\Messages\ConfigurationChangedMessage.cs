﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 配置变更消息
/// </summary>
public class ConfigurationChangedMessage : ValueChangedMessage<string>
{
    /// <summary>
    /// 配置节点路径
    /// </summary>
    public string ConfigPath => Value;

    /// <summary>
    /// 创建配置变更消息
    /// </summary>
    /// <param name="configPath">配置节点路径，为空表示整个配置已变更</param>
    public ConfigurationChangedMessage(string configPath = "") : base(configPath)
    {
    }
}
