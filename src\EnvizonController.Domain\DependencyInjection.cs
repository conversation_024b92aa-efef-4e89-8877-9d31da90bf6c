using EnvizonController.Domain.Services;
using EnvizonController.Domain.Services.Implementations;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.Domain
{
    /// <summary>
    /// 领域层依赖注入扩展方法
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// 添加领域服务
        /// </summary>
        public static IServiceCollection AddDomainServices(this IServiceCollection services)
        {
            // 注册领域服务
            services.AddScoped<ITestRunDomainService, TestRunDomainService>();
            services.AddScoped<IAlarmDomainService, AlarmDomainService>();
            services.AddScoped<IDataCollectionDomainService, DataCollectionDomainService>();

            // 注册协议服务
            services.AddScoped<IProtocolService, ProtocolDomainService>();
            services.AddScoped<IProgramDomainService, ProgramDomainService>();

            return services;
        }
    }
}
