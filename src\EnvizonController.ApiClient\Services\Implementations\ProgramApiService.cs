using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 程式表API服务实现
    /// </summary>
    public class ProgramApiService : IProgramApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public ProgramApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 获取所有程式表
        /// </summary>
        public async Task<Result<PagedResultDto<ProgramDTO>>> GetProgramsAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<ProgramDTO>>("api/programs", queryParams);
        }

        /// <summary>
        /// 根据ID获取程式表
        /// </summary>
        public async Task<Result<ProgramDTO>> GetProgramAsync(long id)
        {
            return await _httpClient.GetAsync<ProgramDTO>($"api/programs/{id}");
        }

        /// <summary>
        /// 按名称获取程式表
        /// </summary>
        public async Task<Result<ProgramDTO>> GetProgramByNameAsync(string name)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["name"] = name
            };

            return await _httpClient.GetAsync<ProgramDTO>("api/programs/by-name", queryParams);
        }

        /// <summary>
        /// 创建程式表
        /// </summary>
        public async Task<Result<ProgramDTO>> CreateProgramAsync(ProgramDTO program)
        {
            return await _httpClient.PostAsync<ProgramDTO, ProgramDTO>("api/programs", program);
        }

        /// <summary>
        /// 更新程式表
        /// </summary>
        public async Task<Result<ProgramDTO>> UpdateProgramAsync(ProgramDTO program)
        {
            return await _httpClient.PutAsync<ProgramDTO, ProgramDTO>($"api/programs/{program.Id}", program);
        }

        /// <summary>
        /// 删除程式表
        /// </summary>
        public async Task<Result<bool>> DeleteProgramAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/programs/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 获取程式表的所有步骤
        /// </summary>
        public async Task<Result<List<ProgramStepDTO>>> GetProgramStepsAsync(long programId)
        {
            return await _httpClient.GetAsync<List<ProgramStepDTO>>($"api/programs/{programId}/steps");
        }

        /// <summary>
        /// 创建程式步骤
        /// </summary>
        public async Task<Result<ProgramStepDTO>> CreateProgramStepAsync(ProgramStepDTO step)
        {
            return await _httpClient.PostAsync<ProgramStepDTO, ProgramStepDTO>($"api/programs/{step.ProgramId}/steps", step);
        }

        /// <summary>
        /// 更新程式步骤
        /// </summary>
        public async Task<Result<ProgramStepDTO>> UpdateProgramStepAsync(ProgramStepDTO step)
        {
            return await _httpClient.PutAsync<ProgramStepDTO, ProgramStepDTO>($"api/programs/{step.ProgramId}/steps/{step.Id}", step);
        }

        /// <summary>
        /// 删除程式步骤
        /// </summary>
        public async Task<Result<bool>> DeleteProgramStepAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/programs/steps/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 批量更新程式步骤
        /// </summary>
        public async Task<Result<List<ProgramStepDTO>>> BatchUpdateProgramStepsAsync(long programId, List<ProgramStepDTO> steps)
        {
            return await _httpClient.PutAsync<List<ProgramStepDTO>, List<ProgramStepDTO>>($"api/programs/{programId}/steps/batch", steps);
        }
    }
}