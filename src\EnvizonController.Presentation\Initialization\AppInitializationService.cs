using System.Diagnostics;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Presentation.Handlers;
using EnvizonController.Presentation.Mqtt;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace EnvizonController.Presentation.Initialization;

/// <summary>
///     应用程序初始化服务的实现，负责管理所有启动任务
/// </summary>
public class AppInitializationService : IAppInitializationService
{
    private readonly TimeSpan _defaultTimeout = TimeSpan.FromSeconds(30);
    private readonly ILogger _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly object _stateLock = new();

    private InitializationProgress _currentState;
    private CancellationTokenSource _initializationCts;

    /// <summary>
    ///     初始化 <see cref="AppInitializationService" /> 类的新实例
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    public AppInitializationService(IServiceProvider serviceProvider, ILogger logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _currentState = InitializationProgress.NotStarted();
    }

    /// <summary>
    ///     在初始化进度变化时发生
    /// </summary>
    public event EventHandler<InitializationProgressEventArgs> ProgressChanged;

    /// <summary>
    ///     获取当前初始化状态
    /// </summary>
    public InitializationProgress CurrentState
    {
        get
        {
            lock (_stateLock)
            {
                return _currentState;
            }
        }
        private set
        {
            lock (_stateLock)
            {
                _currentState = value;
            }

            // 触发进度变化事件
            OnProgressChanged(new InitializationProgressEventArgs(_currentState));
        }
    }

    /// <summary>
    ///     异步执行应用程序初始化
    /// </summary>
    /// <param name="progress">用于报告进度的接口</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>表示异步操作的任务</returns>
    public async Task InitializeAsync(IProgress<InitializationProgress> progress = null,
        CancellationToken cancellationToken = default)
    {
        if (CurrentState.IsCompleted)
        {
            _logger.Information("初始化已完成，不需要重新初始化");
            progress?.Report(CurrentState);
            return;
        }

        // 如果初始化已失败，需要调用Reset方法重置状态
        if (CurrentState.IsFailed)
        {
            _logger.Warning("初始化已失败，请先调用Reset方法重置状态");
            progress?.Report(CurrentState);
            return;
        }

        // 创建组合的取消令牌，包含超时和外部传入的取消令牌
        _initializationCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _initializationCts.CancelAfter(_defaultTimeout);

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.Information("开始应用程序初始化");

            // 更新进度状态并报告
            UpdateProgressState(new InitializationProgress(
                0,
                "开始初始化...",
                InitializationStage.NotStarted));

            // 加载配置
            await LoadConfigurationAsync(_initializationCts.Token);
            if (_initializationCts.Token.IsCancellationRequested) return;

            // 注册服务
            await RegisterServicesAsync(_initializationCts.Token);

            await InitializeMqttSubscriptionsAsync_Internal(cancellationToken);

            if (_initializationCts.Token.IsCancellationRequested) return;

            // 完成初始化
            _logger.Information($"应用程序初始化完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
            UpdateProgressState(InitializationProgress.Completed());
        }
        catch (OperationCanceledException)
        {
            _logger.Warning($"应用程序初始化已取消，耗时: {stopwatch.ElapsedMilliseconds}ms");
            UpdateProgressState(InitializationProgress.Failed("初始化已取消"));
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"应用程序初始化失败，耗时: {stopwatch.ElapsedMilliseconds}ms");
            UpdateProgressState(InitializationProgress.Failed(ex.Message));
        }
        finally
        {
            stopwatch.Stop();
            _initializationCts?.Dispose();
            _initializationCts = null;
        }

        void UpdateProgressState(InitializationProgress state)
        {
            CurrentState = state;
            progress?.Report(state);
        }
    }

    /// <summary>
    ///     重置初始化状态，允许重新初始化
    /// </summary>
    public void Reset()
    {
        _logger.Information("重置初始化服务状态");
        CurrentState = InitializationProgress.NotStarted();
        _initializationCts?.Cancel();
        _initializationCts?.Dispose();
        _initializationCts = null;
    }

    /// <summary>
    ///     触发ProgressChanged事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnProgressChanged(InitializationProgressEventArgs e)
    {
        ProgressChanged?.Invoke(this, e);
    }

    /// <summary>
    ///     加载应用程序配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>表示异步操作的任务</returns>
    private async Task LoadConfigurationAsync(CancellationToken cancellationToken)
    {
        _logger.Information("开始加载应用程序配置");

        UpdateProgressState(new InitializationProgress(
            (int)InitializationStage.LoadingConfiguration,
            "正在加载配置...",
            InitializationStage.LoadingConfiguration));

        try
        {
            // 异步加载配置
            await AppConfigurationProvider.InitializeAsync();

            _logger.Information("应用程序配置加载成功");

            // 配置阶段完成后更新进度
            UpdateProgressState(new InitializationProgress(
                (int)InitializationStage.LoadingConfiguration + 5,
                "配置已加载",
                InitializationStage.LoadingConfiguration));
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "加载应用程序配置失败");
            throw new InitializationException("加载配置失败", ex);
        }

        // 检查取消状态
        cancellationToken.ThrowIfCancellationRequested();

        void UpdateProgressState(InitializationProgress state)
        {
            CurrentState = state;
        }
    }

    /// <summary>
    ///     注册应用程序服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>表示异步操作的任务</returns>
    private async Task RegisterServicesAsync(CancellationToken cancellationToken)
    {
        _logger.Information("开始注册应用程序服务");

        UpdateProgressState(new InitializationProgress(
            (int)InitializationStage.RegisteringServices,
            "正在注册服务...",
            InitializationStage.RegisteringServices));

        try
        {
            // 这里实际上是在DI容器中注册服务的地方
            // 但由于依赖注入通常在应用启动时完成，这里模拟一个异步操作
            await Task.Delay(100, cancellationToken);

            _logger.Information("应用程序服务注册成功");

            // 服务注册阶段完成后更新进度
            UpdateProgressState(new InitializationProgress(
                (int)InitializationStage.RegisteringServices + 5,
                "服务已注册",
                InitializationStage.RegisteringServices));
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "注册应用程序服务失败");
            throw new InitializationException("注册服务失败", ex);
        }

        // 检查取消状态
        cancellationToken.ThrowIfCancellationRequested();

        void UpdateProgressState(InitializationProgress state)
        {
            CurrentState = state;
        }
    }

    private async Task InitializeMqttSubscriptionsAsync_Internal(CancellationToken cancellationToken)
    {
        _logger.Information("开始初始化MQTT消息处理器订阅...");
        // UpdateProgressState accordingly

        try
        {
            var topicSubscriptionManager = _serviceProvider.GetRequiredService<MqttTopicSubscriptionManager>();
            var deviceDetailsHandler = _serviceProvider.GetRequiredService<DeviceCollectionDetailsMessageHandler>();

            if (deviceDetailsHandler != null && !cancellationToken.IsCancellationRequested)
            {
                var deviceDataTopic = "device/data/#";
                await topicSubscriptionManager.SubscribeAsync(deviceDataTopic, deviceDetailsHandler);
                _logger.Information("已成功为 DeviceCollectionDetailsMessageHandler 订阅主题: {Topic}", deviceDataTopic);
            }

            // ... (subscribe other handlers, check cancellationToken) ...
            _logger.Information("MQTT消息处理器订阅初始化完成。");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "初始化MQTT消息处理器订阅失败。");
            throw new InitializationException("初始化MQTT订阅失败", ex);
        }

        cancellationToken.ThrowIfCancellationRequested();
    }
}

/// <summary>
///     表示初始化过程中发生的异常
/// </summary>
public class InitializationException : Exception
{
    /// <summary>
    ///     初始化 <see cref="InitializationException" /> 类的新实例
    /// </summary>
    /// <param name="message">异常消息</param>
    public InitializationException(string message) : base(message)
    {
    }

    /// <summary>
    ///     初始化 <see cref="InitializationException" /> 类的新实例
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public InitializationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}