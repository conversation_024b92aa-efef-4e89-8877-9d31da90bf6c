using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 读取离散输入状态响应
    /// </summary>
    public class ReadDiscreteInputsResponse : ModbusResponse
    {
        /// <summary>
        /// 字节计数
        /// </summary>
        public byte ByteCount { get; private set; }

        /// <summary>
        /// 输入值
        /// </summary>
        public bool[] InputValues { get; private set; } = Array.Empty<bool>();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReadDiscreteInputsResponse()
        {
            FunctionCode = ModbusFunction.ReadDiscreteInputs;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 3)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            ByteCount = frame[2];

            // 验证帧长度是否与字节计数匹配
            if (frame.Length < 3 + ByteCount)
                throw new ArgumentException("响应帧长度与字节计数不匹配", nameof(frame));

            // 解析输入值
            List<bool> inputValues = new List<bool>();
            for (int i = 0; i < ByteCount; i++)
            {
                byte inputByte = frame[3 + i];
                for (int bit = 0; bit < 8; bit++)
                {
                    bool inputValue = (inputByte & (1 << bit)) != 0;
                    inputValues.Add(inputValue);
                }
            }

            InputValues = inputValues.ToArray();
        }
    }
}
