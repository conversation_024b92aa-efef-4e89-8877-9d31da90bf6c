using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Models;
using EnvizonController.Application.DeviceCommands.Configuration;
using EnvizonController.Application.DeviceCommands.SmartMatching;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace EnvizonController.Application.DeviceCommands.Services
{
    /// <summary>
    /// 协议智能解析器实现
    /// </summary>
    public class ProtocolCommandResolver : IProtocolCommandResolver
    {
        private readonly IDeviceRepository _deviceRepository;
        private readonly IProtocolRepository _protocolRepository;
        private readonly SmartMatchingEngine _smartMatchingEngine;
        private readonly CommandMappingConfig _config;
        private readonly ConcurrentDictionary<string, object> _cache;
        private readonly ILogger<ProtocolCommandResolver> _logger;

        private const string DEVICE_PROTOCOL_CACHE_KEY = "device_protocol_{0}";
        private const string PROTOCOL_ITEMS_CACHE_KEY = "protocol_items_{0}";
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);

        public ProtocolCommandResolver(
            IDeviceRepository deviceRepository,
            IProtocolRepository protocolRepository,
            SmartMatchingEngine smartMatchingEngine,
            IOptions<CommandMappingConfig> config,
            ILogger<ProtocolCommandResolver> logger)
        {
            _deviceRepository = deviceRepository;
            _protocolRepository = protocolRepository;
            _smartMatchingEngine = smartMatchingEngine;
            _config = config.Value;
            _cache = new ConcurrentDictionary<string, object>();
            _logger = logger;
        }

        /// <summary>
        /// 解析指令到协议项
        /// </summary>
        public async Task<ProtocolItem?> ResolveCommandToProtocolItemAsync(long deviceId, string commandName)
        {
            _logger.LogDebug("解析指令到协议项，设备ID: {DeviceId}, 指令名: {CommandName}", deviceId, commandName);

            // 1. 首先检查预定义指令映射
            if (_config.PredefinedCommands.TryGetValue(commandName.ToLower(), out var mapping))
            {
                _logger.LogDebug("找到预定义指令映射: {CommandName} -> {ProtocolItemName}", commandName, mapping.ProtocolItemName);
                return await FindProtocolItemByNameAsync(deviceId, mapping.ProtocolItemName);
            }

            // 2. 尝试通过ValueMappings进行反向查找
            var valueMappingMatch = await FindProtocolItemByValueMappingAsync(deviceId, commandName);
            if (valueMappingMatch != null)
            {
                _logger.LogDebug("通过ValueMappings找到匹配: {CommandName} -> {ProtocolItemName}", commandName, valueMappingMatch.Name);
                return valueMappingMatch;
            }

            // 3. 如果没有预定义映射和值映射匹配，则使用智能匹配
            return await SmartMatchProtocolItemAsync(deviceId, commandName);
        }

        /// <summary>
        /// 根据名称查找协议项
        /// </summary>
        public async Task<ProtocolItem?> FindProtocolItemByNameAsync(long deviceId, string protocolItemName)
        {
            var protocolItems = await GetDeviceProtocolItemsAsync(deviceId);

            return protocolItems.FirstOrDefault(item =>
                string.Equals(item.Name, protocolItemName, StringComparison.OrdinalIgnoreCase) ||
                string.Equals(item.DisplayName, protocolItemName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 通过ValueMappings反向查找协议项
        /// </summary>
        public async Task<ProtocolItem?> FindProtocolItemByValueMappingAsync(long deviceId, string commandValue)
        {
            var protocolItems = await GetDeviceProtocolItemsAsync(deviceId);

            _logger.LogDebug("开始通过ValueMappings查找协议项，搜索值: {CommandValue}", commandValue);

            // 遍历所有协议项，检查其ValueMappings
            foreach (var item in protocolItems)
            {
                if (item.ValueMappings != null && item.ValueMappings.Any())
                {
                    // 检查ValueMappings中是否包含匹配的值
                    var matchingMapping = item.ValueMappings.FirstOrDefault(mapping =>
                        string.Equals(mapping.Value, commandValue, StringComparison.OrdinalIgnoreCase));

                    if (!matchingMapping.Equals(default(KeyValuePair<int, string>)))
                    {
                        _logger.LogDebug("在协议项 '{ItemName}' 的ValueMappings中找到匹配: {Key} -> {Value}",
                            item.Name, matchingMapping.Key, matchingMapping.Value);
                        return item;
                    }
                }

                // 同时检查BitMappings（如果存在）
                if (item.BitMappings != null && item.BitMappings.Any())
                {
                    var matchingBitMapping = item.BitMappings.FirstOrDefault(mapping =>
                        string.Equals(mapping.Value, commandValue, StringComparison.OrdinalIgnoreCase));

                    if (!matchingBitMapping.Equals(default(KeyValuePair<int, string>)))
                    {
                        _logger.LogDebug("在协议项 '{ItemName}' 的BitMappings中找到匹配: {Key} -> {Value}",
                            item.Name, matchingBitMapping.Key, matchingBitMapping.Value);
                        return item;
                    }
                }
            }

            _logger.LogDebug("未在ValueMappings或BitMappings中找到匹配的值: {CommandValue}", commandValue);
            return null;
        }

        /// <summary>
        /// 智能匹配协议项
        /// </summary>
        public async Task<ProtocolItem?> SmartMatchProtocolItemAsync(long deviceId, string searchTerm)
        {
            var protocolItems = await GetDeviceProtocolItemsAsync(deviceId);
            return await _smartMatchingEngine.FuzzyMatchProtocolItemAsync(protocolItems, searchTerm);
        }

        /// <summary>
        /// 获取设备支持的所有指令
        /// </summary>
        public async Task<IEnumerable<CommandInfo>> GetSupportedCommandsAsync(long deviceId)
        {
            var commands = new List<CommandInfo>();

            // 1. 添加预定义指令
            foreach (var predefinedCommand in _config.PredefinedCommands)
            {
                var protocolItem = await FindProtocolItemByNameAsync(deviceId, predefinedCommand.Value.ProtocolItemName);
                if (protocolItem != null)
                {
                    commands.Add(new CommandInfo
                    {
                        Name = predefinedCommand.Key,
                        DisplayName = predefinedCommand.Value.ProtocolItemName,
                        Description = $"预定义指令: {predefinedCommand.Value.ProtocolItemName}",
                        Type = predefinedCommand.Value.CommandType,
                        Aliases = predefinedCommand.Value.Aliases.ToList()
                    });
                }
            }

            // 2. 添加基于协议项的指令
            var protocolItems = await GetDeviceProtocolItemsAsync(deviceId);
            foreach (var item in protocolItems)
            {
                var commandType = item.IsWritable ? CommandType.Write : CommandType.Read;

                commands.Add(new CommandInfo
                {
                    Name = item.Name,
                    DisplayName = item.DisplayName,
                    Description = item.Description,
                    Type = commandType,
                    Aliases = new List<string> { item.Name, item.DisplayName }
                });
            }

            return commands;
        }

        /// <summary>
        /// 验证指令参数
        /// </summary>
        public async Task<ValidationResult> ValidateCommandParametersAsync(ProtocolItem protocolItem, object? parameters)
        {
            var result = new ValidationResult { IsValid = true };

            if (protocolItem == null)
            {
                result.IsValid = false;
                result.ErrorMessages.Add("协议项不能为空");
                return result;
            }

            // 检查写入权限
            if (parameters != null && !protocolItem.IsWritable)
            {
                result.IsValid = false;
                result.ErrorMessages.Add($"协议项 '{protocolItem.DisplayName}' 不支持写入操作");
            }

            // 检查数值范围
            if (parameters != null && protocolItem.HasAlarmThresholds)
            {
                if (double.TryParse(parameters.ToString(), out double value))
                {
                    if (protocolItem.MinValue.HasValue && value < protocolItem.MinValue.Value)
                    {
                        result.IsValid = false;
                        result.ErrorMessages.Add($"参数值 {value} 小于最小值 {protocolItem.MinValue.Value}");
                    }

                    if (protocolItem.MaxValue.HasValue && value > protocolItem.MaxValue.Value)
                    {
                        result.IsValid = false;
                        result.ErrorMessages.Add($"参数值 {value} 大于最大值 {protocolItem.MaxValue.Value}");
                    }
                }
            }

            return await Task.FromResult(result);
        }

        /// <summary>
        /// 获取设备的协议项列表
        /// </summary>
        public async Task<List<ProtocolItem>> GetDeviceProtocolItemsAsync(long deviceId)
        {
            var cacheKey = string.Format(PROTOCOL_ITEMS_CACHE_KEY, deviceId);

            if (_cache.TryGetValue(cacheKey, out var cachedValue) && cachedValue is CacheItem<List<ProtocolItem>> cachedItem)
            {
                if (DateTime.UtcNow < cachedItem.ExpiresAt)
                {
                    return cachedItem.Value;
                }
                else
                {
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            var device = await GetDeviceWithProtocolAsync(deviceId);
            if (device?.Protocol == null)
            {
                _logger.LogWarning("设备 {DeviceId} 没有关联的协议", deviceId);
                return new List<ProtocolItem>();
            }

            var protocolItems = device.Protocol.Items ?? new List<ProtocolItem>();

            // 缓存结果
            var newCacheItem = new CacheItem<List<ProtocolItem>>
            {
                Value = protocolItems,
                ExpiresAt = DateTime.UtcNow.Add(_cacheExpiration)
            };
            _cache.TryAdd(cacheKey, newCacheItem);

            return protocolItems;
        }

        /// <summary>
        /// 获取推荐的协议项
        /// </summary>
        public async Task<List<ProtocolItem>> GetRecommendedProtocolItemsAsync(long deviceId, string searchTerm, int maxRecommendations = 5)
        {
            var protocolItems = await GetDeviceProtocolItemsAsync(deviceId);
            return await _smartMatchingEngine.GetRecommendationsAsync(protocolItems, searchTerm, maxRecommendations);
        }

        /// <summary>
        /// 获取命令对应的值映射信息
        /// </summary>
        public async Task<(ProtocolItem? protocolItem, int? mappingKey, string? mappingValue)> GetCommandValueMappingAsync(long deviceId, string commandName)
        {
            _logger.LogDebug("获取命令值映射信息，设备ID: {DeviceId}, 命令名: {CommandName}", deviceId, commandName);

            var protocolItem = await ResolveCommandToProtocolItemAsync(deviceId, commandName);
            if (protocolItem == null)
            {
                _logger.LogDebug("未找到对应的协议项，命令名: {CommandName}", commandName);
                return (null, null, null);
            }

            // 检查ValueMappings中是否有匹配的值
            if (protocolItem.ValueMappings != null && protocolItem.ValueMappings.Count > 0)
            {
                var matchingMapping = protocolItem.ValueMappings.FirstOrDefault(mapping =>
                    string.Equals(mapping.Value, commandName, StringComparison.OrdinalIgnoreCase));

                if (!matchingMapping.Equals(default(KeyValuePair<int, string>)))
                {
                    _logger.LogDebug("找到ValueMappings匹配: {Key} -> {Value}", matchingMapping.Key, matchingMapping.Value);
                    return (protocolItem, matchingMapping.Key, matchingMapping.Value);
                }
            }

            // 检查BitMappings中是否有匹配的值
            if (protocolItem.BitMappings != null && protocolItem.BitMappings.Count > 0)
            {
                var matchingBitMapping = protocolItem.BitMappings.FirstOrDefault(mapping =>
                    string.Equals(mapping.Value, commandName, StringComparison.OrdinalIgnoreCase));

                if (!matchingBitMapping.Equals(default(KeyValuePair<int, string>)))
                {
                    _logger.LogDebug("找到BitMappings匹配: {Key} -> {Value}", matchingBitMapping.Key, matchingBitMapping.Value);
                    return (protocolItem, matchingBitMapping.Key, matchingBitMapping.Value);
                }
            }

            // 如果没有找到值映射，但找到了协议项，返回协议项信息
            _logger.LogDebug("协议项 '{ItemName}' 没有对应的值映射", protocolItem.Name);
            return (protocolItem, null, null);
        }

        /// <summary>
        /// 获取设备及其协议信息（带缓存）
        /// </summary>
        private async Task<Device?> GetDeviceWithProtocolAsync(long deviceId)
        {
            var cacheKey = string.Format(DEVICE_PROTOCOL_CACHE_KEY, deviceId);

            if (_cache.TryGetValue(cacheKey, out var cachedValue) && cachedValue is CacheItem<Device> cachedItem)
            {
                if (DateTime.UtcNow < cachedItem.ExpiresAt)
                {
                    return cachedItem.Value;
                }
                else
                {
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            var device = await _deviceRepository.GetByIdAsync(deviceId);
            if (device == null)
            {
                _logger.LogWarning("未找到设备，ID: {DeviceId}", deviceId);
                return null;
            }

            // 加载协议信息
            if (device.ProtocolId > 0 && device.Protocol == null)
            {
                device.Protocol = await _protocolRepository.GetByIdAsync(device.ProtocolId);
            }

            // 缓存结果
            if (device.Protocol != null)
            {
                var newCacheItem = new CacheItem<Device>
                {
                    Value = device,
                    ExpiresAt = DateTime.UtcNow.Add(_cacheExpiration)
                };
                _cache.TryAdd(cacheKey, newCacheItem);
            }

            return device;
        }

        /// <summary>
        /// 缓存项
        /// </summary>
        private class CacheItem<T>
        {
            public T Value { get; set; } = default!;
            public DateTime ExpiresAt { get; set; }
        }
    }
}