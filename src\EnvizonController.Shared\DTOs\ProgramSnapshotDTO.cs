namespace EnvizonController.Shared.DTOs;

public class ProgramSnapshotDTO
{
    /// <summary>
    ///     程式名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///     循环次数
    /// </summary>
    public int CycleCount { get; set; }

    /// <summary>
    ///     循环开始
    /// </summary>
    public int CycleStart { get; set; }

    /// <summary>
    ///     循环结束
    /// </summary>
    public int CycleEnd { get; set; }

    /// <summary>
    ///     程式步骤列表
    /// </summary>
    public List<StepSnapshotDTO> Steps { get; set; } = new();
}

public class StepSnapshotDTO
{
    /// <summary>
    ///     步骤索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    ///     温度（摄氏度）
    /// </summary>
    public double Temperature { get; set; }

    /// <summary>
    ///     湿度（百分比）
    /// </summary>
    public double Humidity { get; set; }

    /// <summary>
    ///     持续时间（秒）
    /// </summary>
    public int DurationSeconds { get; set; }

    /// <summary>
    ///     是否线性变化
    /// </summary>
    public bool IsLinear { get; set; }
}

public class ProgramLinkSnapshotDTO
{
    /// <summary>
    ///     程式链接名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///     循环次数
    /// </summary>
    public int CycleCount { get; set; } = 1;

    /// <summary>
    ///     程式链接项集合
    /// </summary>
    public List<ProgramLinkItemSnapshotDTO> Programs { get; set; } = new();
}

public class ProgramLinkItemSnapshotDTO
{
    /// <summary>
    ///     执行顺序
    /// </summary>
    public int ExecutionOrder { get; set; }

    /// <summary>
    ///     程式快照
    /// </summary>
    public ProgramSnapshotDTO ProgramSnapshot { get; set; } = new();
}
