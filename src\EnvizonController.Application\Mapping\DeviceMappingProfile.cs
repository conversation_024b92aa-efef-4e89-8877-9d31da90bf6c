using AutoMapper;
using EnvizonController.Domain.Aggregates;
using System;
using EnvizonController.Shared.DTOs;
using DomainEnums = EnvizonController.Domain.Enums;
using SharedEnums = EnvizonController.Shared.Enums;

namespace EnvizonController.Application.Mapping
{
    /// <summary>
    /// Device 领域对象的 AutoMapper 配置文件
    /// </summary>
    public class DeviceMappingProfile : Profile
    {
        public DeviceMappingProfile()
        {
            // Device -> DeviceDto
            CreateMap<Device, DeviceDto>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (SharedEnums.DeviceStatus)(int)src.Status))
                .ForMember(dest => dest.ConnectionStatus, opt => opt.MapFrom(src => (SharedEnums.ConnectionStatus)(int)src.ConnectionStatus))
                .ForMember(dest => dest.PortName, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.PortName) ? null : src.PortName))
                .ForMember(dest => dest.HostAddress, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.HostAddress) ? null : src.HostAddress))
                .ForMember(dest => dest.IsSelected, opt => opt.Ignore());

            // CreateDeviceDto -> Device
            CreateMap<CreateDeviceDto, Device>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => DomainEnums.DeviceStatus.Normal))
                .ForMember(dest => dest.ConnectionStatus, opt => opt.MapFrom(src => DomainEnums.ConnectionStatus.Disconnected))
                .ForMember(dest => dest.PortName, opt => opt.MapFrom(src => src.PortName ?? string.Empty))
                .ForMember(dest => dest.BaudRate, opt => opt.MapFrom(src => src.BaudRate ?? 9600))
                .ForMember(dest => dest.DataBits, opt => opt.MapFrom(src => src.DataBits ?? 8))
                .ForMember(dest => dest.Parity, opt => opt.MapFrom(src => src.Parity ?? 0))
                .ForMember(dest => dest.StopBits, opt => opt.MapFrom(src => src.StopBits ?? 1))
                .ForMember(dest => dest.HostAddress, opt => opt.MapFrom(src => src.HostAddress ?? string.Empty))
                .ForMember(dest => dest.Port, opt => opt.MapFrom(src => src.Port ?? 502))
                .ForMember(dest => dest.Protocol, opt => opt.Ignore())
                .ForMember(dest => dest.LastUpdatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.LastConnectedAt, opt => opt.Ignore())
                .ForMember(dest => dest.TestId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.OperatingStatus, opt => opt.Ignore());

            // UpdateDeviceDto -> Device
            CreateMap<UpdateDeviceDto, Device>()
                .ForMember(dest => dest.LastUpdatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (DomainEnums.DeviceStatus)(int)src.Status))
                .ForMember(dest => dest.Protocol, opt => opt.Ignore())
                .ForMember(dest => dest.ConnectionStatus, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastConnectedAt, opt => opt.Ignore())
                .ForMember(dest => dest.TestId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.OperatingStatus, opt => opt.Ignore())
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
        }
    }
} 