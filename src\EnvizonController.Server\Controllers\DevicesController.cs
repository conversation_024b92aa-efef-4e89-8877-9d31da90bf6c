using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using AutoMapper;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using DomainEnums = EnvizonController.Domain.Enums;
using SharedEnums = EnvizonController.Shared.Enums;
using EnvizonController.Application.Services;

namespace EnvizonController.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DevicesController : ControllerBase
    {
        private readonly IDeviceAppService _deviceService;
        private readonly ILogger<DevicesController> _logger;
        private readonly IMapper _mapper;
        private readonly IDeviceTestService _deviceTestService;

        public DevicesController(
            IDeviceAppService deviceService,
            ILogger<DevicesController> logger,
            IMapper mapper,
            IDeviceTestService deviceTestService)
        {
            _deviceService = deviceService;
            _logger = logger;
            _mapper = mapper;
            _deviceTestService = deviceTestService;
        }

        /// <summary>
        /// 获取所有设备
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>设备列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<DeviceDto>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<DeviceDto>>> GetDevices(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var devices = await _deviceService.GetAllDevicesAsync();
                var devicesList = devices.ToList();
                
                // 使用 AutoMapper 将Domain实体映射到DTO
                var deviceDtos = devicesList
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(d => _mapper.Map<DeviceDto>(d))
                    .ToList();

                return Ok(new PagedResultDto<DeviceDto>
                {
                    Items = deviceDtos,
                    TotalCount = devicesList.Count,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取设备列表时发生错误");
            }
        }

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>设备详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DeviceDto>> GetDevice(long id)
        {
            try
            {
                var device = await _deviceService.GetDeviceByIdAsync(id);
                if (device == null)
                {
                    return NotFound($"ID为{id}的设备不存在");
                }

                // 使用 AutoMapper 映射到DTO
                var deviceDto = _mapper.Map<DeviceDto>(device);

                return Ok(deviceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取设备详情时发生错误");
            }
        }

        /// <summary>
        /// 创建设备
        /// </summary>
        /// <param name="deviceDto">设备信息</param>
        /// <returns>创建的设备</returns>
        [HttpPost]
        [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<DeviceDto>> CreateDevice(CreateDeviceDto deviceDto)
        {
            try
            {
                // 使用 AutoMapper 将DTO映射到Domain实体
                var device = _mapper.Map<Device>(deviceDto);

                // 创建设备
                var createdDevice = await _deviceService.CreateDeviceAsync(device);

                // 使用 AutoMapper 映射回DTO
                var createdDeviceDto = _mapper.Map<DeviceDto>(createdDevice);

                return CreatedAtAction(nameof(GetDevice), new { id = createdDevice.Id }, createdDeviceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建设备时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "创建设备时发生错误");
            }
        }

        /// <summary>
        /// 更新设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="deviceDto">更新的设备信息</param>
        /// <returns>更新后的设备</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(DeviceDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<DeviceDto>> UpdateDevice(long id, UpdateDeviceDto deviceDto)
        {
            try
            {
                // 检查设备是否存在
                var existingDevice = await _deviceService.GetDeviceByIdAsync(id);
                if (existingDevice == null)
                {
                    return NotFound($"ID为{id}的设备不存在");
                }

                // 使用 AutoMapper 更新设备属性
                _mapper.Map(deviceDto, existingDevice);
                existingDevice.LastUpdatedAt = DateTime.Now;

                // 更新设备
                var updatedDevice = await _deviceService.UpdateDeviceAsync(existingDevice);

                // 使用 AutoMapper 映射到DTO
                var updatedDeviceDto = _mapper.Map<DeviceDto>(updatedDevice);

                return Ok(updatedDeviceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备 {DeviceId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "更新设备时发生错误");
            }
        }

        /// <summary>
        /// 删除设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteDevice(long id)
        {
            try
            {
                // 检查设备是否存在
                var existingDevice = await _deviceService.GetDeviceByIdAsync(id);
                if (existingDevice == null)
                {
                    return NotFound($"ID为{id}的设备不存在");
                }

                // 删除设备
                await _deviceService.DeleteDeviceAsync(id);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备 {DeviceId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "删除设备时发生错误");
            }
        }

        #region 设备测试功能

        /// <summary>
        /// 启动设备测试（简化版本）
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="request">测试启动请求</param>
        /// <returns>测试启动结果</returns>
        [HttpPost("{id}/test/start")]
        [ProducesResponseType(typeof(DeviceTestStartResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DeviceTestStartResult>> StartDeviceTest(
            long id, 
            [FromBody] DeviceTestStartRequest request)
        {
            try
            {
                // 验证设备是否存在
                var device = await _deviceService.GetDeviceByIdAsync(id);
                if (device == null)
                {
                    return NotFound($"ID为{id}的设备不存在");
                }

                // 确保请求中的设备ID与路径参数一致
                request.DeviceId = id;

                _logger.LogInformation("启动设备 {DeviceId} 的测试：{TestName}", id, request.TestName);

                var result = await _deviceTestService.StartDeviceTestAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动设备 {DeviceId} 测试时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new DeviceTestStartResult 
                    { 
                        IsSuccess = false, 
                        Message = $"启动测试失败: {ex.Message}" 
                    });
            }
        }

        /// <summary>
        /// 停止设备测试
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="testId">测试ID（可选）</param>
        /// <returns>测试停止结果</returns>
        [HttpPost("{id}/test/stop")]
        [ProducesResponseType(typeof(DeviceTestStopResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DeviceTestStopResult>> StopDeviceTest(
            long id, 
            [FromQuery] long? testId = null)
        {
            try
            {
                // 验证设备是否存在
                var device = await _deviceService.GetDeviceByIdAsync(id);
                if (device == null)
                {
                    return NotFound($"ID为{id}的设备不存在");
                }

                _logger.LogInformation("停止设备 {DeviceId} 的测试", id);

                var result = await _deviceTestService.StopDeviceTestAsync(id, testId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止设备 {DeviceId} 测试时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new DeviceTestStopResult 
                    { 
                        IsSuccess = false, 
                        Message = $"停止测试失败: {ex.Message}" 
                    });
            }
        }

        /// <summary>
        /// 获取设备测试状态
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>设备测试状态</returns>
        [HttpGet("{id}/test/status")]
        [ProducesResponseType(typeof(DeviceTestStatus), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DeviceTestStatus>> GetDeviceTestStatus(long id)
        {
            try
            {
                _logger.LogDebug("获取设备 {DeviceId} 的测试状态", id);

                var status = await _deviceTestService.GetDeviceTestStatusAsync(id);
                
                if (status.TestStatus == "DeviceNotFound")
                {
                    return NotFound(status);
                }

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 测试状态时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new DeviceTestStatus 
                    { 
                        DeviceId = id,
                        TestStatus = "Error",
                        Message = $"获取状态失败: {ex.Message}" 
                    });
            }
        }

        #endregion
    }
}