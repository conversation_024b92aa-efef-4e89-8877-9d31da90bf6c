using System;
using System.ComponentModel.DataAnnotations.Schema;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 程式步骤实体
    /// 表示程式表中的一个步骤
    /// </summary>
    /// 
    [Table("ProgramSteps")]
    public class ProgramStep : BaseEntity<long>
    {
        /// <summary>
        /// 所属程式表ID
        /// </summary>
        public long ProgramId { get; set; }

        /// <summary>
        /// 湿度（%）
        /// </summary>
        public double Humidity { get; set; }

        /// <summary>
        /// 温度（℃）
        /// </summary>
        public double Temperature { get; set; }

        /// <summary>
        /// 是否线性变化
        /// </summary>
        public bool IsLinear { get; set; }

        /// <summary>
        /// 持续时间（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 排序索引 1 开始
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}