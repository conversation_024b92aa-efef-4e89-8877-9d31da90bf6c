﻿using CommunityToolkit.Mvvm.ComponentModel;

namespace EnvizonController.Presentation.ViewModels.Dashboard;

public partial class DeviceLifetimeItem : ObservableObject
{
    [ObservableProperty] private double _currentUsage;
    [ObservableProperty] private double _maxLifetime;
    [ObservableProperty] private string _name;
    [ObservableProperty] private double _percentRemaining;
    [ObservableProperty] private string _unit;

    public string RemainingText => $"{MaxLifetime - CurrentUsage:F1}{Unit}";
    public string PercentText => $"{PercentRemaining:F1}%";
}