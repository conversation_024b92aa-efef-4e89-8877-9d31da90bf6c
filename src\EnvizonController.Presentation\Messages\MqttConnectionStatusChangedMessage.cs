using System;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Messages
{
    /// <summary>
    /// MQTT连接状态变化消息
    /// </summary>
    public class MqttConnectionStatusChangedMessage
    {
        /// <summary>
        /// 旧连接状态
        /// </summary>
        public ConnectionStatus OldStatus { get; set; }

        /// <summary>
        /// 新连接状态
        /// </summary>
        public ConnectionStatus NewStatus { get; set; }

        /// <summary>
        /// 状态变化描述
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 如果发生错误，包含相关异常
        /// </summary>
        public Exception Error { get; set; }
    }
}