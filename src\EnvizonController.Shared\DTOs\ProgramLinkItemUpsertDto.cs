using System;

namespace EnvizonController.Shared.DTOs
{
    /// <summary>
    /// 程式链接项批量新增/更新数据传输对象
    /// </summary>
    public class ProgramLinkItemUpsertDto
    {
        /// <summary>
        /// 链接项ID（为0时表示新增）
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 关联的程式ID
        /// </summary>
        public long ProgramId { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int ExecutionOrder { get; set; }
    }
} 