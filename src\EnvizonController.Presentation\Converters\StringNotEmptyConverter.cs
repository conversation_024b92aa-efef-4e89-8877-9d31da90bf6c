using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将非空字符串转换为true，空字符串转换为false的转换器
    /// </summary>
    public class StringNotEmptyConverter : IValueConverter
    {
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return !string.IsNullOrEmpty(stringValue);
            }

            return false;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}