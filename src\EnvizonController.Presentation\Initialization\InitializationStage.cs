namespace EnvizonController.Presentation.Initialization
{
    /// <summary>
    /// 定义应用程序初始化的各个阶段
    /// </summary>
    public enum InitializationStage
    {
        /// <summary>
        /// 初始化尚未开始
        /// </summary>
        NotStarted = 0,

        /// <summary>
        /// 正在加载配置
        /// </summary>
        LoadingConfiguration = 10,

        /// <summary>
        /// 正在注册服务
        /// </summary>
        RegisteringServices = 30,

        /// <summary>
        /// 正在连接MQTT服务
        /// </summary>
        ConnectingMqtt = 60,

        /// <summary>
        /// 初始化已完成
        /// </summary>
        Completed = 100,

        /// <summary>
        /// 初始化失败
        /// </summary>
        Failed = -1
    }
}