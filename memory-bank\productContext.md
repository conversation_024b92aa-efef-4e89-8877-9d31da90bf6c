# Product Context

这个文件提供了项目的高级概述以及将要创建的预期产品。它最初基于projectBrief.md（如果提供）以及工作目录中所有其他可用的项目相关信息。该文件旨在随着项目的发展而更新，并应用于告知所有其他模式项目的目标和上下文。
2025-05-19 14:41:05 - 日志更新将作为脚注附加到本文件末尾。

*

## 项目目标

* EnvizonController 是一个综合型环境控制与监测系统，设计用于实验室环境测试和工业生产环境监控。
* 提供全面的环境参数（温度、湿度等）监测与控制
* 支持可编程测试计划的定义和执行
* 实现跨平台部署和访问
* 确保不同环境下数据的可靠采集和传输
* 提供实时监控和历史数据分析

## 关键特性

* 多协议支持：系统支持多种通信协议（Modbus、MQTT），便于与各类设备集成
* 跨平台：支持多平台部署（Desktop、Android、iOS、Browser）
* 统一管理：提供统一的设备管理、数据采集、测试计划执行和告警管理功能
* 实时监控：提供实时环境参数监控和状态显示
* 测试计划：支持可编程测试计划的创建和执行
* 数据分析：提供历史数据查询和分析功能
* 告警系统：支持多级别告警和告警处理

## 整体架构

* 清晰的分层架构：表示层、应用层、领域层和基础设施层
* 领域驱动设计(DDD)：通过领域模型捕获业务规则和流程
* MVVM模式：用于表示层的UI逻辑分离
* 依赖注入：实现组件之间的松耦合
* 仓储模式：用于数据访问的抽象
* 工厂模式：用于创建复杂对象，如Modbus客户端
* 适配器模式：用于处理不同平台的特定实现

2025-05-19 14:41:05 - 初始化文件，根据EnvizonController项目结构说明.md和API-Client-Architecture.md文档提取项目相关信息。