﻿using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Markup.Xaml.Templates;
using EnvizonController.Presentation.ViewModels.Dashboard;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.DataTemplates;

public class AlarmItemViewModelTemplateSelector : IDataTemplate
{
    public DataTemplate ErrorTemplate { get; set; }
    public DataTemplate WarningTemplate { get; set; }
    public DataTemplate InfoTemplate { get; set; }
    public DataTemplate ResolvedTemplate { get; set; }

    public Control? Build(object? param)
    {
        if (param is AlarmItemViewModel alarmItem)
        {
            // 首先检查报警状态
            if (alarmItem.Status == AlarmStatus.Processed) return ResolvedTemplate?.Build(param);

            if (alarmItem.Level == AlarmSeverity.High) return ErrorTemplate?.Build(param);

            if (alarmItem.Level == AlarmSeverity.Medium) return WarningTemplate?.Build(param);

            return InfoTemplate?.Build(param);
        }

        throw new ArgumentNullException(nameof(param));
    }

    public bool Match(object? data)
    {
        return data is AlarmItemViewModel;
    }
}