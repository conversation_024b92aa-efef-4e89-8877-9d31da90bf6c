using System.Collections.ObjectModel;
using Avalonia;
using AvaloniaUiKit.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.ApiClient.Results;
using EnvizonController.ApiClient.Services;
using EnvizonController.Shared.DTOs;
using HanumanInstitute.MvvmDialogs;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;

namespace EnvizonController.Presentation.ViewModels.Program;

/// <summary>
///     程式表项视图模型
/// </summary>
public class ProgramItemViewModel : ObservableObject, IModalDialogViewModel, ICloseable
{
    private int _cycleCount = 1;

    private int _cycleEnd = 1;

    private int _cycleStart = 1;
    private long _id;

    private string _name = string.Empty;

    /// <summary>
    ///     构造函数
    /// </summary>
    public ProgramItemViewModel()
    {
        // 初始化命令
        SaveCommand = new AsyncRelayCommand(SaveProgram);
        CancelCommand = new RelayCommand(CancelEdit);
    }

    /// <summary>
    ///     是否为编辑模式
    /// </summary>
    public bool IsEditMode => Id > 0;

    /// <summary>
    ///     程式表ID
    /// </summary>
    public long Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    /// <summary>
    ///     程式名称
    /// </summary>
    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    /// <summary>
    ///     循环次数
    /// </summary>
    public int CycleCount
    {
        get => _cycleCount;
        set => SetProperty(ref _cycleCount, value);
    }

    /// <summary>
    ///     循环开始
    /// </summary>
    public int CycleStart
    {
        get => _cycleStart;
        set => SetProperty(ref _cycleStart, value);
    }

    /// <summary>
    ///     循环结束
    /// </summary>
    public int CycleEnd
    {
        get => _cycleEnd;
        set => SetProperty(ref _cycleEnd, value);
    }

    /// <summary>
    ///     程式步骤列表
    /// </summary>
    public ObservableCollection<ProgramStepItemViewModel> Steps { get; } = new();

    /// <summary>
    ///     步骤数量
    /// </summary>
    public int StepCount => Steps.Count;

    /// <summary>
    ///     总持续时间（秒）
    /// </summary>
    public int TotalDurationSeconds => Steps.Sum(step => step.Duration);

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    ///     保存命令
    /// </summary>
    public IRelayCommand SaveCommand { get; }

    /// <summary>
    ///     取消命令
    /// </summary>
    public IRelayCommand CancelCommand { get; }

    /// <summary>
    ///     标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     消息
    /// </summary>
    public string Message { get; set; }
    public ProgramDTO Program { get; set; }
    /// <summary>
    ///     从DTO创建视图模型
    /// </summary>
    public static ProgramItemViewModel FromDto(ProgramDTO dto)
    {
        var viewModel = new ProgramItemViewModel
        {
            Id = dto.Id,
            Name = dto.Name,
            CycleCount = dto.CycleCount,
            CycleStart = dto.CycleStart,
            CycleEnd = dto.CycleEnd,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            Program = dto
        };

        foreach (var stepDto in dto.Steps) viewModel.Steps.Add(ProgramStepItemViewModel.FromDto(stepDto));

        return viewModel;
    }

    /// <summary>
    ///     转换为DTO
    /// </summary>
    public ProgramDTO ToDto()
    {
        return new ProgramDTO
        {
            Id = Id,
            Name = Name,
            CycleCount = CycleCount,
            CycleStart = CycleStart,
            CycleEnd = CycleEnd,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            Steps = Steps.Select(s => s.ToDto()).ToList()
        };
    }

    /// <summary>
    ///     保存程式
    /// </summary>
    private async Task SaveProgram()
    {
        // 验证输入
        if (string.IsNullOrWhiteSpace(Name))
        {
            var nameBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "程式名称不能为空",
                    ButtonEnum.Ok, Icon.Warning);
            await nameBox.ShowAsync();
            return;
        }

        if (Name.Length < 2 || Name.Length > 50)
        {
            var lengthBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "程式名称长度必须为2-50个字符",
                    ButtonEnum.Ok, Icon.Warning);
            await lengthBox.ShowAsync();
            return;
        }

        if (CycleStart > CycleEnd)
        {
            var cycleBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "循环开始值不能大于循环结束值",
                    ButtonEnum.Ok, Icon.Warning);
            await cycleBox.ShowAsync();
            return;
        }

        if (CycleCount <= 0)
        {
            var countBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "循环次数必须大于0",
                    ButtonEnum.Ok, Icon.Warning);
            await countBox.ShowAsync();
            return;
        }

        if (Steps.Count == 0)
        {
            // 添加一个默认步骤
            Steps.Add(new ProgramStepItemViewModel
            {
                Humidity = 50, // 默认湿度50%
                Temperature = 25, // 默认温度25°C
                IsLinear = false, // 默认非线性变化
                Duration = 3600, // 默认持续时间1小时(3600秒)
                Index = 1, // 第一个步骤
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
            });
        }

        try
        {
            Result<ProgramDTO> result;
            var programApiService = Ioc.Default.GetRequiredService<IProgramApiService>();
            
            if (IsEditMode)
            {
                // 更新现有程序
                result = await programApiService.UpdateProgramAsync(ToDto());
            }
            else
            {
                // 创建新程序
                result = await programApiService.CreateProgramAsync(ToDto());
            }
            
            if (result.IsSuccess)
            {
                // 使用返回的DTO更新当前ViewModel，以获取正确的ID和时间戳
                var updatedDto = result.Data;
                this.Id = updatedDto.Id;
                this.CreatedAt = updatedDto.CreatedAt;
                this.UpdatedAt = updatedDto.UpdatedAt;
                
                // 更新步骤的ID和关联
                if (updatedDto.Steps != null && updatedDto.Steps.Any())
                {
                    Steps.Clear();
                    foreach (var stepDto in updatedDto.Steps)
                    {
                        Steps.Add(ProgramStepItemViewModel.FromDto(stepDto));
                    }
                }
                
                DialogResult = true;
                RequestClose?.Invoke(this, EventArgs.Empty);
            }
            else
            {
                var operation = IsEditMode ? "更新" : "创建";
                var errorBox = MessageBoxManager
                    .GetMessageBoxStandard($"{operation}失败", $"{operation}程式时发生错误，请检查输入并重试。错误信息：" + result.ErrorMessage,
                        ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }
        catch (Exception ex)
        {
            var operation = IsEditMode ? "更新" : "创建";
            var exceptionBox = MessageBoxManager
                .GetMessageBoxStandard($"{operation}异常", $"{operation}过程中发生异常：{ex.Message}",
                    ButtonEnum.Ok, Icon.Error);
            await exceptionBox.ShowAsync();
        }
    }

    /// <summary>
    ///     取消编辑
    /// </summary>
    private void CancelEdit()
    {
        // 通知对话框关闭并返回空结果

        DialogResult = false;
        RequestClose?.Invoke(this, EventArgs.Empty);
    }

    public bool? DialogResult { get; set; }
    public event EventHandler? RequestClose;
}