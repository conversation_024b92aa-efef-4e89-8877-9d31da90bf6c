using AutoMapper;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Application.Mapping
{
    /// <summary>
    /// DataPoint 领域对象的 AutoMapper 配置文件
    /// </summary>
    public class DataPointMappingProfile : Profile
    {
        public DataPointMappingProfile()
        {
            // 如果有 DataPoint 相关的映射配置，可以在这里添加
            // 例如：
            // CreateMap<DataPoint, DataPointDTO>();
            // CreateMap<CreateDataPointDto, DataPoint>();
        }
    }
} 