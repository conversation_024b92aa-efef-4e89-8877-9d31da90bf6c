﻿using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Application.Interfaces;

/// <summary>
///     测试项应用服务接口
///     处理测试项相关的应用层逻辑，包括DTO转换
/// </summary>
public interface ITestItemAppService
{
    /// <summary>
    ///     获取所有测试项
    /// </summary>
    /// <returns>测试项DTO列表</returns>
    Task<IEnumerable<TestRunDTO>> GetAllTestItemsAsync();
    
    /// <summary>
    ///     获取测试项列表（支持查询参数和分页）
    /// </summary>
    /// <param name="queryParams">查询参数，包括分页、名称、日期范围、设备ID、状态等</param>
    /// <returns>分页的测试项DTO列表</returns>
    Task<PagedResultDto<TestRunDTO>> GetTestItemsAsync(TestItemQueryParams queryParams);

    /// <summary>
    ///     根据ID获取测试项
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>测试项DTO，如果未找到则返回null</returns>
    Task<TestRunDTO?> GetTestRunByIdAsync(long id);

    /// <summary>
    ///     根据名称查找测试项
    /// </summary>
    /// <param name="name">测试项名称</param>
    /// <returns>测试项DTO列表</returns>
    Task<IEnumerable<TestRunDTO>> FindTestItemsByNameAsync(string name);

    /// <summary>
    ///     创建新的测试项
    /// </summary>
    /// <param name="testRunDTO">测试项DTO</param>
    /// <returns>创建的测试项DTO</returns>
    Task<TestRunDTO> CreateTestItemAsync(TestRunDTO testRunDTO);

    /// <summary>
    ///     更新测试项
    /// </summary>
    /// <param name="testRunDTO">测试项DTO</param>
    /// <returns>更新后的测试项DTO</returns>
    Task<TestRunDTO> UpdateTestItemAsync(TestRunDTO testRunDTO);

    /// <summary>
    ///     删除测试项
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>是否成功删除</returns>
    Task<bool> DeleteTestItemAsync(long id);

    /// <summary>
    ///     启动测试
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项DTO，如果启动失败则返回null</returns>
    Task<TestRunDTO?> StartTestAsync(long id);

    /// <summary>
    ///     停止测试
    /// </summary>
    /// <param name="id">测试项ID</param>
    /// <returns>更新后的测试项DTO，如果停止失败则返回null</returns>
    Task<TestRunDTO?> StopTestAsync(long id);

    /// <summary>
    ///     初始化默认测试项
    /// </summary>
    /// <returns>初始化的默认测试项</returns>
    Task<IEnumerable<TestRunDTO>> InitializeDefaultTestItemsAsync();
}