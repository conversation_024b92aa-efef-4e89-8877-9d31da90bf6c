﻿using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Modbus.Client.Extensions;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.Application.DataCollection.Protocol;
using static EnvizonController.Application.DataCollection.Protocol.ProtocolDataCollector;

namespace EnvizonController.Application.DataCollection.Strategies
{
    public class AlarmCollectionStrategy : ICollectionStrategy
    {
        private readonly ILogger _logger;

        // Strategies can have their own dependencies injected by DI, e.g., specific service clients
        public AlarmCollectionStrategy(ILogger logger)
        {
            _logger = logger.ForContext<AlarmCollectionStrategy>();
        }

        public bool CanHandle(ProtocolItem item)
        {
            // Assuming ProtocolItem has a 'Type' string property
            return item.GroupName == "报警";
        }

        public async Task<Dictionary<string, object>> CollectAsync(DeviceCollectionDetails deviceCollectionDetails, ProtocolItem item, Device device, IModbusDeviceService modbusDeviceService)
        {
            _logger.Debug("Collecting ALARM data for item: {ItemName} from device: {DeviceName} using address {Address}", item.Name, device.Name, item.Address);
            var ret = await modbusDeviceService.ModbusClient.ReadSingleCoilAsync(device.SlaveId, item.Address,  CancellationToken.None);
            // Simulate actual data collection for an alarm item
            // This would involve communication with the device (e.g., Modbus read)
            if (ret)
            {
                deviceCollectionDetails.Alarms.Add(Alarm.AddAlarm("设备报警", item.DisplayName, device.Id, device.TestId, device.ProtocolId, item.Index));

            }
            var collectedData = new Dictionary<string, object>
            {
                { "value", ret }, // Example: Alarm is active
                { "severity", "High" },
                { "description", $"Simulated alarm data for {item.Name}" },
                { "Name", item.Name }
            };

            return collectedData;
        }
    }
}