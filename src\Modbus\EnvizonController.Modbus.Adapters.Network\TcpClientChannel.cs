using EnvizonController.Modbus.Abstractions.Channels;
using System.Net.Sockets;

namespace EnvizonController.Modbus.Adapters.Network
{
    /// <summary>
    /// 基于TcpClient的TCP通道
    /// </summary>
    public class TcpClientChannel : ModbusChannelBase
    {
        private readonly string _hostAddress;
        private readonly int _port;
        private readonly int _connectionTimeout;
        private TcpClient? _tcpClient;
        private NetworkStream? _stream;
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private bool _isDisposed;

        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        public override bool IsConnected => _tcpClient?.Connected ?? false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="hostAddress">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="connectionTimeout">连接超时（毫秒）</param>
        public TcpClientChannel(string hostAddress, int port = 502, int connectionTimeout = 5000)
        {
            _hostAddress = hostAddress ?? throw new ArgumentNullException(nameof(hostAddress));
            _port = port;
            _connectionTimeout = connectionTimeout;
        }

        /// <summary>
        /// 连接到通道
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_tcpClient?.Connected ?? false)
                    return;

                // 创建新的TcpClient
                _tcpClient = new TcpClient();

                // 连接到服务器
                using var timeoutCts = new CancellationTokenSource(_connectionTimeout);
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);
                
                try
                {
                    await _tcpClient.ConnectAsync(_hostAddress, _port, linkedCts.Token);
                }
                catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                {
                    throw new TimeoutException($"连接到 {_hostAddress}:{_port} 超时");
                }

                // 获取网络流
                _stream = _tcpClient.GetStream();
            }
            catch
            {
                // 如果连接失败，释放资源
                _tcpClient?.Dispose();
                _tcpClient = null;
                _stream = null;
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 断开通道连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                _stream?.Dispose();
                _tcpClient?.Dispose();
                _stream = null;
                _tcpClient = null;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null || data.Length == 0)
                return;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_stream == null || !IsConnected)
                    throw new InvalidOperationException("TCP通道未连接");

                await _stream.WriteAsync(data, cancellationToken);
                await _stream.FlushAsync(cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收到的数据</returns>
        public override async Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_stream == null || !IsConnected)
                    throw new InvalidOperationException("TCP通道未连接");

                // 创建超时取消令牌
                using var timeoutCts = new CancellationTokenSource(timeout);
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);

                try
                {
                    // 等待数据可用
                    while (!_stream.DataAvailable)
                    {
                        await Task.Delay(10, linkedCts.Token);
                    }

                    // 给予一些时间让所有数据到达
                    await Task.Delay(50, linkedCts.Token);

                    // 读取所有可用数据
                    byte[] buffer = new byte[_tcpClient!.Available];
                    if (buffer.Length == 0)
                        return Array.Empty<byte>();

                    int bytesRead = await _stream.ReadAsync(buffer, linkedCts.Token);
                    
                    // 如果读取的字节数小于缓冲区大小，则调整缓冲区大小
                    if (bytesRead < buffer.Length)
                    {
                        byte[] result = new byte[bytesRead];
                        Array.Copy(buffer, result, bytesRead);
                        return result;
                    }
                    
                    return buffer;
                }
                catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                {
                    throw new TimeoutException("接收数据超时");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        /// <returns>异步任务</returns>
        public override async Task ClearReceiveBufferAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                if (_stream == null || !IsConnected)
                    return;

                // 读取并丢弃所有可用数据
                while (_tcpClient!.Available > 0)
                {
                    byte[] buffer = new byte[_tcpClient.Available];
                    await _stream.ReadAsync(buffer);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 释放托管资源
                _semaphore.Dispose();
                _stream?.Dispose();
                _tcpClient?.Dispose();
                _stream = null;
                _tcpClient = null;
            }

            _isDisposed = true;
            base.Dispose(disposing);
        }
    }
}
