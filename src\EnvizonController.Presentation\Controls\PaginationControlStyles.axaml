<Styles
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:EnvizonController.Presentation.Controls">
    
    <!-- 合并PaginationControl.axaml中的资源 -->
    <Styles.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="/Controls/PaginationControl.axaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Styles.Resources>
    
    <!--  根据Size属性应用不同主题的样式  -->
    <Style Selector="controls|PaginationControl[Size=Small]">
        <Setter Property="Theme" Value="{StaticResource SmallPaginationControlTheme}" />
    </Style>

</Styles> 