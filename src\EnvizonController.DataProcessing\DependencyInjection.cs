using EnvizonController.DataProcessing.Implementation;
using EnvizonController.DataProcessing.Processors.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 依赖注入扩展
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// 添加数据处理服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDataProcessingServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册数据处理上下文
            services.AddTransient<IDataProcessingContext, DataProcessingContext>();
            
            // 注册数据处理管道
            services.AddTransient<IDataProcessingPipeline>(provider => 
                new BaseDataProcessingPipeline("DefaultPipeline", "默认处理管道"));
            
            services.AddTransient<IDataProcessingPipeline>(provider => 
                new ParallelDataProcessingPipeline("ParallelPipeline", "并行处理管道"));
            
            // 注册数据处理服务
            services.AddSingleton<IDataProcessingService, DataProcessingService>();
            
            // 注册数据处理协调器
            services.AddSingleton<DataProcessingCoordinator>();
            
            return services;
        }
    }
}
