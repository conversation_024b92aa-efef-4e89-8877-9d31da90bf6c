using AutoMapper;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using System;

namespace EnvizonController.Application.Mappings
{
    /// <summary>
    /// 程式表领域对象的 AutoMapper 配置文件
    /// </summary>
    public class ProgramMappingProfile : Profile
    {
        public ProgramMappingProfile()
        {
            // Program -> ProgramDTO
            CreateMap<Program, ProgramDTO>()
                .ForMember(dest => dest.Steps, opt => opt.MapFrom(src => src.Steps))
                .ForMember(dest => dest.StepCount, opt => opt.Ignore()) // 由DTO计算得出
                .ForMember(dest => dest.TotalDurationSeconds, opt => opt.Ignore()); // 由DTO计算得出

            // ProgramDTO -> Program
            CreateMap<ProgramDTO, Program>()
                .ForMember(dest => dest.Steps, opt => opt.Ignore()) // 单独处理步骤
                .ForMember(dest => dest.CreatedAt, opt => opt.Condition(src => src.Id == 0)) // 只在新建时设置
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.Now));

            // ProgramStep -> ProgramStepDTO
            CreateMap<ProgramStep, ProgramStepDTO>();

            // ProgramStepDTO -> ProgramStep
            CreateMap<ProgramStepDTO, ProgramStep>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Condition(src => src.Id == 0)) // 只在新建时设置
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}