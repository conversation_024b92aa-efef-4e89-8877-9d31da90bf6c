using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Services;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Server.Controllers;

/// <summary>
/// 设备测试控制器
/// 提供设备测试的启动、停止和状态查询功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DeviceTestController : ControllerBase
{
    private readonly IDeviceTestService _deviceTestService;
    private readonly ILogger<DeviceTestController> _logger;

    public DeviceTestController(
        IDeviceTestService deviceTestService,
        ILogger<DeviceTestController> logger)
    {
        _deviceTestService = deviceTestService;
        _logger = logger;
    }

    /// <summary>
    /// 启动设备测试
    /// </summary>
    /// <param name="request">测试启动请求</param>
    /// <returns>测试启动结果</returns>
    [HttpPost("start")]
    [ProducesResponseType(typeof(DeviceTestStartResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DeviceTestStartResult>> StartDeviceTest([FromBody] DeviceTestStartRequest request)
    {
        try
        {
            _logger.LogInformation("收到设备测试启动请求，设备ID: {DeviceId}, 测试名称: {TestName}", 
                request.DeviceId, request.TestName);

            var result = await _deviceTestService.StartDeviceTestAsync(request);

            if (result.IsSuccess)
            {
                _logger.LogInformation("设备测试启动成功，设备ID: {DeviceId}, 测试ID: {TestId}", 
                    request.DeviceId, result.TestRun?.Id);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("设备测试启动失败，设备ID: {DeviceId}, 原因: {Message}", 
                    request.DeviceId, result.Message);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动设备测试时发生异常，设备ID: {DeviceId}", request.DeviceId);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new DeviceTestStartResult 
                { 
                    IsSuccess = false, 
                    Message = $"服务器内部错误: {ex.Message}" 
                });
        }
    }

    /// <summary>
    /// 停止设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>测试停止结果</returns>
    [HttpPost("{deviceId}/stop")]
    [ProducesResponseType(typeof(DeviceTestStopResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DeviceTestStopResult>> StopDeviceTest(
        long deviceId, 
        [FromQuery] long? testId = null)
    {
        try
        {
            _logger.LogInformation("收到设备测试停止请求，设备ID: {DeviceId}, 测试ID: {TestId}", 
                deviceId, testId);

            var result = await _deviceTestService.StopDeviceTestAsync(deviceId, testId);

            if (result.IsSuccess)
            {
                _logger.LogInformation("设备测试停止成功，设备ID: {DeviceId}", deviceId);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("设备测试停止失败，设备ID: {DeviceId}, 原因: {Message}", 
                    deviceId, result.Message);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止设备测试时发生异常，设备ID: {DeviceId}", deviceId);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new DeviceTestStopResult 
                { 
                    IsSuccess = false, 
                    Message = $"服务器内部错误: {ex.Message}" 
                });
        }
    }

    /// <summary>
    /// 获取设备测试状态
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备测试状态</returns>
    [HttpGet("{deviceId}/status")]
    [ProducesResponseType(typeof(DeviceTestStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DeviceTestStatus>> GetDeviceTestStatus(long deviceId)
    {
        try
        {
            _logger.LogDebug("获取设备测试状态，设备ID: {DeviceId}", deviceId);

            var status = await _deviceTestService.GetDeviceTestStatusAsync(deviceId);

            if (status.TestStatus == "DeviceNotFound")
            {
                return NotFound(status);
            }

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备测试状态时发生异常，设备ID: {DeviceId}", deviceId);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new DeviceTestStatus 
                { 
                    DeviceId = deviceId,
                    TestStatus = "Error",
                    Message = $"服务器内部错误: {ex.Message}" 
                });
        }
    }

    /// <summary>
    /// 批量启动多个设备测试
    /// </summary>
    /// <param name="requests">批量测试启动请求</param>
    /// <returns>批量启动结果</returns>
    [HttpPost("batch-start")]
    [ProducesResponseType(typeof(BatchDeviceTestStartResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<BatchDeviceTestStartResult>> BatchStartDeviceTests(
        [FromBody] BatchDeviceTestStartRequest requests)
    {
        try
        {
            _logger.LogInformation("收到批量设备测试启动请求，设备数量: {Count}", requests.Requests.Count);

            var results = new List<DeviceTestStartResult>();
            var successCount = 0;
            var failCount = 0;

            foreach (var request in requests.Requests)
            {
                try
                {
                    var result = await _deviceTestService.StartDeviceTestAsync(request);
                    results.Add(result);

                    if (result.IsSuccess)
                        successCount++;
                    else
                        failCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量启动中单个设备测试失败，设备ID: {DeviceId}", request.DeviceId);
                    results.Add(new DeviceTestStartResult
                    {
                        IsSuccess = false,
                        Message = $"启动失败: {ex.Message}"
                    });
                    failCount++;
                }
            }

            var batchResult = new BatchDeviceTestStartResult
            {
                Results = results,
                TotalCount = requests.Requests.Count,
                SuccessCount = successCount,
                FailCount = failCount,
                Message = $"批量启动完成：成功 {successCount} 个，失败 {failCount} 个"
            };

            _logger.LogInformation("批量设备测试启动完成，成功: {SuccessCount}, 失败: {FailCount}", 
                successCount, failCount);

            return Ok(batchResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量启动设备测试时发生异常");
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new BatchDeviceTestStartResult 
                { 
                    Message = $"服务器内部错误: {ex.Message}",
                    Results = new List<DeviceTestStartResult>()
                });
        }
    }

    /// <summary>
    /// 获取多个设备的测试状态
    /// </summary>
    /// <param name="deviceIds">设备ID列表（逗号分隔）</param>
    /// <returns>设备测试状态列表</returns>
    [HttpGet("status")]
    [ProducesResponseType(typeof(List<DeviceTestStatus>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<List<DeviceTestStatus>>> GetMultipleDeviceTestStatus(
        [FromQuery] string deviceIds)
    {
        try
        {
            _logger.LogDebug("获取多个设备测试状态，设备ID列表: {DeviceIds}", deviceIds);

            var statusList = new List<DeviceTestStatus>();
            var ids = deviceIds.Split(',').Select(id => long.Parse(id.Trim())).ToList();

            foreach (var deviceId in ids)
            {
                try
                {
                    var status = await _deviceTestService.GetDeviceTestStatusAsync(deviceId);
                    statusList.Add(status);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取设备测试状态失败，设备ID: {DeviceId}", deviceId);
                    statusList.Add(new DeviceTestStatus 
                    { 
                        DeviceId = deviceId,
                        TestStatus = "Error",
                        Message = $"获取状态失败: {ex.Message}"
                    });
                }
            }

            return Ok(statusList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取多个设备测试状态时发生异常");
            return StatusCode(StatusCodes.Status500InternalServerError, 
                $"服务器内部错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 暂停设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>暂停结果</returns>
    [HttpPost("{deviceId}/pause")]
    [ProducesResponseType(typeof(DeviceTestPauseResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DeviceTestPauseResult>> PauseDeviceTest(
        long deviceId, 
        [FromQuery] long? testId = null)
    {
        try
        {
            _logger.LogInformation("收到设备测试暂停请求，设备ID: {DeviceId}, 测试ID: {TestId}", 
                deviceId, testId);

            var result = await _deviceTestService.PauseDeviceTestAsync(deviceId, testId);

            if (result.IsSuccess)
            {
                _logger.LogInformation("设备测试暂停成功，设备ID: {DeviceId}", deviceId);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("设备测试暂停失败，设备ID: {DeviceId}, 原因: {Message}", 
                    deviceId, result.Message);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "暂停设备测试时发生异常，设备ID: {DeviceId}", deviceId);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new DeviceTestPauseResult 
                { 
                    IsSuccess = false, 
                    Message = $"服务器内部错误: {ex.Message}" 
                });
        }
    }

    /// <summary>
    /// 恢复设备测试
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testId">测试ID（可选）</param>
    /// <returns>恢复结果</returns>
    [HttpPost("{deviceId}/resume")]
    [ProducesResponseType(typeof(DeviceTestResumeResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DeviceTestResumeResult>> ResumeDeviceTest(
        long deviceId, 
        [FromQuery] long? testId = null)
    {
        try
        {
            _logger.LogInformation("收到设备测试恢复请求，设备ID: {DeviceId}, 测试ID: {TestId}", 
                deviceId, testId);

            var result = await _deviceTestService.ResumeDeviceTestAsync(deviceId, testId);

            if (result.IsSuccess)
            {
                _logger.LogInformation("设备测试恢复成功，设备ID: {DeviceId}", deviceId);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("设备测试恢复失败，设备ID: {DeviceId}, 原因: {Message}", 
                    deviceId, result.Message);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复设备测试时发生异常，设备ID: {DeviceId}", deviceId);
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new DeviceTestResumeResult 
                { 
                    IsSuccess = false, 
                    Message = $"服务器内部错误: {ex.Message}" 
                });
        }
    }
}

#region 批量操作相关的数据传输对象

/// <summary>
/// 批量设备测试启动请求
/// </summary>
public class BatchDeviceTestStartRequest
{
    /// <summary>
    /// 测试启动请求列表
    /// </summary>
    public List<DeviceTestStartRequest> Requests { get; set; } = new();
}

/// <summary>
/// 批量设备测试启动结果
/// </summary>
public class BatchDeviceTestStartResult
{
    /// <summary>
    /// 各个设备的启动结果
    /// </summary>
    public List<DeviceTestStartResult> Results { get; set; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailCount { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

#endregion 