using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using System.Windows.Media;

namespace EnvizonController.Modbus.SerialTester.ViewModels
{
    /// <summary>
    /// 日志项
    /// </summary>
    public class LogItem
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; }
        public string Message { get; set; }
        public Brush Color { get; set; }

        public LogItem(string level, string message)
        {
            Timestamp = DateTime.Now;
            Level = level;
            Message = message;

            Color = level switch
            {
                "ERROR" => new SolidColorBrush(Colors.Red),
                "WARNING" => new SolidColorBrush(Colors.Orange),
                "INFO" => new SolidColorBrush(Colors.White),
                "DEBUG" => new SolidColorBrush(Colors.Gray),
                _ => new SolidColorBrush(Colors.White)
            };
        }

        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level}] {Message}";
        }
    }

    /// <summary>
    /// 日志视图模型
    /// </summary>
    public partial class LogViewModel : ViewModelBase
    {
        [ObservableProperty] private MainViewModel _mainViewModel = null!;
        [ObservableProperty] private ObservableCollection<LogItem> _logs = new();
        [ObservableProperty] private string _logText = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LogViewModel()
        {
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        public void AddLog(string level, string message)
        {
            var logItem = new LogItem(level, message);
            Logs.Add(logItem);
            LogText += logItem.ToString() + Environment.NewLine;
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        public void ClearLogs()
        {
            Logs.Clear();
            LogText = string.Empty;
        }
    }
}
