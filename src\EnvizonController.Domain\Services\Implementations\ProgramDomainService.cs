using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;

namespace EnvizonController.Domain.Services.Implementations
{
    /// <summary>
    /// 程式表领域服务实现
    /// 实现与程式表聚合相关的领域逻辑
    /// </summary>
    public class ProgramDomainService : IProgramDomainService
    {
        private readonly IProgramRepository _programRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="programRepository">程式表仓储接口</param>
        public ProgramDomainService(IProgramRepository programRepository)
        {
            _programRepository = programRepository;
        }

        /// <summary>
        /// 验证程式名称是否有效
        /// </summary>
        /// <param name="name">程式名称</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        public async Task<bool> ValidateProgramNameAsync(string name)
        {
            // 名称不能为空
            if (string.IsNullOrWhiteSpace(name))
                return false;

            // 名称长度限制（2-50个字符）
            if (name.Length < 2 || name.Length > 50)
                return false;

            // 检查名称是否已存在（确保唯一性）
            var existingProgram = await _programRepository.GetByNameAsync(name);
            if (existingProgram != null)
                return false;

            return true;
        }

        /// <summary>
        /// 验证程式循环参数是否有效
        /// </summary>
        /// <param name="cycleStart">循环开始步骤</param>
        /// <param name="cycleEnd">循环结束步骤</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        public Task<bool> ValidateProgramCycleAsync(int cycleStart, int cycleEnd)
        {
            // 循环开始和结束必须为正整数
            if (cycleStart <= 0 || cycleEnd <= 0)
                return Task.FromResult(false);

            // 循环结束必须大于等于循环开始
            if (cycleEnd < cycleStart)
                return Task.FromResult(false);

            // 循环不应过长（假设合理的最大循环范围为100步）
            if (cycleEnd - cycleStart > 100)
                return Task.FromResult(false);

            return Task.FromResult(true);
        }

        /// <summary>
        /// 验证程式步骤参数是否有效
        /// </summary>
        /// <param name="step">程式步骤</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        public Task<bool> ValidateProgramStepAsync(ProgramStep step)
        {
            if (step == null)
                return Task.FromResult(false);

            // 验证温度范围（假设环境实验的合理温度范围：-60°C 到 180°C）
            if (step.Temperature < -60 || step.Temperature > 180)
                return Task.FromResult(false);

            // 验证湿度范围（0% - 100%）
            if (step.Humidity < 0 || step.Humidity > 100)
                return Task.FromResult(false);

            // 验证持续时间（至少1秒，最长7天 = 604,800秒）
            if (step.Duration < 1 || step.Duration > 604800)
                return Task.FromResult(false);

            // 验证排序索引为正整数
            if (step.Index <= 0)
                return Task.FromResult(false);

            return Task.FromResult(true);
        }

        /// <summary>
        /// 计算步骤总持续时间
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>总持续时间（秒）</returns>
        public int CalculateTotalDuration(IEnumerable<ProgramStep> steps)
        {
            if (steps == null)
                return 0;

            return steps.Sum(step => step.Duration);
        }

        /// <summary>
        /// 按索引排序步骤
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>排序后的步骤集合</returns>
        public IEnumerable<ProgramStep> OrderStepsByIndex(IEnumerable<ProgramStep> steps)
        {
            if (steps == null)
                return Enumerable.Empty<ProgramStep>();

            return steps.OrderBy(step => step.Index);
        }

        /// <summary>
        /// 验证程式步骤顺序的完整性
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        public bool ValidateProgramStepsOrder(IEnumerable<ProgramStep> steps)
        {
            if (steps == null || !steps.Any())
                return false;

            var orderedSteps = OrderStepsByIndex(steps).ToList();

            // 至少需要一个步骤
            if (orderedSteps.Count < 1)
                return false;

            // 检查索引是否连续
            for (int i = 0; i < orderedSteps.Count; i++)
            {
                // 索引应从1开始，并且连续递增
                if (orderedSteps[i].Index != i + 1)
                    return false;
            }

            // 检查是否有重复的索引
            var uniqueIndices = orderedSteps.Select(s => s.Index).Distinct();
            if (uniqueIndices.Count() != orderedSteps.Count)
                return false;

            return true;
        }
    }
}