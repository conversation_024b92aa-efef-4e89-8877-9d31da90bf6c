using EnvizonController.Domain.Aggregates;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EnvizonController.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 程式步骤实体配置
    /// </summary>
    public class ProgramStepConfiguration : IEntityTypeConfiguration<ProgramStep>
    {
        public void Configure(EntityTypeBuilder<ProgramStep> builder)
        {
            // 表名
            builder.ToTable("ProgramSteps");

            // 主键
            builder.HasKey(e => e.Id);

            // 配置自增长主键
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            // 必填属性
            builder.Property(e => e.ProgramId)
                .IsRequired();

            builder.Property(e => e.Humidity)
                .IsRequired();

            builder.Property(e => e.Temperature)
                .IsRequired();

            builder.Property(e => e.IsLinear)
                .IsRequired();

            builder.Property(e => e.Duration)
                .IsRequired();

            builder.Property(e => e.Index)
                .IsRequired();

            builder.Property(e => e.CreatedAt)
                .IsRequired();

            builder.Property(e => e.UpdatedAt)
                .IsRequired();

            // 索引
            builder.HasIndex(e => new { e.ProgramId, e.Index });

            // 外键关系配置在Program实体中已经定义
        }
    }
}