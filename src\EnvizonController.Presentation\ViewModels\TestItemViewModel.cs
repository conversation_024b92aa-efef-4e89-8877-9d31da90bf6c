using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 测试项视图模型
    /// </summary>
    public partial class TestItemViewModel : ObservableObject
    {
        /// <summary>
        /// 测试项ID
        /// </summary>
        [ObservableProperty] private long _id;

        /// <summary>
        /// 测试项名称
        /// </summary>
        [ObservableProperty] private string _name = string.Empty;

        /// <summary>
        /// 测试项创建/执行时间
        /// </summary>
        [ObservableProperty] private DateTime _executionTime;

        /// <summary>
        /// 活动报警数量
        /// </summary>
        [ObservableProperty] private int _activeAlarmCount;

        /// <summary>
        /// 是否有活动报警
        /// </summary>
        public bool HasActiveAlarms => ActiveAlarmCount > 0;
    }
} 