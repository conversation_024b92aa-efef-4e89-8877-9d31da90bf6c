# EnvizonController.Mqtt 组件使用指南

## Client 客户端模块

### 初始化与配置参数

MqttClientService 需要以下参数进行初始化：

```csharp
// 客户端选项
var options = new MqttClientOptions
{
    BrokerUrl = "localhost",
    Port = 1883,
    ClientId = $"client-{Guid.NewGuid()}",
    Username = "username", // 可选
    Password = "password", // 可选
    UseTls = false
};

// 依赖注入方式
services.AddMqttClient(options);

// 手动创建
var mqttClientFactory = new MqttClientFactory();
var mqttClient = new MqttClientService(
    mqttClientFactory,
    options,
    handlerRegistry,
    logger);
```

### 连接管理方法

```csharp
// 连接到MQTT服务器
await mqttClient.ConnectAsync();

// 断开连接
await mqttClient.DisconnectAsync();

// 检查连接状态
bool isConnected = mqttClient.IsConnected;
```

### 消息发布与订阅功能

```csharp
// 发布消息
await mqttClient.PublishAsync(
    "device/status", 
    "{\"status\":\"online\"}", 
    MqttQualityOfServiceLevel.AtLeastOnce);

// 订阅主题
await mqttClient.SubscribeAsync(
    "device/commands", 
    MqttQualityOfServiceLevel.ExactlyOnce);

// 取消订阅
await mqttClient.UnsubscribeAsync("device/commands");
```

### 事件处理机制

客户端使用消息处理器注册表处理不同主题的消息：

```csharp
// 实现消息处理器
public class DeviceCommandHandler : IMessageHandler
{
    public bool CanHandle(string topic) => topic.StartsWith("device/commands/");
    
    public async Task HandleMessageAsync(string topic, string message)
    {
        // 处理消息
        Console.WriteLine($"收到命令: {message}");
    }
}

// 注册处理器
handlerRegistry.RegisterHandler(new DeviceCommandHandler());
```

### 错误处理与重连策略

客户端内置自动重连机制：
- 断开连接时自动触发
- 默认延迟5秒后尝试重连
- 连接失败时记录警告日志
- 异常时抛出并记录错误日志

### 高级功能与优化选项

```csharp
// 使用TLS加密连接
var options = new MqttClientOptions
{
    BrokerUrl = "localhost",
    Port = 8883,
    UseTls = true,
    // 可以配置证书
    CertificatePath = "/path/to/certificate.pfx",
    CertificatePassword = "cert-password"
};

// 设置消息服务质量
await mqttClient.PublishAsync(
    "important/data",
    jsonData,
    MqttQualityOfServiceLevel.ExactlyOnce); // 确保消息仅被传递一次
```

### 实际应用场景代码示例

```csharp
// 设备监控系统示例
public class DeviceMonitor
{
    private readonly IMqttClientService _mqttClient;
    
    public DeviceMonitor(IMqttClientService mqttClient)
    {
        _mqttClient = mqttClient;
    }
    
    public async Task StartMonitoringAsync()
    {
        await _mqttClient.ConnectAsync();
        await _mqttClient.SubscribeAsync("devices/+/status");
    }
    
    public async Task SendCommand(string deviceId, string command)
    {
        var message = JsonSerializer.Serialize(new { command, timestamp = DateTime.Now });
        await _mqttClient.PublishAsync($"devices/{deviceId}/commands", message);
    }
}
```

## Server 服务端模块

### 服务器设置与启动流程

```csharp
// 创建和配置服务器
// 依赖注入方式
services.AddMqttServer();
services.AddHostedService<MqttServerBackgroundService>();

// 手动创建
var messageProcessor = new MessageProcessor();
var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<MqttServerService>();
var mqttServer = new MqttServerService(messageProcessor, logger);

// 启动服务器
await mqttServer.StartAsync(CancellationToken.None);

// 停止服务器
await mqttServer.StopAsync(CancellationToken.None);
```

### 客户端连接管理

服务器会自动处理客户端连接事件：

```csharp
// 服务器内部处理连接事件
private Task HandleClientConnectedAsync(ClientConnectedEventArgs args)
{
    _logger.LogInformation($"客户端已连接: ClientId={args.ClientId}");
    return Task.CompletedTask;
}

private Task HandleClientDisconnectedAsync(ClientDisconnectedEventArgs args)
{
    _logger.LogInformation($"客户端已断开连接: ClientId={args.ClientId}, 原因={args.DisconnectType}");
    return Task.CompletedTask;
}
```

### 主题与权限控制

```csharp
// 实现自定义授权服务
public class MqttAuthorizationService : IMqttServerConnectionValidator, IMqttTopicPermissions
{
    public Task ValidateConnectionAsync(ValidatingConnectionEventArgs args)
    {
        // 验证连接凭据
        if (args.Username == "admin" && args.Password == "password")
        {
            args.ReasonCode = MqttConnectReasonCode.Success;
        }
        else
        {
            args.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
        }
        return Task.CompletedTask;
    }
    
    public Task ValidatePublishAsync(ValidatingPublishEventArgs args)
    {
        // 只允许特定客户端发布到特定主题
        if (args.ClientId == "device1" && !args.Topic.StartsWith("devices/device1/"))
        {
            args.ReasonCode = MqttPublishReasonCode.NotAuthorized;
        }
        return Task.CompletedTask;
    }
}
```

### 消息路由与转发机制

服务器使用消息处理器处理接收到的消息：

```csharp
// 注册消息处理器
messageProcessor.RegisterHandler("devices/+/status", new DeviceStatusHandler());
messageProcessor.RegisterHandler("sensors/+/data", new SensorDataHandler());

// 实现消息处理器
public class DeviceStatusHandler : IMessageHandler
{
    public Task HandleMessageAsync(string topic, string message, string clientId)
    {
        // 处理设备状态消息
        Console.WriteLine($"设备状态更新: {topic}, {message}");
        return Task.CompletedTask;
    }
}
```

### 持久化选项配置

```csharp
// 配置服务器持久化选项
var optionsBuilder = new MqttServerOptionsBuilder()
    .WithDefaultEndpoint()
    .WithDefaultEndpointPort(1883)
    .WithPersistentSessions() // 启用会话持久化
    .WithStorage(new MqttServerFileStorage("mqtt_storage"));
```

### 性能监控与调优

```csharp
// 性能监控示例
services.AddMqttServerMetrics();
services.AddHealthChecks().AddCheck<MqttServerHealthCheck>("mqtt_server");

// 调优选项
var optionsBuilder = new MqttServerOptionsBuilder()
    .WithDefaultEndpoint()
    .WithDefaultEndpointPort(1883)
    .WithMaxPendingMessagesPerClient(100) // 限制每个客户端的待处理消息数
    .WithConnectionBacklog(100) // 调整连接积压队列大小
    .WithMaximumQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce);
```

### 实际部署场景示例

```csharp
// 创建生产环境的MQTT服务器
public class ProductionMqttServerSetup
{
    public static void ConfigureServices(IServiceCollection services)
    {
        // 添加MQTT服务器
        services.AddMqttServer(options => {
            options.WithDefaultEndpointPort(1883)
                   .WithTlsEndpointPort(8883)
                   .WithCertificate(new X509Certificate2("server.pfx", "password"))
                   .WithMaxPendingMessagesPerClient(200)
                   .WithMaximumQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce);
        });
        
        // 添加授权服务
        services.AddSingleton<IMqttServerConnectionValidator, MqttAuthorizationService>();
        
        // 添加消息处理器
        services.AddSingleton<IMessageProcessor, MessageProcessor>();
        services.AddSingleton<IMessageHandler, DeviceStatusHandler>();
        services.AddSingleton<IMessageHandler, TelemetryDataHandler>();
        
        // 添加后台服务
        services.AddHostedService<MqttServerBackgroundService>();
        
        // 添加监控
        services.AddMqttServerMetrics();
        services.AddHealthChecks();
    }
} 