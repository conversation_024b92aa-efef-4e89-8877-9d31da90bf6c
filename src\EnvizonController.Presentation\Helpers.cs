﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Data;
using Avalonia.Interactivity;
using Avalonia.Media;
using System.Windows.Input;

namespace EnvizonController.Presentation.Helpers;

public class CommonHelper : AvaloniaObject
{
    // 1) 注册附加属性，类型为 IBrush，也可以换成 Color
    public static readonly AttachedProperty<IBrush> CommonBrushProperty =
        AvaloniaProperty.RegisterAttached<CommonHelper, Button, IBrush>(
            "CommonBrush",
            Brushes.Transparent, // 默认颜色
            defaultBindingMode: BindingMode.OneWay // 可双向绑定
        );

    // 2) Getter / Setter 供 XAML 和 代码调用
    public static void SetCommonBrush(Button target, IBrush value)
    {
        target.SetValue(CommonBrushProperty, value);
    }

    public static IBrush GetCommonBrush(Button target)
    {
        return target.GetValue(CommonBrushProperty);
    }

    #region CommonBrush2
    public static readonly AttachedProperty<IBrush> CommonBrush2Property =
        AvaloniaProperty.RegisterAttached<CommonHelper, Button, IBrush>(
            "CommonBrush2",
            Brushes.Transparent, // 默认颜色
            defaultBindingMode: BindingMode.OneWay // 可双向绑定
        );

    // 2) Getter / Setter 供 XAML 和 代码调用
    public static void SetCommonBrush2(Button target, IBrush value)
    {
        target.SetValue(CommonBrush2Property, value);
    }

    public static IBrush GetCommonBrush2(Button target)
    {
        return target.GetValue(CommonBrush2Property);
    }
    #endregion

    static CommonHelper()
    {
        CommonColorProperty.Changed.AddClassHandler<AvaloniaObject, Color>(OnCommonColorChanged);
    }

    private static void OnCommonColorChanged(AvaloniaObject @object, AvaloniaPropertyChangedEventArgs<Color> args)
    {

        if (@object is Button button)
        {
            // 将 Color 转换为 SolidColorBrush 并设置给 CommonBrush
            SetCommonBrush(button, new SolidColorBrush(args.NewValue.Value));
        }
    }

    // 新增附加属性，类型为 Color，用于设置颜色并自动更新 CommonBrush
    public static readonly AttachedProperty<Color> CommonColorProperty =
        AvaloniaProperty.RegisterAttached<CommonHelper, AvaloniaObject, Color>(
            "CommonColor",
            Colors.Transparent, // 默认颜色
            defaultBindingMode: BindingMode.OneWay // 可双向绑定
        );

    // Getter / Setter 供 XAML 和代码调用
    public static void SetCommonColor(AvaloniaObject target, Color value)
    {
        target.SetValue(CommonColorProperty, value);
    }

    public static Color GetCommonColor(AvaloniaObject target)
    {
        return target.GetValue(CommonColorProperty);
    }

}