using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 程式表仓储实现
    /// </summary>
    public class ProgramRepository : Repository<Program, long>, IProgramRepository
    {
        public ProgramRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 根据名称获取程式表
        /// </summary>
        /// <param name="name">程式表名称</param>
        /// <returns>程式表，如果未找到则返回null</returns>
        public async Task<Program?> GetByNameAsync(string name)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Name == name);
        }

        /// <summary>
        /// 获取所有程式表及其步骤
        /// </summary>
        /// <returns>包含步骤的程式表集合</returns>
        public async Task<IEnumerable<Program>> GetAllWithStepsAsync()
        {
            return await _dbSet.Include(p => p.Steps)
                              .OrderBy(p => p.Name)
                              .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取程式表及其步骤
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>包含步骤的程式表，如果未找到则返回null</returns>
        public async Task<Program?> GetByIdWithStepsAsync(long id)
        {
            return await _dbSet.Include(p => p.Steps)
                              .FirstOrDefaultAsync(p => p.Id == id);
        }

        /// <summary>
        /// 根据ID集合获取多个程式
        /// </summary>
        /// <param name="ids">程式ID集合</param>
        /// <returns>程式集合</returns>
        public async Task<IEnumerable<Program>> GetProgramsByIdsAsync(IEnumerable<long> ids)
        {
            return await _dbSet.Where(p => ids.Contains(p.Id)).ToListAsync();
        }
    }
}