<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQuery.DataChartView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AvaloniaLineSeriesChart.Controls;assembly=AvaloniaLineSeriesChart"
    xmlns:converters="clr-namespace:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dataQuery="clr-namespace:EnvizonController.Presentation.Views.DataQuery"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="600"
    d:DesignWidth="800"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:BoolToOpacityConverter x:Key="BoolToOpacityConverter" />
    </UserControl.Resources>

    <Grid Margin="15" RowDefinitions="Auto,*,Auto,Auto">
        <!--  标题区域  -->
        <Grid
            Grid.Row="0"
            Margin="0,0,0,15"
            ColumnDefinitions="*,Auto">
            <TextBlock
                Grid.Column="0"
                Classes="h3 glow-primary font-cyber"
                Text="数据图表视图" />

            <!--  筛选按钮  -->
            <Button
                Grid.Column="1"
                Width="40"
                Height="40"
                Padding="0"
                Background="#1A1A2E"
                BorderBrush="#0DF0FF"
                BorderThickness="1"
                Classes="cyber-button"
                CornerRadius="6">
                <Button.Flyout>
                    <Flyout Placement="BottomEdgeAlignedRight">
                        <Border
                            MinWidth="300"
                            Padding="15"
                            Background="#0F0E1D"
                            BorderBrush="#333366"
                            BorderThickness="1"
                            CornerRadius="8">
                            <StackPanel>
                                <TextBlock
                                    Margin="0,0,0,15"
                                    Classes="h5 primary font-cyber"
                                    Text="数据系列筛选" />

                                <!--  全选/全不选按钮  -->
                                <Grid Margin="0,0,0,15" ColumnDefinitions="*,*">
                                    <Button
                                        Grid.Column="0"
                                        Margin="0,0,5,0"
                                        Padding="8,6"
                                        Background="#1A1A2E"
                                        BorderBrush="#0DF0FF"
                                        BorderThickness="1"
                                        Classes="cyber-button"
                                        Click="OnShowAllSeries"
                                        Content="全部显示"
                                        CornerRadius="4"
                                        FontSize="12" />
                                    <Button
                                        Grid.Column="1"
                                        Margin="5,0,0,0"
                                        Padding="8,6"
                                        Background="#1A1A2E"
                                        BorderBrush="#FF6B6B"
                                        BorderThickness="1"
                                        Classes="cyber-button"
                                        Click="OnHideAllSeries"
                                        Content="全部隐藏"
                                        CornerRadius="4"
                                        FontSize="12" />
                                </Grid>

                                <ItemsControl Name="FilterItems">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate x:DataType="dataQuery:SeriesFilterItem">
                                            <Border
                                                Margin="0,0,0,10"
                                                Padding="12"
                                                Background="#1A1A2E"
                                                BorderBrush="#333366"
                                                BorderThickness="1"
                                                CornerRadius="6"
                                                Opacity="{Binding Opacity}">
                                                <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                                    <!--  颜色指示器  -->
                                                    <Border
                                                        Grid.Column="0"
                                                        Width="16"
                                                        Height="16"
                                                        Margin="0,0,10,0"
                                                        Background="{Binding Color}"
                                                        CornerRadius="3">
                                                        <Border.Effect>
                                                            <DropShadowEffect
                                                                BlurRadius="4"
                                                                OffsetX="0"
                                                                OffsetY="0"
                                                                Opacity="0.3"
                                                                Color="#0DF0FF" />
                                                        </Border.Effect>
                                                    </Border>

                                                    <!--  系列名称  -->
                                                    <TextBlock
                                                        Grid.Column="1"
                                                        VerticalAlignment="Center"
                                                        Classes="font-cyber"
                                                        FontSize="13"
                                                        FontWeight="Medium"
                                                        Foreground="#E0E0E0"
                                                        Text="{Binding SeriesName}" />

                                                    <!--  系列可见性控制  -->
                                                    <StackPanel
                                                        Grid.Column="2"
                                                        Margin="10,0"
                                                        Orientation="Horizontal">
                                                        <TextBlock
                                                            Margin="0,0,5,0"
                                                            VerticalAlignment="Center"
                                                            Classes="font-cyber"
                                                            FontSize="11"
                                                            Foreground="#999999"
                                                            Text="系列:" />
                                                        <CheckBox
                                                            Checked="OnSeriesVisibilityChanged"
                                                            Classes="cyber-checkbox"
                                                            IsChecked="{Binding IsSeriesVisible, Mode=TwoWay}"
                                                            Unchecked="OnSeriesVisibilityChanged" />
                                                    </StackPanel>

                                                    <!--  Y轴可见性控制  -->
                                                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                                                        <TextBlock
                                                            Margin="0,0,5,0"
                                                            VerticalAlignment="Center"
                                                            Classes="font-cyber"
                                                            FontSize="11"
                                                            Foreground="#999999"
                                                            Text="Y轴:" />
                                                        <CheckBox
                                                            Checked="OnYAxisVisibilityChanged"
                                                            Classes="cyber-checkbox"
                                                            IsChecked="{Binding IsYAxisVisible, Mode=TwoWay}"
                                                            Unchecked="OnYAxisVisibilityChanged" />
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>
                    </Flyout>
                </Button.Flyout>

                <!--  筛选图标  -->
                <Path
                    Width="20"
                    Height="20"
                    Data="M3,2H21V2H21V4H20.92L14,10.92V22.91L10,18.91V10.92L3.09,4H3V2Z"
                    Fill="#0DF0FF"
                    Stretch="Uniform">
                    <Path.Effect>
                        <DropShadowEffect
                            BlurRadius="8"
                            OffsetX="0"
                            OffsetY="0"
                            Opacity="0.6"
                            Color="#0DF0FF" />
                    </Path.Effect>
                </Path>
            </Button>
        </Grid>

        <!--  图表区域  -->
        <Border
            Grid.Row="1"
            Background="#0F0E1D"
            Classes="cyber-noGlow-border">
            <controls:LineChartControl Name="LineChart" VerticalAlignment="Stretch" />
        </Border>

        <!--  图例区域  -->
        <Border
            Grid.Row="2"
            Margin="0,15,0,0"
            Padding="15"
            Background="#0F0E1D"
            Classes="cyber-noGlow-border">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  图例标题  -->
                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,10"
                    Classes="h5 primary font-cyber"
                    Text="数据系列" />

                <!--  图例项  -->
                <ItemsControl Name="LegendItems" Grid.Row="1">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate x:DataType="dataQuery:LegendItem">
                            <Border
                                Margin="0,0,20,8"
                                Padding="10,6"
                                Background="#1A1A2E"
                                BorderBrush="#333366"
                                BorderThickness="1"
                                CornerRadius="6"
                                Cursor="Hand"
                                Opacity="{Binding Opacity}"
                                PointerPressed="OnLegendItemClick">
                                <Border.Styles>
                                    <Style Selector="Border:pointerover">
                                        <Setter Property="Background" Value="#2A2A4E" />
                                        <Setter Property="BorderBrush" Value="#0DF0FF" />
                                    </Style>
                                </Border.Styles>
                                <StackPanel Orientation="Horizontal">
                                    <Border
                                        Width="18"
                                        Height="18"
                                        Margin="0,0,10,0"
                                        Background="{Binding Color}"
                                        CornerRadius="3">
                                        <Border.Effect>
                                            <DropShadowEffect
                                                BlurRadius="6"
                                                OffsetX="0"
                                                OffsetY="0"
                                                Opacity="0.4"
                                                Color="#0DF0FF" />
                                        </Border.Effect>
                                    </Border>
                                    <TextBlock
                                        VerticalAlignment="Center"
                                        Classes="font-cyber"
                                        FontSize="13"
                                        FontWeight="Medium"
                                        Foreground="#E0E0E0"
                                        Text="{Binding Name}" />
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </Grid>
        </Border>

        <!--  统计信息区域  -->
        <Grid
            Grid.Row="3"
            Margin="0,15,0,0"
            ColumnDefinitions="*,*,*">
            <Border
                Grid.Column="0"
                Margin="0,0,8,0"
                Padding="15"
                Background="#0F0E1D"
                Classes="cyber-noGlow-border">
                <StackPanel>
                    <TextBlock
                        HorizontalAlignment="Center"
                        Classes="font-cyber"
                        FontSize="11"
                        Foreground="#FF00FF"
                        Text="MAX VALUE" />
                    <TextBlock
                        Name="MaxValueText"
                        HorizontalAlignment="Center"
                        Classes="glow-primary font-cyber"
                        FontSize="20"
                        FontWeight="Bold"
                        Foreground="#0DF0FF"
                        Text="--" />
                </StackPanel>
            </Border>

            <Border
                Grid.Column="1"
                Margin="4,0"
                Padding="15"
                Background="#0F0E1D"
                Classes="cyber-noGlow-border">
                <StackPanel>
                    <TextBlock
                        HorizontalAlignment="Center"
                        Classes="font-cyber"
                        FontSize="11"
                        Foreground="#FF00FF"
                        Text="MIN VALUE" />
                    <TextBlock
                        Name="MinValueText"
                        HorizontalAlignment="Center"
                        Classes="glow-primary font-cyber"
                        FontSize="20"
                        FontWeight="Bold"
                        Foreground="#0DF0FF"
                        Text="--" />
                </StackPanel>
            </Border>

            <Border
                Grid.Column="2"
                Margin="8,0,0,0"
                Padding="15"
                Background="#0F0E1D"
                Classes="cyber-noGlow-border">
                <StackPanel>
                    <TextBlock
                        HorizontalAlignment="Center"
                        Classes="font-cyber"
                        FontSize="11"
                        Foreground="#FF00FF"
                        Text="AVG VALUE" />
                    <TextBlock
                        Name="AvgValueText"
                        HorizontalAlignment="Center"
                        Classes="glow-primary font-cyber"
                        FontSize="20"
                        FontWeight="Bold"
                        Foreground="#0DF0FF"
                        Text="--" />
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</UserControl> 