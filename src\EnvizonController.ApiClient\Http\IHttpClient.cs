using EnvizonController.ApiClient.Results;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.ApiClient.Http
{
    /// <summary>
    /// HTTP客户端接口
    /// </summary>
    public interface IHttpClient
    {
        /// <summary>
        /// 发送GET请求
        /// </summary>
        Task<Result<T>> GetAsync<T>(string url, IDictionary<string, string> queryParams = null);

        /// <summary>
        /// 发送POST请求
        /// </summary>
        Task<Result<T>> PostAsync<T, TContent>(string url, TContent content);

        /// <summary>
        /// 发送PUT请求
        /// </summary>
        Task<Result<T>> PutAsync<T, TContent>(string url, TContent content);

        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        Task<Result<T>> DeleteAsync<T>(string url);
    }
}