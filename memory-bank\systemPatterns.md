# System Patterns *Optional*

该文件记录项目中使用的重复模式和标准。
它是可选的，但建议随着项目的发展进行更新。
2025-05-19 14:42:28 - 日志更新。

*

## 编码模式

* **接口驱动设计**：所有主要组件都定义接口，实现与抽象分离
* **Result模式**：用于API调用结果的封装，统一处理成功和错误情况
* **异步操作模式**：所有网络和IO操作都设计为异步，使用Task和await
* **工厂创建模式**：复杂对象通过专用工厂创建，如ModbusClientFactory
* **装饰器模式**：用于扩展功能，如CachedApiServiceBase装饰器为API服务添加缓存
* **消息处理器注册模式**：通过处理器注册表实现消息路由，如MQTT中的MessageHandlerRegistry

## 架构模式

* **分层架构**：系统分为表示层、应用层、领域层和基础设施层
* **领域驱动设计**：使用领域模型捕获业务规则和流程
* **MVVM模式**：在表示层实现Model-View-ViewModel模式
* **仓储模式**：用于数据访问的抽象，如API服务接口
* **适配器模式**：用于平台特定实现，如不同平台的Modbus通道实现
* **依赖注入**：通过IoC容器管理依赖关系
* **服务定位器**：在某些场景下用于获取服务实例

## 测试模式

* **单元测试**：针对独立组件的测试，如Modbus.Tests中的单元测试
* **模拟对象**：测试时使用模拟对象替代真实依赖，如MockModbusChannel
* **集成测试**：针对多个组件协作的测试
* **测试治具**：提供测试辅助功能，如TestModbusChannel
[2025-05-23 14:50:35] - 服务器启动初始化模式

## 服务初始化模式

* **作用域初始化模式**：使用 `CreateScope()` 创建服务作用域进行初始化操作
  - 确保在正确的依赖注入上下文中获取服务实例
  - 自动管理服务生命周期和资源释放

* **顺序依赖初始化模式**：按照依赖关系顺序初始化系统组件
  - 协议 → 设备 → 测试项的初始化顺序
  - 避免初始化时的依赖冲突

* **托管服务模式**：使用ASP.NET Core的IHostedService实现后台服务
  - 自动处理服务启动、停止和生命周期管理
  - 与应用程序生命周期集成

## 数据库初始化模式

* **条件初始化模式**：支持多种数据库初始化策略
  - 开发环境：`EnsureCreated()` 快速创建
  - 生产环境：`Migrate()` 迁移策略
  - 通过注释切换不同的初始化方式

## 服务注册模式

* **扩展方法注册模式**：通过扩展方法组织服务注册
  - `AddApplicationServices()`、`AddInfrastructureServices()`
  - 保持Program.cs的简洁性，分层管理依赖注册