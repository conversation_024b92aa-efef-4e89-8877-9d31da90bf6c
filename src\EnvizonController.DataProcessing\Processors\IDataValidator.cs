using System;

namespace EnvizonController.DataProcessing.Processors
{
    /// <summary>
    /// 数据验证器接口
    /// </summary>
    public interface IDataValidator : IDataProcessor
    {
        /// <summary>
        /// 验证函数
        /// </summary>
        Func<object, bool> ValidationFunction { get; }
        
        /// <summary>
        /// 获取验证错误信息
        /// </summary>
        string ValidationErrorMessage { get; }
    }
}
