using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 字符串格式化转换器
    /// </summary>
    public class StringFormatConverter : IValueConverter
    {
        public static readonly StringFormatConverter Instance = new();

        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return string.Empty;

            string format = parameter.ToString() ?? string.Empty;
            
            try
            {
                return string.Format(culture, format, value);
            }
            catch
            {
                return value.ToString() ?? string.Empty;
            }
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 