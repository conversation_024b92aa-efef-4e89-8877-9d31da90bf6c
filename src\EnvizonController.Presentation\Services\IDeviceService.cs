using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Presentation.Services
{
    /// <summary>
    /// 设备服务接口
    /// </summary>
    public interface IDeviceService
    {
        /// <summary>
        /// 获取所有设备
        /// </summary>
        Task<ServiceResult<PagedResultDto<DeviceDto>>> GetDevicesAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        Task<ServiceResult<DeviceDto>> GetDeviceAsync(long id);

        /// <summary>
        /// 创建设备
        /// </summary>
        Task<ServiceResult<DeviceDto>> CreateDeviceAsync(DeviceDto device);

        /// <summary>
        /// 更新设备
        /// </summary>
        Task<ServiceResult<DeviceDto>> UpdateDeviceAsync(long id, DeviceDto device);

        /// <summary>
        /// 删除设备
        /// </summary>
        Task<ServiceResult<bool>> DeleteDeviceAsync(long id);
    }

    /// <summary>
    /// 服务结果包装器
    /// </summary>
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; private set; }
        public string ErrorMessage { get; private set; }
        public int StatusCode { get; private set; }
        public T Data { get; private set; }

        private ServiceResult(bool isSuccess, T data, string errorMessage, int statusCode)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            StatusCode = statusCode;
        }

        public static ServiceResult<T> Success(T data, int statusCode = 200)
        {
            return new ServiceResult<T>(true, data, null, statusCode);
        }

        public static ServiceResult<T> Failure(string errorMessage, int statusCode)
        {
            return new ServiceResult<T>(false, default, errorMessage, statusCode);
        }
    }
}