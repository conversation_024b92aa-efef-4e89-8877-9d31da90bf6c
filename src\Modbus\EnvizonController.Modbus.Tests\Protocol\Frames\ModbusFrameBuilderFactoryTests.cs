using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Frames
{
    public class ModbusFrameBuilderFactoryTests
    {
        [Fact]
        public void CreateFrameBuilder_WithRtuTransportType_ReturnsRtuFrameBuilder()
        {
            // Act
            var frameBuilder = ModbusFrameBuilderFactory.CreateFrameBuilder(ModbusTransportType.Rtu);
            
            // Assert
            Assert.IsType<ModbusRtuFrameBuilder>(frameBuilder);
        }
        
        [Fact]
        public void CreateFrameBuilder_WithAsciiTransportType_ReturnsAsciiFrameBuilder()
        {
            // Act
            var frameBuilder = ModbusFrameBuilderFactory.CreateFrameBuilder(ModbusTransportType.Ascii);
            
            // Assert
            Assert.IsType<ModbusAsciiFrameBuilder>(frameBuilder);
        }
        
        [Fact]
        public void CreateFrameBuilder_WithTcpTransportType_ReturnsTcpFrameBuilder()
        {
            // Act
            var frameBuilder = ModbusFrameBuilderFactory.CreateFrameBuilder(ModbusTransportType.Tcp);
            
            // Assert
            Assert.IsType<ModbusTcpFrameBuilder>(frameBuilder);
        }
        
        [Fact]
        public void CreateFrameBuilder_WithInvalidTransportType_ThrowsArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                ModbusFrameBuilderFactory.CreateFrameBuilder((ModbusTransportType)99));
            
            Assert.Contains("不支持的传输类型", exception.Message);
        }
    }
}
