﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 协议类
    /// 表示一个通信协议的定义
    /// </summary>
    [Table("Protocols")]
    public class Protocol : BaseEntity<long>
    {
        /// <summary>
        /// 协议名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 协议显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 协议描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 协议版本
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 协议项集合
        /// </summary>
        public List<ProtocolItem> Items { get; set; } = new List<ProtocolItem>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加协议项
        /// </summary>
        /// <param name="item">协议项</param>
        public void AddItem(ProtocolItem item)
        {
            Items.Add(item);
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 移除协议项
        /// </summary>
        /// <param name="item">协议项</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveItem(ProtocolItem item)
        {
            bool result = Items.Remove(item);
            if (result)
            {
                UpdatedAt = DateTime.Now;
            }
            return result;
        }

        /// <summary>
        /// 根据名称查找协议项
        /// </summary>
        /// <param name="name">协议项名称</param>
        /// <returns>协议项，如果未找到则返回null</returns>
        public ProtocolItem? FindItemByName(string name)
        {
            return Items.FirstOrDefault(item => item.Name == name);
        }

        /// <summary>
        /// 根据地址查找协议项
        /// </summary>
        /// <param name="address">地址</param>
        /// <returns>协议项，如果未找到则返回null</returns>
        public ProtocolItem? FindItemByAddress(ushort address)
        {
            return Items.FirstOrDefault(item => item.Address == address);
        }

        /// <summary>
        /// 根据分组名称获取协议项
        /// </summary>
        /// <param name="groupName">分组名称</param>
        /// <returns>协议项列表</returns>
        public List<ProtocolItem> GetItemsByGroup(string groupName)
        {
            return Items.Where(item => item.GroupName == groupName).ToList();
        }

     
    }
}
