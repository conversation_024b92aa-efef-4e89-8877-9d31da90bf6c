<UserControl
    x:Class="EnvizonController.Presentation.Views.AlarmView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:EnvizonController.Presentation.Views.Controls"
    xmlns:controls1="clr-namespace:EnvizonController.Presentation.Controls"
    xmlns:converters="clr-namespace:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:enums="using:EnvizonController.Shared.Enums"
    xmlns:helpers="clr-namespace:EnvizonController.Presentation.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:presentation="clr-namespace:EnvizonController.Presentation"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="768"
    d:DesignWidth="1024"
    x:DataType="vm:AlarmViewModel"
    mc:Ignorable="d">

    <Design.DataContext>
        <vm:AlarmViewModel />
    </Design.DataContext>
    <UserControl.Resources>
        <converters:CountToBrushConverter x:Key="CountToBrushConverter" />
        <converters:LevelToBrushConverter x:Key="LevelToBrushConverter" />
        <converters:EqualityConverter x:Key="EqualityConverter" />
        <converters:StatusToVisibilityConverter x:Key="StatusToVisibilityConverter" />
        <converters:StatusToBrushConverter x:Key="StatusToBrushConverter" />
        <converters:StatusToStringConverter x:Key="StatusToStringConverter" />
        <converters:LevelToStringConverter x:Key="LevelToStringConverter" />
        <converters:EqualityMultiConverter x:Key="EqualityMultiConverter" />
        <converters:NotEqualityMultiConverter x:Key="NotEqualityMultiConverter" />
        <converters:NullOrEmptyToTrueConverter x:Key="NullOrEmptyToTrueConverter" />


        <converters:NotEqualityConverter x:Key="NotEqualityConverter" />
    </UserControl.Resources>

    <UserControl.Styles>
        <Style Selector="TextBox">
            <Setter Property="Background" Value="#1A2A3A" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="3" />
        </Style>
        <Style Selector="FlyoutPresenter.wider">
            <Setter Property="MaxWidth" Value="850" />
        </Style>
        <!--<Style Selector="Button">
            <Setter Property="Background" Value="#1A2A3A" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="Padding" Value="10,5" />
        </Style>-->

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="Transparent" />
            <!--<Setter Property="BorderBrush" Value="#56EFFE" />-->
        </Style>

        <Style Selector="Button.accent">
            <Setter Property="Background" Value="#0DF0FF" />
            <Setter Property="Foreground" Value="#0B0A1A" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <Style Selector="Button.clear">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
        </Style>

        <!--<Style Selector="ListBox">
            <Setter Property="Background" Value="#0B0A1A" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderBrush" Value="#224F5F" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>

        <Style Selector="ListBoxItem:selected">
            <Setter Property="Background" Value="#224F5F" />
            <Setter Property="Foreground" Value="#0DF0FF" />
        </Style>

        <Style Selector="ListBoxItem:pointerover">
            <Setter Property="Background" Value="#1A2A3A" />
        </Style>

        <Style Selector="Expander">
            <Setter Property="Background" Value="#0B0A1A" />
            <Setter Property="BorderBrush" Value="#224F5F" />
            <Setter Property="Foreground" Value="White" />
        </Style>-->

        <!--<Style Selector="Expander /template/ ToggleButton">
            <Setter Property="Template">
                <ControlTemplate>
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4 4 0 0">
                        <Grid ColumnDefinitions="*,Auto">
                            <ContentPresenter
                                Grid.Column="0"
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding Content}" />
                            <Border
                                Grid.Column="1"
                                Width="20"
                                Height="20"
                                Margin="0,0,10,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Classes="font-icon"
                                    Foreground="#0DF0FF"
                                    Text="&#xf078;" />
                            </Border>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>-->
    </UserControl.Styles>

    <Grid Background="{DynamicResource SurfaceBrush}" ColumnDefinitions="320,*">
        <!--  左侧栏 - 测试项选择与筛选区  -->
        <Border
            Grid.Column="0"
            Margin="5,5,3,5"
            BorderThickness="1"
            Classes="cyber-border">

            <Grid RowDefinitions="Auto,*">
                <!--  顶部搜索与筛选区  -->
                <Border
                    Grid.Row="0"
                    Padding="10"
                    Background="#0B0A1A"
                    BorderBrush="#224F5F"
                    BorderThickness="0,0,0,1">
                    <Grid RowDefinitions="Auto,Auto">
                        <!--  标题区  -->
                        <TextBlock
                            Grid.Row="0"
                            Margin="0,0,0,10"
                            Classes="primary"
                            FontSize="18"
                            FontWeight="Bold"
                            Text="测试项" />

                        <!--  搜索与筛选区  -->
                        <Grid Grid.Row="1" RowDefinitions="Auto,Auto">
                            <!--  主搜索框  -->
                            <DockPanel Grid.Row="0">
                                <Button
                                    Margin="5,0,0,5"
                                    VerticalAlignment="Stretch"
                                    Classes="glow2"
                                    Command="{Binding ApplyTestItemFiltersCommand}"
                                    DockPanel.Dock="Right"
                                    ToolTip.Tip="精确搜索">
                                    <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                        <TextBlock
                                            Width="16"
                                            Height="16"
                                            VerticalAlignment="Center"
                                            Classes="font-icon"
                                            Text="&#xf002;" />
                                        <TextBlock
                                            Margin="3,0,0,0"
                                            VerticalAlignment="Center"
                                            Text="搜索" />
                                    </StackPanel>
                                </Button>
                                <TextBox
                                    Grid.Row="0"
                                    Margin="0,0,0,5"
                                    DockPanel.Dock="Left"
                                    Text="{Binding TestItemSearchText}"
                                    Watermark="搜索测试项名称..." />
                            </DockPanel>


                            <!--  高级筛选按钮  -->
                            <Grid Grid.Row="1" ColumnDefinitions="*,Auto">
                                <Button
                                    Grid.Column="0"
                                    HorizontalAlignment="Left"
                                    Classes="glow2"
                                    Command="{Binding ToggleFilterPanelCommand}">
                                    <Button.Flyout>
                                        <Flyout
                                            Flyout.FlyoutPresenterClasses="wider"
                                            IsOpen="{Binding IsFilterPanelOpen}"
                                            Placement="BottomEdgeAlignedLeft"
                                            ShowMode="Transient">
                                            <Border
                                                Padding="10"
                                                Background="#0B0A1A"
                                                BorderBrush="#224F5F"
                                                BorderThickness="1"
                                                CornerRadius="4">
                                                <StackPanel>
                                                    <!--  测试项名称精确筛选  -->
                                                    <TextBlock
                                                        Margin="0,0,0,5"
                                                        Foreground="White"
                                                        Text="测试项名称" />
                                                    <TextBox
                                                        Margin="0,0,0,10"
                                                        Text="{Binding TestItemNameFilter}"
                                                        Watermark="输入精确名称..." />

                                                    <!--  执行时间范围  -->
                                                    <TextBlock
                                                        Margin="0,0,0,5"
                                                        Foreground="White"
                                                        Text="执行时间范围" />
                                                    <Grid ColumnDefinitions="*,Auto,*">
                                                        <DatePicker
                                                            Grid.Column="0"
                                                            Background="#1A2A3A"
                                                            BorderBrush="#0DF0FF"
                                                            Foreground="White"
                                                            SelectedDate="{Binding TestItemStartDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                        <TextBlock
                                                            Grid.Column="1"
                                                            Margin="5,0"
                                                            VerticalAlignment="Center"
                                                            Foreground="White"
                                                            Text="至" />
                                                        <DatePicker
                                                            Grid.Column="2"
                                                            Background="#1A2A3A"
                                                            BorderBrush="#0DF0FF"
                                                            Foreground="White"
                                                            SelectedDate="{Binding TestItemEndDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                    </Grid>

                                                    <!--  筛选按钮区  -->
                                                    <StackPanel
                                                        Margin="0,10,0,0"
                                                        HorizontalAlignment="Right"
                                                        Orientation="Horizontal">
                                                        <Button
                                                            Margin="0,0,10,0"
                                                            Classes="accent"
                                                            Command="{Binding ApplyTestItemFiltersCommand}"
                                                            Content="应用筛选" />
                                                        <Button
                                                            Classes="secondary"
                                                            Command="{Binding ClearTestItemFiltersCommand}"
                                                            Content="清除筛选" />
                                                    </StackPanel>
                                                </StackPanel>
                                            </Border>
                                        </Flyout>
                                    </Button.Flyout>
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock
                                                Width="16"
                                                Height="16"
                                                Margin="0,0,5,0"
                                                VerticalAlignment="Center"
                                                Classes="font-icon"
                                                Text="&#xf0b0;" />
                                            <TextBlock Text="高级筛选" />
                                        </StackPanel>
                                    </Button.Content>
                                </Button>

                                <Button
                                    Grid.Column="1"
                                    Padding="6"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Classes="clear"
                                    Command="{Binding ClearTestItemFiltersCommand}"
                                    IsVisible="{Binding HasActiveTestItemFilters}"
                                    ToolTip.Tip="清除所有筛选">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Classes="font-icon"
                                        FontSize="21"
                                        Foreground="Red"
                                        Text="&#xf00d;" />
                                </Button>
                            </Grid>
                        </Grid>
                    </Grid>
                </Border>

                <!--  测试项列表与高级筛选面板  -->
                <Grid Grid.Row="1" RowDefinitions="Auto,*">
                    <!--  测试项列表  -->
                    <ListBox
                        Grid.Row="1"
                        Margin="5,5,5,0"
                        Classes="cyber"
                        ItemsSource="{Binding TestItems}"
                        SelectedItem="{Binding SelectedTestItem}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="5" ColumnDefinitions="*,Auto">
                                    <TextBlock
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        Foreground="White"
                                        Text="{Binding Name}" />
                                    <Border
                                        Grid.Column="1"
                                        Padding="9,2"
                                        VerticalAlignment="Center"
                                        Background="{Binding ActiveAlarmCount, Converter={StaticResource CountToBrushConverter}}"
                                        CornerRadius="10"
                                        Effect="{DynamicResource GlowEffect2}"
                                        IsVisible="{Binding HasActiveAlarms}">
                                        <TextBlock
                                            FontSize="12"
                                            Foreground="White"
                                            Text="{Binding ActiveAlarmCount}" />
                                    </Border>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Grid>
        </Border>

        <!--  右侧栏 - 报警列表与详情  -->
        <Border
            Grid.Column="1"
            Margin="3,5,5,5"
            BorderThickness="1"
            Classes="cyber-border">

            <Grid RowDefinitions="Auto,*">
                <!--  顶部操作区  -->
                <Border
                    Grid.Row="0"
                    Margin="0"
                    Padding="15,10"
                    Background="#0B0A1A"
                    BorderBrush="#224F5F"
                    BorderThickness="0,0,0,1">

                    <Grid RowDefinitions="Auto,Auto">
                        <!--  当前选中测试项提示  -->
                        <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto">
                            <TextBlock
                                Grid.Column="0"
                                Margin="0,0,0,10"
                                Classes="glow-primary"
                                FontSize="16"
                                FontWeight="SemiBold"
                                Text="{Binding SelectedTestItemDisplay}" />

                            <!--  添加报警状态筛选按钮组  -->
                            <StackPanel
                                Grid.Column="2"
                                Margin="0,0,0,10"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <Button
                                    Margin="0,0,5,0"
                                    Classes.glow2="{Binding SelectedAlarmStatus, Converter={StaticResource NullOrEmptyToTrueConverter}}"
                                    Command="{Binding FilterAllAlarmsCommand}">

                                    <TextBlock Text="全部" />
                                </Button>
                                <Button
                                    Margin="0,0,5,0"
                                    Classes.glow2="{Binding SelectedAlarmStatus, Converter={StaticResource EqualityConverter}, ConverterParameter={x:Static enums:AlarmStatus.Active}}"
                                    Command="{Binding FilterActiveAlarmsCommand}">
                                    <TextBlock Text="待处理" />
                                </Button>
                                <Button Classes.glow2="{Binding SelectedAlarmStatus, Converter={StaticResource EqualityConverter}, ConverterParameter={x:Static enums:AlarmStatus.Processed}}" Command="{Binding FilterProcessedAlarmsCommand}">

                                    <TextBlock Text="已处理" />
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!--  报警搜索和筛选区  -->
                        <Grid Grid.Row="1" ColumnDefinitions="*,Auto,Auto">
                            <!--  搜索框  -->
                            <TextBox
                                Grid.Column="0"
                                Margin="0,0,10,0"
                                Text="{Binding AlarmSearchText}"
                                Watermark="搜索报警名称或消息内容..." />
                            <Button
                                Grid.Column="1"
                                Margin="5,0"
                                VerticalAlignment="Stretch"
                                Classes="glow2"
                                Command="{Binding ApplyAlarmFiltersCommand}"
                                DockPanel.Dock="Right"
                                ToolTip.Tip="精确搜索">
                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                    <TextBlock
                                        Width="16"
                                        Height="16"
                                        VerticalAlignment="Center"
                                        Classes="font-icon"
                                        Text="&#xf002;" />
                                    <TextBlock
                                        Margin="3,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="搜索" />
                                </StackPanel>
                            </Button>
                            <!--  高级报警筛选按钮  -->
                            <Button
                                Grid.Column="2"
                                Classes="glow2"
                                Command="{Binding ToggleAlarmFilterPanelCommand}">
                                <Button.Flyout>
                                    <Flyout
                                        Flyout.FlyoutPresenterClasses="wider"
                                        IsOpen="{Binding IsAlarmFilterPanelOpen}"
                                        Placement="BottomEdgeAlignedRight"
                                        ShowMode="Transient">
                                        <Border
                                            Padding="0"
                                            Background="#0B0A1A"
                                            BorderBrush="#224F5F"
                                            BorderThickness="1"
                                            CornerRadius="4">
                                            <StackPanel>
                                                <Grid Margin="10" ColumnDefinitions="*,*">
                                                    <!--  左侧筛选选项  -->
                                                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                                        <!--  报警级别筛选  -->
                                                        <TextBlock
                                                            Margin="0,0,0,5"
                                                            Foreground="White"
                                                            Text="报警级别" />
                                                        <ComboBox
                                                            Width="180"
                                                            Margin="0,0,0,10"
                                                            HorizontalAlignment="Left"
                                                            Background="#1A2A3A"
                                                            BorderBrush="#0DF0FF"
                                                            Foreground="White"
                                                            ItemsSource="{Binding AlarmLevels}"
                                                            SelectedItem="{Binding SelectedAlarmLevel}">
                                                            <ComboBox.ItemTemplate>
                                                                <DataTemplate>
                                                                    <TextBlock Foreground="White" Text="{Binding}" />
                                                                </DataTemplate>
                                                            </ComboBox.ItemTemplate>
                                                        </ComboBox>

                                                        <!--  报警状态筛选  -->
                                                        <TextBlock
                                                            Margin="0,0,0,5"
                                                            Foreground="White"
                                                            Text="报警状态" />
                                                        <ComboBox
                                                            Width="180"
                                                            Margin="0,0,0,10"
                                                            HorizontalAlignment="Left"
                                                            Background="#1A2A3A"
                                                            BorderBrush="#0DF0FF"
                                                            Foreground="White"
                                                            ItemsSource="{Binding AlarmStatuses}"
                                                            SelectedItem="{Binding SelectedAlarmStatus}">
                                                            <ComboBox.ItemTemplate>
                                                                <DataTemplate>
                                                                    <TextBlock Foreground="White" Text="{Binding}" />
                                                                </DataTemplate>
                                                            </ComboBox.ItemTemplate>
                                                        </ComboBox>

                                                        <!--  设备ID筛选  -->
                                                        <TextBlock
                                                            Margin="0,0,0,5"
                                                            Foreground="White"
                                                            Text="设备ID" />
                                                        <TextBox
                                                            Width="180"
                                                            Margin="0,0,0,10"
                                                            HorizontalAlignment="Left"
                                                            Text="{Binding DeviceIdFilter}"
                                                            Watermark="输入设备ID..." />
                                                    </StackPanel>

                                                    <!--  右侧筛选选项  -->
                                                    <StackPanel Grid.Column="1">
                                                        <!--  发生时间范围  -->
                                                        <TextBlock
                                                            Margin="0,0,0,5"
                                                            Foreground="White"
                                                            Text="发生时间范围" />
                                                        <Grid ColumnDefinitions="*,Auto,*">
                                                            <DatePicker
                                                                Grid.Column="0"
                                                                Background="#1A2A3A"
                                                                BorderBrush="#0DF0FF"
                                                                Foreground="White"
                                                                SelectedDate="{Binding AlarmStartDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                            <TextBlock
                                                                Grid.Column="1"
                                                                Margin="5,0"
                                                                VerticalAlignment="Center"
                                                                Foreground="White"
                                                                Text="至" />
                                                            <DatePicker
                                                                Grid.Column="2"
                                                                Background="#1A2A3A"
                                                                BorderBrush="#0DF0FF"
                                                                Foreground="White"
                                                                SelectedDate="{Binding AlarmEndDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                        </Grid>

                                                        <!--  处理人筛选  -->
                                                        <TextBlock
                                                            Margin="0,10,0,5"
                                                            Foreground="White"
                                                            Text="处理人" />
                                                        <TextBox
                                                            Width="180"
                                                            Margin="0,0,0,10"
                                                            HorizontalAlignment="Left"
                                                            Text="{Binding ProcessedByFilter}"
                                                            Watermark="输入处理人名称..." />
                                                    </StackPanel>
                                                </Grid>

                                                <!--  筛选按钮区  -->
                                                <StackPanel
                                                    Margin="10"
                                                    HorizontalAlignment="Right"
                                                    Orientation="Horizontal">
                                                    <Button
                                                        Margin="0,0,10,0"
                                                        Classes="accent"
                                                        Command="{Binding ApplyAlarmFiltersCommand}"
                                                        Content="应用筛选" />
                                                    <Button
                                                        Classes="secondary"
                                                        Command="{Binding ClearAlarmFiltersCommand}"
                                                        Content="清除筛选" />
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Flyout>
                                </Button.Flyout>
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Width="16"
                                            Height="16"
                                            Margin="0,0,5,0"
                                            Classes="font-icon"
                                            Foreground="{Binding $parent[Button].Foreground}"
                                            Text="&#xf0b0;" />
                                        <TextBlock Text="高级筛选" />
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </Grid>
                    </Grid>
                </Border>

                <!--  报警列表与报警筛选区  -->
                <Grid Grid.Row="1" RowDefinitions="*,Auto">
                    <!--  报警列表  -->
                    <ItemsControl
                        Grid.Row="0"
                        Margin="10"
                        ItemsSource="{Binding Alarms}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Expander
                                    Margin="0,5,0,5"
                                    HorizontalAlignment="Stretch"
                                    Classes="cyber"
                                    IsExpanded="{Binding IsExpanded, Mode=TwoWay}">
                                    <Expander.Styles>
                                        <Style Selector="Expander">
                                            <Setter Property="Background" Value="#0B0A1A" />
                                            <Setter Property="BorderBrush" Value="#224F5F" />
                                            <Setter Property="BorderThickness" Value="1" />
                                            <Setter Property="CornerRadius" Value="4" />
                                        </Style>
                                    </Expander.Styles>

                                    <Expander.Header>
                                        <Grid ColumnDefinitions="Auto,*,Auto,Auto,Auto">
                                            <!--  级别指示器  -->
                                            <Border
                                                Grid.Column="0"
                                                Width="4"
                                                Height="20"
                                                Margin="0,0,10,0"
                                                Background="{Binding Level, Converter={StaticResource LevelToBrushConverter}}"
                                                CornerRadius="2"
                                                Effect="{DynamicResource GlowEffect2}" />

                                            <!--  报警名称和信息  -->
                                            <StackPanel Grid.Column="1" Orientation="Vertical">
                                                <TextBlock
                                                    FontWeight="SemiBold"
                                                    Foreground="White"
                                                    Text="{Binding Name}" />
                                                <TextBlock
                                                    Classes="gray"
                                                    FontSize="12"
                                                    MaxLines="1"
                                                    Text="{Binding Message}"
                                                    TextTrimming="CharacterEllipsis" />
                                            </StackPanel>

                                            <!--  发生时间  -->
                                            <TextBlock
                                                Grid.Column="2"
                                                Margin="10,0"
                                                VerticalAlignment="Center"
                                                Classes="gray"
                                                FontSize="12"
                                                Text="{Binding Timestamp, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" />

                                            <!--  设备ID  -->
                                            <TextBlock
                                                Grid.Column="3"
                                                Margin="10,0"
                                                VerticalAlignment="Center"
                                                Classes="gray"
                                                FontSize="12"
                                                Text="{Binding DeviceId, StringFormat=设备: {0}}" />

                                            <!--  状态标签  -->
                                            <Border
                                                Grid.Column="4"
                                                Margin="10,0,0,0"
                                                Padding="8,3"
                                                VerticalAlignment="Center"
                                                Background="{Binding Status, Converter={StaticResource StatusToBrushConverter}}"
                                                CornerRadius="10"
                                                Effect="{DynamicResource GlowEffect2}">
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="White"
                                                    Text="{Binding Status, Converter={StaticResource StatusToStringConverter}}" />
                                            </Border>
                                        </Grid>
                                    </Expander.Header>

                                    <!--  详细信息  -->
                                    <Grid Margin="10" RowDefinitions="Auto,Auto,Auto">
                                        <!--  详细信息区域  -->
                                        <Grid
                                            Grid.Row="0"
                                            ColumnDefinitions="Auto,*"
                                            RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                                            <!--  测试ID  -->
                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="测试ID:" />
                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding TestId}" />

                                            <!--  设备ID  -->
                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="设备ID:" />
                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding DeviceId}" />

                                            <!--  报警名称  -->
                                            <TextBlock
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="报警名称:" />
                                            <TextBlock
                                                Grid.Row="2"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding Name}" />

                                            <!--  报警消息  -->
                                            <TextBlock
                                                Grid.Row="3"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                VerticalAlignment="Top"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="报警消息:" />
                                            <TextBlock
                                                Grid.Row="3"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding Message}"
                                                TextWrapping="Wrap" />

                                            <!--  报警级别  -->
                                            <TextBlock
                                                Grid.Row="4"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="报警级别:" />
                                            <TextBlock
                                                Grid.Row="4"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding Level, Converter={StaticResource LevelToStringConverter}}" />

                                            <!--  报警状态  -->
                                            <TextBlock
                                                Grid.Row="5"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="报警状态:" />
                                            <TextBlock
                                                Grid.Row="5"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding Status, Converter={StaticResource StatusToStringConverter}}" />

                                            <!--  处理人  -->
                                            <TextBlock
                                                Grid.Row="6"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="处理人:" />
                                            <TextBlock
                                                Grid.Row="6"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding ProcessedBy}" />
                                        </Grid>

                                        <!--  时间信息区域  -->
                                        <Grid
                                            Grid.Row="1"
                                            Margin="0,10,0,0"
                                            ColumnDefinitions="Auto,*"
                                            RowDefinitions="Auto,Auto,Auto">
                                            <!--  发生时间  -->
                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="发生时间:" />
                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding Timestamp, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" />

                                            <!--  最后更新时间  -->
                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="最后更新:" />
                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding LastUpdated, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" />

                                            <!--  处理时间  -->
                                            <TextBlock
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                Margin="0,5,10,5"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="处理时间:" />
                                            <TextBlock
                                                Grid.Row="2"
                                                Grid.Column="1"
                                                Margin="0,5,0,5"
                                                Foreground="White"
                                                Text="{Binding ProcessedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" />
                                        </Grid>

                                        <!--  操作按钮区域  -->
                                        <StackPanel
                                            Grid.Row="2"
                                            Margin="0,15,0,0"
                                            HorizontalAlignment="Right"
                                            Orientation="Horizontal">
                                            <Button
                                                Classes="glow2"
                                                Command="{Binding $parent[ItemsControl].((vm:AlarmViewModel)DataContext).ProcessAlarmCommand}"
                                                CommandParameter="{Binding}"
                                                Content="处理报警"
                                                IsVisible="{Binding Status, Converter={StaticResource StatusToVisibilityConverter}}" />
                                        </StackPanel>
                                    </Grid>
                                </Expander>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!--  分页控件  -->
                    <controls1:PaginationControl
                        Grid.Row="1"
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Bottom"
                        MaxVisiblePages="3"
                        PageChangedCommand="{Binding PageChangedCommand}"
                        PaginationInfo="{Binding PaginationInfo}"
                        ShowCurrentPageInfo="True"
                        ShowFirstLastButtons="False"
                        ShowNumericButtons="False" />
                </Grid>
            </Grid>
        </Border>


        <presentation:LoadingControl
            Grid.Column="0"
            Grid.ColumnSpan="2"
            IsVisible="{Binding IsLoading}" />
    </Grid>
</UserControl> 