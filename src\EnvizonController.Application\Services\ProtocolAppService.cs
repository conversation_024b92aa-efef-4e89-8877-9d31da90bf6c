﻿using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Application.Services
{
    /// <summary>
    /// 协议应用服务实现
    /// </summary>
    public class ProtocolAppService : IProtocolAppService
    {
        private readonly IProtocolService _protocolService;
        private readonly ILogger<ProtocolAppService> _logger;
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="protocolService">协议服务</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="unitOfWork">工作单元</param>
        public ProtocolAppService(IProtocolService protocolService, ILogger<ProtocolAppService> logger, IUnitOfWork unitOfWork)
        {
            _protocolService = protocolService;
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 获取所有协议
        /// </summary>
        public List<Protocol> GetAllProtocols()
        {
            try
            {
                var protocols = _unitOfWork.Protocols.GetAllAsync().Result.ToList();
                return protocols;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有协议时出错");
                return new List<Protocol>();
            }
        }

        /// <summary>
        /// 根据ID获取协议
        /// </summary>
        public Protocol? GetProtocolById(long id)
        {
            try
            {
                return _unitOfWork.Protocols.GetByIdAsync(id).Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取协议时出错: {Index}", id);
                return null;
            }
        }

        /// <summary>
        /// 根据名称获取协议
        /// </summary>
        public Protocol? GetProtocolByName(string name)
        {
            try
            {
                return _unitOfWork.Protocols.GetByNameAsync(name).Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据名称获取协议时出错: {Name}", name);
                return null;
            }
        }

        /// <summary>
        /// 添加协议
        /// </summary>
        public void AddProtocol(Protocol protocol)
        {
            try
            {
                _unitOfWork.BeginTransactionAsync().Wait();
                
                _unitOfWork.Protocols.AddAsync(protocol).Wait();
                _unitOfWork.SaveChangesAsync().Wait();
                
                _unitOfWork.CommitTransactionAsync().Wait();
                
                _logger.LogInformation("已添加协议: {Name}", protocol.Name);
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTransactionAsync().Wait();
                _logger.LogError(ex, "添加协议时出错: {Name}", protocol.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新协议
        /// </summary>
        public bool UpdateProtocol(Protocol protocol)
        {
            try
            {
                _unitOfWork.BeginTransactionAsync().Wait();
                
                var existingProtocol = _unitOfWork.Protocols.GetByIdAsync(protocol.Id).Result;
                if (existingProtocol == null)
                {
                    _unitOfWork.RollbackTransactionAsync().Wait();
                    _logger.LogWarning("更新协议失败，未找到协议: {Index}", protocol.Id);
                    return false;
                }
                
                // 更新属性
                existingProtocol.Name = protocol.Name;
                existingProtocol.DisplayName = protocol.DisplayName;
                existingProtocol.Description = protocol.Description;
                existingProtocol.Version = protocol.Version;
                existingProtocol.Items = protocol.Items;
                existingProtocol.UpdatedAt = DateTime.Now;
                
                _unitOfWork.Protocols.UpdateAsync(existingProtocol).Wait();
                _unitOfWork.SaveChangesAsync().Wait();
                
                _unitOfWork.CommitTransactionAsync().Wait();
                
                _logger.LogInformation("已更新协议: {Name}", protocol.Name);
                return true;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTransactionAsync().Wait();
                _logger.LogError(ex, "更新协议时出错: {Name}", protocol.Name);
                return false;
            }
        }

        /// <summary>
        /// 删除协议
        /// </summary>
        public bool DeleteProtocol(long id)
        {
            try
            {
                _unitOfWork.BeginTransactionAsync().Wait();
                
                var protocol = _unitOfWork.Protocols.GetByIdAsync(id).Result;
                if (protocol == null)
                {
                    _unitOfWork.RollbackTransactionAsync().Wait();
                    _logger.LogWarning("删除协议失败，未找到协议: {Index}", id);
                    return false;
                }
                
                _unitOfWork.Protocols.Remove(protocol);
                _unitOfWork.SaveChangesAsync().Wait();
                
                _unitOfWork.CommitTransactionAsync().Wait();
                
                _logger.LogInformation("已删除协议: {Index}", id);
                return true;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTransactionAsync().Wait();
                _logger.LogError(ex, "删除协议时出错: {Index}", id);
                return false;
            }
        }

        /// <summary>
        /// 初始化默认协议
        /// </summary>
        public void InitializeDefaultProtocols()
        {
            try
            {
                // 检查是否已有协议
                var existingProtocols = GetAllProtocols();
                if (existingProtocols.Count > 0)
                {
                    _logger.LogInformation("已存在{Count}个协议，跳过初始化", existingProtocols.Count);
                    return;
                }

                _unitOfWork.BeginTransactionAsync().Wait();

                // 创建环境控制协议
                var protocol = new Protocol
                {
                    Name = "EnvizonController",
                    DisplayName = "环境控制器",
                    Description = "环境控制器通信协议",
                    Version = "1.0",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                int index = 1;
                // 添加环境测量项
                var items = new List<ProtocolItem>
                {
                    #region 环境
                    // 环境测量项
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "环境温度",
                        DisplayName = "环境温度",
                        UnitId = 1,
                        Address = 3500,
                        DataType = DataType.Int16,
                        ScaleFactor = 0.01,
                        GroupName = "环境",
                        MinValue = -50,
                        MaxValue = 150,
                        AlarmSeverity = AlarmSeverity.Medium
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "设定温度",
                        DisplayName = "设定温度",
                        UnitId = 1,
                        Address = 3501,
                        DataType = DataType.Int16,
                        ScaleFactor = 0.01,
                        GroupName = "环境",
                        MinValue = -50,
                        MaxValue = 150,
                        AlarmSeverity = AlarmSeverity.Medium
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "环境湿度",
                        DisplayName = "环境湿度",
                        UnitId = 1,
                        Address = 1650,
                        DataType = DataType.Int16,
                        ScaleFactor = 0.01,
                        GroupName = "环境",
                        MinValue = -50,
                        MaxValue = 150,
                        AlarmSeverity = AlarmSeverity.Medium
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "设定湿度",
                        DisplayName = "设定湿度",
                        UnitId = 1,
                        Address = 4105,
                        DataType = DataType.Int16,
                        ScaleFactor = 0.1,
                        GroupName = "环境",
                        MinValue = -50,
                        MaxValue = 150,
                        AlarmSeverity = AlarmSeverity.Medium
                    },
                    #endregion

                    #region 运行设置
                    // 运行设置项
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "定时运行_小时",
                        DisplayName = "定时运行_小时",
                        UnitId = 1,
                        Address = 3050,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "定时运行_分钟",
                        DisplayName = "定时运行_分钟",
                        UnitId = 1,
                        Address = 3051,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "定时已运行_小时",
                        DisplayName = "定时已运行_小时",
                        UnitId = 1,
                        Address = 3042,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "定时已运行_分钟",
                        DisplayName = "定时已运行_分钟",
                        UnitId = 1,
                        Address = 3041,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "当前程式步骤是否完成",
                        DisplayName = "当前程式步骤是否完成",
                        UnitId = 1,
                        Address = 3054,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "当前程式步骤",
                        DisplayName = "当前程式步骤",
                        UnitId = 1,
                        Address = 3056,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        MinValue = 0,
                        MaxValue = 9999
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "当前启动模式",
                        DisplayName = "当前启动模式",
                        UnitId = 1,
                        Address = 3012,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "2:热启动\n1:冷启动\n0:复位启动"
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "运行状态",
                        DisplayName = "运行状态",
                        UnitId = 1,
                        Address = 3057,
                        DataType = DataType.Enum,
                        IsWritable = true,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:停止\n1:运行\n2:暂停",
                        ValueMappings = new Dictionary<int, string>
                        {
                            { 0, "停止" },
                            { 1, "运行" },
                            { 2, "暂停" }
                        }
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "定时温度",
                        DisplayName = "定时温度",
                        UnitId = 1,
                        Address = 3058,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "°C",
                        ScaleFactor = 0.01,
                        MinValue = 0,
                        MaxValue = 300
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "设定温度",
                        DisplayName = "设定温度",
                        UnitId = 1,
                        Address = 2708,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "°C",
                        ScaleFactor = 0.01,
                        MinValue = -100,
                        MaxValue = 300
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "设定湿度",
                        DisplayName = "设定湿度",
                        UnitId = 1,
                        Address = 4101,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "%",
                        ScaleFactor = 0.1,
                        MinValue = -100,
                        MaxValue = 300
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "加热输出",
                        DisplayName = "加热输出",
                        UnitId = 1,
                        Address = 3502,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "%",
                        ScaleFactor = 0.1,
                        MinValue = 0,
                        MaxValue = 100,
                        IsWritable = false
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "制冷输出",
                        DisplayName = "制冷输出",
                        UnitId = 1,
                        Address = 3503,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "%",
                        ScaleFactor = 0.1,
                        MinValue = 0,
                        MaxValue = 100,
                        IsWritable = false
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "加湿输出",
                        DisplayName = "加湿输出",
                        UnitId = 1,
                        Address = 5802,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "%",
                        ScaleFactor = 0.1,
                        MinValue = 0,
                        MaxValue = 100,
                        IsWritable = false
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "除湿输出",
                        DisplayName = "除湿输出",
                        UnitId = 1,
                        Address = 5803,
                        DataType = DataType.Int16,
                        GroupName = "运行设置",
                        Unit = "%",
                        ScaleFactor = 0.1,
                        MinValue = 0,
                        MaxValue = 100,
                        IsWritable = false
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "复位按钮",
                        DisplayName = "复位按钮",
                        UnitId = 1,
                        Address = 0,
                        DataType = DataType.Bool,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:无\n1:复位"
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "照明按钮",
                        DisplayName = "照明按钮",
                        UnitId = 1,
                        Address = 1150,
                        DataType = DataType.Bool,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:关灯\n1:开灯"
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "线性按钮",
                        DisplayName = "线性按钮",
                        UnitId = 1,
                        Address = 37,
                        DataType = DataType.Bool,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:非线性\n1:线性"
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "通讯状态",
                        DisplayName = "通讯状态",
                        UnitId = 1,
                        Address = 38,
                        DataType = DataType.Bool,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:断线\n1:在线"
                    },
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "关闭按钮",
                        DisplayName = "关闭按钮",
                        UnitId = 1,
                        Address = 1,
                        DataType = DataType.Bool,
                        GroupName = "运行设置",
                        Location = "选择器",
                        Description = "0:蜂鸣\n1:消音"
                    },
              
                    #endregion

                    #region 报警
                    
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "相序报警",
                        DisplayName = "相序报警",
                        UnitId = 1,
                        Address = 1000,
                        DataType = DataType.Bool,
                        GroupName = "报警",
                    },      
                    new ProtocolItem
                    {
                        Index = index ++,
                        Name = "超温报警",
                        DisplayName = "超温报警",
                        UnitId = 1,
                        Address = 1001,
                        DataType = DataType.Bool,
                        GroupName = "报警",
                    },
                    #endregion

                };

                // 将协议项添加到协议中
                foreach (var item in items)
                {
                    protocol.AddItem(item);
                }

                // 保存协议
                _unitOfWork.Protocols.AddAsync(protocol).Wait();
                _unitOfWork.SaveChangesAsync().Wait();
                _unitOfWork.CommitTransactionAsync().Wait();
                
                _logger.LogInformation("已初始化默认协议: {Name}", protocol.Name);
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTransactionAsync().Wait();
                _logger.LogError(ex, "初始化默认协议时出错");
            }
        }
    }
}
