using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 程式链接仓储实现
    /// </summary>
    public class ProgramLinkRepository : Repository<ProgramLink, long>, IProgramLinkRepository
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        public ProgramLinkRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <inheritdoc/>
        public async Task<ProgramLink> GetByNameAsync(string name)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Name == name);
        }

        /// <inheritdoc/>
        public async Task<ProgramLink> GetByIdWithItemsAsync(long id)
        {
            return await _dbSet
                .Include(p => p.Items)
                    .ThenInclude(i => i.Program)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        /// <summary>
        /// 获取所有程式表及其步骤
        /// </summary>
        /// <returns>包含步骤的程式表集合</returns>
        public async Task<IEnumerable<ProgramLink>> GetAllWithItemsAsync()
        {
            return await _dbSet.Include(p => p.Items)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

    }
} 