using System;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Processors.Implementation
{
    /// <summary>
    /// 架构验证器
    /// </summary>
    public class SchemaValidator : BaseDataProcessor, IDataValidator
    {
        /// <summary>
        /// 验证函数
        /// </summary>
        public Func<object, bool> ValidationFunction { get; }
        
        /// <summary>
        /// 获取验证错误信息
        /// </summary>
        public string ValidationErrorMessage { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="description">处理器描述</param>
        /// <param name="validationFunction">验证函数</param>
        /// <param name="validationErrorMessage">验证错误信息</param>
        public SchemaValidator(string name, string description, Func<object, bool> validationFunction, string validationErrorMessage = "数据验证失败")
            : base(name, description)
        {
            ValidationFunction = validationFunction ?? throw new ArgumentNullException(nameof(validationFunction));
            ValidationErrorMessage = validationErrorMessage;
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public override Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            try
            {
                // 应用验证函数
                bool isValid = ValidationFunction(context.RawData);
                
                if (isValid)
                {
                    // 验证通过，继续处理
                    context.ProcessedData = context.RawData;
                }
                else
                {
                    // 验证失败，标记为失败
                    context.Status = ProcessingStatus.Failed;
                    context.AddError($"{ValidationErrorMessage}");
                }
                
                return Task.FromResult(context);
            }
            catch (Exception ex)
            {
                context.Status = ProcessingStatus.Failed;
                context.AddError($"验证器 {Name} 处理时出错: {ex.Message}");
                return Task.FromResult(context);
            }
        }
    }
}
