namespace EnvizonController.Shared.DTOs.Common
{
    /// <summary>
    /// 分页结果DTO - 用于API分页响应
    /// </summary>
    /// <typeparam name="T">分页项目类型</typeparam>
    public class PagedResultDto<T>
    {
        /// <summary>
        /// 当前页的数据项
        /// </summary>
        public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
        
        /// <summary>
        /// 总数据项数量
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }
        
        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (TotalCount + PageSize - 1) / PageSize;
        
        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => Page > 1;
        
        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => Page < TotalPages;
    }
}