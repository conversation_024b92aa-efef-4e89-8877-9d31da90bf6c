using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    public class DoubleParseConverter : IValueConverter
    {
        public static readonly DoubleParseConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return 0d;
            
            if (double.TryParse(value.ToString(), out double result))
                return result;
            
            return 0d;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return "0";
            
            return value.ToString();
        }
    }
} 