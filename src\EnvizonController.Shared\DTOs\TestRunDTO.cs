using System.Text.Json;

namespace EnvizonController.Shared.DTOs;

/// <summary>
///     测试项数据传输对象
/// </summary>
public class TestRunDTO
{
    /// <summary>
    ///     测试项ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     测试项名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///     测试项描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    ///     测试开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    ///     测试状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    ///     设备ID
    /// </summary>
    public long DeviceId { get; set; }
    
    /// <summary>
    ///     测试步骤集合
    /// </summary>
    public List<TestStepDTO> TestSteps { get; set; } = new();
    
    /// <summary>
    ///     测试步骤数量
    /// </summary>
    public int StepCount => TestSteps.Count;

    /// <summary>
    ///    预计执行时间（秒）
    /// </summary>
    public int EstimatedDurationSeconds => TestSteps.Sum(step => step.DurationSeconds);

    /// <summary>
    ///     实际执行时间（秒）
    /// </summary>
    public int ActualDurationSeconds { get; set; }

    /// <summary>
    /// ProgramId 或 ProgramLinkId  
    /// </summary>
    public long? ExecutionId { get; set; }
    /// <summary>
    ///     执行类型（Program、ProgramLink）
    /// </summary>
    public string ExecutionType { get; set; } = string.Empty;

    /// <summary>
    ///     执行配置JSON（程式或程式链接快照）
    /// </summary>
    public string ExecutionConfigJson { get; set; } = string.Empty;

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    ///     获取程式快照
    /// </summary>
    /// <returns>程式快照对象，如果ExecutionType不是"Program"或JSON解析失败则返回null</returns>
    public ProgramSnapshotDTO? GetProgramSnapshot()
    {
        if (ExecutionType != "Program" || string.IsNullOrEmpty(ExecutionConfigJson))
            return null;
        
        try
        {
            return JsonSerializer.Deserialize<ProgramSnapshotDTO>(ExecutionConfigJson);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     获取程式链接快照
    /// </summary>
    /// <returns>程式链接快照对象，如果ExecutionType不是"ProgramLink"或JSON解析失败则返回null</returns>
    public ProgramLinkSnapshotDTO? GetProgramLinkSnapshot()
    {
        if (ExecutionType != "ProgramLink" || string.IsNullOrEmpty(ExecutionConfigJson))
            return null;
        
        try
        {
            return JsonSerializer.Deserialize<ProgramLinkSnapshotDTO>(ExecutionConfigJson);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     设置程式快照
    /// </summary>
    /// <param name="snapshot">程式快照对象</param>
    public void SetProgramSnapshot(ProgramSnapshotDTO snapshot)
    {
        ExecutionType = "Program";
        ExecutionConfigJson = JsonSerializer.Serialize(snapshot);
    }

    /// <summary>
    ///     设置程式链接快照
    /// </summary>
    /// <param name="snapshot">程式链接快照对象</param>
    public void SetProgramLinkSnapshot(ProgramLinkSnapshotDTO snapshot)
    {
        ExecutionType = "ProgramLink";
        ExecutionConfigJson = JsonSerializer.Serialize(snapshot);
    }

    /// <summary>
    ///     根据执行类型生成快照并创建测试步骤列表
    /// </summary>
    /// <param name="executionType">执行类型（Program或ProgramLink）</param>
    /// <param name="snapshot">程式或程式链接快照对象</param>
    /// <returns>生成的测试步骤列表</returns>
    public List<TestStepDTO> GenerateSnapshotAndTestSteps(string executionType, object snapshot)
    {
        // 验证并设置执行类型
        if (executionType != "Program" && executionType != "ProgramLink")
            throw new ArgumentException("执行类型必须是Program或ProgramLink", nameof(executionType));

        // 根据执行类型保存快照
        if (executionType == "Program" && snapshot is ProgramSnapshotDTO programSnapshot)
        {
            SetProgramSnapshot(programSnapshot);
            return CreateTestStepsFromProgramSnapshot(programSnapshot);
        }
        else if (executionType == "ProgramLink" && snapshot is ProgramLinkSnapshotDTO programLinkSnapshot)
        {
            SetProgramLinkSnapshot(programLinkSnapshot);
            return CreateTestStepsFromProgramLinkSnapshot(programLinkSnapshot);
        }

        throw new ArgumentException("快照类型与执行类型不匹配", nameof(snapshot));
    }

    /// <summary>
    ///     从程式快照创建测试步骤列表
    /// </summary>
    /// <param name="snapshot">程式快照</param>
    /// <returns>测试步骤列表</returns>
    private List<TestStepDTO> CreateTestStepsFromProgramSnapshot(ProgramSnapshotDTO snapshot)
    {
        var testSteps = new List<TestStepDTO>();
        var orderedSteps = snapshot.Steps.OrderBy(s => s.Index).ToList();

        if (!orderedSteps.Any())
            return testSteps;

        int stepNumber = 1;

        // 如果没有循环配置或循环配置无效，执行所有步骤一次
        if (snapshot.CycleCount <= 0 || snapshot.CycleStart <= 0 || snapshot.CycleEnd <= 0 ||
            snapshot.CycleStart > snapshot.CycleEnd || snapshot.CycleStart > orderedSteps.Count ||
            snapshot.CycleEnd > orderedSteps.Count)
        {
            foreach (var step in orderedSteps)
            {
                testSteps.Add(CreateTestStep(step, stepNumber++));
            }
        }
        else
        {
            // 添加循环前的步骤
            foreach (var step in orderedSteps.Where(s => s.Index < snapshot.CycleStart))
            {
                testSteps.Add(CreateTestStep(step, stepNumber++));
            }

            // 添加循环步骤
            var cycleSteps = orderedSteps.Where(s => s.Index >= snapshot.CycleStart && s.Index <= snapshot.CycleEnd).ToList();

            for (int cycle = 0; cycle < snapshot.CycleCount; cycle++)
            {
                foreach (var step in cycleSteps)
                {
                    testSteps.Add(CreateTestStep(step, stepNumber++));
                }
            }

            // 添加循环后的步骤
            foreach (var step in orderedSteps.Where(s => s.Index > snapshot.CycleEnd))
            {
                testSteps.Add(CreateTestStep(step, stepNumber++));
            }
        }

        return testSteps;
    }

    /// <summary>
    ///     从程式链接快照创建测试步骤列表
    /// </summary>
    /// <param name="snapshot">程式链接快照</param>
    /// <returns>测试步骤列表</returns>
    private List<TestStepDTO> CreateTestStepsFromProgramLinkSnapshot(ProgramLinkSnapshotDTO snapshot)
    {
        var testSteps = new List<TestStepDTO>();
        var orderedPrograms = snapshot.Programs.OrderBy(p => p.ExecutionOrder).ToList();

        if (!orderedPrograms.Any())
            return testSteps;

        int stepNumber = 1;

        for (int cycle = 0; cycle < Math.Max(1, snapshot.CycleCount); cycle++)
        {
            foreach (var programItem in orderedPrograms)
            {
                var programSteps = programItem.ProgramSnapshot.Steps.OrderBy(s => s.Index).ToList();
                var programSnapshot = programItem.ProgramSnapshot;

                // 检查程式是否有循环配置
                if (programSnapshot.CycleCount <= 0 || programSnapshot.CycleStart <= 0 || programSnapshot.CycleEnd <= 0 ||
                    programSnapshot.CycleStart > programSnapshot.CycleEnd || programSnapshot.CycleStart > programSteps.Count ||
                    programSnapshot.CycleEnd > programSteps.Count)
                {
                    // 没有有效的循环配置，执行所有步骤一次
                    foreach (var step in programSteps)
                    {
                        testSteps.Add(CreateTestStep(step, stepNumber++));
                    }
                }
                else
                {
                    // 添加循环前的步骤
                    foreach (var step in programSteps.Where(s => s.Index < programSnapshot.CycleStart))
                    {
                        testSteps.Add(CreateTestStep(step, stepNumber++));
                    }

                    // 添加循环步骤
                    var cycleSteps = programSteps.Where(s => s.Index >= programSnapshot.CycleStart &&
                                                            s.Index <= programSnapshot.CycleEnd).ToList();

                    for (int programCycle = 0; programCycle < programSnapshot.CycleCount; programCycle++)
                    {
                        foreach (var step in cycleSteps)
                        {
                            testSteps.Add(CreateTestStep(step, stepNumber++));
                        }
                    }

                    // 添加循环后的步骤
                    foreach (var step in programSteps.Where(s => s.Index > programSnapshot.CycleEnd))
                    {
                        testSteps.Add(CreateTestStep(step, stepNumber++));
                    }
                }
            }
        }

        return testSteps;
    }

    /// <summary>
    ///     创建单个测试步骤
    /// </summary>
    /// <param name="stepSnapshot">步骤快照</param>
    /// <param name="stepNumber">步骤序号</param>
    /// <returns>测试步骤对象</returns>
    private TestStepDTO CreateTestStep(StepSnapshotDTO stepSnapshot, int stepNumber)
    {
        return new TestStepDTO
        {
            TestItemId = Id,
            Temperature = stepSnapshot.Temperature,
            Humidity = stepSnapshot.Humidity,
            IsLinear = stepSnapshot.IsLinear,
            DurationSeconds = stepSnapshot.DurationSeconds,
            StepNumber = stepNumber,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }
    /// <summary>
    ///     自动根据当前执行类型生成测试步骤
    /// </summary>
    /// <returns>生成的测试步骤列表，如果当前没有有效的执行配置则返回空列表</returns>
    public List<TestStepDTO> GenerateTestSteps()
    {
        // 清空当前的测试步骤列表
        TestSteps.Clear();

        // 根据当前ExecutionType获取相应的快照
        switch (ExecutionType)
        {
            case "Program":
                var programSnapshot = GetProgramSnapshot();
                if (programSnapshot != null)
                {
                    // 使用现有的方法从程式快照创建测试步骤
                    TestSteps = CreateTestStepsFromProgramSnapshot(programSnapshot);
                }
                break;

            case "ProgramLink":
                var programLinkSnapshot = GetProgramLinkSnapshot();
                if (programLinkSnapshot != null)
                {
                    // 使用现有的方法从程式链接快照创建测试步骤
                    TestSteps = CreateTestStepsFromProgramLinkSnapshot(programLinkSnapshot);
                }
                break;
        }

        return TestSteps;
    }

    /// <summary>
    ///     根据当前配置计算测试总步骤数和预计执行时间
    /// </summary>
    /// <returns>是否成功计算配置</returns>
    public bool CalculateExecutionDetails()
    {
        // 先生成测试步骤
        GenerateTestSteps();

        // TestSteps集合已由GenerateTestSteps方法填充
        // StepCount和EstimatedDurationSeconds已经是计算属性，会自动根据TestSteps计算

        // 检查是否成功生成步骤
        return TestSteps.Count > 0;
    }

    /// <summary>
    ///     获取当前执行类型
    /// </summary>
    /// <returns>当前执行类型，如果未设置则返回空字符串</returns>
    public string GetCurrentExecutionType()
    {
        return ExecutionType;
    }

}

/// <summary>
/// 测试项查询参数 - 用于API请求过滤
/// </summary>
public class TestItemQueryParams
{
    /// <summary>
    /// 页码，默认为1
    /// </summary>
    public int Page { get; set; } = 1;
    
    /// <summary>
    /// 每页记录数，默认为20
    /// </summary>
    public int PageSize { get; set; } = 20;
    
    /// <summary>
    /// 搜索文本，用于模糊搜索
    /// </summary>
    public string? SearchText { get; set; }
    
    /// <summary>
    /// 测试项名称，用于精确筛选
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 开始日期筛选
    /// </summary>
    public DateTime? StartDate { get; set; }
    
    /// <summary>
    /// 结束日期筛选
    /// </summary>
    public DateTime? EndDate { get; set; }
    
    /// <summary>
    /// 设备ID筛选
    /// </summary>
    public long? DeviceId { get; set; }
    
    /// <summary>
    /// 测试状态筛选
    /// </summary>
    public string? Status { get; set; }
}