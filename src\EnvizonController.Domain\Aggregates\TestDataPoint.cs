using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;
using EnvizonController.Domain.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Domain.Aggregates;

/// <summary>
///     测试数据点实体，表示测试项中的一个具体数据点。
/// </summary>
[Table("TestDataPoints")]
public class TestDataPoint : BaseEntity<long>
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new();

    private List<ValueData>? _valuesCache ;

    private string _valuesJson = null!;

    /// <summary>
    ///     所属测试项的 ID。
    /// </summary>
    public long TestId { get; set; }

    /// <summary>
    ///     以 JSON 格式存储的数据点值字符串。
    /// </summary>
    public string ValuesJson
    {
        get => _valuesJson;
        set
        {
            if (_valuesJson != value)
            {
                _valuesJson = value;
                _valuesCache = null; // JSON 改变时清空缓存
            }
        }
    }

    /// <summary>
    ///     获取或设置反序列化后的值字典对象。
    ///     此属性不会被 EF Core 映射到数据库。
    /// </summary>
    [NotMapped]
    public List<ValueData> Values
    {
        get
        {
            if (_valuesCache == null && !string.IsNullOrEmpty(ValuesJson))
                try
                {
                    _valuesCache =
                        JsonSerializer.Deserialize<List<ValueData>>(ValuesJson, JsonSerializerOptions);
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"反序列化 TestDataPoint Index {Id} 的 ValuesJson 出错：{ex.Message}");
                    _valuesCache = new List<ValueData>();
                }
            else if (_valuesCache == null && string.IsNullOrEmpty(ValuesJson))
                _valuesCache = new List<ValueData>();

            return _valuesCache!;
        }
        set
        {
            _valuesCache = value;
            _valuesJson = JsonSerializer.Serialize(value, JsonSerializerOptions);
        }
    }

    /// <summary>
    ///     创建时间，默认为当前时间。
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class ValueData
{
    public int ProtocolIndex { get; set; }
    public string Value { get; set; } = null!;

}