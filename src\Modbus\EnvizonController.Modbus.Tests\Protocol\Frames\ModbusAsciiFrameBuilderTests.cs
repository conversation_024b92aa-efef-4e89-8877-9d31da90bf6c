using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;
using System.Text;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Frames
{
    public class ModbusAsciiFrameBuilderTests
    {
        private readonly ModbusAsciiFrameBuilder _frameBuilder;

        public ModbusAsciiFrameBuilderTests()
        {
            _frameBuilder = new ModbusAsciiFrameBuilder();
        }

        [Fact]
        public void BuildRequestFrame_WithReadHoldingRegistersRequest_ReturnsCorrectFrame()
        {
            // Arrange
            var request = new ReadHoldingRegistersRequest(1,0,10);

            // Act
            byte[] frame = _frameBuilder.BuildRequestFrame(request);

            // Assert
            // ASCII帧格式: ":SlaveAddressFunctionCodeDataLRC\r\n"
            string asciiFrame = Encoding.ASCII.GetString(frame);
            Assert.StartsWith(":", asciiFrame);
            Assert.EndsWith("\r\n", asciiFrame);

            // 验证内容
            string content = asciiFrame.Substring(1, asciiFrame.Length - 3);
            Assert.Equal("01030000000AF2", content);
        }

        [Fact]
        public void ParseResponseFrame_WithValidReadHoldingRegistersResponse_ReturnsTrue()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14,
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05,
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseWithLrc = ModbusLrc.AppendLrc(responseData);
            string asciiContent = ModbusLrc.BytesToAscii(responseWithLrc);
            string asciiFrame = ":" + asciiContent + "\r\n";
            byte[] responseFrame = Encoding.ASCII.GetBytes(asciiFrame);

            var response = new ReadHoldingRegistersResponse();

            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);

            // Assert
            Assert.True(result);
            Assert.Equal(1, response.SlaveAddress);
            Assert.Equal(ModbusFunction.ReadHoldingRegisters, response.FunctionCode);
            Assert.Equal(10, response.RegisterValues.Length);

            for (int i = 0; i < 10; i++)
            {
                Assert.Equal((ushort)(i + 1), response.RegisterValues[i]);
            }
        }

        [Fact]
        public void ParseResponseFrame_WithInvalidLrc_ReturnsFalse()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14,
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05,
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseWithLrc = ModbusLrc.AppendLrc(responseData);
            // 修改LRC使其无效
            responseWithLrc[responseWithLrc.Length - 1] = 0x00;
            string asciiContent = ModbusLrc.BytesToAscii(responseWithLrc);
            string asciiFrame = ":" + asciiContent + "\r\n";
            byte[] responseFrame = Encoding.ASCII.GetBytes(asciiFrame);

            var response = new ReadHoldingRegistersResponse();

            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateResponseFrame_WithValidFrame_ReturnsTrue()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14,
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05,
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseWithLrc = ModbusLrc.AppendLrc(responseData);
            string asciiContent = ModbusLrc.BytesToAscii(responseWithLrc);
            string asciiFrame = ":" + asciiContent + "\r\n";
            byte[] responseFrame = Encoding.ASCII.GetBytes(asciiFrame);

            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateResponseFrame_WithInvalidFrame_ReturnsFalse()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14,
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05,
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseWithLrc = ModbusLrc.AppendLrc(responseData);
            // 修改LRC使其无效
            responseWithLrc[responseWithLrc.Length - 1] = 0x00;
            string asciiContent = ModbusLrc.BytesToAscii(responseWithLrc);
            string asciiFrame = ":" + asciiContent + "\r\n";
            byte[] responseFrame = Encoding.ASCII.GetBytes(asciiFrame);

            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);

            // Assert
            Assert.False(result);
        }
    }
}
