using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;
using Serilog;
using System.Collections.Generic;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Application.Services;

/// <summary>
///     数据采集应用服务实现
/// </summary>
public class DataCollectionAppService : IDataCollectionAppService
{
    private readonly IDataCollectionDomainService _dataCollectionService;
    private readonly ILogger _logger;
    private readonly IUnitOfWork _unitOfWork;

    public DataCollectionAppService(
        IDataCollectionDomainService dataCollectionService,
        IUnitOfWork unitOfWork,
        ILogger logger)
    {
        _dataCollectionService = dataCollectionService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    ///     保存采集的数据点
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="testDataPoint">采集的数据</param>
    /// <returns>保存的数据点，如果测试不在运行中则返回null</returns>
    public async Task<TestDataPoint?> SaveDataPointAsync(long testRunId, TestDataPoint testDataPoint)
    {
        try
        {
            _logger.Information("开始保存测试运行 {TestId} 的数据点", testRunId);

            await _unitOfWork.BeginTransactionAsync();

            var result = await _dataCollectionService.SaveDataPointAsync(testRunId, testDataPoint);
            if (result == null)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.Warning("保存数据点失败，测试运行 {TestId} 不在运行中", testRunId);
                return null;
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            return result;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.Error(ex, "保存测试运行 {TestId} 的数据点时发生异常", testRunId);
            throw;
        }
    }

    /// <summary>
    ///     根据设备ID保存采集的数据点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testDataPoint">采集的数据</param>
    /// <returns>保存的数据点，如果没有找到运行中的测试项则返回null</returns>
    public async Task<TestDataPoint?> SaveDataPointByDeviceIdAsync(long deviceId, TestDataPoint testDataPoint)
    {
        try
        {
            _logger.Information("开始保存设备 {DeviceId} 的数据点", deviceId);

            await _unitOfWork.BeginTransactionAsync();

            var result = await _dataCollectionService.SaveDataPointByDeviceIdAsync(deviceId, testDataPoint);
            if (result == null)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.Warning("保存数据点失败，设备 {DeviceId} 没有关联的运行中测试项", deviceId);
                return null;
            }
            await _unitOfWork.TestDataPoints.AddAsync(result);
     

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            return result;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.Error(ex, "保存设备 {DeviceId} 的数据点时发生异常", deviceId);
            throw;
        }
    }

    /// <summary>
    ///     创建数据采集报警
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="message">报警消息</param>
    /// <returns>创建的报警，如果测试不在运行中则返回null</returns>
    public async Task<Alarm?> CreateCollectionAlarmAsync(long testRunId, string message)
    {
        try
        {
            _logger.Information("开始为测试运行 {TestId} 创建报警", testRunId);

            await _unitOfWork.BeginTransactionAsync();

            var result = await _dataCollectionService.CreateCollectionAlarmAsync(testRunId, message);
            if (result == null)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.Warning("创建报警失败，测试运行 {TestId} 不在运行中", testRunId);
                return null;
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            return result;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.Error(ex, "为测试运行 {TestId} 创建报警时发生异常", testRunId);
            throw;
        }
    }

    /// <summary>
    ///     根据设备ID创建数据采集报警
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="message">报警消息</param>
    /// <returns>创建的报警，如果没有找到运行中的测试项则返回null</returns>
    public async Task<Alarm?> CreateCollectionAlarmByDeviceIdAsync(long deviceId, string message)
    {
        try
        {
            _logger.Information("开始为设备 {DeviceId} 创建报警", deviceId);

            await _unitOfWork.BeginTransactionAsync();

            var result = await _dataCollectionService.CreateCollectionAlarmByDeviceIdAsync(deviceId, message);
            if (result == null)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.Warning("创建报警失败，设备 {DeviceId} 没有关联的运行中测试项", deviceId);
                return null;
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            return result;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.Error(ex, "为设备 {DeviceId} 创建报警时发生异常", deviceId);
            throw;
        }
    }

    /// <summary>
    ///     获取测试运行的数据点列表
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>数据点列表</returns>
    public async Task<IEnumerable<TestDataPoint>> GetTestRunDataPointsAsync(long testRunId, int page = 1,
        int pageSize = 20)
    {
        try
        {
            _logger.Information("查询测试运行 {TestId} 的数据点，页码：{Page}，每页大小：{PageSize}", testRunId, page, pageSize);

            // 获取特定测试运行的数据点
            var dataPoints = await _unitOfWork.TestDataPoints.GetByTestRunIdAsync(testRunId);

            // 分页处理
            return dataPoints
                .OrderByDescending(dp => dp.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询测试运行 {TestId} 的数据点时发生异常", testRunId);
            throw;
        }
    }

    /// <summary>
    ///     获取测试运行的数据点列表（分页）
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页的数据点列表</returns>
    public async Task<PagedResultDto<DataPointDto>> GetTestRunDataPointsPagedAsync(long testRunId, int page = 1,
        int pageSize = 20)
    {
        try
        {
            _logger.Information("查询测试运行 {TestId} 的数据点（分页），页码：{Page}，每页大小：{PageSize}", testRunId, page, pageSize);

            // 获取特定测试运行的数据点
            var pagedResultDto = await _unitOfWork.TestDataPoints.GetPagedByTestRunIdAsync(testRunId, page, pageSize);
            var dataPointsList = pagedResultDto.Items.ToList();

            // 获取测试运行信息
            var testRun = await _unitOfWork.TestItems.GetByIdAsync(testRunId);
            if (testRun == null)
            {
                _logger.Warning("未找到测试运行 {TestId}", testRunId);
                return new PagedResultDto<DataPointDto>
                {
                    Items = new List<DataPointDto>(),
                    Page = page,
                    PageSize = pageSize,
                    TotalCount = 0
                };
            }

            // 获取设备关联的协议
            var device = await _unitOfWork.Devices.GetByIdAsync(testRun.DeviceId);
            Protocol? protocol = null;
            if (device != null)
            {
                protocol = await _unitOfWork.Protocols.GetByIdAsync(device.ProtocolId);
            }

            // 手动映射到DTO
            var dataPointDtos = dataPointsList.Select(d => new DataPointDto
            {
                Id = d.Id,
                TestId = d.TestId,
                Timestamp = d.Timestamp,
                Values = d.Values.Select(v => 
                {
                    var valueDto = new ValueDataDto
                    {
                        ProtocolItemIndex = v.ProtocolIndex,
                        Value = v.Value,
                        ProtocolName = string.Empty,
                        Unit = string.Empty
                    };

                    // 如果找到协议，根据ProtocolIndex查找对应的ProtocolItem
                    if (protocol != null)
                    {
                        var protocolItem = protocol.Items.FirstOrDefault(item => item.Index == v.ProtocolIndex);
                        if (protocolItem != null)
                        {
                            valueDto.ProtocolName = protocolItem.DisplayName;
                            valueDto.Unit = protocolItem.Unit;
                        }
                    }

                    return valueDto;
                }).ToList()
            }).ToList();

            return new PagedResultDto<DataPointDto>
            {
                Items = dataPointDtos,
                Page = pagedResultDto.Page,
                PageSize = pagedResultDto.PageSize,
                TotalCount = pagedResultDto.TotalCount
            };
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询测试运行 {TestId} 的数据点（分页）时发生异常", testRunId);
            throw;
        }
    }

    /// <summary>
    ///     获取设备的数据点列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>数据点列表</returns>
    public async Task<IEnumerable<TestDataPoint>> GetDeviceDataPointsAsync(long deviceId, DateTime? startTime = null,
        DateTime? endTime = null, int page = 1, int pageSize = 20)
    {
        try
        {
            _logger.Information("查询设备 {DeviceId} 的数据点，开始时间：{StartTime}，结束时间：{EndTime}，页码：{Page}，每页大小：{PageSize}",
                deviceId, startTime, endTime, page, pageSize);

            // 查找设备相关的测试项
            var testItems = await _unitOfWork.TestItems.FindAsync(t => t.DeviceId == deviceId);

            var enumerable = testItems as TestRun[] ?? testItems.ToArray();
            if (!enumerable.Any()) return [];

            // 获取测试项ID列表
            var testItemIds = enumerable.Select(t => t.Id);

            // 查找与这些测试项相关的数据点
            var dataPoints = new List<TestDataPoint>();
            foreach (var testId in testItemIds)
            {
                var points = await _unitOfWork.TestDataPoints.GetByTestRunIdAsync(testId);
                dataPoints.AddRange(points);
            }

            // 按时间过滤
            var filteredDataPoints = dataPoints.AsEnumerable();
            if (startTime.HasValue)
                filteredDataPoints = filteredDataPoints.Where(dp => dp.Timestamp >= startTime.Value);
            if (endTime.HasValue) filteredDataPoints = filteredDataPoints.Where(dp => dp.Timestamp <= endTime.Value);

            // 分页处理
            return filteredDataPoints
                .OrderByDescending(dp => dp.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询设备 {DeviceId} 的数据点时发生异常", deviceId);
            throw;
        }
    }

    /// <summary>
    ///     获取报警列表
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>报警列表</returns>
    public async Task<IEnumerable<Alarm>> GetAlarmsAsync(long? testRunId = null, int page = 1, int pageSize = 20)
    {
        try
        {
            _logger.Information("查询报警列表，测试运行ID：{TestId}，页码：{Page}，每页大小：{PageSize}",
                testRunId, page, pageSize);

            IEnumerable<Alarm> alarms;

            if (testRunId.HasValue)
                // 查询特定测试运行的报警
                alarms = await _unitOfWork.Alarms.FindAsync(a => a.TestId == testRunId);
            else
                // 查询所有报警
                alarms = await _unitOfWork.Alarms.GetAllAsync();

            // 分页处理
            return alarms
                .OrderByDescending(a => a.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询报警列表时发生异常，测试运行ID：{TestId}", testRunId);
            throw;
        }
    }

    /// <summary>
    ///     获取数据点详情
    /// </summary>
    /// <param name="dataPointId">数据点ID</param>
    /// <returns>数据点详情</returns>
    public async Task<TestDataPoint?> GetDataPointDetailAsync(long dataPointId)
    {
        try
        {
            _logger.Information("查询数据点 {DataPointId} 的详情", dataPointId);

            // 根据ID查询数据点
            return await _unitOfWork.TestDataPoints.GetByIdAsync(dataPointId);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询数据点 {DataPointId} 的详情时发生异常", dataPointId);
            throw;
        }
    }
    
    /// <summary>
    ///     保存报警数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="alarms">报警数据列表</param>
    /// <returns>保存是否成功</returns>
    public async Task<bool> SaveAlarmsAsync(long deviceId, List<AlarmDTO> alarms)
    {
        try
        {
            _logger.Information("开始保存设备 {DeviceId} 的 {Count} 个报警数据", deviceId, alarms.Count);
            
            await _unitOfWork.BeginTransactionAsync();
            
            // 将DTO转换为领域实体
            var alarmEntities = alarms.Select(dto => new Alarm
            {
                DeviceId = dto.DeviceId,
                TestId = dto.TestId,
                Name = dto.Name,
                Message = dto.Message,
                Level = (int)dto.Level,
                Status = (int)dto.Status,
                ProcessedBy = dto.ProcessedBy,
                Timestamp = dto.Timestamp,
                LastUpdated = dto.LastUpdated ?? DateTime.Now,
                ProcessedAt = dto.ProcessedAt
            }).ToList();
            
            // 使用领域服务处理报警数据
            var success = await _dataCollectionService.SaveAlarmsByDeviceIdAsync(deviceId, alarmEntities);
            
            if (!success)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.Warning("处理设备 {DeviceId} 的报警数据失败", deviceId);
                return false;
            }
            
            // 将报警数据添加到仓储
            foreach (var alarm in alarmEntities)
            {
                await _unitOfWork.Alarms.AddAsync(alarm);
            }
            
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();
            
            _logger.Information("成功保存设备 {DeviceId} 的 {Count} 个报警数据", deviceId, alarms.Count);
            return true;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.Error(ex, "保存设备 {DeviceId} 的报警数据时发生异常", deviceId);
            return false;
        }
    }
}