# Envizon Controller API 文档

本文档提供了Envizon Controller系统的API接口说明，包括所有可用的端点、参数和响应格式。

## 目录

1. [设备管理 API](#1-设备管理-api)
2. [数据点 API](#2-数据点-api)
3. [程式表 API](#3-程式表-api)
4. [报警 API](#4-报警-api)
5. [设备测试 API](#5-设备测试-api)
6. [测试项 API](#6-测试项-api)
7. [程式表链接 API](#7-程式表链接-api)

## 1. 设备管理 API

设备管理API用于创建、查询、更新和删除设备信息。

### 1.1 获取所有设备

获取系统中的所有设备，支持分页。

- **URL**: `/api/Devices`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `pageSize`: 每页大小，默认为20
- **响应**: 
  - 状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<DeviceDto>`

```json
{
  "items": [
    {
      "id": 1,
      "name": "设备1",
      "description": "这是设备1的描述",
      "deviceType": "Modbus",
      "connectionString": "COM1:9600",
      "status": "Online",
      "createdAt": "2023-01-01T00:00:00Z",
      "lastUpdatedAt": "2023-01-02T00:00:00Z"
    }
  ],
  "totalCount": 100,
  "page": 1,
  "pageSize": 20
}
```

### 1.2 根据ID获取设备

根据设备ID获取单个设备的详细信息。

- **URL**: `/api/Devices/{id}`
- **方法**: `GET`
- **路径参数**:
  - `id`: 设备ID
- **响应**: 
  - 状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `DeviceDto`

```json
{
  "id": 1,
  "name": "设备1",
  "description": "这是设备1的描述",
  "deviceType": "Modbus",
  "connectionString": "COM1:9600",
  "status": "Online",
  "createdAt": "2023-01-01T00:00:00Z",
  "lastUpdatedAt": "2023-01-02T00:00:00Z"
}
```

### 1.3 创建设备

创建一个新的设备。

- **URL**: `/api/Devices`
- **方法**: `POST`
- **请求体**: `CreateDeviceDto`
- **响应**: 
  - 状态码: 201 Created 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `DeviceDto`

**请求体示例**:
```json
{
  "name": "新设备",
  "description": "这是一个新设备",
  "deviceType": "Modbus",
  "connectionString": "COM2:9600"
}
```

### 1.4 更新设备

更新现有设备的信息。

- **URL**: `/api/Devices/{id}`
- **方法**: `PUT`
- **路径参数**:
  - `id`: 设备ID
- **请求体**: `UpdateDeviceDto`
- **响应**: 
  - 状态码: 200 OK, 404 Not Found 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `DeviceDto`

**请求体示例**:
```json
{
  "name": "更新后的设备名称",
  "description": "更新后的设备描述",
  "deviceType": "Modbus",
  "connectionString": "COM2:9600"
}
```

### 1.5 删除设备

删除指定ID的设备。

- **URL**: `/api/Devices/{id}`
- **方法**: `DELETE`
- **路径参数**:
  - `id`: 设备ID
- **响应**: 
  - 状态码: 204 No Content 或 404 Not Found

## 2. 数据点 API

数据点API用于查询设备和测试的数据点信息。

### 2.1 获取测试运行的数据点

获取特定测试运行的数据点，支持分页。

- **URL**: `/api/DataPoints/by-test/{testId}`
- **方法**: `GET`
- **路径参数**:
  - `testId`: 测试ID
- **查询参数**:
  - `page`: 页码
  - `pageSize`: 每页大小
- **响应**: 
  - 状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<DataPointDto>`

```json
{
  "items": [
    {
      "id": 1,
      "testId": 5,
      "timestamp": "2023-01-01T12:00:00Z",
      "values": [
        {
          "protocolItemIndex": 1,
          "value": 23.5
        }
      ]
    }
  ],
  "totalCount": 100,
  "page": 1,
  "pageSize": 20
}
```

### 2.2 获取设备的数据点

获取特定设备的数据点，支持时间范围和分页。

- **URL**: `/api/DataPoints/by-device/{deviceId}`
- **方法**: `GET`
- **路径参数**:
  - `deviceId`: 设备ID
- **查询参数**:
  - `startTime`: 开始时间
  - `endTime`: 结束时间
  - `page`: 页码
  - `pageSize`: 每页大小
- **响应**: 
  - 状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<DataPointDto>`

### 2.3 获取数据点详情

根据数据点ID获取单个数据点的详细信息。

- **URL**: `/api/DataPoints/{id}`
- **方法**: `GET`
- **路径参数**:
  - `id`: 数据点ID
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `DataPointDto`

### 2.4 获取数据点聚合统计

获取数据点的聚合统计信息。

- **URL**: `/api/DataPoints/aggregate`
- **方法**: `GET`
- **查询参数**:
  - `startTime`: 开始时间
  - `endTime`: 结束时间
  - `aggregationType`: 聚合类型
- **响应**: 
  - 状态码: 200 OK 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `IEnumerable<AggregateDataPointDto>`

## 3. 程式表 API

程式表API用于管理系统中的程式表。

### 3.1 获取所有程式表

获取系统中的所有程式表，支持分页。

- **URL**: `/api/Programs`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `pageSize`: 每页大小，默认为20
- **响应**: 
  - 状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<ProgramDTO>`

### 3.2 根据ID获取程式表

根据程式表ID获取单个程式表的详细信息。

- **URL**: `/api/Programs/{id}`
- **方法**: `GET`
- **路径参数**:
  - `id`: 程式表ID
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `ProgramDTO`

### 3.3 按名称获取程式表

根据程式表名称获取单个程式表的详细信息。

- **URL**: `/api/Programs/name/{name}`
- **方法**: `GET`
- **路径参数**:
  - `name`: 程式表名称
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `ProgramDTO`

### 3.4 创建程式表

创建一个新的程式表。

- **URL**: `/api/Programs`
- **方法**: `POST`
- **请求体**: `ProgramDTO`
- **响应**: 
  - 状态码: 201 Created 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `ProgramDTO`

### 3.5 更新程式表

更新现有程式表的信息。

- **URL**: `/api/Programs/{id}`
- **方法**: `PUT`
- **路径参数**:
  - `id`: 程式表ID
- **请求体**: `ProgramDTO`
- **响应**: 
  -状态码: 200 OK, 404 Not Found 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `ProgramDTO`

### 3.6 删除程式表

删除指定ID的程式表。

- **URL**: `/api/Programs/{id}`
- **方法**: `DELETE`
- **路径参数**:
  - `id`: 程式表ID
- **响应**: 
  -状态码: 204 No Content 或 404 Not Found

## 4. 报警 API

报警API用于管理系统中的报警信息。

### 4.1 获取报警列表

获取系统中的报警列表，支持分页和筛选。

- **URL**: `/api/Alarms`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码
  - `pageSize`: 每页大小
  - `status`: 报警状态
  - `level`: 报警级别
  - `startTime`: 开始时间
  - `endTime`: 结束时间
- **响应**: 
  - 状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<AlarmDTO>`

### 4.2 根据ID获取报警

根据报警ID获取单个报警的详细信息。

- **URL**: `/api/Alarms/{id}`
- **方法**: `GET`
- **路径参数**:
  - `id`: 报警ID
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `AlarmDTO`

### 4.3 创建报警

创建一个新的报警。

- **URL**: `/api/Alarms`
- **方法**: `POST`
- **请求体**: `CreateAlarmDto`
- **响应**: 
  - 状态码: 201 Created 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `AlarmDTO`

**请求体示例**:
```json
{
  "testId": 1,
  "name": "温度过高",
  "message": "设备温度超过阈值",
  "level": "Warning"
}
```

### 4.4 处理报警

处理指定ID的报警。

- **URL**: `/api/Alarms/{id}/process`
- **方法**: `PUT`
- **路径参数**:
  - `id`: 报警ID
- **请求体**: `ProcessAlarmDto`
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `AlarmDTO`

**请求体示例**:
```json
{
  "processedBy": "操作员A"
}
```

### 4.5 获取所有活动报警

获取系统中所有活动状态的报警，支持分页。

- **URL**: `/api/Alarms/active`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `pageSize`: 每页大小，默认为20
- **响应**: 
  -状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<AlarmDTO>`

## 5. 设备测试 API

设备测试API用于管理设备测试的启动、停止和状态查询。

### 5.1 启动设备测试

启动一个设备测试。

- **URL**: `/api/DeviceTest/start`
- **方法**: `POST`
- **请求体**: `DeviceTestStartRequest`
- **响应**: 
  -状态码: 200 OK 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `DeviceTestStartResult`

**请求体示例**:
```json
{
  "deviceId": 1,
  "testName": "温度测试",
  "programId": 2,
  "parameters": {
    "duration": 3600,
    "maxTemperature": 100
  }
}
```

### 5.2 停止设备测试

停止一个正在运行的设备测试。

- **URL**: `/api/DeviceTest/{deviceId}/stop`
- **方法**: `POST`
- **路径参数**:
  - `deviceId`: 设备ID
- **查询参数**:
  - `testId`: 测试ID（可选）
- **响应**: 
  -状态码: 200 OK 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `DeviceTestStopResult`

### 5.3 获取设备测试状态

获取设备测试的当前状态。

- **URL**: `/api/DeviceTest/{deviceId}/status`
- **方法**: `GET`
- **路径参数**:
  - `deviceId`: 设备ID
- **响应**: 
  - 状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `DeviceTestStatus`

### 5.4 批量启动多个设备测试

批量启动多个设备测试。

- **URL**: `/api/DeviceTest/batch-start`
- **方法**: `POST`
- **请求体**: `BatchDeviceTestStartRequest`
- **响应**: 
  -状态码: 200 OK 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `BatchDeviceTestStartResult`

## 6. 测试项 API

测试项API用于管理系统中的测试项。

### 6.1 获取所有测试项

获取系统中的所有测试项，支持分页和筛选。

- **URL**: `/api/TestItems`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码
  - `pageSize`: 每页大小
  - `deviceId`: 设备ID
  - `status`: 测试状态
  - `startTime`: 开始时间
  - `endTime`: 结束时间
- **响应**: 
  -状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `PagedResultDto<TestRunDTO>`

### 6.2 根据ID获取测试项

根据测试项ID获取单个测试项的详细信息。

- **URL**: `/api/TestItems/{id}`
- **方法**: `GET`
- **路径参数**:
  - `id`: 测试项ID
- **响应**: 
  -状态码: 200 OK 或 404 Not Found
  - 内容类型: `application/json`
  - 响应体: `TestRunDTO`

### 6.3 创建测试项

创建一个新的测试项。

- **URL**: `/api/TestItems`
- **方法**: `POST`
- **请求体**: `TestRunDTO`
- **响应**: 
  -状态码: 201 Created 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `TestRunDTO`

### 6.4 更新测试项

更新现有测试项的信息。

- **URL**: `/api/TestItems/{id}`
- **方法**: `PUT`
- **路径参数**:
  - `id`: 测试项ID
- **请求体**: `TestRunDTO`
- **响应**: 
  -状态码: 200 OK, 404 Not Found 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `TestRunDTO`

### 6.5 删除测试项

删除指定ID的测试项。

- **URL**: `/api/TestItems/{id}`
- **方法**: `DELETE`
- **路径参数**:
  - `id`: 测试项ID
- **响应**: 
  -状态码: 204 No Content 或 404 Not Found

### 6.6 启动测试

启动指定ID的测试项。

- **URL**: `/api/TestItems/{id}/start`
- **方法**: `POST`
- **路径参数**:
  - `id`: 测试项ID
- **响应**: 
  -状态码: 200 OK, 404 Not Found 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `TestRunDTO`

### 6.7 停止测试

停止指定ID的测试项。

- **URL**: `/api/TestItems/{id}/stop`
- **方法**: `POST`
- **路径参数**:
  - `id`: 测试项ID
- **响应**: 
  -状态码: 200 OK, 404 Not Found 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `TestRunDTO`

## 7. 程式表链接 API

程式表链接API用于管理程式表与其他实体之间的关联关系。

### 7.1 获取设备的程式表链接

获取指定设备的所有程式表链接。

- **URL**: `/api/ProgramLinks/device/{deviceId}`
- **方法**: `GET`
- **路径参数**:
  - `deviceId`: 设备ID
- **响应**: 
  -状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `IEnumerable<ProgramLinkDTO>`

### 7.2 获取程式表的链接

获取指定程式表的所有链接。

- **URL**: `/api/ProgramLinks/program/{programId}`
- **方法**: `GET`
- **路径参数**:
  - `programId`: 程式表ID
- **响应**: 
  -状态码: 200 OK
  - 内容类型: `application/json`
  - 响应体: `IEnumerable<ProgramLinkDTO>`

### 7.3 创建程式表链接

创建一个新的程式表链接。

- **URL**: `/api/ProgramLinks`
- **方法**: `POST`
- **请求体**: `CreateProgramLinkDTO`
- **响应**: 
  -状态码: 201 Created 或 400 Bad Request
  - 内容类型: `application/json`
  - 响应体: `ProgramLinkDTO`

### 7.4 删除程式表链接

删除指定ID的程式表链接。

- **URL**: `/api/ProgramLinks/{id}`
- **方法**: `DELETE`
- **路径参数**:
  - `id`: 程式表链接ID
- **响应**: 
  -状态码: 204 No Content 或 404 Not Found

## 数据模型

### DeviceDto

```json
{
  "id": 1,
  "name": "设备1",
  "description": "这是设备1的描述",
  "deviceType": "Modbus",
  "connectionString": "COM1:9600",
  "status": "Online",
  "createdAt": "2023-01-01T00:00:00Z",
  "lastUpdatedAt": "2023-01-02T00:00:00Z"
}
```

### DataPointDto

```json
{
  "id": 1,
  "testId": 5,
  "timestamp": "2023-01-01T12:00:00Z",
  "values": [
    {
      "protocolItemIndex": 1,
      "value": 23.5
    }
  ]
}
```

### AlarmDTO

```json
{
  "id": 1,
  "testId": 5,
  "name": "温度过高",
  "message": "设备温度超过阈值",
  "level": "Warning",
  "status": "Active",
  "processedBy": null,
  "timestamp": "2023-01-01T12:00:00Z",
  "lastUpdated": "2023-01-01T12:00:00Z",
  "processedAt": null
}
```

### TestRunDTO

```json
{
  "id": 1,
  "deviceId": 2,
  "programId": 3,
  "name": "温度测试",
  "status": "Running",
  "startTime": "2023-01-01T12:00:00Z",
  "endTime": null,
  "parameters": {
    "duration": 3600,
    "maxTemperature": 100
  }
}
```

### ProgramDTO

```json
{
  "id": 1,
  "name": "标准温度测试",
  "description": "用于测试设备在标准温度下的性能",
  "version": "1.0",
  "createdAt": "2023-01-01T00:00:00Z",
  "lastUpdatedAt": "2023-01-02T00:00:00Z",
  "steps": [
    {
      "id": 1,
      "name": "预热",
      "description": "设备预热阶段",
      "duration": 300,
      "parameters": {
        "temperature": 50
      }
    }
  ]
}
```

### ProgramLinkDTO

```json
{
  "id": 1,
  "programId": 3,
  "deviceId": 2,
  "isDefault": true,
  "parameters": {
    "customParam1": "value1"
  }
}
```

## 错误响应

当API发生错误时，将返回相应的HTTP状态码和错误信息。

### 错误响应示例

```json
{
  "message": "获取设备列表时发生错误",
  "details": "数据库连接失败"
}
```

## 状态码说明

- **200 OK**: 请求成功
- **201 Created**: 资源创建成功
- **204 No Content**: 请求成功，无返回内容
- **400 Bad Request**: 请求参数错误
- **404 Not Found**: 请求的资源不存在
- **500 Internal Server Error**: 服务器内部错误
