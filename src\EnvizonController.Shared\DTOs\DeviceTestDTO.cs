using System.ComponentModel.DataAnnotations;

namespace EnvizonController.Shared.DTOs
{
    /// <summary>
    /// 设备测试启动请求
    /// </summary>
    public class DeviceTestStartRequest
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public long DeviceId { get; set; }

        /// <summary>
        /// 测试名称
        /// </summary>
        [Required]
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// 测试描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 执行类型（Manual, Program, ProgramLink）
        /// </summary>
        public string ExecutionType { get; set; } = "Manual";

        /// <summary>
        /// 执行ID（程式ID或程式链接ID）
        /// </summary>
        public long? ExecutionId { get; set; }

        /// <summary>
        /// 额外配置参数
        /// </summary>
        public Dictionary<string, object>? ExtraConfig { get; set; }
    }

    /// <summary>
    /// 设备测试启动结果
    /// </summary>
    public class DeviceTestStartResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 测试运行信息
        /// </summary>
        public TestRunDTO? TestRun { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据采集是否启动
        /// </summary>
        public bool CollectionStarted { get; set; }
    }

    /// <summary>
    /// 设备测试停止结果
    /// </summary>
    public class DeviceTestStopResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 测试运行信息
        /// </summary>
        public TestRunDTO? TestRun { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据采集是否停止
        /// </summary>
        public bool CollectionStopped { get; set; }
    }

    /// <summary>
    /// 设备测试状态
    /// </summary>
    public class DeviceTestStatus
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public long DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// 设备是否连接
        /// </summary>
        public bool IsDeviceConnected { get; set; }

        /// <summary>
        /// 测试状态
        /// </summary>
        public string TestStatus { get; set; } = string.Empty;

        /// <summary>
        /// 当前测试
        /// </summary>
        public TestRunDTO? CurrentTest { get; set; }

        /// <summary>
        /// 是否正在采集数据
        /// </summary>
        public bool IsDataCollecting { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量设备测试启动请求
    /// </summary>
    public class BatchDeviceTestStartRequest
    {
        /// <summary>
        /// 测试启动请求列表
        /// </summary>
        public List<DeviceTestStartRequest> Requests { get; set; } = new();
    }

    /// <summary>
    /// 批量设备测试启动结果
    /// </summary>
    public class BatchDeviceTestStartResult
    {
        /// <summary>
        /// 各个设备的启动结果
        /// </summary>
        public List<DeviceTestStartResult> Results { get; set; } = new();

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailCount { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }


    /// <summary>
    /// 设备测试暂停结果
    /// </summary>
    public class DeviceTestPauseResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 测试运行信息
        /// </summary>
        public TestRunDTO? TestRun { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 设备测试恢复结果
    /// </summary>
    public class DeviceTestResumeResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 测试运行信息
        /// </summary>
        public TestRunDTO? TestRun { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

}