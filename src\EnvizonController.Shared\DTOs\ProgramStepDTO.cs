using System;

namespace EnvizonController.Shared.DTOs;

/// <summary>
///     程式步骤数据传输对象
/// </summary>
public class ProgramStepDTO
{
    /// <summary>
    ///     步骤ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     所属程式表ID
    /// </summary>
    public long ProgramId { get; set; }

    /// <summary>
    ///     湿度（百分比）
    /// </summary>
    public double Humidity { get; set; }

    /// <summary>
    ///     温度（摄氏度）
    /// </summary>
    public double Temperature { get; set; }

    /// <summary>
    ///     是否线性变化
    /// </summary>
    public bool IsLinear { get; set; }

    /// <summary>
    ///     持续时间（秒）
    /// </summary>
    public int Duration { get; set; }

    /// <summary>
    ///     排序索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}