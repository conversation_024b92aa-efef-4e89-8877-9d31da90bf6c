﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Modbus.Abstractions\EnvizonController.Modbus.Abstractions.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Adapters.Desktop\EnvizonController.Modbus.Adapters.Desktop.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Client\EnvizonController.Modbus.Client.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Protocol\EnvizonController.Modbus.Protocol.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
  </ItemGroup>

</Project>
