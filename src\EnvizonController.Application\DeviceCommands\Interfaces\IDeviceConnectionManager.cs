using EnvizonController.Application.Devices;
using EnvizonController.Application.DeviceCommands.Models;

namespace EnvizonController.Application.DeviceCommands.Interfaces
{
    /// <summary>
    /// 设备连接管理器接口
    /// </summary>
    public interface IDeviceConnectionManager : IDisposable
    {
        /// <summary>
        /// 获取设备的Modbus服务
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>Modbus设备服务</returns>
        Task<IModbusDeviceService> GetDeviceServiceAsync(long deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 确保设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否连接成功</returns>
        Task<bool> EnsureConnectionAsync(long deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否断开成功</returns>
        Task<bool> DisconnectDeviceAsync(long deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取连接池状态
        /// </summary>
        /// <returns>连接池状态</returns>
        Task<ConnectionPoolStatus> GetConnectionPoolStatusAsync();
        
        /// <summary>
        /// 清理空闲连接
        /// </summary>
        /// <param name="idleTimeout">空闲超时时间</param>
        /// <returns>清理的连接数</returns>
        Task<int> CleanupIdleConnectionsAsync(TimeSpan idleTimeout);
        
        /// <summary>
        /// 检查设备是否在线
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>是否在线</returns>
        Task<bool> IsDeviceOnlineAsync(long deviceId);
        
        /// <summary>
        /// 获取设备连接信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接信息</returns>
        Task<ConnectionInfo?> GetConnectionInfoAsync(long deviceId);
    }
} 