using Avalonia.Data.Converters;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警级别转换为对应的字符串
    /// </summary>
    public class LevelToStringConverter : IValueConverter
    {
        /// <summary>
        /// 将报警级别转换为对应的字符串
        /// </summary>
        /// <param name="value">报警级别</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>对应的字符串表示</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not AlarmSeverity level) return "未知级别";

            return level switch
            {
                AlarmSeverity.High => "高",
                AlarmSeverity.Medium => "中",
                AlarmSeverity.Low => "低",
                _ => "未知级别"
            };
        }

        /// <summary>
        /// 将字符串转换回报警级别（不支持）
        /// </summary>
        /// <param name="value">字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 