﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;

// 需要引入 Avalonia 命名空间以访问 WindowStateProperty

namespace EnvizonController.Presentation.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            var fontFamilies = FontManager.Current.SystemFonts;
            // (可选) 监听窗口状态变化，用于更新最大化/还原按钮的显示
            this.PropertyChanged += MainWindow_PropertyChanged;
        }

        // 标题栏鼠标按下事件处理，用于拖动窗口
        private void TitleBar_PointerPressed(object? sender, PointerPressedEventArgs e)
        {
            // 检查是否是鼠标左键按下
            if (e.GetCurrentPoint(this).Properties.IsLeftButtonPressed)
            {
                // 开始拖动窗口
                this.BeginMoveDrag(e);
            }
        }

        // 最小化按钮点击事件
        private void MinimizeButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        // 最大化/还原按钮点击事件
        private void MaximizeButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (this.WindowState == WindowState.Normal)
            {
                this.WindowState = WindowState.Maximized;
            }
            else // 如果是 Maximized 或 Minimized (虽然最小化时按钮通常不可见)
            {
                this.WindowState = WindowState.Normal;
            }
        }

        // 关闭按钮点击事件
        private void CloseButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            this.Close();
        }

        // (可选) 处理窗口状态变化事件
        private void MainWindow_PropertyChanged(object? sender, AvaloniaPropertyChangedEventArgs e)
        {
            // 检查是否是 WindowState 属性发生了变化
            if (e.Property == WindowStateProperty)
            {
                // 更新最大化/还原按钮的内容或样式
                var maximizeButton = this.FindControl<Button>("MaximizeButton");
                if (maximizeButton != null)
                {
                    if (this.WindowState == WindowState.Maximized)
                    {
                        maximizeButton.Content = "❐"; // 或者使用图标，表示“还原”
                        // (可选) 最大化时可能需要移除或调整 RootBorder 的边框/圆角
                        var rootBorder = this.FindControl<Border>("RootBorder");
                        if (rootBorder != null)
                        {
                            rootBorder.CornerRadius = new CornerRadius(0);
                            // 根据需要调整 Margin 或 Padding
                        }
                    }
                    else // WindowState.Normal
                    {
                        maximizeButton.Content = "口"; // 表示“最大化”
                                                      // (可选) 恢复正常状态的边框/圆角
                        var rootBorder = this.FindControl<Border>("RootBorder");
                        if (rootBorder != null)
                        {
                            rootBorder.CornerRadius = new CornerRadius(8); // 恢复圆角
                            // 恢复 Margin 或 Padding
                        }
                    }
                }
            }
        }
    }
}