using System;
using System.Collections.Generic;

namespace EnvizonController.Shared.DTOs
{
    /// <summary>
    /// 程式链接数据传输对象
    /// </summary>
    public class ProgramLinkDTO
    {
        /// <summary>
        /// 程式链接ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 程式链接名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 循环次数
        /// </summary>
        public int CycleCount { get; set; } = 1;

        /// <summary>
        /// 程式链接项集合
        /// </summary>
        public List<ProgramLinkStepDTO> Items { get; set; } = new List<ProgramLinkStepDTO>();

        /// <summary>
        /// 链接项数量
        /// </summary>
        public int ItemCount => Items.Count;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
}