using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写单个寄存器响应
    /// </summary>
    public class WriteSingleRegisterResponse : ModbusResponse
    {
        /// <summary>
        /// 寄存器地址
        /// </summary>
        public ushort Address { get; private set; }

        /// <summary>
        /// 寄存器值
        /// </summary>
        public ushort Value { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public WriteSingleRegisterResponse()
        {
            FunctionCode = ModbusFunction.WriteSingleRegister;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 6)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            Address = (ushort)((frame[2] << 8) | frame[3]);
            Value = (ushort)((frame[4] << 8) | frame[5]);
        }
    }
}
