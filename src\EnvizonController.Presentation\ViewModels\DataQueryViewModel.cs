using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.Shared.DTOs;
using Serilog;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using EnvizonController.ApiClient.Services;
using System.Collections.Generic;
using System.Linq;
using Avalonia.Threading;
using EnvizonController.Presentation.Extensions;
using EnvizonController.Presentation.ViewModels.Program;
using HanumanInstitute.MvvmDialogs;
using HanumanInstitute.MvvmDialogs.Avalonia;
using EnvizonController.ApiClient.Results;
using EnvizonController.Presentation.ViewModels.DataQuery;
using EnvizonController.Presentation.Controls.Models;

namespace EnvizonController.Presentation.ViewModels
{
    public partial class DataQueryViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 视图索引
        /// </summary>
        [ObservableProperty]
        private int _selectedViewIndex;

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string _testDataSearchText = string.Empty;

        /// <summary>
        /// 是否显示筛选面板
        /// </summary>
        [ObservableProperty]
        private bool _isTestDataFilterPanelOpen;
        /// <summary>
        /// 开始日期
        /// </summary>
        [ObservableProperty]
        private DateTime? _startDate;

        /// <summary>
        /// 结束日期
        /// </summary>
        [ObservableProperty]
        private DateTime? _endDate;

        /// <summary>
        /// 选中的状态
        /// </summary>
        [ObservableProperty]
        private string _selectedStatus;

        /// <summary>
        /// 状态选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _statusOptions = new ObservableCollection<string>
        {
            "全部",
            "正常",
            "警告",
            "错误"
        };


        /// <summary>
        /// 数据点集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DataPointDto> _dataPoints = new ObservableCollection<DataPointDto>();

        /// <summary>
        /// 分页信息
        /// </summary>
        [ObservableProperty]
        private PaginationInfo _paginationInfo = new PaginationInfo
        {
            CurrentPage = 1,
            PageSize = 10,
            TotalCount = 0
        };

        /// <summary>
        /// 页码变更命令
        /// </summary>
        public IRelayCommand<PaginationInfo> PageChangedCommand => new RelayCommand<PaginationInfo>(OnPageChanged);

        #region 测试项相关

        /// <summary>
        /// 是否显示测试项筛选面板
        /// </summary>
        [ObservableProperty]
        private bool _isTestItemFilterPanelOpen;
        /// <summary>
        /// 测试项搜索文本
        /// </summary>
        [ObservableProperty]
        private string _testItemSearchText = string.Empty;

        /// <summary>
        /// 测试项名称精确筛选
        /// </summary>
        [ObservableProperty]
        private string _testItemNameFilter = string.Empty;

        /// <summary>
        /// 测试项开始日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _testItemStartDate;

        /// <summary>
        /// 测试项结束日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _testItemEndDate;

        /// <summary>
        /// 是否有活动的测试项筛选条件
        /// </summary>
        [ObservableProperty]
        private bool _hasActiveTestItemFilters;

        /// <summary>
        /// 所有测试项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TestItemViewModel> _testItems = new();
        
        /// <summary>
        /// 当前选中的测试项
        /// </summary>
        [ObservableProperty]
        private TestItemViewModel? _selectedTestItem;

        /// <summary>
        /// 当前选中测试项的显示文本
        /// </summary>
        public string SelectedTestItemDisplay => SelectedTestItem != null ?
            $"测试项: {SelectedTestItem.Name}" : "全部测试项";
        #endregion

        /// <summary>
        /// 测试步骤集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TestStepItemViewModel> _testSteps = new();

        /// <summary>
        /// 当前选中的测试步骤
        /// </summary>
        [ObservableProperty]
        private TestStepItemViewModel? _selectedTestStep;

        [ObservableProperty] private bool _isLoading;
        #endregion
        
        private readonly ITestItemApiService _testItemApiService;
        private readonly IProgramApiService _programApiService;
        private readonly IDialogService _dialogService;
        private readonly IDataPointApiService _dataPointApiService;
        #region 构造函数

        public DataQueryViewModel(ITestItemApiService testItemApiService, IProgramApiService programApiService, IDialogService dialogService, IDataPointApiService dataPointApiService)
        {
            _testItemApiService = testItemApiService;
            _programApiService = programApiService;
            _dialogService = dialogService;
            _dataPointApiService = dataPointApiService;
            // 初始化测试数据
            Task.Run(async () => await InitializeTestData());
        }

        #endregion

        #region 命令

        /// <summary>
        /// 搜索命令
        /// </summary>
        [RelayCommand]
        private async Task Search()
        {
            // 模拟搜索操作
            await Task.Delay(500);
            // 重新加载数据
            LoadData();
        }

        /// <summary>
        /// 切换筛选面板显示状态
        /// </summary>
        [RelayCommand]
        private void ToggleTestItemFilterPanel()
        {
            IsTestItemFilterPanelOpen = !IsTestItemFilterPanelOpen;
        }
        [RelayCommand]
        private void ToggleTestDataFilterPanel()
        {
            IsTestDataFilterPanelOpen = !IsTestDataFilterPanelOpen;
        }
        
        /// <summary>
        /// 应用筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ApplyTestDataFilters()
        {
            // 模拟筛选操作
            await Task.Delay(500);
            // 重新加载数据
            await LoadData();
            // 关闭筛选面板
            IsTestDataFilterPanelOpen = false;
        }

        
   
        
        /// <summary>
        /// 页码变更处理方法
        /// </summary>
        private void OnPageChanged(PaginationInfo? pageInfo)
        {
            if (pageInfo != null)
            {
                _ = LoadData();
            }
        }

        /// <summary>
        /// 应用测试项筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ApplyTestItemFilters()
        {
            HasActiveTestItemFilters = !string.IsNullOrEmpty(TestItemNameFilter) ||
                                       TestItemStartDate.HasValue ||
                                       TestItemEndDate.HasValue;

            await LoadTestItemsAsync();
        }
        
        /// <summary>
        /// 刷新测试步骤命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshTestSteps()
        {
            IsLoading = true;
            try
            {
                await LoadTestStepsAsync();
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "加载程式步骤异常");
                await _dialogService.ShowErrorAsync("错误", $"加载程式步骤异常: {ex.Message}");
            }
            finally
            {

                IsLoading = false;
            }
        }
        
        /// <summary>
        /// 保存测试步骤报告命令
        /// </summary>
        [RelayCommand]
        private async Task SaveTestStepsReport()
        {
            IsLoading = true;
            try
            {
                // 模拟保存报告操作
                await Task.Delay(1000);
                Log.Information("已导出测试步骤报告");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        /// <summary>
        /// 重新运行测试命令
        /// </summary>
        [RelayCommand]
        private async Task RerunTests()
        {
            IsLoading = true;
            try
            {
                // 模拟重新运行测试
                await Task.Delay(2000);
                await LoadTestStepsAsync();
                Log.Information("已重新运行测试");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private async Task LoadData()
        {
            try
            {
                ObservableCollection<DataPointDto> dataPoints = new ObservableCollection<DataPointDto>();

                // 如果有选中的测试项，则获取该测试项的数据点
                if (SelectedTestItem != null)
                {
                    var result = await _dataPointApiService.GetTestRunDataPointsAsync(
                        SelectedTestItem.Id,
                        PaginationInfo.CurrentPage,
                        PaginationInfo.PageSize);

                    if (result is { IsSuccess: true, Data: not null })
                    {
                        foreach (var dataPoint in result.Data.Items)
                        {
                            dataPoints.Add(dataPoint);
                        }

                        // 更新分页信息
                        PaginationInfo.TotalCount = result.Data.TotalCount;
                    }
                    else
                    {
                        Log.Error("获取数据点失败: {Message}", result.ErrorMessage);
                        await _dialogService.ShowErrorAsync("错误", $"获取数据点失败: {result.ErrorMessage}");
                    }
                }

                await Dispatcher.UIThread.InvokeAsync(() => { DataPoints = dataPoints; });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "加载数据点异常");
                await _dialogService.ShowErrorAsync("错误", $"加载数据点异常: {ex.Message}");
            }
        }


        private async Task LoadTestStepsAsync()
        {
            TestSteps.Clear();
            if(SelectedTestItem == null)
                return;
            var result = await _testItemApiService.GetTestItemAsync(SelectedTestItem.Id);

            if (result.IsSuccess && result.Data.CalculateExecutionDetails())
            {
            
                foreach (var stepDto in result.Data.TestSteps) TestSteps.Add(TestStepItemViewModel.FromDto(stepDto));

                // 排序
                var sortedSteps = TestSteps.OrderBy(s => s.Index).ToList();
                TestSteps.Clear();
                foreach (var step in sortedSteps) TestSteps.Add(step);
            }
            else
            {
                await _dialogService.ShowErrorAsync("错误", $"加载测试项「{SelectedTestItem.Name}」的程式步骤失败: {result.ErrorMessage}");

            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        private async Task InitializeTestData()
        {
            IsLoading = true;
            try
            {
                await LoadTestItemsAsync();
                await LoadData();
                await LoadTestStepsAsync(); // 加载测试步骤数据
            }
            finally
            {
                IsLoading = false;
            }
        }

        partial void OnSelectedTestItemChanged(TestItemViewModel? testItemViewModel)
        {
            AfterSelectedTestItemChanged();
        }

        private async void AfterSelectedTestItemChanged()
        {
            try
            {
                IsLoading = true; await LoadData();
                await LoadTestStepsAsync(); // 加载测试步骤数据
                   
            }
            catch (Exception ex)
            {
               Log.Logger.Error(ex,"加载测试项数据异常");

               await _dialogService.ShowErrorAsync("错误", $"加载测试项数据异常: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 加载测试项数据
        /// </summary>
        private async Task LoadTestItemsAsync()
        {// 构建查询参数
            var queryParams = new TestItemQueryParams();

            // 应用名称搜索
            if (!string.IsNullOrEmpty(TestItemSearchText))
            {
                queryParams.SearchText = TestItemSearchText;
            }

            // 应用名称精确筛选
            if (HasActiveTestItemFilters && !string.IsNullOrEmpty(TestItemNameFilter))
            {
                queryParams.Name = TestItemNameFilter;
            }

            // 应用开始日期筛选
            if (HasActiveTestItemFilters && TestItemStartDate.HasValue)
            {
                queryParams.StartDate = TestItemStartDate.Value;
            }

            // 应用结束日期筛选
            if (HasActiveTestItemFilters && TestItemEndDate.HasValue)
            {
                queryParams.EndDate = TestItemEndDate.Value;
            }

            // 设置页码和每页大小
            queryParams.Page = 1;
            queryParams.PageSize = 100;

            // 调用API获取筛选后的测试项
            var result = await _testItemApiService.GetTestItemsAsync(queryParams);

            if (!result.IsSuccess)
            {
                Log.Error("获取测试项失败: {Message}", result.ErrorMessage);
                return;
            }

            var testItems = new List<TestItemViewModel>();

            // 将API返回的测试项转换为视图模型
            foreach (var item in result.Data.Items)
            {
                testItems.Add(new TestItemViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    ExecutionTime = item.CreatedAt,
                    // 此处可以添加活动报警计数的逻辑，例如从报警服务获取
                    ActiveAlarmCount = 0 // 暂时设为0，后续可以通过接口获取
                });
            }

            TestItems = new ObservableCollection<TestItemViewModel>(testItems);

            // 默认选择全部测试项
            if (SelectedTestItem == null)
            {
                SelectedTestItem = TestItems.FirstOrDefault();
            }
        }

        /// <summary>
        /// 清除测试项筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ClearTestItemFilters()
        {
            // 清除所有筛选条件
            TestItemSearchText = string.Empty;
            TestItemNameFilter = string.Empty;
            TestItemStartDate = null;
            TestItemEndDate = null;
            HasActiveTestItemFilters = false;

            // 重新加载数据
            await LoadTestItemsAsync();
        }

        #endregion
    }

    /// <summary>
    /// 数据类别
    /// </summary>
    public class DataCategory
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}