using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为选中/非选中状态的前景色
    /// </summary>
    public class BoolToSelectedForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected 
                    ? new SolidColorBrush(Color.Parse("#0DF0FF")) // 选中状态的前景色（青色）
                    : new SolidColorBrush(Color.Parse("#FFFFFF")); // 非选中状态的前景色（白色）
            }

            return new SolidColorBrush(Color.Parse("#FFFFFF"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}