using System.Collections.ObjectModel;
using System.Windows.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Styling;
using EnvizonController.Presentation.Controls.Models;

namespace EnvizonController.Presentation.Controls
{
    /// <summary>
    /// 分页控件尺寸枚举
    /// </summary>
    public enum PaginationSize
    {
        /// <summary>
        /// 默认尺寸
        /// </summary>
        Default,
        /// <summary>
        /// 小尺寸
        /// </summary>
        Small
    }
    
    public class PaginationControl : TemplatedControl
    {
        // 依赖属性定义
        public static readonly StyledProperty<PaginationInfo> PaginationInfoProperty =
            AvaloniaProperty.Register<PaginationControl, PaginationInfo>(nameof(PaginationInfo));

        public static readonly StyledProperty<ICommand?> PageChangedCommandProperty =
            AvaloniaProperty.Register<PaginationControl, ICommand?>(nameof(PageChangedCommand));

        public static readonly StyledProperty<ICommand?> PageSizeChangedCommandProperty =
            AvaloniaProperty.Register<PaginationControl, ICommand?>(nameof(PageSizeChangedCommand));

        public static readonly StyledProperty<bool> ShowPageSizeSelectorProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowPageSizeSelector), true);

        public static readonly StyledProperty<bool> ShowPageInfoProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowPageInfo), true);

        public static readonly StyledProperty<bool> ShowQuickJumpProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowQuickJump), true);

        public static readonly StyledProperty<int> MaxVisiblePagesProperty =
            AvaloniaProperty.Register<PaginationControl, int>(nameof(MaxVisiblePages), 7);

        public static readonly StyledProperty<ObservableCollection<int>> PageSizeOptionsProperty =
            AvaloniaProperty.Register<PaginationControl, ObservableCollection<int>>(nameof(PageSizeOptions));

        public static readonly StyledProperty<bool> ShowFirstLastButtonsProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowFirstLastButtons), true);

        public static readonly StyledProperty<bool> ShowNumericButtonsProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowNumericButtons), true);

        public static readonly StyledProperty<bool> ShowEllipsisProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowEllipsis), true);

        public static readonly StyledProperty<bool> ShowCurrentPageInfoProperty =
            AvaloniaProperty.Register<PaginationControl, bool>(nameof(ShowCurrentPageInfo), true);

        public static readonly StyledProperty<PaginationSize> SizeProperty =
            AvaloniaProperty.Register<PaginationControl, PaginationSize>(nameof(Size), PaginationSize.Default);

        // 内部UI元素
        private Button? _firstPageButton;
        private Button? _prevPageButton;
        private Button? _nextPageButton;
        private Button? _lastPageButton;
        private StackPanel? _pageNumbersPanel;
        private ComboBox? _pageSizeComboBox;
        private TextBox? _jumpPageTextBox;
        private Button? _jumpButton;

        static PaginationControl()
        {
            PaginationInfoProperty.Changed.AddClassHandler<PaginationControl>((x, e) => x.OnPaginationInfoChanged(e));
        }

        public PaginationControl()
        {
            PageSizeOptions = new ObservableCollection<int> { 10, 20, 50, 100 };
            PaginationInfo = new PaginationInfo();
        }

        // 公共属性
        public PaginationInfo PaginationInfo
        {
            get => GetValue(PaginationInfoProperty);
            set => SetValue(PaginationInfoProperty, value);
        }

        public ICommand? PageChangedCommand
        {
            get => GetValue(PageChangedCommandProperty);
            set => SetValue(PageChangedCommandProperty, value);
        }

        public ICommand? PageSizeChangedCommand
        {
            get => GetValue(PageSizeChangedCommandProperty);
            set => SetValue(PageSizeChangedCommandProperty, value);
        }

        public bool ShowPageSizeSelector
        {
            get => GetValue(ShowPageSizeSelectorProperty);
            set => SetValue(ShowPageSizeSelectorProperty, value);
        }

        public bool ShowPageInfo
        {
            get => GetValue(ShowPageInfoProperty);
            set => SetValue(ShowPageInfoProperty, value);
        }

        public bool ShowQuickJump
        {
            get => GetValue(ShowQuickJumpProperty);
            set => SetValue(ShowQuickJumpProperty, value);
        }

        public int MaxVisiblePages
        {
            get => GetValue(MaxVisiblePagesProperty);
            set => SetValue(MaxVisiblePagesProperty, value);
        }

        public ObservableCollection<int> PageSizeOptions
        {
            get => GetValue(PageSizeOptionsProperty);
            set => SetValue(PageSizeOptionsProperty, value);
        }

        public bool ShowFirstLastButtons
        {
            get => GetValue(ShowFirstLastButtonsProperty);
            set => SetValue(ShowFirstLastButtonsProperty, value);
        }

        public bool ShowNumericButtons
        {
            get => GetValue(ShowNumericButtonsProperty);
            set => SetValue(ShowNumericButtonsProperty, value);
        }

        public bool ShowEllipsis
        {
            get => GetValue(ShowEllipsisProperty);
            set => SetValue(ShowEllipsisProperty, value);
        }

        public bool ShowCurrentPageInfo
        {
            get => GetValue(ShowCurrentPageInfoProperty);
            set => SetValue(ShowCurrentPageInfoProperty, value);
        }

        public PaginationSize Size
        {
            get => GetValue(SizeProperty);
            set => SetValue(SizeProperty, value);
        }

        protected override void OnApplyTemplate(TemplateAppliedEventArgs e)
        {
            base.OnApplyTemplate(e);

            // 获取模板中的UI元素
            _firstPageButton = e.NameScope.Find<Button>("PART_FirstPageButton");
            _prevPageButton = e.NameScope.Find<Button>("PART_PrevPageButton");
            _nextPageButton = e.NameScope.Find<Button>("PART_NextPageButton");
            _lastPageButton = e.NameScope.Find<Button>("PART_LastPageButton");
            _pageNumbersPanel = e.NameScope.Find<StackPanel>("PART_PageNumbersPanel");
            _pageSizeComboBox = e.NameScope.Find<ComboBox>("PART_PageSizeComboBox");
            _jumpPageTextBox = e.NameScope.Find<TextBox>("PART_JumpPageTextBox");
            _jumpButton = e.NameScope.Find<Button>("PART_JumpButton");

            // 绑定事件
            if (_firstPageButton != null)
                _firstPageButton.Click += (s, e) => NavigateToPage(1);

            if (_prevPageButton != null)
                _prevPageButton.Click += (s, e) => NavigateToPage(PaginationInfo.CurrentPage - 1);

            if (_nextPageButton != null)
                _nextPageButton.Click += (s, e) => NavigateToPage(PaginationInfo.CurrentPage + 1);

            if (_lastPageButton != null)
                _lastPageButton.Click += (s, e) => NavigateToPage(PaginationInfo.TotalPages);

            if (_pageSizeComboBox != null)
                _pageSizeComboBox.SelectionChanged += OnPageSizeChanged;

            if (_jumpButton != null)
                _jumpButton.Click += OnJumpButtonClick;

            if (_jumpPageTextBox != null)
                _jumpPageTextBox.KeyDown += OnJumpTextBoxKeyDown;

            UpdateUI();
        }

        private void OnPaginationInfoChanged(AvaloniaPropertyChangedEventArgs e)
        {
            if (e.OldValue is PaginationInfo oldInfo)
            {
                oldInfo.PropertyChanged -= OnPaginationInfoPropertyChanged;
            }

            if (e.NewValue is PaginationInfo newInfo)
            {
                newInfo.PropertyChanged += OnPaginationInfoPropertyChanged;
            }

            UpdateUI();
        }

        private void OnPaginationInfoPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            UpdateUI();
        }

        private void UpdateUI()
        {
            if (PaginationInfo == null) return;

            UpdateNavigationButtons();
            UpdatePageNumbers();
            UpdatePageSizeSelector();
            UpdateVisibility();
        }

        private void UpdateVisibility()
        {
            if (_firstPageButton != null)
                _firstPageButton.IsVisible = ShowFirstLastButtons;

            if (_lastPageButton != null)
                _lastPageButton.IsVisible = ShowFirstLastButtons;

            if (_pageNumbersPanel != null)
                _pageNumbersPanel.IsVisible = ShowNumericButtons;

            if (_jumpPageTextBox != null && _jumpButton != null)
            {
                var jumpVisible = ShowQuickJump && PaginationInfo.TotalPages > 1;
                _jumpPageTextBox.IsVisible = jumpVisible;
                _jumpButton.IsVisible = jumpVisible;
            }
        }

        private void UpdateNavigationButtons()
        {
            if (_firstPageButton != null)
                _firstPageButton.IsEnabled = PaginationInfo.HasPrevious;

            if (_prevPageButton != null)
                _prevPageButton.IsEnabled = PaginationInfo.HasPrevious;

            if (_nextPageButton != null)
                _nextPageButton.IsEnabled = PaginationInfo.HasNext;

            if (_lastPageButton != null)
                _lastPageButton.IsEnabled = PaginationInfo.HasNext;
        }

        private void UpdatePageNumbers()
        {
            if (_pageNumbersPanel == null || !ShowNumericButtons) return;

            _pageNumbersPanel.Children.Clear();

            var (startPage, endPage, showStartEllipsis, showEndEllipsis) = CalculateVisiblePageRangeWithEllipsis();

            // 添加起始省略号
            if (showStartEllipsis && ShowEllipsis)
            {
                AddEllipsisButton(true);
            }

            // 添加页码按钮
            for (int i = startPage; i <= endPage; i++)
            {
                AddPageButton(i);
            }

            // 添加结束省略号
            if (showEndEllipsis && ShowEllipsis)
            {
                AddEllipsisButton(false);
            }
        }

        private void AddPageButton(int pageNumber)
        {
            // 根据Size属性选择合适的主题和尺寸
            var themeKey = Size == PaginationSize.Small ? "CyberPaginationPageButtonSmallTheme" : "CyberPaginationPageButtonTheme";
            var minWidth = Size == PaginationSize.Small ? 24 : 35;
            var margin = Size == PaginationSize.Small ? new Thickness(1) : new Thickness(2);

            var pageButton = new Button
            {
                Content = pageNumber.ToString(),
                Tag = pageNumber,
                Margin = margin,
                MinWidth = minWidth,
                Theme = (ControlTheme)this.FindResource(themeKey)!
            };

            // 设置当前页样式
            if (pageNumber == PaginationInfo.CurrentPage)
            {
                pageButton.Classes.Add("current");
            }

            pageButton.Click += (s, e) =>
            {
                if (s is Button button && button.Tag is int page)
                {
                    NavigateToPage(page);
                }
            };

            _pageNumbersPanel?.Children.Add(pageButton);
        }

        private void AddEllipsisButton(bool isStart)
        {
            // 根据Size属性选择合适的主题和尺寸
            var themeKey = Size == PaginationSize.Small ? "CyberPaginationPageButtonSmallTheme" : "CyberPaginationPageButtonTheme";
            var minWidth = Size == PaginationSize.Small ? 24 : 35;
            var margin = Size == PaginationSize.Small ? new Thickness(1) : new Thickness(2);

            var ellipsisButton = new Button
            {
                Content = "...",
                Margin = margin,
                MinWidth = minWidth,
                Theme = (ControlTheme)this.FindResource(themeKey)!
            };

            ellipsisButton.Click += (s, e) =>
            {
                // 点击起始省略号跳转到前一组页码，点击结束省略号跳转到后一组页码
                int targetPage = isStart
                    ? Math.Max(1, PaginationInfo.CurrentPage - MaxVisiblePages)
                    : Math.Min(PaginationInfo.TotalPages, PaginationInfo.CurrentPage + MaxVisiblePages);
                NavigateToPage(targetPage);
            };

            _pageNumbersPanel?.Children.Add(ellipsisButton);
        }

        private (int startPage, int endPage, bool showStartEllipsis, bool showEndEllipsis) CalculateVisiblePageRangeWithEllipsis()
        {
            int totalPages = PaginationInfo.TotalPages;
            int currentPage = PaginationInfo.CurrentPage;
            
            // 如果总页数小于等于最大可见页数，则显示所有页码
            if (totalPages <= MaxVisiblePages)
            {
                return (1, totalPages, false, false);
            }

            // 确保当前页在可见范围内
            int maxVisibleBeforeAfter = (MaxVisiblePages - 1) / 2;
            
            bool showStartEllipsis = currentPage > maxVisibleBeforeAfter + 1;
            bool showEndEllipsis = currentPage < totalPages - maxVisibleBeforeAfter;

            int startPage, endPage;

            // 调整可见页码范围
            if (showStartEllipsis && showEndEllipsis)
            {
                // 当前页在中间，两侧都显示省略号
                startPage = currentPage - maxVisibleBeforeAfter;
                endPage = currentPage + maxVisibleBeforeAfter;
            }
            else if (showStartEllipsis)
            {
                // 当前页靠近末尾，只显示开始省略号
                startPage = totalPages - MaxVisiblePages + 1;
                endPage = totalPages;
            }
            else
            {
                // 当前页靠近开始，只显示结束省略号
                startPage = 1;
                endPage = MaxVisiblePages;
            }

            return (startPage, endPage, showStartEllipsis, showEndEllipsis);
        }

        private void UpdatePageSizeSelector()
        {
            if (_pageSizeComboBox != null && !PageSizeOptions.Contains(PaginationInfo.PageSize))
            {
                _pageSizeComboBox.SelectedItem = PaginationInfo.PageSize;
            }
        }

        private (int startPage, int endPage) CalculateVisiblePageRange()
        {
            int totalPages = PaginationInfo.TotalPages;
            int currentPage = PaginationInfo.CurrentPage;
            int maxVisible = Math.Min(MaxVisiblePages, totalPages);

            int startPage = Math.Max(1, currentPage - maxVisible / 2);
            int endPage = Math.Min(totalPages, startPage + maxVisible - 1);

            if (endPage - startPage + 1 < maxVisible)
            {
                startPage = Math.Max(1, endPage - maxVisible + 1);
            }

            return (startPage, endPage);
        }

        private void NavigateToPage(int page)
        {
            if (page < 1 || page > PaginationInfo.TotalPages || page == PaginationInfo.CurrentPage)
                return;

            var oldPage = PaginationInfo.CurrentPage;
            PaginationInfo.CurrentPage = page;

            // 执行命令
            if (PageChangedCommand?.CanExecute(PaginationInfo) == true)
            {
                PageChangedCommand.Execute(PaginationInfo);
            }

            // 触发事件
            var args = new PageChangedEventArgs(page, oldPage);
            OnPageChanged(args);
        }

        private void OnPageSizeChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (_pageSizeComboBox?.SelectedItem is int newPageSize && newPageSize != PaginationInfo.PageSize)
            {
                var oldPageSize = PaginationInfo.PageSize;
                PaginationInfo.PageSize = newPageSize;
                PaginationInfo.CurrentPage = 1; // 重置到第一页

                if (PageSizeChangedCommand?.CanExecute(PaginationInfo) == true)
                {
                    PageSizeChangedCommand.Execute(PaginationInfo);
                }

                var args = new PageSizeChangedEventArgs(newPageSize, oldPageSize);
                OnPageSizeChanged(args);
            }
        }

        private void OnJumpButtonClick(object? sender, RoutedEventArgs e)
        {
            JumpToPage();
        }

        private void OnJumpTextBoxKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                JumpToPage();
            }
        }

        private void JumpToPage()
        {
            if (_jumpPageTextBox != null && int.TryParse(_jumpPageTextBox.Text, out int page))
            {
                NavigateToPage(page);
                _jumpPageTextBox.Text = string.Empty;
            }
        }

        // 事件定义
        public event EventHandler<PageChangedEventArgs>? PageChanged;
        public event EventHandler<PageSizeChangedEventArgs>? PageSizeChanged;

        protected virtual void OnPageChanged(PageChangedEventArgs e)
        {
            PageChanged?.Invoke(this, e);
        }

        protected virtual void OnPageSizeChanged(PageSizeChangedEventArgs e)
        {
            PageSizeChanged?.Invoke(this, e);
        }
    }

    // 事件参数类
    public class PageChangedEventArgs : EventArgs
    {
        public int NewPage { get; }
        public int OldPage { get; }

        public PageChangedEventArgs(int newPage, int oldPage)
        {
            NewPage = newPage;
            OldPage = oldPage;
        }
    }

    public class PageSizeChangedEventArgs : EventArgs
    {
        public int NewPageSize { get; }
        public int OldPageSize { get; }

        public PageSizeChangedEventArgs(int newPageSize, int oldPageSize)
        {
            NewPageSize = newPageSize;
            OldPageSize = oldPageSize;
        }
    }
}
