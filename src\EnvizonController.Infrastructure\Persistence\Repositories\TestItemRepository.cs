using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 测试项仓储实现
    /// </summary>
    public class TestItemRepository : Repository<TestRun, long>, ITestItemRepository
    {

        public TestItemRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 获取分页的测试项列表（支持多参数查询）
        /// </summary>
        public async Task<(IEnumerable<TestRun> TestItems, int TotalCount)> GetPagedTestItemsAsync(int page, int pageSize, string searchText = null, string name = null, string status = null,
            DateTime? startDate = null, DateTime? endDate = null, long? deviceId = null)
        {
            // 构建基础查询
            IQueryable<TestRun> query = _dbSet;

            // 应用所有过滤条件
            
            // 如果提供了搜索文本，应用到名称和描述
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                query = query.Where(t => t.Name.Contains(searchText) ||
                                       (t.Description != null && t.Description.Contains(searchText)));
            }

            // 如果提供了名称，精确筛选名称
            if (!string.IsNullOrWhiteSpace(name))
            {
                query = query.Where(t => t.Name.Contains(name));
            }

            // 如果提供了状态，精确筛选状态
            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(t => t.Status == status);
            }

            // 如果提供了开始日期，筛选在该日期之后开始的测试项
            if (startDate.HasValue)
            {
                query = query.Where(t => t.StartTime >= startDate.Value);
            }

            // 如果提供了结束日期，筛选在该日期之前开始的测试项
            if (endDate.HasValue)
            {
                query = query.Where(t => t.StartTime <= endDate.Value);
            }

            // 如果提供了设备ID，筛选特定设备的测试项
            if (deviceId.HasValue)
            {
                query = query.Where(t => t.DeviceId == deviceId.Value);
            }

            // 计算总条数
            int totalCount = await query.CountAsync();

            // 应用分页，并按开始时间倒序排列（最新的先显示）
            var pagedItems = await query
                .OrderByDescending(t => t.StartTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 返回测试项列表和总条数
            return (pagedItems, totalCount);
        }

        /// <summary>
        /// 根据名称查找测试项
        /// </summary>
        public async Task<IEnumerable<TestRun>> FindByNameAsync(string name)
        {
            return await _dbSet
                .Where(t => t.Name.Contains(name))
                .ToListAsync();
        }

        /// <summary>
        /// 根据设备ID获取测试项
        /// </summary>
        public async Task<IEnumerable<TestRun>> GetByDeviceIdAsync(long deviceId)
        {
            return await _dbSet
                .Where(t => t.DeviceId == deviceId)
                .OrderByDescending(t => t.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取设备的最新运行中测试项
        /// </summary>
        public async Task<TestRun?> GetLatestRunningTestItemByDeviceIdAsync(long deviceId)
        {
            return await _dbSet
                .Where(t => t.DeviceId == deviceId && t.Status == "Running")
                .OrderByDescending(t => t.StartTime)
                .FirstOrDefaultAsync();
        }
        
     
    }
}
