using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 测试项API服务实现
    /// </summary>
    public class TestItemApiService : ITestItemApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public TestItemApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 获取所有测试项
        /// </summary>
        public async Task<Result<PagedResultDto<TestRunDTO>>> GetTestItemsAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<TestRunDTO>>("api/testitems", queryParams);
        }

        
        /// <summary>
        /// 获取所有测试项(带查询参数对象)
        /// </summary>
        public async Task<Result<PagedResultDto<TestRunDTO>>> GetTestItemsAsync(TestItemQueryParams queryParams)
        {
            // 将查询参数对象转换为字典
            var paramsDictionary = new Dictionary<string, string>();
            
            // 覆盖分页参数
            paramsDictionary["page"] = queryParams.Page.ToString();
            paramsDictionary["pageSize"] = queryParams.PageSize.ToString();
            
            // 添加其他查询参数
            if (!string.IsNullOrEmpty(queryParams.SearchText))
            {
                paramsDictionary["searchText"] = queryParams.SearchText;
            }
            
            if (!string.IsNullOrEmpty(queryParams.Name))
            {
                paramsDictionary["name"] = queryParams.Name;
            }
            
            if (queryParams.StartDate.HasValue)
            {
                paramsDictionary["startDate"] = queryParams.StartDate.Value.ToString("yyyy-MM-dd");
            }
            
            if (queryParams.EndDate.HasValue)
            {
                paramsDictionary["endDate"] = queryParams.EndDate.Value.ToString("yyyy-MM-dd");
            }
            
            if (queryParams.DeviceId.HasValue)
            {
                paramsDictionary["deviceId"] = queryParams.DeviceId.Value.ToString();
            }
            
            if (!string.IsNullOrEmpty(queryParams.Status))
            {
                paramsDictionary["status"] = queryParams.Status;
            }
            
            return await _httpClient.GetAsync<PagedResultDto<TestRunDTO>>("api/testitems", paramsDictionary);
        }

        /// <summary>
        /// 根据ID获取测试项
        /// </summary>
        public async Task<Result<TestRunDTO>> GetTestItemAsync(long id)
        {
            return await _httpClient.GetAsync<TestRunDTO>($"api/Testitems/{id}");
        }

        /// <summary>
        /// 创建测试项
        /// </summary>
        public async Task<Result<TestRunDTO>> CreateTestItemAsync(TestRunDTO testRun)
        {
            return await _httpClient.PostAsync<TestRunDTO, TestRunDTO>("api/testitems", testRun);
        }

        /// <summary>
        /// 更新测试项
        /// </summary>
        public async Task<Result<TestRunDTO>> UpdateTestItemAsync(long id, TestRunDTO testRun)
        {
            return await _httpClient.PutAsync<TestRunDTO, TestRunDTO>($"api/testitems/{id}", testRun);
        }

        /// <summary>
        /// 删除测试项
        /// </summary>
        public async Task<Result<bool>> DeleteTestItemAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/testitems/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 启动测试
        /// </summary>
        public async Task<Result<TestRunDTO>> StartTestAsync(long id)
        {
            return await _httpClient.PostAsync<TestRunDTO, object>($"api/testitems/{id}/start", null);
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        public async Task<Result<TestRunDTO>> StopTestAsync(long id)
        {
            return await _httpClient.PostAsync<TestRunDTO, object>($"api/testitems/{id}/stop", null);
        }

      
        /// <summary>
        /// 删除测试步骤
        /// </summary>
        public async Task<Result<bool>> RemoveTestStepAsync(long testItemId, int stepNumber)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/testitems/{testItemId}/steps/{stepNumber}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 清空测试步骤
        /// </summary>
        public async Task<Result<bool>> ClearTestStepsAsync(long testItemId)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/testitems/{testItemId}/steps");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }
    }
} 