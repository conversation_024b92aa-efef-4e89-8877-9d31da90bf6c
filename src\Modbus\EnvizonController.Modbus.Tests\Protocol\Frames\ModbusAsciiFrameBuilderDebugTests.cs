using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;
using System.Text;
using Xunit;
using Xunit.Abstractions;

namespace EnvizonController.Modbus.Tests.Protocol.Frames
{
    public class ModbusAsciiFrameBuilderDebugTests
    {
        private readonly ModbusAsciiFrameBuilder _frameBuilder;
        private readonly ITestOutputHelper _output;
        
        public ModbusAsciiFrameBuilderDebugTests(ITestOutputHelper output)
        {
            _frameBuilder = new ModbusAsciiFrameBuilder();
            _output = output;
        }
        
        [Fact]
        public void DebugBuildRequestFrame_WithReadHoldingRegistersRequest()
        {
            // Arrange
            var request = new ReadHoldingRegistersRequest(1, 0, 10);
            
            // Act
            byte[] frame = _frameBuilder.BuildRequestFrame(request);
            
            // 获取原始帧
            byte[] rawFrame = request.GetFrame();
            _output.WriteLine($"原始帧长度: {rawFrame.Length}");
            _output.WriteLine($"原始帧内容: {BitConverter.ToString(rawFrame).Replace("-", " ")}");
            
            // 添加LRC后的帧
            byte[] frameWithLrc = ModbusLrc.AppendLrc(rawFrame);
            _output.WriteLine($"添加LRC后帧长度: {frameWithLrc.Length}");
            _output.WriteLine($"添加LRC后帧内容: {BitConverter.ToString(frameWithLrc).Replace("-", " ")}");
            
            // 转换为ASCII字符串
            string asciiFrame = ModbusLrc.BytesToAscii(frameWithLrc);
            _output.WriteLine($"ASCII字符串长度: {asciiFrame.Length}");
            _output.WriteLine($"ASCII字符串内容: {asciiFrame}");
            
            // 完整的ASCII帧
            string completeFrame = ":" + asciiFrame + "\r\n";
            _output.WriteLine($"完整ASCII帧长度: {completeFrame.Length}");
            _output.WriteLine($"完整ASCII帧内容: {completeFrame}");
            
            // 最终的字节数组
            _output.WriteLine($"最终帧长度: {frame.Length}");
            _output.WriteLine($"最终帧内容: {BitConverter.ToString(frame).Replace("-", " ")}");
            
            // 解码为字符串
            string decodedFrame = Encoding.ASCII.GetString(frame);
            _output.WriteLine($"解码后的帧: {decodedFrame}");
            
            // 验证内容
            string content = decodedFrame.Substring(1, decodedFrame.Length - 3);
            _output.WriteLine($"内容部分: {content}");
            _output.WriteLine($"期望内容: 0103000000000AF2");
            
            // 检查每个字符的ASCII码
            _output.WriteLine("内容部分的ASCII码:");
            for (int i = 0; i < content.Length; i++)
            {
                _output.WriteLine($"位置 {i}: '{content[i]}' (ASCII: {(int)content[i]})");
            }
            
            // 检查期望内容的ASCII码
            string expected = "0103000000000AF2";
            _output.WriteLine("期望内容的ASCII码:");
            for (int i = 0; i < expected.Length; i++)
            {
                _output.WriteLine($"位置 {i}: '{expected[i]}' (ASCII: {(int)expected[i]})");
            }
        }
    }
}
