using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.DataPush;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 数据处理服务接口
    /// </summary>
    public interface IDataProcessingService
    {
        /// <summary>
        /// 获取所有注册的管道
        /// </summary>
        IReadOnlyDictionary<string, IDataProcessingPipeline> Pipelines { get; }
        
        /// <summary>
        /// 注册管道
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <param name="pipeline">处理管道</param>
        void RegisterPipeline(string name, IDataProcessingPipeline pipeline);
        
        /// <summary>
        /// 注销管道
        /// </summary>
        /// <param name="name">管道名称</param>
        void UnregisterPipeline(string name);
        
        /// <summary>
        /// 获取管道
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <returns>处理管道</returns>
        IDataProcessingPipeline GetPipeline(string name);
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="rawData">原始数据</param>
        /// <param name="metadata">元数据</param>
        /// <returns>处理结果</returns>
        Task<IDataProcessingContext> ProcessDataAsync(string pipelineName, object rawData, Dictionary<string, object> metadata = null);
        
        /// <summary>
        /// 处理数据并推送
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="rawData">原始数据</param>
        /// <param name="topic">推送主题</param>
        /// <param name="messageType">消息类型</param>
        /// <param name="metadata">元数据</param>
        /// <returns>处理结果</returns>
        Task<IDataProcessingContext> ProcessAndPushAsync(string pipelineName, object rawData, string topic, MessageType messageType, Dictionary<string, object> metadata = null);
    }
}
