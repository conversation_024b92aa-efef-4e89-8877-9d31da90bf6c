using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 协议仓储实现
    /// </summary>
    public class ProtocolRepository : Repository<Protocol, long>, IProtocolRepository
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ProtocolRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 根据名称获取协议
        /// </summary>
        public async Task<Protocol?> GetByNameAsync(string name)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Name == name);
        }
    }
}
