using EnvizonController.Modbus.Protocol.Utils;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Utils
{
    public class ModbusLrcTests
    {
        [Fact]
        public void CalculateLrc_WithValidData_ReturnsCorrectLrc()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act
            byte lrc = ModbusLrc.CalculateLrc(data);
            
            // Assert
            Assert.Equal(0xF2, lrc);
        }
        
        [Fact]
        public void AppendLrc_WithValidData_ReturnsByteArrayWithLrc()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act
            byte[] result = ModbusLrc.AppendLrc(data);
            
            // Assert
            Assert.Equal(7, result.Length);
            Assert.Equal(data, result.Take(data.Length).ToArray());
            Assert.Equal(0xF2, result[6]);
        }
        
        [Fact]
        public void ValidateLrc_WithValidLrc_ReturnsTrue()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            byte[] dataWithLrc = ModbusLrc.AppendLrc(data);
            
            // Act
            bool isValid = ModbusLrc.ValidateLrc(dataWithLrc);
            
            // Assert
            Assert.True(isValid);
        }
        
        [Fact]
        public void ValidateLrc_WithInvalidLrc_ReturnsFalse()
        {
            // Arrange
            byte[] dataWithInvalidLrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A, 0x00 };
            
            // Act
            bool isValid = ModbusLrc.ValidateLrc(dataWithInvalidLrc);
            
            // Assert
            Assert.False(isValid);
        }
        
        [Fact]
        public void ValidateLrc_WithTooShortData_ReturnsFalse()
        {
            // Arrange
            byte[] tooShortData = new byte[] { };
            
            // Act
            bool isValid = ModbusLrc.ValidateLrc(tooShortData);
            
            // Assert
            Assert.False(isValid);
        }
        
        [Fact]
        public void ValidateLrc_WithNullData_ReturnsFalse()
        {
            // Act
            bool isValid = ModbusLrc.ValidateLrc(null);
            
            // Assert
            Assert.False(isValid);
        }
        
        [Fact]
        public void BytesToAscii_WithValidData_ReturnsCorrectAsciiString()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x0A };
            
            // Act
            string result = ModbusLrc.BytesToAscii(data);
            
            // Assert
            Assert.Equal("01030A", result);
        }
        
        [Fact]
        public void AsciiToBytes_WithValidAsciiString_ReturnsCorrectBytes()
        {
            // Arrange
            string ascii = "01030A";
            
            // Act
            byte[] result = ModbusLrc.AsciiToBytes(ascii);
            
            // Assert
            Assert.Equal(new byte[] { 0x01, 0x03, 0x0A }, result);
        }
    }
}
