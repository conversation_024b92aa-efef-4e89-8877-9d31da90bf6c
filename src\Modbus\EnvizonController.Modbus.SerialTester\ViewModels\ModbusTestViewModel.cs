using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Protocol.Enums;
using System.Collections.ObjectModel;
using System.Text;

namespace EnvizonController.Modbus.SerialTester.ViewModels
{
    /// <summary>
    /// Modbus测试视图模型
    /// </summary>
    public partial class ModbusTestViewModel : ViewModelBase
    {
        private IModbusClient? _modbusClient;
        
        [ObservableProperty] private MainViewModel _mainViewModel = null!;

        // 读取操作相关属性
        [ObservableProperty] private ObservableCollection<string> _readFunctions = new()
        {
            "读取线圈状态 (0x01)",
            "读取离散输入状态 (0x02)",
            "读取保持寄存器 (0x03)",
            "读取输入寄存器 (0x04)"
        };
        [ObservableProperty] private string _selectedReadFunction = "读取保持寄存器 (0x03)";
        [ObservableProperty] private ushort _readStartAddress = 0;
        [ObservableProperty] private ushort _readCount = 10;
        [ObservableProperty] private string _readResult = string.Empty;
        [ObservableProperty] private bool _isHexDisplay = true;
        
        // 字节顺序相关属性
        [ObservableProperty] private ObservableCollection<string> _byteOrders = new()
        {
            "大端序 (BigEndian)",
            "小端序 (LittleEndian)"
        };
        [ObservableProperty] private string _selectedByteOrder = "大端序 (BigEndian)";
        [ObservableProperty] private bool _useExtensions = false;

        // 写入操作相关属性
        [ObservableProperty] private ObservableCollection<string> _writeFunctions = new()
        {
            "写单个线圈 (0x05)",
            "写单个寄存器 (0x06)",
            "写多个线圈 (0x0F)",
            "写多个寄存器 (0x10)"
        };
        [ObservableProperty] private string _selectedWriteFunction = "写单个寄存器 (0x06)";
        [ObservableProperty] private ushort _writeAddress = 0;
        [ObservableProperty] private string _writeValues = "0";
        [ObservableProperty] private string _writeResult = string.Empty;

        // 自定义命令相关属性
        [ObservableProperty] private byte _customFunctionCode = 0x03;
        [ObservableProperty] private string _customData = "00 00 00 0A";
        [ObservableProperty] private string _customResult = string.Empty;

        // 连接状态
        [ObservableProperty] private bool _isConnected = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ModbusTestViewModel()
        {
        }

        /// <summary>
        /// 连接成功时调用
        /// </summary>
        public void OnConnected(IModbusClient modbusClient)
        {
            _modbusClient = modbusClient;
            IsConnected = true;
        }

        /// <summary>
        /// 断开连接时调用
        /// </summary>
        public void OnDisconnected()
        {
            _modbusClient = null;
            IsConnected = false;
        }

        /// <summary>
        /// 执行读取操作
        /// </summary>
        [RelayCommand]
        private async Task ExecuteRead()
        {
            if (_modbusClient == null || !_modbusClient.IsConnected)
            {
                ReadResult = "未连接到设备";
                return;
            }

            try
            {
                MainViewModel.StatusMessage = "正在读取数据...";
                MainViewModel.LogViewModel.AddLog("INFO", $"读取操作: {SelectedReadFunction}, 起始地址: {ReadStartAddress}, 数量: {ReadCount}");

                switch (SelectedReadFunction)
                {
                    case "读取线圈状态 (0x01)":
                        await ReadCoils();
                        break;
                    case "读取离散输入状态 (0x02)":
                        await ReadDiscreteInputs();
                        break;
                    case "读取保持寄存器 (0x03)":
                        await ReadHoldingRegisters();
                        break;
                    case "读取输入寄存器 (0x04)":
                        await ReadInputRegisters();
                        break;
                }

                MainViewModel.StatusMessage = "读取完成";
            }
            catch (Exception ex)
            {
                ReadResult = $"读取失败: {ex.Message}";
                MainViewModel.StatusMessage = $"读取失败: {ex.Message}";
                MainViewModel.LogViewModel.AddLog("ERROR", $"读取失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 读取线圈状态
        /// </summary>
        private async Task ReadCoils()
        {
            bool[] values = await _modbusClient!.ReadCoilsAsync(
                MainViewModel.SlaveAddress,
                ReadStartAddress,
                ReadCount);

            // 格式化结果
            ReadResult = FormatBooleanValues(values);
            MainViewModel.LogViewModel.AddLog("INFO", $"读取线圈状态成功, 结果: {ReadResult}");
        }

        /// <summary>
        /// 读取离散输入状态
        /// </summary>
        private async Task ReadDiscreteInputs()
        {
            bool[] values = await _modbusClient!.ReadDiscreteInputsAsync(
                MainViewModel.SlaveAddress,
                ReadStartAddress,
                ReadCount);

            // 格式化结果
            ReadResult = FormatBooleanValues(values);
            MainViewModel.LogViewModel.AddLog("INFO", $"读取离散输入状态成功, 结果: {ReadResult}");
        }

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        private async Task ReadHoldingRegisters()
        {
            ushort[] values = await _modbusClient!.ReadHoldingRegistersAsync(
                MainViewModel.SlaveAddress,
                ReadStartAddress,
                ReadCount);

            // 格式化结果
            ReadResult = FormatRegisterValues(values);
            
            // 使用扩展功能时，记录浮点数值
            if (UseExtensions && values.Length >= 2)
            {
                bool isBigEndian = SelectedByteOrder.Contains("BigEndian");
                StringBuilder floatValues = new StringBuilder();
                
                for (int i = 0; i < values.Length - 1; i += 2)
                {
                    float floatValue = ConvertRegistersToFloat(values[i], values[i + 1], isBigEndian);
                    floatValues.Append($"[{ReadStartAddress + i}-{ReadStartAddress + i + 1}]: {floatValue} ");
                }
                
                MainViewModel.LogViewModel.AddLog("INFO", $"读取保持寄存器成功, 浮点值: {floatValues}");
            }
            else
            {
                MainViewModel.LogViewModel.AddLog("INFO", $"读取保持寄存器成功");
            }
        }

        /// <summary>
        /// 读取输入寄存器
        /// </summary>
        private async Task ReadInputRegisters()
        {
            ushort[] values = await _modbusClient!.ReadInputRegistersAsync(
                MainViewModel.SlaveAddress,
                ReadStartAddress,
                ReadCount);

            // 格式化结果
            ReadResult = FormatRegisterValues(values);
            
            // 使用扩展功能时，记录浮点数值
            if (UseExtensions && values.Length >= 2)
            {
                bool isBigEndian = SelectedByteOrder.Contains("BigEndian");
                StringBuilder floatValues = new StringBuilder();
                
                for (int i = 0; i < values.Length - 1; i += 2)
                {
                    float floatValue = ConvertRegistersToFloat(values[i], values[i + 1], isBigEndian);
                    floatValues.Append($"[{ReadStartAddress + i}-{ReadStartAddress + i + 1}]: {floatValue} ");
                }
                
                MainViewModel.LogViewModel.AddLog("INFO", $"读取输入寄存器成功, 浮点值: {floatValues}");
            }
            else
            {
                MainViewModel.LogViewModel.AddLog("INFO", $"读取输入寄存器成功");
            }
        }

        /// <summary>
        /// 执行写入操作
        /// </summary>
        [RelayCommand]
        private async Task ExecuteWrite()
        {
            if (_modbusClient == null || !_modbusClient.IsConnected)
            {
                WriteResult = "未连接到设备";
                return;
            }

            try
            {
                MainViewModel.StatusMessage = "正在写入数据...";
                MainViewModel.LogViewModel.AddLog("INFO", $"写入操作: {SelectedWriteFunction}, 地址: {WriteAddress}, 值: {WriteValues}");

                bool success = false;

                switch (SelectedWriteFunction)
                {
                    case "写单个线圈 (0x05)":
                        success = await WriteSingleCoil();
                        break;
                    case "写单个寄存器 (0x06)":
                        success = await WriteSingleRegister();
                        break;
                    case "写多个线圈 (0x0F)":
                        success = await WriteMultipleCoils();
                        break;
                    case "写多个寄存器 (0x10)":
                        success = await WriteMultipleRegisters();
                        break;
                }

                WriteResult = success ? "写入成功" : "写入失败";
                MainViewModel.StatusMessage = success ? "写入成功" : "写入失败";
                MainViewModel.LogViewModel.AddLog(success ? "INFO" : "ERROR", $"写入{(success ? "成功" : "失败")}");
            }
            catch (Exception ex)
            {
                WriteResult = $"写入失败: {ex.Message}";
                MainViewModel.StatusMessage = $"写入失败: {ex.Message}";
                MainViewModel.LogViewModel.AddLog("ERROR", $"写入失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 写单个线圈
        /// </summary>
        private async Task<bool> WriteSingleCoil()
        {
            bool value = WriteValues.Trim().ToLower() == "1" || WriteValues.Trim().ToLower() == "true";
            return await _modbusClient!.WriteSingleCoilAsync(
                MainViewModel.SlaveAddress,
                WriteAddress,
                value);
        }

        /// <summary>
        /// 写单个寄存器
        /// </summary>
        private async Task<bool> WriteSingleRegister()
        {
            // 检查是否使用扩展功能并尝试解析浮点数
            if (UseExtensions && float.TryParse(WriteValues.Trim(), out float floatValue))
            {
                bool isBigEndian = SelectedByteOrder.Contains("BigEndian");
                var (register1, register2) = ConvertFloatToRegisters(floatValue, isBigEndian);
                
                // 写入两个寄存器
                bool success = await _modbusClient!.WriteMultipleRegistersAsync(
                    MainViewModel.SlaveAddress,
                    WriteAddress,
                    new ushort[] { register1, register2 });
                
                // 记录浮点数写入日志
                if (success)
                {
                    MainViewModel.LogViewModel.AddLog("INFO", $"写入浮点数成功: {floatValue} (寄存器值: 0x{register1:X4} 0x{register2:X4})");
                }
                
                return success;
            }
            else
            {
                // 常规写入单个寄存器
                if (!ushort.TryParse(WriteValues.Trim(), out ushort value))
                {
                    throw new ArgumentException("无效的寄存器值");
                }

                bool success = await _modbusClient!.WriteSingleRegisterAsync(
                    MainViewModel.SlaveAddress,
                    WriteAddress,
                    value);
                    
                return success;
            }
        }

        /// <summary>
        /// 写多个线圈
        /// </summary>
        private async Task<bool> WriteMultipleCoils()
        {
            string[] valueStrings = WriteValues.Split(new[] { ' ', ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
            bool[] values = new bool[valueStrings.Length];

            for (int i = 0; i < valueStrings.Length; i++)
            {
                values[i] = valueStrings[i].Trim().ToLower() == "1" || valueStrings[i].Trim().ToLower() == "true";
            }

            return await _modbusClient!.WriteMultipleCoilsAsync(
                MainViewModel.SlaveAddress,
                WriteAddress,
                values);
        }

        /// <summary>
        /// 写多个寄存器
        /// </summary>
        private async Task<bool> WriteMultipleRegisters()
        {
            string[] valueStrings = WriteValues.Split(new[] { ' ', ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
            List<ushort> valuesList = new List<ushort>();
            bool isBigEndian = SelectedByteOrder.Contains("BigEndian");
            
            // 使用扩展功能时，尝试解析浮点数
            if (UseExtensions)
            {
                for (int i = 0; i < valueStrings.Length; i++)
                {
                    // 尝试解析为浮点数
                    if (float.TryParse(valueStrings[i].Trim(), out float floatValue))
                    {
                        var (register1, register2) = ConvertFloatToRegisters(floatValue, isBigEndian);
                        valuesList.Add(register1);
                        valuesList.Add(register2);
                        
                        // 记录浮点数转换日志
                        MainViewModel.LogViewModel.AddLog("INFO", $"浮点数 {floatValue} 转换为寄存器值: 0x{register1:X4} 0x{register2:X4}");
                    }
                    else
                    {
                        // 尝试解析为普通整数
                        if (ushort.TryParse(valueStrings[i].Trim(), out ushort value))
                        {
                            valuesList.Add(value);
                        }
                        else
                        {
                            throw new ArgumentException($"无效的寄存器值: {valueStrings[i]}");
                        }
                    }
                }
            }
            else
            {
                // 不使用扩展功能时，按原来的方式解析
                for (int i = 0; i < valueStrings.Length; i++)
                {
                    if (!ushort.TryParse(valueStrings[i].Trim(), out ushort value))
                    {
                        throw new ArgumentException($"无效的寄存器值: {valueStrings[i]}");
                    }
                    valuesList.Add(value);
                }
            }

            bool success = await _modbusClient!.WriteMultipleRegistersAsync(
                MainViewModel.SlaveAddress,
                WriteAddress,
                valuesList.ToArray());
                
            if (success && UseExtensions)
            {
                MainViewModel.LogViewModel.AddLog("INFO", $"写入多个寄存器成功，包含浮点数转换");
            }
                
            return success;
        }

        /// <summary>
        /// 执行自定义命令
        /// </summary>
        [RelayCommand]
        private async Task ExecuteCustomCommand()
        {
            if (_modbusClient == null || !_modbusClient.IsConnected)
            {
                CustomResult = "未连接到设备";
                return;
            }

            try
            {
                MainViewModel.StatusMessage = "正在执行自定义命令...";
                MainViewModel.LogViewModel.AddLog("INFO", $"自定义命令: 功能码: 0x{CustomFunctionCode:X2}, 数据: {CustomData}");

                // 解析自定义数据
                string[] dataStrings = CustomData.Split(new[] { ' ', ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                byte[] data = new byte[dataStrings.Length];

                for (int i = 0; i < dataStrings.Length; i++)
                {
                    string hexValue = dataStrings[i].Trim();
                    if (hexValue.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                    {
                        hexValue = hexValue.Substring(2);
                    }

                    if (!byte.TryParse(hexValue, System.Globalization.NumberStyles.HexNumber, null, out data[i]))
                    {
                        throw new ArgumentException($"无效的十六进制值: {dataStrings[i]}");
                    }
                }

                // 创建自定义请求
                var request = new CustomModbusRequest(MainViewModel.SlaveAddress, (ModbusFunction)CustomFunctionCode, data);
                var response = new CustomModbusResponse();

                // 发送请求
                bool success = await _modbusClient.SendRequestAsync(request, response);

                if (success)
                {
                    if (response.IsException)
                    {
                        CustomResult = $"异常响应: 异常码: 0x{response.ExceptionCode:X2}";
                        MainViewModel.LogViewModel.AddLog("WARNING", $"收到异常响应: 异常码: 0x{response.ExceptionCode:X2}");
                    }
                    else
                    {
                        // 格式化响应数据
                        string responseData = BitConverter.ToString(response.Data).Replace("-", " ");
                        CustomResult = $"响应数据: {responseData}";
                        MainViewModel.LogViewModel.AddLog("INFO", $"自定义命令执行成功, 响应数据: {responseData}");
                    }
                }
                else
                {
                    CustomResult = "命令执行失败";
                    MainViewModel.LogViewModel.AddLog("ERROR", "自定义命令执行失败");
                }

                MainViewModel.StatusMessage = "自定义命令执行完成";
            }
            catch (Exception ex)
            {
                CustomResult = $"命令执行失败: {ex.Message}";
                MainViewModel.StatusMessage = $"命令执行失败: {ex.Message}";
                MainViewModel.LogViewModel.AddLog("ERROR", $"自定义命令执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化布尔值数组
        /// </summary>
        private string FormatBooleanValues(bool[] values)
        {
            if (values == null || values.Length == 0)
            {
                return "无数据";
            }

            var result = new System.Text.StringBuilder();
            for (int i = 0; i < values.Length; i++)
            {
                result.AppendLine($"[{ReadStartAddress + i}]: {(values[i] ? "1" : "0")}");
            }

            return result.ToString();
        }

        /// <summary>
        /// 格式化寄存器值数组
        /// </summary>
        private string FormatRegisterValues(ushort[] values)
        {
            if (values == null || values.Length == 0)
            {
                return "无数据";
            }

            var result = new System.Text.StringBuilder();
            
            // 使用扩展功能时，尝试将寄存器值转换为浮点数
            if (UseExtensions && values.Length >= 2)
            {
                bool isBigEndian = SelectedByteOrder.Contains("BigEndian");
                
                for (int i = 0; i < values.Length; i += 2)
                {
                    if (i + 1 < values.Length)
                    {
                        // 将两个寄存器值转换为浮点数
                        float floatValue = ConvertRegistersToFloat(values[i], values[i + 1], isBigEndian);
                        
                        if (IsHexDisplay)
                        {
                            result.AppendLine($"[{ReadStartAddress + i}-{ReadStartAddress + i + 1}]: 0x{values[i]:X4} 0x{values[i + 1]:X4} (浮点值: {floatValue})");
                        }
                        else
                        {
                            result.AppendLine($"[{ReadStartAddress + i}-{ReadStartAddress + i + 1}]: {values[i]} {values[i + 1]} (浮点值: {floatValue})");
                        }
                    }
                    else
                    {
                        // 处理奇数个寄存器的情况
                        if (IsHexDisplay)
                        {
                            result.AppendLine($"[{ReadStartAddress + i}]: 0x{values[i]:X4} ({values[i]})");
                        }
                        else
                        {
                            result.AppendLine($"[{ReadStartAddress + i}]: {values[i]}");
                        }
                    }
                }
            }
            else
            {
                // 不使用扩展功能时，按原来的方式显示
                for (int i = 0; i < values.Length; i++)
                {
                    if (IsHexDisplay)
                    {
                        result.AppendLine($"[{ReadStartAddress + i}]: 0x{values[i]:X4} ({values[i]})");
                    }
                    else
                    {
                        result.AppendLine($"[{ReadStartAddress + i}]: {values[i]}");
                    }
                }
            }

            return result.ToString();
        }
        
        /// <summary>
        /// 将两个寄存器值转换为浮点数
        /// </summary>
        private float ConvertRegistersToFloat(ushort register1, ushort register2, bool isBigEndian)
        {
            byte[] bytes = new byte[4];
            
            if (isBigEndian)
            {
                // 大端序：高位在前，低位在后
                bytes[0] = (byte)(register1 >> 8);    // 高字节
                bytes[1] = (byte)(register1 & 0xFF);  // 低字节
                bytes[2] = (byte)(register2 >> 8);    // 高字节
                bytes[3] = (byte)(register2 & 0xFF);  // 低字节
            }
            else
            {
                // 小端序：低位在前，高位在后
                bytes[0] = (byte)(register2 & 0xFF);  // 低字节
                bytes[1] = (byte)(register2 >> 8);    // 高字节
                bytes[2] = (byte)(register1 & 0xFF);  // 低字节
                bytes[3] = (byte)(register1 >> 8);    // 高字节
            }
            
            return BitConverter.ToSingle(bytes, 0);
        }
        
        /// <summary>
        /// 将浮点数转换为两个寄存器值
        /// </summary>
        private (ushort, ushort) ConvertFloatToRegisters(float value, bool isBigEndian)
        {
            byte[] bytes = BitConverter.GetBytes(value);
            ushort register1, register2;
            
            if (isBigEndian)
            {
                // 大端序：高位在前，低位在后
                register1 = (ushort)((bytes[0] << 8) | bytes[1]);
                register2 = (ushort)((bytes[2] << 8) | bytes[3]);
            }
            else
            {
                // 小端序：低位在前，高位在后
                register2 = (ushort)((bytes[1] << 8) | bytes[0]);
                register1 = (ushort)((bytes[3] << 8) | bytes[2]);
            }
            
            return (register1, register2);
        }
    }

    /// <summary>
    /// 自定义Modbus请求
    /// </summary>
    public class CustomModbusRequest : EnvizonController.Modbus.Protocol.Models.ModbusRequest
    {
        private readonly byte[] _data;

        public CustomModbusRequest(byte slaveAddress, ModbusFunction functionCode, byte[] data)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = functionCode;
            _data = data;
        }

        public override byte[] GetData()
        {
            return _data;
        }
    }

    /// <summary>
    /// 自定义Modbus响应
    /// </summary>
    public class CustomModbusResponse : EnvizonController.Modbus.Protocol.Models.ModbusResponse
    {
        public byte[] Data { get; private set; } = Array.Empty<byte>();

        //public override bool ParseData(byte[] data)
        //{
        //    if (data == null || data.Length == 0)
        //    {
        //        return false;
        //    }

        //    Data = data;
        //    return true;
        //}
    }
}
