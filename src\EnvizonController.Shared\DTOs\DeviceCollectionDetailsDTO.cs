﻿using System.Text.Json;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Shared.DTOs;


public class DeviceCollectionDetailsDTO
{
    public long? TestId { get; set; }
    public long DeviceId { get; set; }
    public Dictionary<string, Dictionary<string, object>> EnvironmentalData { get; set; } = new();
    public Dictionary<string, Dictionary<string, object>> DeviceStateData { get; set; } = new();

    public List<ItemErrorInfo> CollectionErrors { get; set; } = new();
    public List<AlarmDTO> Alarms { get; set; } = new();
    
    /// <summary>
    /// 设备运行状态
    /// </summary>
    public DeviceOperatingStatus OperatingStatus { get; set; } = DeviceOperatingStatus.Unknown;
    
    /// <summary>
    /// 当前程式步骤
    /// </summary>
    public int CurrentProgramStep { get; set; } = 0;
    
    /// <summary>
    /// 当前程式步骤是否完成
    /// </summary>
    public bool IsCurrentProgramStepCompleted { get; set; } = false;
}

public class DeviceCollectionDetailsReceivedDTO
{
    public long? TestId { get; set; }
    public long DeviceId { get; set; }
    public Dictionary<string, Dictionary<string, JsonElement>> EnvironmentalData { get; set; } = new();
    public Dictionary<string, Dictionary<string, JsonElement>> DeviceStateData { get; set; } = new();
    public Dictionary<string, Dictionary<string, JsonElement>> AlarmData { get; set; } = new();

    public List<ItemErrorInfo> CollectionErrors { get; set; } = new();
    public List<AlarmDTO> Alarms { get; set; } = new();
    
    /// <summary>
    /// 设备运行状态
    /// </summary>
    public DeviceOperatingStatus OperatingStatus { get; set; } = DeviceOperatingStatus.Unknown;
    
    /// <summary>
    /// 当前程式步骤
    /// </summary>
    public int CurrentProgramStep { get; set; } = 0;
    
    /// <summary>
    /// 当前程式步骤是否完成
    /// </summary>
    public bool IsCurrentProgramStepCompleted { get; set; } = false;
}
