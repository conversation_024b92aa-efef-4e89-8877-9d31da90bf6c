﻿// JsonElementHelper.cs (New File)

using System.Text.Json;

namespace EnvizonController.Presentation.Helpers;

public static class JsonElementHelper
{
    // Helper to get a string property from a JsonElement (which is an object)
    public static bool TryGetStringProperty(this JsonElement element, string propertyName, out string? value)
    {
        value = null;
        if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty(propertyName, out var propertyElement))
        {
            if (propertyElement.ValueKind == JsonValueKind.String)
            {
                value = propertyElement.GetString();
                return true;
            }
            // Handle cases where the value might be a number but needs to be a string
            if (propertyElement.ValueKind == JsonValueKind.Number)
            {
                value = propertyElement.GetRawText(); // Or .ToString()
                return true;
            }
        }
        return false;
    }

    public static string GetStringPropertyOrDefault(this JsonElement element, string propertyName, string defaultValue = "")
    {
        return TryGetStringProperty(element, propertyName, out var value) ? value ?? defaultValue : defaultValue;
    }

    // Helper to get a double property
    public static bool TryGetDoubleProperty(this JsonElement element, string propertyName, out double value)
    {
        value = 0;
        if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.Number)
        {
            return propertyElement.TryGetDouble(out value);
        }
        return false;
    }

    public static double GetDoublePropertyOrDefault(this JsonElement element, string propertyName, double defaultValue = 0.0)
    {
        return TryGetDoubleProperty(element, propertyName, out var value) ? value : defaultValue;
    }

    // Helper to get an int property
    public static bool TryGetInt32Property(this JsonElement element, string propertyName, out int value)
    {
        value = 0;
        if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.Number)
        {
            return propertyElement.TryGetInt32(out value);
        }
        return false;
    }

    // --- Helpers for Dictionary<string, JsonElement> ---

    public static bool TryGetStringValue(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, out string? value)
    {
        value = null;
        if (dictionary.TryGetValue(key, out var element) && element.ValueKind == JsonValueKind.String)
        {
            value = element.GetString();
            return true;
        }
        // Handle cases where the value might be a number but needs to be a string
        if (dictionary.TryGetValue(key, out element) && element.ValueKind == JsonValueKind.Number)
        {
            value = element.GetRawText(); // Or .ToString()
            return true;
        }
        return false;
    }

    public static string GetStringValueOrDefault(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, string defaultValue = "")
    {
        return TryGetStringValue(dictionary, key, out var value) ? value ?? defaultValue : defaultValue;
    }

    public static bool TryGetDoubleValue(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, out double value)
    {
        value = 0;
        if (dictionary.TryGetValue(key, out var element) && element.ValueKind == JsonValueKind.Number)
        {
            return element.TryGetDouble(out value);
        }
        return false;
    }

    public static double GetDoubleValueOrDefault(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, double defaultValue = 0.0)
    {
        return TryGetDoubleValue(dictionary, key, out var value) ? value : defaultValue;
    }

    public static bool TryGetInt32Value(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, out int value)
    {
        value = 0;
        if (dictionary.TryGetValue(key, out var element) && element.ValueKind == JsonValueKind.Number)
        {
            return element.TryGetInt32(out value);
        }
        return false;
    }
    public static int GetInt32ValueOrDefault(this IReadOnlyDictionary<string, JsonElement> dictionary, string key, int defaultValue = 0)
    {
        return TryGetInt32Value(dictionary, key, out var value) ? value : defaultValue;
    }
}