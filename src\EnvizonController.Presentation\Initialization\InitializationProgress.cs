namespace EnvizonController.Presentation.Initialization
{
    /// <summary>
    /// 表示应用程序初始化进度的类
    /// </summary>
    public class InitializationProgress
    {
        /// <summary>
        /// 获取或设置完成百分比（0-100）
        /// </summary>
        public int PercentComplete { get; set; }

        /// <summary>
        /// 获取或设置当前正在执行的操作描述
        /// </summary>
        public string CurrentOperation { get; set; }

        /// <summary>
        /// 获取或设置当前初始化阶段
        /// </summary>
        public InitializationStage Stage { get; set; }

        /// <summary>
        /// 获取一个值，指示初始化是否已完成
        /// </summary>
        public bool IsCompleted => Stage == InitializationStage.Completed;

        /// <summary>
        /// 获取一个值，指示初始化是否已失败
        /// </summary>
        public bool IsFailed => Stage == InitializationStage.Failed;

        /// <summary>
        /// 初始化 <see cref="InitializationProgress"/> 类的新实例
        /// </summary>
        /// <param name="percentComplete">完成百分比</param>
        /// <param name="currentOperation">当前操作描述</param>
        /// <param name="stage">当前初始化阶段</param>
        public InitializationProgress(int percentComplete, string currentOperation, InitializationStage stage)
        {
            PercentComplete = percentComplete;
            CurrentOperation = currentOperation ?? string.Empty;
            Stage = stage;
        }

        /// <summary>
        /// 创建一个表示未开始状态的进度实例
        /// </summary>
        /// <returns>表示未开始状态的进度实例</returns>
        public static InitializationProgress NotStarted()
        {
            return new InitializationProgress(0, "准备初始化...", InitializationStage.NotStarted);
        }

        /// <summary>
        /// 创建一个表示已完成状态的进度实例
        /// </summary>
        /// <returns>表示已完成状态的进度实例</returns>
        public static InitializationProgress Completed()
        {
            return new InitializationProgress(100, "初始化已完成", InitializationStage.Completed);
        }

        /// <summary>
        /// 创建一个表示失败状态的进度实例
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>表示失败状态的进度实例</returns>
        public static InitializationProgress Failed(string errorMessage)
        {
            return new InitializationProgress(0, $"初始化失败: {errorMessage}", InitializationStage.Failed);
        }
    }
}