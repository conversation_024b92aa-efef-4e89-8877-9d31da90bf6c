using AutoMapper;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Enums;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.Services
{
    /// <summary>
    /// 报警应用服务实现
    /// 负责在应用层处理DTO转换
    /// </summary>
    public class AlarmAppService : IAlarmAppService
    {
        private readonly IAlarmDomainService _alarmService;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;

        public AlarmAppService(IAlarmDomainService alarmService, IMapper mapper, IUnitOfWork unitOfWork)
        {
            _alarmService = alarmService;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 创建新的报警
        /// </summary>
        public async Task<AlarmDTO?> CreateAlarmAsync(long testRunId, string name, string message, AlarmSeverity level = AlarmSeverity.High)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                var alarm = await _alarmService.CreateAlarmAsync(testRunId, name, message, level);

                // 如果测试不在运行中，CreateAlarmAsync会返回null
                if (alarm == null)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return null;
                }

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                return _mapper.Map<AlarmDTO>(alarm);
            }
            catch (Exception)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// 处理报警
        /// </summary>
        public async Task<AlarmDTO> ProcessAlarmAsync(long alarmId, string processedBy)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                var alarm = await _alarmService.ProcessAlarmAsync(alarmId, processedBy);

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                return _mapper.Map<AlarmDTO>(alarm);
            }
            catch (Exception)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// 获取特定测试运行的所有报警
        /// </summary>
        public async Task<IEnumerable<AlarmDTO>> GetAlarmsByTestRunAsync(long testRunId)
        {
            var alarms = await _unitOfWork.Alarms.GetAlarmsByTestRunIdAsync(testRunId);
            return _mapper.Map<IEnumerable<AlarmDTO>>(alarms);
        }

        /// <summary>
        /// 获取所有活动状态的报警
        /// </summary>
        public async Task<IEnumerable<AlarmDTO>> GetActiveAlarmsAsync()
        {
            var alarms = await _unitOfWork.Alarms.GetActiveAlarmsAsync();
            return _mapper.Map<IEnumerable<AlarmDTO>>(alarms);
        }

        /// <summary>
        /// 获取特定级别的报警
        /// </summary>
        public async Task<IEnumerable<AlarmDTO>> GetAlarmsByLevelAsync(AlarmSeverity level)
        {
            var alarms = await _unitOfWork.Alarms.GetAlarmsByLevelAsync(level);
            return _mapper.Map<IEnumerable<AlarmDTO>>(alarms);
        }
        
        /// <summary>
        /// 获取报警列表（支持多参数查询和分页）
        /// </summary>
        public async Task<PagedResultDto<AlarmDTO>> GetAlarmsAsync(AlarmQueryParams queryParams)
        {
            // 从查询参数中提取过滤条件
            var page = queryParams.Page <= 0 ? 1 : queryParams.Page;
            var pageSize = queryParams.PageSize <= 0 ? 20 : queryParams.PageSize;
            
            // 调用仓储查询方法
            var (alarms, totalCount) = await _unitOfWork.Alarms.GetPagedAlarmsAsync(
                page,
                pageSize,
                queryParams.Status,
                queryParams.Level,
                queryParams.StartDate,
                queryParams.EndDate,
                queryParams.TestId,
                queryParams.DeviceId);
                
            // 映射结果到DTO
            var alarmDtos = _mapper.Map<IEnumerable<AlarmDTO>>(alarms);
            
            // 构建并返回分页结果
            return new PagedResultDto<AlarmDTO>
            {
                Items = alarmDtos,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
    }
}
