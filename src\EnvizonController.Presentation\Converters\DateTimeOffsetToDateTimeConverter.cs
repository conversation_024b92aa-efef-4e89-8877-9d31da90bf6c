﻿using System.Globalization;
using Avalonia.Data.Converters;

namespace EnvizonController.Presentation.Converters;

public class DateTimeOffsetToDateTimeConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        // 从ViewModel(DateTime?)到UI(DateTimeOffset?)
        if (value is DateTime dateTime)
            return new DateTimeOffset(dateTime);
        return null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        // 从UI(DateTimeOffset?)到ViewModel(DateTime?)
        if (value is DateTimeOffset dateTimeOffset)
            return dateTimeOffset.DateTime;
        return null;
    }
}