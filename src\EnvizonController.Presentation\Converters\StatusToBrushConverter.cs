using Avalonia.Data.Converters;
using Avalonia.Media;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警状态转换为对应的颜色画刷
    /// </summary>
    public class StatusToBrushConverter : IValueConverter
    {
        /// <summary>
        /// 将报警状态转换为对应的颜色画刷
        /// </summary>
        /// <param name="value">报警状态</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>对应的颜色画刷</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not AlarmStatus status) return new SolidColorBrush(Colors.Gray);

            return status switch
            {
                AlarmStatus.Active => new SolidColorBrush(Color.Parse("#E53935")),     // 红色
                AlarmStatus.Processed => new SolidColorBrush(Color.Parse("#4CAF50")),  // 绿色
                _ => new SolidColorBrush(Colors.Gray)
            };
        }

        /// <summary>
        /// 将颜色画刷转换回报警状态（不支持）
        /// </summary>
        /// <param name="value">颜色画刷</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 