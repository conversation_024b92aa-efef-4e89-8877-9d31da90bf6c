using EnvizonController.Modbus.Client;
using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Utils;
using EnvizonController.Modbus.Tests.Mocks;
using Xunit;

namespace EnvizonController.Modbus.Tests.Client
{
    public class ModbusClientTests
    {
        private readonly TestModbusChannel _channel;
        private readonly ModbusClient _client;

        public ModbusClientTests()
        {
            _channel = new TestModbusChannel();
            _client = new ModbusClient(_channel, ModbusTransportType.Rtu);
        }

        [Fact]
        public async Task ConnectAsync_CallsChannelConnectAsync()
        {
            // Act
            await _client.ConnectAsync();

            // Assert
            Assert.True(_channel.IsConnected);
        }

        [Fact]
        public async Task DisconnectAsync_CallsChannelDisconnectAsync()
        {
            // Arrange
            await _client.ConnectAsync();

            // Act
            await _client.DisconnectAsync();

            // Assert
            Assert.False(_channel.IsConnected);
        }

        [Fact]
        public async Task ReadHoldingRegistersAsync_SendsCorrectRequestAndParsesResponse()
        {
            // Arrange
            await _client.ConnectAsync();

            // 模拟响应
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadHoldingRegisters;
            byte byteCount = 4;
            byte[] registerValues = new byte[] { 0x00, 0x0A, 0x00, 0x0B };

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(registerValues, 0, responseData, 3, byteCount);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            _channel.EnqueueResponse(responseFrame);

            // Act
            ushort[] result = await _client.ReadHoldingRegistersAsync(slaveAddress, 0, 2);

            // Assert
            Assert.Equal(2, result.Length);
            Assert.Equal(10, result[0]);
            Assert.Equal(11, result[1]);

            // 验证发送的请求
            Assert.Single(_channel.SentData);
            byte[] requestFrame = _channel.SentData[0];
            Assert.Equal(slaveAddress, requestFrame[0]);
            Assert.Equal(functionCode, requestFrame[1]);
            Assert.Equal(0x00, requestFrame[2]); // 起始地址高字节
            Assert.Equal(0x00, requestFrame[3]); // 起始地址低字节
            Assert.Equal(0x00, requestFrame[4]); // 寄存器数量高字节
            Assert.Equal(0x02, requestFrame[5]); // 寄存器数量低字节
        }

        [Fact]
        public async Task WriteSingleRegisterAsync_SendsCorrectRequestAndParsesResponse()
        {
            // Arrange
            await _client.ConnectAsync();

            // 模拟响应
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.WriteSingleRegister;
            byte[] address = new byte[] { 0x00, 0x01 };
            byte[] value = new byte[] { 0x00, 0x0A };

            byte[] responseData = new byte[6]; // 修改为6个字节，以容纳所有数据
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            Array.Copy(address, 0, responseData, 2, 2);
            Array.Copy(value, 0, responseData, 4, 2);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            _channel.EnqueueResponse(responseFrame);

            // Act
            bool result = await _client.WriteSingleRegisterAsync(slaveAddress, 1, 10);

            // Assert
            Assert.True(result);

            // 验证发送的请求
            Assert.Single(_channel.SentData);
            byte[] requestFrame = _channel.SentData[0];
            Assert.Equal(slaveAddress, requestFrame[0]);
            Assert.Equal(functionCode, requestFrame[1]);
            Assert.Equal(address[0], requestFrame[2]); // 寄存器地址高字节
            Assert.Equal(address[1], requestFrame[3]); // 寄存器地址低字节
            Assert.Equal(value[0], requestFrame[4]); // 寄存器值高字节
            Assert.Equal(value[1], requestFrame[5]); // 寄存器值低字节
        }

        [Fact]
        public async Task WriteMultipleRegistersAsync_SendsCorrectRequestAndParsesResponse()
        {
            // Arrange
            await _client.ConnectAsync();

            // 模拟响应
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.WriteMultipleRegisters;
            byte[] address = new byte[] { 0x00, 0x01 };
            byte[] count = new byte[] { 0x00, 0x02 };

            byte[] responseData = new byte[5];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            Array.Copy(address, 0, responseData, 2, 2);
            Array.Copy(count, 0, responseData, 4, 2);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            _channel.EnqueueResponse(responseFrame);

            // Act
            bool result = await _client.WriteMultipleRegistersAsync(slaveAddress, 1, new ushort[] { 10, 20 });

            // Assert
            Assert.True(result);

            // 验证发送的请求
            Assert.Single(_channel.SentData);
            byte[] requestFrame = _channel.SentData[0];
            Assert.Equal(slaveAddress, requestFrame[0]);
            Assert.Equal(functionCode, requestFrame[1]);
            Assert.Equal(address[0], requestFrame[2]); // 起始地址高字节
            Assert.Equal(address[1], requestFrame[3]); // 起始地址低字节
            Assert.Equal(count[0], requestFrame[4]); // 寄存器数量高字节
            Assert.Equal(count[1], requestFrame[5]); // 寄存器数量低字节
            Assert.Equal(4, requestFrame[6]); // 字节数
            Assert.Equal(0x00, requestFrame[7]); // 第一个寄存器值高字节
            Assert.Equal(0x0A, requestFrame[8]); // 第一个寄存器值低字节
            Assert.Equal(0x00, requestFrame[9]); // 第二个寄存器值高字节
            Assert.Equal(0x14, requestFrame[10]); // 第二个寄存器值低字节
        }

        [Fact]
        public async Task ReadCoilsAsync_SendsCorrectRequestAndParsesResponse()
        {
            // Arrange
            await _client.ConnectAsync();

            // 模拟响应
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadCoils;
            byte byteCount = 1;
            byte[] coilValues = new byte[] { 0x05 }; // 0000 0101 - 第1和第3位为1

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(coilValues, 0, responseData, 3, byteCount);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);

            // 设置响应
            _channel.EnqueueResponse(responseFrame);

            // Act
            bool[] result = await _client.ReadCoilsAsync(slaveAddress, 0, 8);

            // Assert
            Assert.Equal(8, result.Length);
            Assert.True(result[0]);
            Assert.False(result[1]);
            Assert.True(result[2]);
            Assert.False(result[3]);
            Assert.False(result[4]);
            Assert.False(result[5]);
            Assert.False(result[6]);
            Assert.False(result[7]);

            // 验证发送的请求
            Assert.Single(_channel.SentData);
            byte[] requestFrame = _channel.SentData[0];
            Assert.Equal(slaveAddress, requestFrame[0]);
            Assert.Equal(functionCode, requestFrame[1]);
            Assert.Equal(0x00, requestFrame[2]); // 起始地址高字节
            Assert.Equal(0x00, requestFrame[3]); // 起始地址低字节
            Assert.Equal(0x00, requestFrame[4]); // 线圈数量高字节
            Assert.Equal(0x08, requestFrame[5]); // 线圈数量低字节
        }
    }
}
