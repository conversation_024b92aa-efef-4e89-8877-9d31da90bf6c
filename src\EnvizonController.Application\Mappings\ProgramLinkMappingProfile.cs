﻿using AutoMapper;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using System;

namespace EnvizonController.Application.Mappings
{
    /// <summary>
    /// 程式链接领域对象的 AutoMapper 配置文件
    /// </summary>
    public class ProgramLinkMappingProfile : Profile
    {
        public ProgramLinkMappingProfile()
        {
            // ProgramLink -> ProgramLinkDTO
            CreateMap<ProgramLink, ProgramLinkDTO>()
                .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items))
                .ForMember(dest => dest.ItemCount, opt => opt.Ignore()); // DTO自动计算

            // ProgramLinkDTO -> ProgramLink
            CreateMap<ProgramLinkDTO, ProgramLink>()
                .ForMember(dest => dest.Items, opt => opt.Ignore()) // 需单独处理
                .ForMember(dest => dest.CreatedAt, opt => opt.Condition(src => src.Id == 0))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.Now));

            // ProgramLinkItem -> ProgramLinkItemDTO
            CreateMap<ProgramLinkStep, ProgramLinkStepDTO>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Program != null ? src.Program.Name : string.Empty))
                .ForMember(dest => dest.Program, opt => opt.MapFrom(src => src.Program));

            // ProgramLinkItemDTO -> ProgramLinkItem
            CreateMap<ProgramLinkStepDTO, ProgramLinkStep>()
                .ForMember(dest => dest.Program, opt => opt.Ignore()) // 需单独处理
                .ForMember(dest => dest.ProgramLink, opt => opt.Ignore()) // 需单独处理
            
                .ForMember(dest => dest.CreatedAt, opt => opt.Condition(src => src.Id == 0))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}