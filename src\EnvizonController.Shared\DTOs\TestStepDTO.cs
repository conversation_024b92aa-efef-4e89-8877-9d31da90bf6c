namespace EnvizonController.Shared.DTOs;

/// <summary>
///     测试步骤数据传输对象
/// </summary>
public class TestStepDTO
{
    /// <summary>
    ///     测试步骤ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     所属测试项ID
    /// </summary>
    public long TestItemId { get; set; }
    
    /// <summary>
    ///     温度（摄氏度）
    /// </summary>
    public double Temperature { get; set; }
    
    /// <summary>
    ///     湿度（百分比）
    /// </summary>
    public double Humidity { get; set; }
    
    /// <summary>
    ///     是否线性变化
    /// </summary>
    public bool IsLinear { get; set; }
    
    /// <summary>
    ///     持续时间（秒）
    /// </summary>
    public int DurationSeconds { get; set; }
    
    /// <summary>
    ///     步骤序号（从1开始）
    /// </summary>
    public int StepNumber { get; set; }
    
    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}