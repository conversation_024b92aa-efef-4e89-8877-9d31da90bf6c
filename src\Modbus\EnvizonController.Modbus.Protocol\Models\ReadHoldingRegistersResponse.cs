using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 读取保持寄存器响应
    /// </summary>
    public class ReadHoldingRegistersResponse : ModbusResponse
    {
        /// <summary>
        /// 字节计数
        /// </summary>
        public byte ByteCount { get; private set; }

        /// <summary>
        /// 寄存器值
        /// </summary>
        public ushort[] RegisterValues { get; private set; } = Array.Empty<ushort>();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReadHoldingRegistersResponse()
        {
            FunctionCode = ModbusFunction.ReadHoldingRegisters;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 3)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            ByteCount = frame[2];

            // 验证帧长度是否与字节计数匹配
            if (frame.Length < 3 + ByteCount)
                throw new ArgumentException("响应帧长度与字节计数不匹配", nameof(frame));

            // 计算寄存器数量
            int registerCount = ByteCount / 2;
            RegisterValues = new ushort[registerCount];

            // 解析寄存器值
            for (int i = 0; i < registerCount; i++)
            {
                int offset = 3 + i * 2;
                RegisterValues[i] = (ushort)((frame[offset] << 8) | frame[offset + 1]);
            }
        }
    }
}
