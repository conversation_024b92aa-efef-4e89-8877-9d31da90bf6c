# EnvizonController 设备测试系统设计文档

## 1. 系统概述

本设备测试系统是EnvizonController项目的核心功能模块，负责管理设备测试的完整生命周期，包括测试创建、启动、执行、监控和停止。系统采用领域驱动设计(DDD)原则，使用多种设计模式确保代码的可维护性和可扩展性。

### 1.1 核心功能

- **设备测试启动**：支持手动测试、程式测试和程式链接测试
- **测试状态管理**：实时监控测试状态和进度
- **数据采集集成**：自动启动和停止数据采集
- **批量操作**：支持多设备并发测试
- **扩展性设计**：易于添加新的测试类型和策略

### 1.2 技术特点

- **设计模式驱动**：策略模式、工厂模式、命令模式等
- **依赖注入**：松耦合的组件设计
- **异步编程**：提升系统并发性能
- **事务一致性**：确保数据操作的完整性
- **日志记录**：完整的操作审计和错误跟踪

## 2. 架构设计

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API层 (Controllers)                      │
│   DeviceTestController + DevicesController (设备测试端点)    │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                   应用服务层 (Services)                     │
│  DeviceTestService + TestExecutionStrategyFactory          │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                  领域层 (Domain Models)                     │
│      TestRun + Device + TestDataPoint + 业务规则           │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│               基础设施层 (Infrastructure)                    │
│     数据采集协调器 + 仓储实现 + 外部服务集成                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 DeviceTestService (设备测试服务)
- **职责**：协调整个测试流程，管理测试生命周期
- **依赖**：设备服务、测试项服务、数据采集协调器、策略工厂
- **特点**：使用依赖注入，支持事务操作

#### 2.2.2 TestExecutionStrategyFactory (测试执行策略工厂)
- **职责**：根据执行类型创建相应的测试策略
- **模式**：工厂模式
- **扩展性**：易于添加新的测试类型

#### 2.2.3 ITestExecutionStrategy (测试执行策略接口)
- **职责**：定义测试创建的抽象行为
- **模式**：策略模式
- **实现**：手动测试、程式测试、程式链接测试策略

## 3. 设计模式应用

### 3.1 策略模式 (Strategy Pattern)

**应用场景**：不同类型的测试创建逻辑

```csharp
public interface ITestExecutionStrategy
{
    Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device);
}

// 具体策略实现
public class ManualTestExecutionStrategy : ITestExecutionStrategy
public class ProgramTestExecutionStrategy : ITestExecutionStrategy  
public class ProgramLinkTestExecutionStrategy : ITestExecutionStrategy
```

**优势**：
- 消除条件分支，提高代码可读性
- 支持运行时策略切换
- 易于添加新的测试类型

### 3.2 工厂模式 (Factory Pattern)

**应用场景**：创建测试执行策略实例

```csharp
public class TestExecutionStrategyFactory : ITestExecutionStrategyFactory
{
    public ITestExecutionStrategy CreateStrategy(string executionType)
    {
        return executionType?.ToLower() switch
        {
            "manual" => new ManualTestExecutionStrategy(_serviceProvider, _logger),
            "program" => new ProgramTestExecutionStrategy(_serviceProvider, _logger),
            "programlink" => new ProgramLinkTestExecutionStrategy(_serviceProvider, _logger),
            _ => new ManualTestExecutionStrategy(_serviceProvider, _logger)
        };
    }
}
```

**优势**：
- 封装对象创建逻辑
- 支持依赖注入
- 简化客户端代码

### 3.3 依赖注入模式 (Dependency Injection)

**应用场景**：组件间的松耦合设计

```csharp
public class DeviceTestService : IDeviceTestService
{
    private readonly IDeviceAppService _deviceService;
    private readonly ITestItemAppService _testItemService;
    private readonly IDataCollectionCoordinator _dataCollectionCoordinator;
    // ... 其他依赖
}
```

**优势**：
- 减少组件间的直接依赖
- 便于单元测试
- 支持运行时配置

### 3.4 模板方法模式 (Template Method)

**应用场景**：测试执行的通用流程

```csharp
public abstract class BaseTestExecutionStrategy : ITestExecutionStrategy
{
    protected virtual void ValidateRequest(DeviceTestStartRequest request, Device device)
    protected virtual string CreateBaseConfig(DeviceTestStartRequest request, string strategyType)
    public abstract Task<TestRun> CreateTestRunAsync(DeviceTestStartRequest request, Device device);
}
```

## 4. API设计

### 4.1 RESTful API端点

#### 4.1.1 独立设备测试控制器

```http
POST /api/devicetest/start
POST /api/devicetest/{deviceId}/stop  
GET  /api/devicetest/{deviceId}/status
POST /api/devicetest/batch-start
GET  /api/devicetest/status?deviceIds=1,2,3
```

#### 4.1.2 设备控制器集成端点

```http
POST /api/devices/{id}/test/start
POST /api/devices/{id}/test/stop
GET  /api/devices/{id}/test/status
```

### 4.2 请求/响应模型

#### 4.2.1 启动测试请求

```json
{
  "deviceId": 1,
  "testName": "温度压力测试",
  "description": "测试设备在高温高压环境下的性能",
  "executionType": "manual|program|programlink",
  "executionId": 123,
  "extraConfig": {
    "duration": 3600,
    "maxTemperature": 80
  }
}
```

#### 4.2.2 测试状态响应

```json
{
  "deviceId": 1,
  "deviceName": "温控设备-1",
  "isDeviceConnected": true,
  "testStatus": "Running",
  "currentTest": {
    "id": 456,
    "name": "温度压力测试",
    "status": "Running",
    "startTime": "2025-01-20T10:00:00Z"
  },
  "isDataCollecting": true,
  "message": "测试运行中"
}
```

## 5. 数据流程

### 5.1 测试启动流程

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DeviceTestService
    participant StrategyFactory
    participant Strategy
    participant DataCoordinator
    
    Client->>API: POST /api/devicetest/start
    API->>DeviceTestService: StartDeviceTestAsync()
    DeviceTestService->>DeviceTestService: ValidateStartRequestAsync()
    DeviceTestService->>DeviceTestService: GetAndValidateDeviceAsync()
    DeviceTestService->>StrategyFactory: CreateStrategy()
    StrategyFactory->>Strategy: new Strategy()
    DeviceTestService->>Strategy: CreateTestRunAsync()
    DeviceTestService->>DeviceTestService: StartTestExecutionAsync()
    DeviceTestService->>DataCoordinator: StartTestCollectionAsync()
    DeviceTestService->>API: DeviceTestStartResult
    API->>Client: JSON Response
```

### 5.2 数据采集集成

```mermaid
graph TD
    A[设备测试启动] --> B[创建测试运行]
    B --> C[启动数据采集]
    C --> D[Modbus通信]
    D --> E[数据缓冲]
    E --> F[数据持久化]
    F --> G[实时监控]
    G --> H[测试完成]
    H --> I[停止数据采集]
```

## 6. 扩展指南

### 6.1 添加新的测试策略

1. **创建策略类**：

```csharp
public class CustomTestExecutionStrategy : BaseTestExecutionStrategy
{
    public override async Task<TestRun> CreateTestRunAsync(
        DeviceTestStartRequest request, 
        Device device)
    {
        ValidateRequest(request, device);
        
        // 实现自定义测试逻辑
        var testRun = TestRun.Create(
            name: request.TestName,
            deviceId: device.Id,
            status: "Created",
            description: request.Description ?? "自定义测试");

        testRun.ExecutionType = "Custom";
        testRun.ExecutionConfigJson = CreateBaseConfig(request, "Custom");
        
        // 添加自定义逻辑
        await AddCustomLogicAsync(testRun, request);
        
        return testRun;
    }
    
    private async Task AddCustomLogicAsync(TestRun testRun, DeviceTestStartRequest request)
    {
        // 实现特定的业务逻辑
    }
}
```

2. **更新工厂类**：

```csharp
public ITestExecutionStrategy CreateStrategy(string executionType)
{
    return executionType?.ToLower() switch
    {
        "manual" => new ManualTestExecutionStrategy(_serviceProvider, _logger),
        "program" => new ProgramTestExecutionStrategy(_serviceProvider, _logger),
        "programlink" => new ProgramLinkTestExecutionStrategy(_serviceProvider, _logger),
        "custom" => new CustomTestExecutionStrategy(_serviceProvider, _logger), // 新增
        _ => new ManualTestExecutionStrategy(_serviceProvider, _logger)
    };
}
```

### 6.2 添加新的验证规则

```csharp
public class AdvancedTestRequestValidator : ITestRequestValidator
{
    public async Task<ValidationResult> ValidateAsync(DeviceTestStartRequest request)
    {
        var result = new ValidationResult();
        
        // 添加自定义验证逻辑
        if (request.ExecutionType == "advanced")
        {
            await ValidateAdvancedParametersAsync(request, result);
        }
        
        return result;
    }
}
```

### 6.3 添加新的数据采集策略

```csharp
public class HighFrequencyCollectionStrategy : ICollectionStrategy
{
    public async Task<bool> CanHandleAsync(Device device, TestRun testRun)
    {
        return testRun.ExecutionType == "HighFrequency";
    }
    
    public async Task<List<TestDataPoint>> CollectDataAsync(Device device, Protocol protocol)
    {
        // 实现高频采集逻辑
        return await PerformHighFrequencyCollection(device, protocol);
    }
}
```

## 7. 性能考虑

### 7.1 并发控制

- 使用`ConcurrentDictionary`管理活动测试
- 异步操作减少线程阻塞
- 合理的连接池配置

### 7.2 资源管理

- 及时释放Modbus连接
- 内存缓冲区大小控制
- 数据库连接复用

### 7.3 错误处理

- 分层的异常处理机制
- 详细的错误日志记录
- 优雅的降级策略

## 8. 安全考虑

### 8.1 输入验证

- 严格的参数验证
- SQL注入防护
- XSS攻击防护

### 8.2 权限控制

- 基于角色的访问控制
- API密钥认证
- 操作审计日志

## 9. 监控和运维

### 9.1 健康检查

```csharp
public class DeviceTestHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        // 检查关键组件状态
        var isDataCoordinatorHealthy = await CheckDataCoordinatorAsync();
        var isModbusServiceHealthy = await CheckModbusServiceAsync();
        
        return isDataCoordinatorHealthy && isModbusServiceHealthy
            ? HealthCheckResult.Healthy("设备测试系统运行正常")
            : HealthCheckResult.Unhealthy("设备测试系统存在问题");
    }
}
```

### 9.2 性能指标

- 测试启动成功率
- 平均测试执行时间
- 数据采集延迟
- 错误率统计

## 10. 总结

本设备测试系统通过良好的架构设计和设计模式应用，实现了：

1. **高度可扩展性**：易于添加新的测试类型和功能
2. **良好的可维护性**：清晰的分层和职责分离
3. **强壮的错误处理**：完善的异常处理和日志记录
4. **优秀的性能**：异步编程和资源优化
5. **易于测试**：依赖注入和接口抽象

系统已经具备了生产环境使用的基础条件，并为未来的功能扩展提供了良好的基础架构。 