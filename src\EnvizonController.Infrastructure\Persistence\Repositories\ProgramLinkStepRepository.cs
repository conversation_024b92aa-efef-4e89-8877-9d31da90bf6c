using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories;

/// <summary>
///     链接步骤仓储实现
/// </summary>
public class ProgramLinkStepRepository : Repository<ProgramLinkStep, long>, IProgramLinkStepRepository
{
    public ProgramLinkStepRepository(AppDbContext dbContext) : base(dbContext)
    {
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProgramLinkStep>> GetByProgramLinkIdAsync(long programLinkId)
    {
        return await _dbSet.Where(ps => ps.ProgramLinkId == programLinkId)
            .OrderBy(ps => ps.ExecutionOrder)
            .ToListAsync();
    }


    /// <inheritdoc />
    public async Task<ProgramLinkStep?> GetByProgramLinkIdAndIndexAsync(long programLinkId, int index)
    {
        return await _dbSet.FirstOrDefaultAsync(ps => ps.ProgramId == programLinkId && ps.ExecutionOrder == index);
    }

    /// <inheritdoc />
    public async Task DeleteByProgramLinkIdAsync(long programLinkId)
    {
        var stepsToDelete = await _dbSet.Where(ps => ps.ProgramId == programLinkId).ToListAsync();
        if (stepsToDelete.Any()) _dbSet.RemoveRange(stepsToDelete);
    }
}