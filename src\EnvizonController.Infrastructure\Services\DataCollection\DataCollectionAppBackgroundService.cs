﻿using EnvizonController.Application.DataCollection;
using Serilog;

namespace EnvizonController.Infrastructure.Services.DataCollection;

public class DataCollectionAppBackgroundService(
    //IDataCollectionTaskScheduler taskScheduler,
    ILogger logger)
{
    private readonly ILogger _logger = logger;

    private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
    private readonly TimeSpan _interval = TimeSpan.FromSeconds(5); // 每5秒执行一次

    public void Start()
    {
        Task.Factory.StartNew(async () => await RunAsync(_cancellationTokenSource.Token), TaskCreationOptions.LongRunning);
    }

    public void Stop()
    {
        _cancellationTokenSource.Cancel();
    }

    private async Task RunAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 在这里执行您的后台任务
                _logger.Information("后台任务正在运行...");
                // 启动数据采集任务调度器
                //await taskScheduler.StartAsync(cancellationToken);

                // 等待取消令牌
                await Task.Delay(Timeout.Infinite, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 任务被取消
                break;
            }
        }
    }
}