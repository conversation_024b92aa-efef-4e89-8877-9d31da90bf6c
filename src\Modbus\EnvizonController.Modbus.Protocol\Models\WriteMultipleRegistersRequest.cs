using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写多个寄存器请求
    /// </summary>
    public class WriteMultipleRegistersRequest : ModbusRequest
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; set; }

        /// <summary>
        /// 寄存器值
        /// </summary>
        public ushort[] RegisterValues { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="registerValues">寄存器值</param>
        public WriteMultipleRegistersRequest(byte slaveAddress, ushort startAddress, ushort[] registerValues)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.WriteMultipleRegisters;
            StartAddress = startAddress;
            RegisterValues = registerValues ?? throw new ArgumentNullException(nameof(registerValues));
            
            if (registerValues.Length > 123) // Modbus规范限制
                throw new ArgumentException("寄存器数量超过Modbus规范限制(123)", nameof(registerValues));
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            byte registerCount = (byte)RegisterValues.Length;
            byte byteCount = (byte)(registerCount * 2);
            
            var data = new byte[5 + byteCount];
            data[0] = (byte)(StartAddress >> 8);    // 起始地址高字节
            data[1] = (byte)(StartAddress & 0xFF);  // 起始地址低字节
            data[2] = (byte)(registerCount >> 8);   // 寄存器数量高字节
            data[3] = (byte)(registerCount & 0xFF); // 寄存器数量低字节
            data[4] = byteCount;                    // 字节计数
            
            // 添加寄存器值
            for (int i = 0; i < registerCount; i++)
            {
                int offset = 5 + i * 2;
                data[offset] = (byte)(RegisterValues[i] >> 8);    // 高字节
                data[offset + 1] = (byte)(RegisterValues[i] & 0xFF); // 低字节
            }
            
            return data;
        }
    }
}
