using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using AvaloniaLineSeriesChart.Data;
using System;
using System.Collections.Generic;
using Avalonia.Controls.Primitives;
using Avalonia.Threading;
using AvaloniaLineSeriesChart.Data.Enums;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Presentation.Messages;
using Avalonia.VisualTree;
using EnvizonController.Shared.DTOs;
using Avalonia.Interactivity;
using EnvizonController.Presentation.ViewModels.Dashboard;

namespace EnvizonController.Presentation.Views.Dashboard
{
    public partial class EnvironmentChartControl : UserControl,  
        IRecipient<DeviceItemViewModelChangedMessage>,
        IRecipient<CurrentDeviceDtoChangedMessage>,
        IRecipient<ChartSeriesVisibleChangedMessage>
    {
        // ݻ
        //private readonly Queue<(DateTime Time, double Temperature, double Humidity)> _dataPoints = new();

        // 
        public TimeSpan DisplayTimeRange { get; set; } = TimeSpan.FromMinutes(10);

        // ϵ
        private const int TemperatureSeriesIndex = 0;
        private const int HumiditySeriesIndex = 1;

        // Ƿǵһݵı־
        private bool _isFirstDataPoint = true;

        public EnvironmentChartControl()
        {
            InitializeComponent();
            // ͼ
            this.LineChart.SetTheme(new ChartTheme
            {
                BackgroundColor = Colors.Transparent,
                TextColor = Colors.White,
                AxisColor = Color.FromRgb((byte)0x66, (byte)0x66, (byte)0x66),
                GridLineColor = Color.FromRgb((byte)0x44, (byte)0x44, (byte)0x44),
                DefaultSeriesColors =
                [
                    Color.FromRgb((byte)0xe6, (byte)0x2f,(byte)0xf3), //¶
                    Color.FromRgb((byte)0x0d, (byte)0xf0,(byte)0xff), // ʪ
                    Colors.DeepPink,
                    Colors.WhiteSmoke,
                    Colors.Gold
                ],
                TooltipBackgroundColor = Color.FromRgb(50, 50, 50)
            });
            LineChart.SetXAxisType(XAxisType.DateTime);
            // ʾ
            LineChart.MaxDisplayPoints = 5000;
            LineChart.SetXTickCount(4);
            LineChart.SetXAxisLabelFormatter(s =>
            {
                Decimal xValue = (Decimal)s;
                return new DateTime((long)xValue).ToString("HH:mm:ss");
            });
            // ʼϵ
            //InitializeSeries();


            WeakReferenceMessenger.Default.Register<DeviceItemViewModelChangedMessage>(this);
            WeakReferenceMessenger.Default.Register<CurrentDeviceDtoChangedMessage>(this);
            WeakReferenceMessenger.Default.Register<ChartSeriesVisibleChangedMessage>(this);
        }
        
        protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);
            WeakReferenceMessenger.Default.Unregister<DeviceItemViewModelChangedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<CurrentDeviceDtoChangedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<ChartSeriesVisibleChangedMessage>(this);
        }

        private void SeriesToggleButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is not ToggleButton toggleButton) 
                return;
                
            string seriesName = toggleButton.Content?.ToString();
            bool isChecked = toggleButton.IsChecked ?? true;
            
            // 直接设置可见性
            SetSeriesVisible(seriesName, isChecked);
            
            // 也可以选择发送消息，以便其他控件可以响应这个变化
            WeakReferenceMessenger.Default.Send(new ChartSeriesVisibleChangedMessage(seriesName, isChecked));
        }

        public void AddSeries(string name, YAxisPosition yAxisPosition = YAxisPosition.Left)
        {
            Dispatcher.UIThread.Invoke(() =>
            {
                var series = new LineSeriesData
                {
                    Name = name,
                    YAxisPosition = yAxisPosition
                };
                LineChart.AddSeries(series);
                series.SetXDataType<DateTime>();
                LineChart.Refresh();
            });
        }

        public void AddPoint(int seriesIndex, Point point)
        {
           
                LineChart.AddPoint(seriesIndex, point);
                LineChart.Refresh();
        }

        //private void InitializeSeries()
        //{
        //    // ¶ϵ
        //    AddSeries("Temperature", YAxisPosition.Left);
        //    AddSeries("Humidity", YAxisPosition.Right);
        //}

        // ͼ
        public void UpdateChart(DateTime timestamp,params EnvironmentalDataGroup[] dataGroups)
        {
            Dispatcher.UIThread.Invoke(() =>
            {
                int index = 0;
                foreach (var data in dataGroups)
                {
                    AddPoint((index++ % 2), new Point(timestamp.Ticks, Convert.ToSingle(data.Value)));
                }
            });
        }

        public void SetSeriesVisible(string name, bool show)
        {
            if (show)
                LineChart.ShowSeriesByName(name);
            else
                LineChart.HideSeriesByName(name);
        }
        // ͼ
        public void ClearChart()
        {
            LineChart.ClearSeries();
            LineChart.Refresh();
        }

        public void Receive(DeviceItemViewModelChangedMessage message)
        {
            // 重新初始化图表系列
            //InitializeSeries();
            
            // 设置第一次数据点标志，以便添加历史数据
            _isFirstDataPoint = true;
            
            // 从ViewModel获取环境数据组
            var environmentalDataGroups = message.ViewModel.EnvironmentalDataGroupList;
            int index = 0;
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                if (LineChart.Series.Count == 0)
                {
                    foreach (var data in environmentalDataGroups)
                    {
                        AddSeries(data.ValueName, (YAxisPosition)(index++ % 2));
                    }
                }

                UpdateChart(DateTime.Now, environmentalDataGroups.ToArray());

            });
         
        }

        public void Receive(CurrentDeviceDtoChangedMessage message)
        {
            // 清除现有图表数据
            ClearChart();
        }
        
        public void Receive(ChartSeriesVisibleChangedMessage message)
        {
            // 从其他地方接收系列可见性变更消息
            string seriesName = message.Value.serilogName;
            bool show = message.Value.show;
            
            // 更新图表系列可见性
            SetSeriesVisible(seriesName, show);
        }
    }
}