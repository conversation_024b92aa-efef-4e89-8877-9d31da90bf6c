using System;

namespace EnvizonController.Shared.DTOs
{
    /// <summary>
    /// 程式链接项数据传输对象
    /// </summary>
    public class ProgramLinkStepDTO
    {
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 程式链接项ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 所属程式链接ID
        /// </summary>
        public long ProgramLinkId { get; set; }

        /// <summary>
        /// 关联的程式ID
        /// </summary>
        public long ProgramId { get; set; }

        /// <summary>
        /// 关联的程式
        /// </summary>
        public ProgramDTO? Program { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int ExecutionOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
}