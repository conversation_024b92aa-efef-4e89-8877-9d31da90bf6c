<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.ApiDevicesControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="300"
    d:DesignWidth="400"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Border
        Padding="15"
        Background="#1A2A3A"
        BorderBrush="#0DF0FF"
        BorderThickness="1"
        CornerRadius="5">
        <Grid RowDefinitions="Auto,*,Auto">
            <Grid Grid.Row="0" ColumnDefinitions="*,Auto">
                <TextBlock
                    Grid.Column="0"
                    Classes="header"
                    Text="API 设备列表" />
                <StackPanel
                    Grid.Column="1"
                    Orientation="Horizontal"
                    Spacing="5">
                    <TextBlock
                        VerticalAlignment="Center"
                        Classes="Second"
                        FontSize="12"
                        Text="{Binding LastRefreshTime, StringFormat=最后刷新: {0:yyyy-MM-dd HH:mm:ss}}" />
                    <Button
                        Padding="5"
                        Command="{Binding RefreshDevicesCommand}"
                        ToolTip.Tip="刷新设备列表">
                        <TextBlock
                            Classes="icon"
                            FontSize="14"
                            Text="&#xf2f1;" />
                    </Button>
                </StackPanel>
            </Grid>

            <Grid Grid.Row="1" RowDefinitions="*">
                <DataGrid
                    Margin="0,10,0,0"
                    Padding="0"
                    AutoGenerateColumns="False"
                    Background="Transparent"
                    BorderThickness="0"
                    CanUserReorderColumns="False"
                    CanUserResizeColumns="True"
                    CanUserSortColumns="True"
                    GridLinesVisibility="All"
                    IsReadOnly="True"
                    ItemsSource="{Binding ApiDevices}"
                    RowBackground="#2D3748"
                    SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding Name}"
                            Header="设备名称" />
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding ConnectionType}"
                            Header="连接类型" />
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding Status}"
                            Header="状态" />
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding ConnectionStatus}"
                            Header="连接状态" />
                        <DataGridTextColumn
                            Width="*"
                            Binding="{Binding DeviceDto.LastUpdatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}"
                            Header="最后更新" />
                    </DataGrid.Columns>
                    <DataGrid.Styles>
                        <Style Selector="DataGridColumnHeader">
                            <Setter Property="Background" Value="#1A2A3A" />
                            <Setter Property="Foreground" Value="#0DF0FF" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="HorizontalContentAlignment" Value="Center" />
                        </Style>
                        <Style Selector="DataGridCell">
                            <Setter Property="Foreground" Value="White" />
                            <Setter Property="HorizontalContentAlignment" Value="Left" />
                        </Style>
                    </DataGrid.Styles>
                </DataGrid>

                <!--  加载指示器  -->
                <Grid Background="#80000000" IsVisible="{Binding IsLoading}">
                    <StackPanel
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical"
                        Spacing="10">
                        <ProgressBar
                            Width="100"
                            Height="10"
                            IsIndeterminate="True" />
                        <TextBlock HorizontalAlignment="Center" Text="加载中..." />
                    </StackPanel>
                </Grid>

                <!--  无数据显示  -->
                <Grid Background="Transparent" IsVisible="{Binding !ApiDevices.Count}">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Classes="Second"
                        Text="暂无设备数据" />
                </Grid>
            </Grid>

            <StackPanel
                Grid.Row="2"
                Margin="0,10,0,0"
                HorizontalAlignment="Right"
                Orientation="Horizontal"
                Spacing="5">
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="Second"
                    FontSize="12"
                    Text="{Binding ApiDevices.Count, StringFormat=总计: {0}个设备}" />
            </StackPanel>
        </Grid>
    </Border>
</UserControl>