using EnvizonController.Application.DeviceCommands.Models;

namespace EnvizonController.Application.DeviceCommands.Configuration
{
    /// <summary>
    /// 指令映射配置
    /// </summary>
    public class CommandMappingConfig
    {
        /// <summary>
        /// 预定义指令映射
        /// </summary>
        public Dictionary<string, CommandMapping> PredefinedCommands { get; set; } = new()
        {
            ["pause"] = new CommandMapping
            {
                ProtocolItemName = "运行状态",
                CommandType = CommandType.Write,
                Value = true,
                Aliases = new[] { "暂停", "pause", "hold" }
            },
            ["resume"] = new CommandMapping 
            { 
                ProtocolItemName = "运行状态", 
                CommandType = CommandType.Write,
                Value = true,
                Aliases = new[] { "恢复", "resume", "start", "继续" }
            },
            ["stop"] = new CommandMapping 
            { 
                ProtocolItemName = "运行状态", 
                CommandType = CommandType.Write,
                Value = true,
                Aliases = new[] { "停止", "stop", "halt" }
            },
            ["reset"] = new CommandMapping 
            { 
                ProtocolItemName = "设备复位", 
                CommandType = CommandType.Write,
                Value = true,
                Aliases = new[] { "复位", "reset", "restart" }
            },
            ["emergency_stop"] = new CommandMapping 
            { 
                ProtocolItemName = "紧急停止", 
                CommandType = CommandType.Write,
                Value = true,
                Aliases = new[] { "紧急停止", "emergency", "estop" }
            }
        };
        
        /// <summary>
        /// 智能匹配规则
        /// </summary>
        public List<MatchingRule> SmartMatchingRules { get; set; } = new()
        {
            new MatchingRule { Pattern = @"温度.*设定", TargetProtocolItemName = "设定温度", Priority = 1 },
            new MatchingRule { Pattern = @"湿度.*设定", TargetProtocolItemName = "设定湿度", Priority = 1 },
            new MatchingRule { Pattern = @"压力.*设定", TargetProtocolItemName = "设定压力", Priority = 1 },
            new MatchingRule { Pattern = @".*温度", TargetProtocolItemName = "当前温度", Priority = 2 },
            new MatchingRule { Pattern = @".*湿度", TargetProtocolItemName = "当前湿度", Priority = 2 },
            new MatchingRule { Pattern = @".*压力", TargetProtocolItemName = "当前压力", Priority = 2 }
        };
    }

    /// <summary>
    /// 指令映射
    /// </summary>
    public class CommandMapping
    {
        /// <summary>
        /// 协议项名称
        /// </summary>
        public string ProtocolItemName { get; set; } = string.Empty;
        
        /// <summary>
        /// 指令类型
        /// </summary>
        public CommandType CommandType { get; set; }
        
        /// <summary>
        /// 默认值
        /// </summary>
        public object? Value { get; set; }
        
        /// <summary>
        /// 别名数组
        /// </summary>
        public string[] Aliases { get; set; } = Array.Empty<string>();
        
        /// <summary>
        /// 指令参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 匹配规则
    /// </summary>
    public class MatchingRule
    {
        /// <summary>
        /// 匹配模式（正则表达式）
        /// </summary>
        public string Pattern { get; set; } = string.Empty;
        
        /// <summary>
        /// 目标协议项名称
        /// </summary>
        public string TargetProtocolItemName { get; set; } = string.Empty;
        
        /// <summary>
        /// 优先级（数值越小优先级越高）
        /// </summary>
        public int Priority { get; set; }
    }

    /// <summary>
    /// 重试策略配置
    /// </summary>
    public class RetryPolicyConfig
    {
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;
        
        /// <summary>
        /// 基础延迟时间
        /// </summary>
        public TimeSpan BaseDelay { get; set; } = TimeSpan.FromMilliseconds(100);
        
        /// <summary>
        /// 最大延迟时间
        /// </summary>
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(5);
        
        /// <summary>
        /// 退避乘数
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;
        
        /// <summary>
        /// 可重试的异常类型
        /// </summary>
        public List<Type> RetryableExceptions { get; set; } = new();
    }

    /// <summary>
    /// 断路器配置
    /// </summary>
    public class CircuitBreakerConfig
    {
        /// <summary>
        /// 失败阈值
        /// </summary>
        public int FailureThreshold { get; set; } = 5;
        
        /// <summary>
        /// 开启超时时间
        /// </summary>
        public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(1);
        
        /// <summary>
        /// 半开状态重试次数
        /// </summary>
        public int HalfOpenRetryCount { get; set; } = 3;
    }
} 