using EnvizonController.Application.DataCollection;
using Microsoft.Extensions.DependencyInjection;
using Quartz;

namespace EnvizonController.Application.Extensions;

/// <summary>
/// Quartz服务扩展
/// </summary>
public static class QuartzServiceExtensions
{
    /// <summary>
    /// 添加Quartz设备数据采集服务
    /// </summary>
    public static IServiceCollection AddQuartzDeviceCollection(this IServiceCollection services)
    {
        // 注册Quartz服务
        services.AddQuartz(q =>
        {
            // 使用可序列化的作业数据映射
            q.UseMicrosoftDependencyInjectionJobFactory();
            q.UseSimpleTypeLoader();
            q.UseDefaultThreadPool(tp =>
            {
                tp.MaxConcurrency = 10; // 设置最大并发数
            });
        });

        // 添加Quartz服务
        services.AddQuartzHostedService(options =>
        {
            options.WaitForJobsToComplete = true;
            options.AwaitApplicationStarted = true;
        });

        // 注册设备数据采集服务
        services.AddSingleton<IQuartzDeviceCollectionService, QuartzDeviceCollectionService>();

        return services;
    }
} 