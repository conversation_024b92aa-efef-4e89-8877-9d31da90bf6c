﻿using Avalonia.Controls;

namespace EnvizonController.Presentation.Geometry;

public class GeometryResourceProvider : IGeometryProvider
{
    private readonly ResourceDictionary _resources;

    public GeometryResourceProvider(IResourceDictionary resources)
    {
        _resources = (ResourceDictionary)resources.MergedDictionaries.FirstOrDefault();
    }

    //public GeometryResourceProvider(IResourceDictionary resources,string xamlName)
    //{
    //    _resources = ((ResourceDictionary)resources).MergedDictionaries.OfType< ResourceDictionary>().FirstOrDefault().;
    //}

    public Avalonia.Media.Geometry GetGeometry(string key)
    {
        if (_resources.TryGetValue(key, out var obj))
            if (obj is Avalonia.Media.Geometry geometry)
                return geometry;
        throw new KeyNotFoundException($"Geometry with key '{key}' not found in resources.");
    }
}