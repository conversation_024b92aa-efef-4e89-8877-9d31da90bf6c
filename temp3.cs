using EnvizonController.Application.Devices;

// 测试ParameterMapping类的定义
public class Test
{
    public void TestMethod()
    {
        // 使用ParameterMapping类
        ParameterMapping parameterMapping = new ParameterMapping
        {
            Name = "温度",
            Address = 0,
            Type = "UInt16",
            ScaleFactor = 0.1,
            Offset = 0.0,
            Unit = "°C",
            Description = "环境温度",
            IsWritable = false,
            MinValue = -40.0,
            MaxValue = 100.0
        };
    }
}
