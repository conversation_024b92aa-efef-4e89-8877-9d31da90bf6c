using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Processors.Implementation
{
    /// <summary>
    /// 基础数据处理器
    /// </summary>
    public abstract class BaseDataProcessor : IDataProcessor
    {
        /// <summary>
        /// 获取处理器名称
        /// </summary>
        public string Name { get; }
        
        /// <summary>
        /// 获取处理器描述
        /// </summary>
        public string Description { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="description">处理器描述</param>
        protected BaseDataProcessor(string name, string description = "")
        {
            Name = name;
            Description = description;
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public abstract Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context);
        
        /// <summary>
        /// 判断是否可以处理指定上下文
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>是否可以处理</returns>
        public virtual bool CanProcess(IDataProcessingContext context)
        {
            return context != null && context.RawData != null;
        }
    }
}
