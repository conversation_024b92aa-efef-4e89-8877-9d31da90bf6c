using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 读取线圈状态响应
    /// </summary>
    public class ReadCoilsResponse : ModbusResponse
    {
        /// <summary>
        /// 字节计数
        /// </summary>
        public byte ByteCount { get; private set; }

        /// <summary>
        /// 线圈值
        /// </summary>
        public bool[] CoilValues { get; private set; } = Array.Empty<bool>();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReadCoilsResponse()
        {
            FunctionCode = ModbusFunction.ReadCoils;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 3)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            ByteCount = frame[2];

            // 验证帧长度是否与字节计数匹配
            if (frame.Length < 3 + ByteCount)
                throw new ArgumentException("响应帧长度与字节计数不匹配", nameof(frame));

            // 解析线圈值
            List<bool> coilValues = new List<bool>();
            for (int i = 0; i < ByteCount; i++)
            {
                byte coilByte = frame[3 + i];
                for (int bit = 0; bit < 8; bit++)
                {
                    bool coilValue = (coilByte & (1 << bit)) != 0;
                    coilValues.Add(coilValue);
                }
            }

            CoilValues = coilValues.ToArray();
        }
    }
}
