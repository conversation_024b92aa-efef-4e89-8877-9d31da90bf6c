<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.EnvironmentChartControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AvaloniaLineSeriesChart.Controls;assembly=AvaloniaLineSeriesChart"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="200"
    d:DesignWidth="400"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Border Margin="3,10,3,0" Classes="cyber-noGlow-border card">
        <DockPanel>
            <Panel Margin="0,0,0,16" DockPanel.Dock="Top">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,6,0"
                        VerticalAlignment="Center"
                        Classes="h4 primary font-icon"
                        Text="&#xf201;" />
                    <TextBlock
                        Margin="0"
                        VerticalAlignment="Center"
                        Classes="h4 primary bottom"
                        Text="环境曲线" />
                </StackPanel>
                <ItemsControl
                    Margin="50,0,0,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    ItemsSource="{Binding CurrentDevice.EnvironmentalDataGroupList}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <ToggleButton Name="btn"
                                Height="30"
                                Margin="0,0,5,0"
                                Padding="9,0"
                                VerticalContentAlignment="Center"
                                Classes="glow"
                                Content="{Binding ValueName}"
                                CornerRadius="3"
                                FontSize="12"
                                IsChecked="True"
                                Click="SeriesToggleButton_Click" />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                </ItemsControl>
            </Panel>

            <Border
                Grid.Row="1"
                Padding="9,0"
                Classes="cyber-border card"
                DockPanel.Dock="Bottom">
                <!--  简易曲线图  -->
                <controls:LineChartControl
                    Name="LineChart"
                    Height="210"
                    VerticalAlignment="Stretch" />
            </Border>
        </DockPanel>
    </Border>
</UserControl>
