using System.Text.Json.Serialization;

namespace EnvizonController.Mqtt.Server.Models
{
    public class DeviceStatusNotification
    {
        [JsonPropertyName("deviceId")]
        public string DeviceId { get; set; } = string.Empty;

        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        [JsonPropertyName("ipAddress")]
        public string IpAddress { get; set; } = string.Empty;

        [JsonPropertyName("lastSeen")]
        public DateTime LastSeen { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("properties")]
        public Dictionary<string, string> Properties { get; set; } = new Dictionary<string, string>();
    }
} 