using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 功能开发中提示界面的视图模型
    /// </summary>
    public partial class FeatureUnderDevelopmentViewModel : ViewModelBase
    {
        /// <summary>
        /// 功能名称
        /// </summary>
        [ObservableProperty]
        private string _featureName = string.Empty;

        /// <summary>
        /// 提示消息
        /// </summary>
        [ObservableProperty]
        private string _message = "此功能正在开发中，敬请期待";

        /// <summary>
        /// 预计完成时间
        /// </summary>
        [ObservableProperty]
        private string _estimatedCompletionDate = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [ObservableProperty]
        private DateTime _createdTime = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <param name="message">可选的自定义消息</param>
        /// <param name="estimatedCompletionDate">预计完成时间</param>
        public FeatureUnderDevelopmentViewModel(string featureName, string? message = null, string? estimatedCompletionDate = null)
        {
            FeatureName = featureName;
            if (!string.IsNullOrEmpty(message))
                Message = message;
            EstimatedCompletionDate = estimatedCompletionDate ?? "未定";
        }
    }
} 