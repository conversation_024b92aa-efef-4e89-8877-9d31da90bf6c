using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.DeviceCommands.Examples
{
    /// <summary>
    /// ValueMapping功能使用示例
    /// 展示改进后的命令解析功能如何处理通过ValueMappings建立的关联
    /// </summary>
    public class ValueMappingExample
    {
        private readonly IProtocolCommandResolver _protocolResolver;
        private readonly ILogger<ValueMappingExample> _logger;

        public ValueMappingExample(
            IProtocolCommandResolver protocolResolver,
            ILogger<ValueMappingExample> logger)
        {
            _protocolResolver = protocolResolver;
            _logger = logger;
        }

        /// <summary>
        /// 演示改进后的命令解析功能
        /// </summary>
        public async Task DemonstrateImprovedCommandResolutionAsync()
        {
            var deviceId = 1L;

            _logger.LogInformation("=== ValueMapping功能演示 ===");

            // 示例1：通过ValueMappings查找"暂停"命令
            await DemonstrateValueMappingResolution(deviceId, "暂停");

            // 示例2：通过ValueMappings查找"运行"命令
            await DemonstrateValueMappingResolution(deviceId, "运行");

            // 示例3：通过ValueMappings查找"停止"命令
            await DemonstrateValueMappingResolution(deviceId, "停止");

            // 示例4：获取完整的值映射信息
            await DemonstrateValueMappingInfo(deviceId, "暂停");

            // 示例5：模糊匹配功能
            await DemonstrateFuzzyMatching(deviceId, "暂");

            _logger.LogInformation("=== 演示完成 ===");
        }

        /// <summary>
        /// 演示ValueMapping解析功能
        /// </summary>
        private async Task DemonstrateValueMappingResolution(long deviceId, string commandName)
        {
            _logger.LogInformation("--- 解析命令: {CommandName} ---", commandName);

            try
            {
                // 使用改进后的解析方法
                var protocolItem = await _protocolResolver.ResolveCommandToProtocolItemAsync(deviceId, commandName);

                if (protocolItem != null)
                {
                    _logger.LogInformation("✅ 成功找到协议项:");
                    _logger.LogInformation("  - 协议项名称: {Name}", protocolItem.Name);
                    _logger.LogInformation("  - 显示名称: {DisplayName}", protocolItem.DisplayName);
                    _logger.LogInformation("  - 地址: {Address}", protocolItem.Address);
                    _logger.LogInformation("  - 是否可写: {IsWritable}", protocolItem.IsWritable);

                    // 显示ValueMappings信息
                    if (protocolItem.ValueMappings != null && protocolItem.ValueMappings.Count > 0)
                    {
                        _logger.LogInformation("  - 值映射:");
                        foreach (var mapping in protocolItem.ValueMappings)
                        {
                            var indicator = mapping.Value.Equals(commandName, StringComparison.OrdinalIgnoreCase) ? "👉" : "  ";
                            _logger.LogInformation("    {Indicator} {Key}: {Value}", indicator, mapping.Key, mapping.Value);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("❌ 未找到命令 '{CommandName}' 对应的协议项", commandName);

                    // 获取推荐
                    var recommendations = await _protocolResolver.GetRecommendedProtocolItemsAsync(deviceId, commandName, 3);
                    if (recommendations.Count > 0)
                    {
                        _logger.LogInformation("💡 推荐的协议项:");
                        foreach (var rec in recommendations)
                        {
                            _logger.LogInformation("  - {Name} ({DisplayName})", rec.Name, rec.DisplayName);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析命令时发生错误: {CommandName}", commandName);
            }

            _logger.LogInformation("");
        }

        /// <summary>
        /// 演示获取值映射信息功能
        /// </summary>
        private async Task DemonstrateValueMappingInfo(long deviceId, string commandName)
        {
            _logger.LogInformation("--- 获取值映射信息: {CommandName} ---", commandName);

            try
            {
                var (protocolItem, mappingKey, mappingValue) = await _protocolResolver.GetCommandValueMappingAsync(deviceId, commandName);

                if (protocolItem != null)
                {
                    _logger.LogInformation("✅ 找到值映射信息:");
                    _logger.LogInformation("  - 协议项: {Name}", protocolItem.Name);
                    
                    if (mappingKey.HasValue && !string.IsNullOrEmpty(mappingValue))
                    {
                        _logger.LogInformation("  - 映射键: {Key}", mappingKey.Value);
                        _logger.LogInformation("  - 映射值: {Value}", mappingValue);
                        _logger.LogInformation("  - 说明: 发送命令时应使用值 {Key} 来表示 '{Value}'", mappingKey.Value, mappingValue);
                    }
                    else
                    {
                        _logger.LogInformation("  - 该协议项没有对应的值映射");
                    }
                }
                else
                {
                    _logger.LogWarning("❌ 未找到命令对应的协议项");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取值映射信息时发生错误: {CommandName}", commandName);
            }

            _logger.LogInformation("");
        }

        /// <summary>
        /// 演示模糊匹配功能
        /// </summary>
        private async Task DemonstrateFuzzyMatching(long deviceId, string searchTerm)
        {
            _logger.LogInformation("--- 模糊匹配: {SearchTerm} ---", searchTerm);

            try
            {
                var protocolItem = await _protocolResolver.SmartMatchProtocolItemAsync(deviceId, searchTerm);

                if (protocolItem != null)
                {
                    _logger.LogInformation("✅ 模糊匹配成功:");
                    _logger.LogInformation("  - 协议项: {Name}", protocolItem.Name);

                    // 检查是否通过ValueMappings匹配
                    if (protocolItem.ValueMappings != null)
                    {
                        var matchingValues = protocolItem.ValueMappings.Values
                            .Where(v => v.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                            .ToList();

                        if (matchingValues.Any())
                        {
                            _logger.LogInformation("  - 通过ValueMappings匹配到: {Values}", string.Join(", ", matchingValues));
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("❌ 模糊匹配未找到结果");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模糊匹配时发生错误: {SearchTerm}", searchTerm);
            }

            _logger.LogInformation("");
        }

        /// <summary>
        /// 创建示例协议项数据
        /// </summary>
        public static List<ProtocolItem> CreateSampleProtocolItems()
        {
            return new List<ProtocolItem>
            {
                new ProtocolItem
                {
                    Index = 1,
                    Name = "运行状态",
                    DisplayName = "运行状态",
                    UnitId = 1,
                    Address = 3057,
                    DataType = DataType.Enum,
                    IsWritable = true,
                    GroupName = "运行设置",
                    Description = "设备运行状态控制",
                    ValueMappings = new Dictionary<int, string>
                    {
                        { 0, "停止" },
                        { 1, "运行" },
                        { 2, "暂停" }
                    }
                },
                new ProtocolItem
                {
                    Index = 2,
                    Name = "当前启动模式",
                    DisplayName = "当前启动模式",
                    UnitId = 1,
                    Address = 3012,
                    DataType = DataType.Int16,
                    GroupName = "运行设置",
                    Description = "启动模式选择",
                    ValueMappings = new Dictionary<int, string>
                    {
                        { 0, "复位启动" },
                        { 1, "冷启动" },
                        { 2, "热启动" }
                    }
                },
                new ProtocolItem
                {
                    Index = 3,
                    Name = "报警状态",
                    DisplayName = "报警状态",
                    UnitId = 1,
                    Address = 3100,
                    DataType = DataType.UInt16,
                    GroupName = "状态监控",
                    Description = "设备报警状态位",
                    BitMappings = new Dictionary<int, string>
                    {
                        { 0, "温度报警" },
                        { 1, "压力报警" },
                        { 2, "流量报警" },
                        { 3, "电源报警" }
                    }
                }
            };
        }
    }
}
