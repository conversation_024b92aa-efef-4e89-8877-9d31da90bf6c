using EnvizonController.Domain.Aggregates;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EnvizonController.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 程式表实体配置
    /// </summary>
    public class ProgramConfiguration : IEntityTypeConfiguration<Program>
    {
        public void Configure(EntityTypeBuilder<Program> builder)
        {
            // 表名
            builder.ToTable("Programs");

            // 主键
            builder.HasKey(e => e.Id);

            // 配置自增长主键
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            // 必填属性
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.CycleCount)
                .IsRequired();

            builder.Property(e => e.CycleStart)
                .IsRequired();

            builder.Property(e => e.CycleEnd)
                .IsRequired();

            builder.Property(e => e.CreatedAt)
                .IsRequired();

            builder.Property(e => e.UpdatedAt)
                .IsRequired();

            // 索引
            builder.HasIndex(e => e.Name)
                .IsUnique();

            // 关系配置 - 一对多关系
            builder.HasMany(e => e.Steps)
                .WithOne()
                .HasForeignKey(e => e.ProgramId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}