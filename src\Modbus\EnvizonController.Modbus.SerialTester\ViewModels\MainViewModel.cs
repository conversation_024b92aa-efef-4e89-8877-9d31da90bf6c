using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Adapters.Desktop;
using EnvizonController.Modbus.Client;
using EnvizonController.Modbus.Protocol.Enums;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Media;

namespace EnvizonController.Modbus.SerialTester.ViewModels
{
    /// <summary>
    /// 主视图模型
    /// </summary>
    public partial class MainViewModel : ViewModelBase
    {
        private IModbusChannel? _modbusChannel;
        private IModbusClient? _modbusClient;

        [ObservableProperty] private SerialConfigViewModel _serialConfigViewModel;
        [ObservableProperty] private ModbusTestViewModel _modbusTestViewModel;
        [ObservableProperty] private LogViewModel _logViewModel;

        [ObservableProperty] private string _statusMessage = "就绪";
        [ObservableProperty] private string _connectionStatus = "未连接";
        [ObservableProperty] private string _connectionButtonText = "连接";
        [ObservableProperty] private Brush _connectionButtonBackground = new SolidColorBrush(Colors.Green);

        [ObservableProperty] private ObservableCollection<string> _transportTypes = new()
        {
            "RTU",
            "ASCII",
            "TCP"
        };
        [ObservableProperty] private string _selectedTransportType = "RTU";
        [ObservableProperty] private byte _slaveAddress = 1;
        [ObservableProperty] private int _responseTimeout = 1000;
        [ObservableProperty] private int _retryCount = 3;
        [ObservableProperty] private int _retryDelay = 100;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainViewModel(SerialConfigViewModel serialConfigViewModel, ModbusTestViewModel modbusTestViewModel, LogViewModel logViewModel)
        {
            _serialConfigViewModel = serialConfigViewModel;
            _modbusTestViewModel = modbusTestViewModel;
            _logViewModel = logViewModel;

            // 设置引用关系
            _modbusTestViewModel.MainViewModel = this;
            _logViewModel.MainViewModel = this;
        }

        /// <summary>
        /// 切换连接状态
        /// </summary>
        [RelayCommand]
        private async Task ToggleConnection()
        {
            if (_modbusClient != null && _modbusClient.IsConnected)
            {
                await DisconnectAsync();
            }
            else
            {
                await ConnectAsync();
            }
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        private async Task ConnectAsync()
        {
            try
            {
                StatusMessage = "正在连接...";

                // 创建串口通道
                _modbusChannel = new SerialPortChannel(
                    portName: SerialConfigViewModel.SelectedPort,
                    baudRate: SerialConfigViewModel.SelectedBaudRate,
                    dataBits: SerialConfigViewModel.SelectedDataBits,
                    parity: SerialConfigViewModel.SelectedParity,
                    stopBits: SerialConfigViewModel.SelectedStopBits,
                    readTimeout: ResponseTimeout,
                    writeTimeout: ResponseTimeout);

                // 创建Modbus客户端
                ModbusTransportType transportType = SelectedTransportType switch
                {
                    "RTU" => ModbusTransportType.Rtu,
                    "ASCII" => ModbusTransportType.Ascii,
                    "TCP" => ModbusTransportType.Tcp,
                    _ => ModbusTransportType.Rtu
                };

                _modbusClient = ModbusClientFactory.CreateClient(
                    _modbusChannel,
                    transportType,
                    RetryCount,
                    RetryDelay,
                    ResponseTimeout);

                // 连接到设备
                await _modbusClient.ConnectAsync();

                // 更新UI
                ConnectionStatus = "已连接";
                ConnectionButtonText = "断开连接";
                ConnectionButtonBackground = new SolidColorBrush(Colors.Red);
                StatusMessage = "连接成功";

                // 记录日志
                LogViewModel.AddLog("INFO", "已连接到设备");
                LogViewModel.AddLog("INFO", $"端口: {SerialConfigViewModel.SelectedPort}, 波特率: {SerialConfigViewModel.SelectedBaudRate}, 数据位: {SerialConfigViewModel.SelectedDataBits}, 校验位: {SerialConfigViewModel.SelectedParity}, 停止位: {SerialConfigViewModel.SelectedStopBits}");
                LogViewModel.AddLog("INFO", $"传输类型: {SelectedTransportType}, 从站地址: {SlaveAddress}, 响应超时: {ResponseTimeout}ms, 重试次数: {RetryCount}, 重试延迟: {RetryDelay}ms");

                // 通知测试视图模型
                ModbusTestViewModel.OnConnected(_modbusClient);
            }
            catch (Exception ex)
            {
                StatusMessage = $"连接失败: {ex.Message}";
                LogViewModel.AddLog("ERROR", $"连接失败: {ex.Message}");
                await DisconnectAsync();
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        private async Task DisconnectAsync()
        {
            try
            {
                if (_modbusClient != null)
                {
                    // 通知测试视图模型
                    ModbusTestViewModel.OnDisconnected();

                    // 断开连接
                    await _modbusClient.DisconnectAsync();
                    _modbusClient.Dispose();
                    _modbusClient = null;
                }

                if (_modbusChannel != null)
                {
                    _modbusChannel.Dispose();
                    _modbusChannel = null;
                }

                // 更新UI
                ConnectionStatus = "未连接";
                ConnectionButtonText = "连接";
                ConnectionButtonBackground = new SolidColorBrush(Colors.Green);
                StatusMessage = "已断开连接";

                // 记录日志
                LogViewModel.AddLog("INFO", "已断开连接");
            }
            catch (Exception ex)
            {
                StatusMessage = $"断开连接失败: {ex.Message}";
                LogViewModel.AddLog("ERROR", $"断开连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        [RelayCommand]
        private void SaveConfig()
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "JSON文件 (*.json)|*.json",
                    Title = "保存配置",
                    DefaultExt = "json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var config = new
                    {
                        SerialConfig = new
                        {
                            Port = SerialConfigViewModel.SelectedPort,
                            BaudRate = SerialConfigViewModel.SelectedBaudRate,
                            DataBits = SerialConfigViewModel.SelectedDataBits,
                            Parity = SerialConfigViewModel.SelectedParity.ToString(),
                            StopBits = SerialConfigViewModel.SelectedStopBits.ToString()
                        },
                        ModbusConfig = new
                        {
                            TransportType = SelectedTransportType,
                            SlaveAddress = SlaveAddress,
                            ResponseTimeout = ResponseTimeout,
                            RetryCount = RetryCount,
                            RetryDelay = RetryDelay
                        }
                    };

                    string json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(saveFileDialog.FileName, json);

                    StatusMessage = "配置已保存";
                    LogViewModel.AddLog("INFO", $"配置已保存到: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"保存配置失败: {ex.Message}";
                LogViewModel.AddLog("ERROR", $"保存配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        [RelayCommand]
        private void LoadConfig()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "JSON文件 (*.json)|*.json",
                    Title = "加载配置"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    string json = File.ReadAllText(openFileDialog.FileName);
                    var config = JsonSerializer.Deserialize<dynamic>(json);

                    // 加载串口配置
                    if (config.SerialConfig != null)
                    {
                        SerialConfigViewModel.SelectedPort = config.SerialConfig.Port.ToString();
                        SerialConfigViewModel.SelectedBaudRate = int.Parse(config.SerialConfig.BaudRate.ToString());
                        SerialConfigViewModel.SelectedDataBits = int.Parse(config.SerialConfig.DataBits.ToString());
                        SerialConfigViewModel.SelectedParity = Enum.Parse<System.IO.Ports.Parity>(config.SerialConfig.Parity.ToString());
                        SerialConfigViewModel.SelectedStopBits = Enum.Parse<System.IO.Ports.StopBits>(config.SerialConfig.StopBits.ToString());
                    }

                    // 加载Modbus配置
                    if (config.ModbusConfig != null)
                    {
                        SelectedTransportType = config.ModbusConfig.TransportType.ToString();
                        SlaveAddress = byte.Parse(config.ModbusConfig.SlaveAddress.ToString());
                        ResponseTimeout = int.Parse(config.ModbusConfig.ResponseTimeout.ToString());
                        RetryCount = int.Parse(config.ModbusConfig.RetryCount.ToString());
                        RetryDelay = int.Parse(config.ModbusConfig.RetryDelay.ToString());
                    }

                    StatusMessage = "配置已加载";
                    LogViewModel.AddLog("INFO", $"配置已从: {openFileDialog.FileName} 加载");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载配置失败: {ex.Message}";
                LogViewModel.AddLog("ERROR", $"加载配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        [RelayCommand]
        private void ClearLog()
        {
            LogViewModel.ClearLogs();
            StatusMessage = "日志已清空";
        }

        /// <summary>
        /// 显示关于信息
        /// </summary>
        [RelayCommand]
        private void ShowAbout()
        {
            MessageBox.Show(
                "Modbus串口测试工具\n\n" +
                "版本: 1.0.0\n" +
                "基于EnvizonController.Modbus框架\n\n" +
                "用于测试Modbus串口通信和协议",
                "关于",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
    }
}
