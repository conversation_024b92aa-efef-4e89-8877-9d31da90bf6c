using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Models;
using Microsoft.Extensions.Logging;

namespace EnvizonController.ApiClient.Http.Implementations
{
    /// <summary>
    /// HTTP客户端实现
    /// </summary>
    public class HttpClientWrapper : IHttpClient
    {
        private readonly System.Net.Http.HttpClient _httpClient;
        private readonly DefaultConfig _config;
        private readonly ILogger<HttpClientWrapper> _logger;

        public HttpClientWrapper(System.Net.Http.HttpClient httpClient, DefaultConfig config, ILogger<HttpClientWrapper> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;

            // 配置HttpClient
            _httpClient.BaseAddress = new Uri(_config.ApiClientSettings.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromMilliseconds(_config.ApiClientSettings.TimeoutMs);
        }

        public async Task<Result<T>> GetAsync<T>(string url, IDictionary<string, string> queryParams = null)
        {
            try
            {
                var requestUrl = url;
                if (queryParams != null && queryParams.Count > 0)
                {
                    var queryString = string.Join("&", queryParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
                    requestUrl = $"{url}?{queryString}";
                }

                var response = await _httpClient.GetAsync(requestUrl);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GET请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> PostAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PostAsync(url, jsonContent);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"POST请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> PutAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PutAsync(url, jsonContent);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"PUT请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> DeleteAsync<T>(string url)
        {
            try
            {
                var response = await _httpClient.DeleteAsync(url);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DELETE请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        private async Task<Result<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var data = JsonSerializer.Deserialize<T>(content, options);
                    return Result<T>.Success(data, (int)response.StatusCode);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "响应解析错误");
                    return Result<T>.Failure($"响应解析错误: {ex.Message}", (int)response.StatusCode);
                }
            }
            else
            {
                _logger.LogWarning($"HTTP错误: {(int)response.StatusCode} {response.ReasonPhrase}");
                return Result<T>.Failure($"HTTP错误: {response.ReasonPhrase}", (int)response.StatusCode);
            }
        }
    }
}