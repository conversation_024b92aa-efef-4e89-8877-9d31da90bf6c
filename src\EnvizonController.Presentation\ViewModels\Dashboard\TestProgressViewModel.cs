﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Presentation.Messages;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.ViewModels.Dashboard;

public partial class TestProgressViewModel : ViewModelBase
{
    [ObservableProperty] private string _currentStage = string.Empty;
    [ObservableProperty] private TestStepDTO _currentStep;

    // 测试信息
    [ObservableProperty] private TestRunDTO _currentTest;
    [ObservableProperty] private DateTime _estimatedEndTime = DateTime.MinValue;
    [ObservableProperty] private string _estimatedEndTimeDate = string.Empty;
    [ObservableProperty] private string _estimatedEndTimeTime = string.Empty;
    [ObservableProperty] private double _stageProgressPercentage;
    [ObservableProperty] private string _stageRemainingTime = string.Empty;

    // 时间信息
    [ObservableProperty] private DateTime _startTime = DateTime.MinValue;
    [ObservableProperty] private string _startTimeDate = string.Empty;

    [ObservableProperty] private string _startTimeTime = string.Empty;

    // 基本测试信息
    [ObservableProperty] private string _testName = string.Empty;
    [ObservableProperty] private long _testRunId;
    [ObservableProperty] private TestRunStatus _testStatus = TestRunStatus.NotStarted;

    // 进度信息
    [ObservableProperty] private double _totalProgressPercentage;
    [ObservableProperty] private string _totalRemainingTime = string.Empty;

    public void Update(TestRunDTO testRun, int currentStepIndex = -1,int currentStepDurationSeconds = 0)
    {
        if (testRun == null) return;

        CurrentTest = testRun;
        TestName = testRun.Name;

        // 测试尚未开始
        if (testRun.StartTime == null)
        {
            TestStatus = TestRunStatus.NotStarted;
            return;
        }

        // 设置起始时间
        StartTime = testRun.StartTime.Value;
        StartTimeDate = StartTime.ToString("yyyy-MM-dd");
        StartTimeTime = StartTime.ToString("HH:mm:ss");

        // 计算当前步骤
        var steps = testRun.TestSteps.OrderBy(s => s.StepNumber).ToList();
        if (steps.Count == 0) return;

        // 不再使用DetermineCurrentStepIndex方法推断当前步骤
        // 而是在DeviceItemViewModel中从DeviceDTO的DeviceStateData获取'当前程式步骤'
        // 如果未指定currentStepIndex，则保持为-1，将会在DeviceItemViewModel中根据DeviceStateData更新

        if (currentStepIndex < steps.Count)
        {
            CurrentStep = steps[currentStepIndex];
            CurrentStage = $"阶段{CurrentStep.StepNumber}/{steps.Count}";

            // 计算总体进度
            var totalSeconds = testRun.EstimatedDurationSeconds;
            var totalStepCount = testRun.StepCount;
            var elapsedSeconds = CalculateElapsedSeconds(testRun, currentStepIndex);
            TotalProgressPercentage = Math.Min(100, (double)currentStepIndex / totalStepCount * 100);

            // 计算总剩余时间
            var totalRemainingSeconds = totalSeconds - elapsedSeconds;
            TotalRemainingTime = FormatRemainingTime(totalRemainingSeconds);

            // 计算当前阶段进度
            StageProgressPercentage = CalculateStageProgress(testRun, currentStepIndex,currentStepDurationSeconds);
            StageRemainingTime = CalculateStageRemainingTime(testRun, currentStepIndex, currentStepDurationSeconds);

            // 精确计算预计结束时间：以当前时间为基准，当前步骤剩余时间加上后续步骤时间
            var now = DateTime.Now;
            var remainingCurrentStep = Math.Max(0, CurrentStep.DurationSeconds - currentStepDurationSeconds);
            var remainingStepsSeconds = 0;
            for (var i = currentStepIndex + 1; i < steps.Count; i++)
            {
                remainingStepsSeconds += steps[i].DurationSeconds;
            }
            var estimatedRemainingSeconds = remainingCurrentStep + remainingStepsSeconds;
            EstimatedEndTime = now.AddSeconds(estimatedRemainingSeconds);
            EstimatedEndTimeDate = EstimatedEndTime.ToString("yyyy-MM-dd");
            EstimatedEndTimeTime = EstimatedEndTime.ToString("HH:mm:ss");

            // 确定测试状态
            TestStatus = DetermineTestStatus(testRun);
        }
    }
    public void Clear()
    {
        CurrentStage = string.Empty;
        CurrentStep = null;

        CurrentTest = null;
        EstimatedEndTime = DateTime.MinValue;
        EstimatedEndTimeDate = string.Empty;
        EstimatedEndTimeTime = string.Empty;
        StageProgressPercentage = 0;
        StageRemainingTime = string.Empty;

        StartTime = DateTime.MinValue;
        StartTimeDate = string.Empty;
        StartTimeTime = string.Empty;

        TestName = string.Empty;
        TestRunId = 0;
        TestStatus = TestRunStatus.NotStarted;

        TotalProgressPercentage = 0;
        TotalRemainingTime = string.Empty;
    }

    public void UpdateStatus(TestRunStatus status)
    {
        TestStatus = status;
    }

    // 根据当前时间和起始时间确定当前步骤索引
    private int DetermineCurrentStepIndex(TestRunDTO testRun)
    {
        if (testRun.StartTime == null) return 0;

        var now = DateTime.Now;
        var elapsed = (now - testRun.StartTime.Value).TotalSeconds;

        var accumulatedTime = 0;
        for (var i = 0; i < testRun.TestSteps.Count; i++)
        {
            accumulatedTime += testRun.TestSteps[i].DurationSeconds;
            if (elapsed < accumulatedTime) return i;
        }

        return testRun.TestSteps.Count - 1;
    }

    // 计算已经过去的时间（秒）
    private int CalculateElapsedSeconds(TestRunDTO testRun, int currentStepIndex)
    {
        if (testRun.StartTime == null) return 0;

        var elapsed = 0;

        // 完全完成的步骤时间
        for (var i = 0; i < currentStepIndex; i++) elapsed += testRun.TestSteps[i].DurationSeconds;

        // 当前步骤已经过去的时间
        if (currentStepIndex < testRun.TestSteps.Count)
        {
            var currentStep = testRun.TestSteps[currentStepIndex];
            var stepStartTime = testRun.StartTime.Value.AddSeconds(elapsed);
            var now = DateTime.Now;

            var stepElapsed = (now - stepStartTime).TotalSeconds;
            elapsed += (int)Math.Min(stepElapsed, currentStep.DurationSeconds);
        }

        return elapsed;
    }

    // 计算当前阶段进度
    private double CalculateStageProgress(TestRunDTO testRun, int currentStepIndex, int durationSeconds = 0)
    {
        if (testRun.StartTime == null || currentStepIndex >= testRun.TestSteps.Count) return 0;

        var currentStep = testRun.TestSteps[currentStepIndex];
        var stepDuration = currentStep.DurationSeconds;

        return Math.Min(100, durationSeconds / stepDuration * 100);
    }

    // 计算当前阶段剩余时间
    private string CalculateStageRemainingTime(TestRunDTO testRun, int currentStepIndex, int durationSeconds = 0)
    {
        if (testRun.StartTime == null || currentStepIndex >= testRun.TestSteps.Count) return "00:00";

        var currentStep = testRun.TestSteps[currentStepIndex];
        var stepDuration = currentStep.DurationSeconds;

        // 计算当前步骤的起始时间
        var prevStepsTime = 0;
        for (var i = 0; i < currentStepIndex; i++) prevStepsTime += testRun.TestSteps[i].DurationSeconds;

        var stepStartTime = testRun.StartTime.Value.AddSeconds(prevStepsTime);
        var now = DateTime.Now;

        var stepElapsed = (now - stepStartTime).TotalSeconds;
        var remainingSeconds = Math.Max(0, (int)(stepDuration - durationSeconds));

        return FormatRemainingTime(remainingSeconds, false);
    }

    // 根据测试信息确定测试状态
    private TestRunStatus DetermineTestStatus(TestRunDTO testRun)
    {
        if (testRun.StartTime == null) return TestRunStatus.NotStarted;

        // 根据Status字段确定状态
        switch (testRun.Status.ToLower())
        {
            case "running":
                return TestRunStatus.Running;
            case "paused":
                return TestRunStatus.Paused;
            case "completed":
                return TestRunStatus.Completed;
            case "cancelled":
                return TestRunStatus.Cancelled;
            case "error":
                return TestRunStatus.Error;
            default:
                // 如果状态未知，根据时间判断
                var totalDuration = testRun.EstimatedDurationSeconds;
                var elapsed = (DateTime.Now - testRun.StartTime.Value).TotalSeconds;
                return elapsed >= totalDuration ? TestRunStatus.Completed : TestRunStatus.Running;
        }
    }

    // 格式化剩余时间
    private string FormatRemainingTime(int seconds, bool includeHours = true)
    {
        if (seconds <= 0) return "00:00";

        var hours = seconds / 3600;
        seconds %= 3600;
        var minutes = seconds / 60;
        seconds %= 60;

        if (includeHours && hours > 0) return $"{hours}h {minutes}m";

        return $"{minutes:D2}:{seconds:D2}";
    }
}