using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using EnvizonController.DataProcessing.Implementation;
using EnvizonController.DataProcessing.Processors.Implementation;
using EnvizonController.DataPush;
using Microsoft.Extensions.Logging;

namespace EnvizonController.DataProcessing.Examples
{
    /// <summary>
    /// 数据处理示例
    /// </summary>
    public class DataProcessingExample
    {
        private readonly IDataProcessingService _processingService;
        private readonly DataProcessingCoordinator _coordinator;
        private readonly ILogger<DataProcessingExample> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="processingService">数据处理服务</param>
        /// <param name="coordinator">数据处理协调器</param>
        /// <param name="logger">日志记录器</param>
        public DataProcessingExample(
            IDataProcessingService processingService,
            DataProcessingCoordinator coordinator,
            ILogger<DataProcessingExample> logger)
        {
            _processingService = processingService;
            _coordinator = coordinator;
            _logger = logger;
        }
        
        /// <summary>
        /// 初始化示例
        /// </summary>
        public async Task InitializeAsync()
        {
            await _coordinator.InitializeAsync();
            
            // 创建并注册处理管道
            RegisterSensorDataPipeline();
            RegisterTemperatureAlertPipeline();
            
            _logger.LogInformation("数据处理示例已初始化");
        }
        
        /// <summary>
        /// 注册传感器数据处理管道
        /// </summary>
        private void RegisterSensorDataPipeline()
        {
            // 创建处理管道
            var pipeline = new BaseDataProcessingPipeline("SensorDataPipeline", "传感器数据处理管道");
            
            // 添加处理器
            pipeline.AddProcessor(new JsonTransformer("JsonTransformer", "JSON转换器", data => 
            {
                // 将传感器数据转换为JSON字符串
                return JsonSerializer.Serialize(data);
            }));
            
            // 注册管道
            _processingService.RegisterPipeline("SensorDataPipeline", pipeline);
            _logger.LogInformation("已注册传感器数据处理管道");
        }
        
        /// <summary>
        /// 注册温度告警处理管道
        /// </summary>
        private void RegisterTemperatureAlertPipeline()
        {
            // 创建处理管道
            var pipeline = new BaseDataProcessingPipeline("TemperatureAlertPipeline", "温度告警处理管道");
            
            // 添加处理器
            pipeline.AddProcessor(new ThresholdFilter("TemperatureFilter", "温度过滤器", data => 
            {
                // 检查温度是否超过阈值
                if (data is SensorData sensorData)
                {
                    return sensorData.Temperature > 30;
                }
                return false;
            }));
            
            pipeline.AddProcessor(new JsonTransformer("AlertTransformer", "告警转换器", data => 
            {
                // 将传感器数据转换为告警消息
                if (data is SensorData sensorData)
                {
                    return new
                    {
                        Type = "TemperatureAlert",
                        DeviceId = sensorData.DeviceId,
                        Temperature = sensorData.Temperature,
                        Timestamp = DateTime.Now,
                        Message = $"设备 {sensorData.DeviceId} 温度过高: {sensorData.Temperature}°C"
                    };
                }
                return data;
            }));
            
            // 注册管道
            _processingService.RegisterPipeline("TemperatureAlertPipeline", pipeline);
            _logger.LogInformation("已注册温度告警处理管道");
        }
        
        /// <summary>
        /// 处理传感器数据
        /// </summary>
        /// <param name="sensorData">传感器数据</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessSensorDataAsync(SensorData sensorData)
        {
            // 处理数据并推送
            var result = await _coordinator.ProcessAndPushAsync(
                "SensorDataPipeline",
                sensorData,
                "sensors/data",
                MessageType.CollectedData);
            
            return result;
        }
        
        /// <summary>
        /// 处理温度告警
        /// </summary>
        /// <param name="sensorData">传感器数据</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessTemperatureAlertAsync(SensorData sensorData)
        {
            // 处理数据并推送
            var result = await _coordinator.ProcessAndPushAsync(
                "TemperatureAlertPipeline",
                sensorData,
                "alerts/temperature",
                MessageType.Alert);
            
            return result;
        }
    }
    
    /// <summary>
    /// 传感器数据
    /// </summary>
    public class SensorData
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// 温度
        /// </summary>
        public double Temperature { get; set; }
        
        /// <summary>
        /// 湿度
        /// </summary>
        public double Humidity { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
