using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.Presentation.Services
{
    /// <summary>
    /// 服务扩展类
    /// </summary>
    public static class ServiceExtensions
    {
        /// <summary>
        /// 添加设备服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDeviceServices(this IServiceCollection services)
        {
            // 注册模拟设备服务
            // 注意：在实际项目中，可以根据配置决定使用真实API实现或模拟实现
            services.AddSingleton<IDeviceService, MockDeviceService>();
            
            return services;
        }
    }
}