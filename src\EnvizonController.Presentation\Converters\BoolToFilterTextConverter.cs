using Avalonia.Data.Converters;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为筛选按钮文本
    /// </summary>
    public class BoolToFilterTextConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为筛选按钮文本
        /// </summary>
        /// <param name="value">是否已展开筛选面板</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>若true则返回"收起筛选"，否则返回"高级筛选"</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not bool isOpen) return "高级筛选";

            return isOpen ? "收起筛选" : "高级筛选";
        }

        /// <summary>
        /// 将筛选按钮文本转换回布尔值（不支持）
        /// </summary>
        /// <param name="value">筛选按钮文本</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 