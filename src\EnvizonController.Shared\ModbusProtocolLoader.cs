﻿using OfficeOpenXml;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;

public class ModbusProtocolLoader
{
    public List<ModbusRegisterDefinition> LoadDefinitions(string excelFilePath, string sheetName)
    {
        var definitions = new List<ModbusRegisterDefinition>();
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // Or your commercial license

        using (var package = new ExcelPackage(new FileInfo(excelFilePath)))
        {
            var worksheet = package.Workbook.Worksheets[sheetName];
            if (worksheet == null)
                throw new FileNotFoundException($"Sheet '{sheetName}' not found in '{excelFilePath}'.");

            // Assuming first row is header
            for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
            {
                try
                {
                    var def = new ModbusRegisterDefinition
                    {
                        // Parse Address: Handle 40001 style or direct 0-based.
                        // For simplicity, let's assume Excel has 0-based numeric address for Holding/Input,
                        // and direct address for Coil/Discrete. Or adjust parsing logic.
                        // This example expects a numeric, 0-based address for Holding/Input registers.
                        Address = Convert.ToUInt16(worksheet.Cells[row, 1].GetValue<string>()), // Or GetValue<ushort>()
                        RegisterType = Enum.Parse<ModbusRegisterType>(worksheet.Cells[row, 2].GetValue<string>()),
                        Description = worksheet.Cells[row, 3].GetValue<string>(),
                        DataType = Enum.Parse<ModbusDataType>(worksheet.Cells[row, 4].GetValue<string>()),
                        LengthInRegisters = worksheet.Cells[row, 5].GetValue<int?>() ?? 1,
                        ScaleFactor = worksheet.Cells[row, 8].GetValue<double?>() ?? 1.0,
                        Unit = worksheet.Cells[row, 9].GetValue<string>(),
                        ReadWrite = worksheet.Cells[row, 10].GetValue<string>()
                    };

                    // Parse ValueMapping (e.g., "1:Start;2:Pause")
                    string valueMappingStr = worksheet.Cells[row, 6].GetValue<string>();
                    if (!string.IsNullOrWhiteSpace(valueMappingStr))
                    {
                        def.ValueMappings = valueMappingStr.Split(';')
                            .Select(pair => pair.Split(':'))
                            .Where(parts => parts.Length == 2)
                            .ToDictionary(parts => int.Parse(parts[0]), parts => parts[1]);
                    }

                    // Parse BitMapping (e.g., "0:Run;1:Stop")
                    string bitMappingStr = worksheet.Cells[row, 7].GetValue<string>();
                    if (!string.IsNullOrWhiteSpace(bitMappingStr))
                    {
                        def.BitMappings = bitMappingStr.Split(';')
                            .Select(pair => pair.Split(':'))
                            .Where(parts => parts.Length == 2)
                            .ToDictionary(parts => int.Parse(parts[0]), parts => parts[1]);
                    }

                    string byteOrderStr = worksheet.Cells[row, 11].GetValue<string>();
                    if (!string.IsNullOrWhiteSpace(byteOrderStr) && Enum.TryParse<ByteOrderType>(byteOrderStr, true, out var bo))
                    {
                        def.ByteOrder = bo;
                    }

                    definitions.Add(def);
                }
                catch (Exception ex)
                {
                    // Log error for this row and continue or throw
                    Console.WriteLine($"Error parsing row {row}: {ex.Message}");
                }
            }
        }
        return definitions;
    }
}