using EnvizonController.Domain.Aggregates;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 测试项仓储接口
    /// </summary>
    public interface ITestItemRepository : IRepository<TestRun, long>
    {
        /// <summary>
        /// 获取分页的测试项列表（支持多参数查询）
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="searchText">搜索文本（名称或描述包含此文本）</param>
        /// <param name="name">测试项名称（可选）</param>
        /// <param name="status">测试状态（可选）</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <param name="deviceId">设备ID（可选）</param>
        /// <returns>元组(测试项列表, 总条数)</returns>
        Task<(IEnumerable<TestRun> TestItems, int TotalCount)> GetPagedTestItemsAsync(
            int page,
            int pageSize,
            string searchText = null,
            string name = null,
            string status = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            long? deviceId = null);
            
        /// <summary>
        /// 根据名称查找测试项
        /// </summary>
        /// <param name="name">测试项名称</param>
        /// <returns>测试项列表</returns>
        Task<IEnumerable<TestRun>> FindByNameAsync(string name);

        /// <summary>
        /// 根据设备ID获取测试项
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>与设备关联的测试项列表</returns>
        Task<IEnumerable<TestRun>> GetByDeviceIdAsync(long deviceId);

        /// <summary>
        /// 获取设备的最新运行中测试项
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备的最新运行中测试项，如果没有则返回null</returns>
        Task<TestRun?> GetLatestRunningTestItemByDeviceIdAsync(long deviceId);
        
    
    }
}
