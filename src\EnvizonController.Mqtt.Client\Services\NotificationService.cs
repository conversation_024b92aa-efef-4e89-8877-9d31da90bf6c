using Serilog;

namespace EnvizonController.Mqtt.Client.Services
{
    public class NotificationService : INotificationService
    {
        private readonly Dictionary<Type, List<object>> _handlers = new();
        private readonly ILogger _logger;

        public NotificationService(ILogger logger)
        {
            _logger = logger;
        }

        public void Subscribe<T>(Action<T> handler) where T : class
        {
            var type = typeof(T);
            if (!_handlers.ContainsKey(type))
            {
                _handlers[type] = new List<object>();
            }
            
            _handlers[type].Add(handler);
            _logger.Debug($"已订阅通知: {type.Name}");
        }

        public void Unsubscribe<T>(Action<T> handler) where T : class
        {
            var type = typeof(T);
            if (_handlers.ContainsKey(type))
            {
                _handlers[type].Remove(handler);
                _logger.Debug($"已取消订阅通知: {type.Name}");
                
                if (_handlers[type].Count == 0)
                {
                    _handlers.Remove(type);
                }
            }
        }

        public void Publish<T>(T notification) where T : class
        {
            var type = typeof(T);
            if (_handlers.ContainsKey(type))
            {
                foreach (var handler in _handlers[type].Cast<Action<T>>())
                {
                    try
                    {
                        handler(notification);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"处理通知时发生错误: {type.Name}");
                    }
                }
            }
        }
    }
} 