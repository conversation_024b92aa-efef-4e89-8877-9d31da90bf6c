namespace EnvizonController.Modbus.Abstractions.Enums
{
    /// <summary>
    /// 校验位模式
    /// </summary>
    public enum Parity
    {
        /// <summary>
        /// 无校验
        /// </summary>
        None = 0,

        /// <summary>
        /// 奇校验
        /// </summary>
        Odd = 1,

        /// <summary>
        /// 偶校验
        /// </summary>
        Even = 2,

        /// <summary>
        /// 标记校验
        /// </summary>
        Mark = 3,

        /// <summary>
        /// 空格校验
        /// </summary>
        Space = 4
    }

    /// <summary>
    /// 停止位模式
    /// </summary>
    public enum StopBits
    {
        /// <summary>
        /// 无停止位
        /// </summary>
        None = 0,

        /// <summary>
        /// 1位停止位
        /// </summary>
        One = 1,

        /// <summary>
        /// 1.5位停止位
        /// </summary>
        OnePointFive = 2,

        /// <summary>
        /// 2位停止位
        /// </summary>
        Two = 3
    }
} 