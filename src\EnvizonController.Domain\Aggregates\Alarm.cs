﻿using System.ComponentModel.DataAnnotations.Schema;
using EnvizonController.Domain.Common;
using EnvizonController.Domain.Enums;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Domain.Aggregates;

[Table("Alarms")] // 指定数据库中的表名
public class Alarm : BaseEntity<long>
{
    public long? TestId { get; set; }
    public long DeviceId { get; set; }
    public long? ProtocolId { get; set; }
    public int? ProtocolItemIndex { get; set; }
    public string Name { get; set; } = null!;

    public string Message { get; set; } = null!;
    public int Level { get; set; } = (int)AlarmSeverity.High;

    public int Status { get; set; } = (int)AlarmStatus.Active; // 默认为活动状态
    public string? ProcessedBy { get; set; }

    #region Time

    /// <summary>
    ///     报警事件发生时的时间戳。
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    ///     此报警记录最后更新的时间戳。
    ///     可用于跟踪记录的修改。
    /// </summary>
    public DateTime? LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    ///     报警被用户手动清除的时间戳。
    ///     这通常在报警已解决或不再相关时发生。
    ///     如果尚未清除，则为 Null。
    /// </summary>
    public DateTime? ProcessedAt { get; set; }

    #endregion


    public static Alarm AddAlarm(string type, string message, long deviceId, long? testId = null, long? protocolId = null, int? protocolItemIndex = null)
    {
        var alarm = new Alarm
        {
            Name = type,
            Message = message,
            DeviceId = deviceId,
            TestId = testId,
            ProtocolId = protocolId,
            ProtocolItemIndex = protocolItemIndex,

            Timestamp = DateTime.Now,
            LastUpdated = DateTime.Now,
            Status = (int)AlarmStatus.Active
        };
        return alarm;
    }

    /// <summary>
    /// 处理报警
    /// </summary>
    /// <param name="processedBy">处理人</param>
    public void Process(string processedBy)
    {
        Status = (int)AlarmStatus.Processed;
        ProcessedBy = processedBy;
        ProcessedAt = DateTime.Now;
        LastUpdated = DateTime.Now;
    }
}
