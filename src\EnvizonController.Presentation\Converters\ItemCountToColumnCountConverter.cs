﻿using System.Globalization;
using Avalonia.Data.Converters;

namespace EnvizonController.Presentation.Converters;

public class ItemCountToColumnCountConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is int count) return count <= 1 ? 1 : 2;
        // 默认情况或当value不是int时（例如ItemsSource为null，ItemCount可能为0）
        // 如果只有0个项目，显示1列（空的）也是合理的
        return 1;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException("ConvertBack is not supported for this converter.");
    }
}