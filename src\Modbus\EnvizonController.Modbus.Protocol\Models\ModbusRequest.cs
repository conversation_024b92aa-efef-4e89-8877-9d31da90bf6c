using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// Modbus 请求基类
    /// </summary>
    public abstract class ModbusRequest
    {
        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveAddress { get; set; }

        /// <summary>
        /// 功能码
        /// </summary>
        public ModbusFunction FunctionCode { get; set; }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public abstract byte[] GetData();

        /// <summary>
        /// 获取请求的完整帧（包括地址和功能码）
        /// </summary>
        /// <returns>完整的请求帧字节数组</returns>
        public byte[] GetFrame()
        {
            var data = GetData();
            var frame = new byte[data.Length + 2];
            frame[0] = SlaveAddress;
            frame[1] = (byte)FunctionCode;
            Array.Copy(data, 0, frame, 2, data.Length);
            return frame;
        }
    }
}
