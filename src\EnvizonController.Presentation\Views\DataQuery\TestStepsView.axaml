<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQuery.TestStepsView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="600"
    d:DesignWidth="800"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">

    <Grid Margin="15" RowDefinitions="Auto,*,Auto">
        <!--  顶部标题和刷新按钮  -->
        <Grid
            Grid.Row="0"
            Margin="0,0,0,15"
            ColumnDefinitions="*,Auto">
            <TextBlock
                Grid.Column="0"
                Classes="h3 glow-primary font-cyber"
                Text="测试步骤列表" />

            <Button
                Grid.Column="1"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#0DF0FF"
                BorderThickness="1"
                Command="{Binding RefreshTestStepsCommand}"
                CornerRadius="4">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon primary"
                        Text="&#xf021;" />
                    <TextBlock Classes="primary font-cyber" Text="刷新步骤" />
                </StackPanel>
            </Button>
        </Grid>

        <!--  步骤列表  -->
        <DataGrid
            Grid.Row="1"
            Margin="0,0,0,10"
            AutoGenerateColumns="False"
            Background="Transparent"
            BorderBrush="#2A2942"
            BorderThickness="1"
            CanUserReorderColumns="False"
            CanUserResizeColumns="True"
            CanUserSortColumns="False"
            GridLinesVisibility="All"
            IsReadOnly="True"
            ItemsSource="{Binding TestSteps}"
            SelectedItem="{Binding SelectedTestStep}"
            SelectionMode="Single">
            <DataGrid.Styles>
                <Style Selector="DataGridRow:selected">
                    <Setter Property="Background" Value="#2A2942" />
                    <Setter Property="BorderBrush" Value="#0DF0FF" />
                    <Setter Property="BorderThickness" Value="1" />
                </Style>
                <Style Selector="DataGridCell">
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="Padding" Value="10,8" />
                </Style>
                <Style Selector="DataGridColumnHeader">
                    <Setter Property="Background" Value="#14131C" />
                    <Setter Property="Foreground" Value="#FFFFFF" />
                    <Setter Property="Padding" Value="10,8" />
                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                    <Setter Property="BorderBrush" Value="#2A2942" />
                </Style>
            </DataGrid.Styles>
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Index}"
                    Header="NO"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Width="*" Header="温度">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Temperature, StringFormat={}{0}°C}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F1"
                                Increment="0.5"
                                Maximum="150"
                                Minimum="-40"
                                Value="{Binding Temperature, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="湿度">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Humidity, StringFormat={}{0}%}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F0"
                                Increment="1"
                                Maximum="100"
                                Minimum="0"
                                Value="{Binding Humidity, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="持续时间">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Duration, StringFormat={}{0}秒}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F0"
                                Increment="60"
                                Maximum="86400"
                                Minimum="0"
                                Value="{Binding Duration, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="线性变化">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox
                                HorizontalAlignment="Center"
                                IsChecked="{Binding IsLinear}"
                                IsEnabled="False" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsLinear, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl> 