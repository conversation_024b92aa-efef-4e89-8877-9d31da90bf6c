﻿using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Avalonia.Markup.Xaml.Templates;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.DataTemplates;

public class AlarmDTOTemplateSelector : IDataTemplate
{
    public DataTemplate ErrorTemplate { get; set; }
    public DataTemplate WarningTemplate { get; set; }
    public DataTemplate InfoTemplate { get; set; }
    public DataTemplate ResolvedTemplate { get; set; }

    public Control? Build(object? param)
    {
        if (param is AlarmDTO alarmItem)
        {
            // 首先检查报警状态
            if (alarmItem.Status == AlarmStatus.Processed)
                return ResolvedTemplate?.Build(param) ?? InfoTemplate.Build(param);

            // 活跃的报警按级别区分
            return alarmItem.Level switch
            {
                AlarmSeverity.High => ErrorTemplate.Build(param),
                AlarmSeverity.Medium => WarningTemplate.Build(param),
                _ => InfoTemplate.Build(param)
            };
        }

        throw new ArgumentNullException(nameof(param));
    }

    public bool Match(object? data)
    {
        return data is AlarmDTO;
    }
}