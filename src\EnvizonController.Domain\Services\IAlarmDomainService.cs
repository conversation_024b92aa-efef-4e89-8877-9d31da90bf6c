using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Domain.Services;

/// <summary>
///     报警领域服务接口
///     处理报警相关的业务逻辑
/// </summary>
public interface IAlarmDomainService
{
    /// <summary>
    ///     创建新的报警
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="name">报警名称</param>
    /// <param name="message">报警消息</param>
    /// <param name="level">报警级别</param>
    /// <returns>创建的报警，如果测试不在运行中则返回null</returns>
    Task<Alarm?> CreateAlarmAsync(long testRunId, string name, string message,
        AlarmSeverity level = AlarmSeverity.High);

    /// <summary>
    ///     处理报警
    /// </summary>
    /// <param name="alarmId">报警ID</param>
    /// <param name="processedBy">处理人</param>
    /// <returns>更新后的报警</returns>
    Task<Alarm> ProcessAlarmAsync(long alarmId, string processedBy);
}