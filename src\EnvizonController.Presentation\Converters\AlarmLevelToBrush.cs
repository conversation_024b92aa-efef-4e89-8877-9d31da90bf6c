using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Converters
{
    public class AlarmLevelToBrush : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is AlarmLevel level)
            {
                return level switch
                {
                    AlarmLevel.Critical => new SolidColorBrush(Color.Parse("#2A0E17")),  // 深红色背景
                    AlarmLevel.Warning => new SolidColorBrush(Color.Parse("#251713")),   // 琥珀色背景
                    AlarmLevel.Notice => new SolidColorBrush(Color.Parse("#0F1A25")),    // 蓝色背景
                    _ => new SolidColorBrush(Color.Parse("#161A23"))                     // 默认深灰色背景
                };
            }
            else if (value is string strLevel)
            {
                return strLevel switch
                {
                    "高" => new SolidColorBrush(Color.Parse("#2A0E17")),  // 深红色背景
                    "中" => new SolidColorBrush(Color.Parse("#251713")),  // 琥珀色背景
                    "低" => new SolidColorBrush(Color.Parse("#0F1A25")),  // 蓝色背景
                    _ => new SolidColorBrush(Color.Parse("#161A23"))      // 默认深灰色背景
                };
            }
            
            return new SolidColorBrush(Color.Parse("#161A23"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 