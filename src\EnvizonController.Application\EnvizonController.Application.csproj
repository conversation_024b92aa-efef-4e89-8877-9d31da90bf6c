﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DataCollection\DataCollectionTaskScheduler.cs" />
    <Compile Remove="DataCollection\IDataCollectionTaskScheduler.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Domain\EnvizonController.Domain.csproj" />
    <ProjectReference Include="..\EnvizonController.Configuration\EnvizonController.Configuration.csproj" />
    <ProjectReference Include="..\EnvizonController.Mqtt.Server\EnvizonController.Mqtt.Server.csproj" />
    <ProjectReference Include="..\EnvizonController.Shared\EnvizonController.Shared.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Abstractions\EnvizonController.Modbus.Abstractions.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Client\EnvizonController.Modbus.Client.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Protocol\EnvizonController.Modbus.Protocol.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Services\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

</Project>
