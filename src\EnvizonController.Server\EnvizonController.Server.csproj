﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DTOs\**" />
    <Content Remove="DTOs\**" />
    <EmbeddedResource Remove="DTOs\**" />
    <None Remove="DTOs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0-preview.3.24173.13" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Application\EnvizonController.Application.csproj" />
    <ProjectReference Include="..\EnvizonController.Domain\EnvizonController.Domain.csproj" />
    <ProjectReference Include="..\EnvizonController.Infrastructure\EnvizonController.Infrastructure.csproj" />
    <ProjectReference Include="..\EnvizonController.Mqtt.Server\EnvizonController.Mqtt.Server.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Adapters.Desktop\EnvizonController.Modbus.Adapters.Desktop.csproj" />
  </ItemGroup>

</Project>
