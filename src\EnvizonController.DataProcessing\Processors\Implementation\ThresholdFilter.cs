using System;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Processors.Implementation
{
    /// <summary>
    /// 阈值过滤器
    /// </summary>
    public class ThresholdFilter : BaseDataProcessor, IDataFilter
    {
        /// <summary>
        /// 过滤条件
        /// </summary>
        public Func<object, bool> FilterCondition { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="description">处理器描述</param>
        /// <param name="filterCondition">过滤条件</param>
        public ThresholdFilter(string name, string description, Func<object, bool> filterCondition)
            : base(name, description)
        {
            FilterCondition = filterCondition ?? throw new ArgumentNullException(nameof(filterCondition));
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public override Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            try
            {
                // 应用过滤条件
                bool passFilter = FilterCondition(context.RawData);
                
                if (passFilter)
                {
                    // 通过过滤，继续处理
                    context.ProcessedData = context.RawData;
                }
                else
                {
                    // 未通过过滤，跳过后续处理
                    context.Status = ProcessingStatus.Skipped;
                    context.AddError($"数据未通过过滤器 {Name} 的条件检查");
                }
                
                return Task.FromResult(context);
            }
            catch (Exception ex)
            {
                context.Status = ProcessingStatus.Failed;
                context.AddError($"过滤器 {Name} 处理时出错: {ex.Message}");
                return Task.FromResult(context);
            }
        }
    }
}
