using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using System.IO.Ports;

namespace EnvizonController.Modbus.SerialTester.ViewModels
{
    /// <summary>
    /// 串口配置视图模型
    /// </summary>
    public partial class SerialConfigViewModel : ViewModelBase
    {
        [ObservableProperty] private ObservableCollection<string> _availablePorts = new();
        [ObservableProperty] private string _selectedPort = string.Empty;

        [ObservableProperty] private ObservableCollection<int> _baudRates = new()
        {
            1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200
        };
        [ObservableProperty] private int _selectedBaudRate = 9600;

        [ObservableProperty] private ObservableCollection<int> _dataBits = new()
        {
            7, 8
        };
        [ObservableProperty] private int _selectedDataBits = 8;

        [ObservableProperty] private ObservableCollection<Parity> _parities = new()
        {
            Parity.None, Parity.Odd, Parity.Even, Parity.Mark, Parity.Space
        };
        [ObservableProperty] private Parity _selectedParity = Parity.None;

        [ObservableProperty] private ObservableCollection<StopBits> _stopBits = new()
        {
            System.IO.Ports.StopBits.One, System.IO.Ports.StopBits.OnePointFive, System.IO.Ports.StopBits.Two
        };
        [ObservableProperty] private StopBits _selectedStopBits = System.IO.Ports.StopBits.One;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SerialConfigViewModel()
        {
            RefreshPorts();
        }

        /// <summary>
        /// 刷新可用端口列表
        /// </summary>
        public void RefreshPorts()
        {
            AvailablePorts.Clear();
            
            string[] ports = SerialPort.GetPortNames();
            foreach (string port in ports)
            {
                AvailablePorts.Add(port);
            }

            if (AvailablePorts.Count > 0)
            {
                SelectedPort = AvailablePorts[0];
            }
        }
    }
}
