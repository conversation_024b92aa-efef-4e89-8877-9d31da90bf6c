using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 数据点API服务接口
    /// </summary>
    public interface IDataPointApiService : IApiService
    {
        /// <summary>
        /// 获取测试运行的数据点
        /// </summary>
        Task<Result<PagedResultDto<DataPointDto>>> GetTestRunDataPointsAsync(long testId, int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取设备的数据点
        /// </summary>
        Task<Result<PagedResultDto<DataPointDto>>> GetDeviceDataPointsAsync(long deviceId, DateTime? startTime, DateTime? endTime, int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取数据点详情
        /// </summary>
        Task<Result<DataPointDto>> GetDataPointAsync(long id);
    }
}