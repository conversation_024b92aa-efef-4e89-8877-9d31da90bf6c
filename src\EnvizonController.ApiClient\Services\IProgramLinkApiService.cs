using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using System.Collections.Generic;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 程式链接API服务接口
    /// </summary>
    public interface IProgramLinkApiService : IApiService
    {
        /// <summary>
        /// 获取所有程式链接
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>程式链接分页结果</returns>
        Task<Result<PagedResultDto<ProgramLinkDTO>>> GetProgramLinksAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取程式链接
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>程式链接详情</returns>
        Task<Result<ProgramLinkDTO>> GetProgramLinkAsync(long id);

        /// <summary>
        /// 根据名称获取程式链接
        /// </summary>
        /// <param name="name">程式链接名称</param>
        /// <returns>程式链接详情</returns>
        Task<Result<ProgramLinkDTO>> GetProgramLinkByNameAsync(string name);

        /// <summary>
        /// 创建程式链接
        /// </summary>
        /// <param name="programLinkDto">程式链接信息</param>
        /// <returns>创建的程式链接</returns>
        Task<Result<ProgramLinkDTO>> CreateProgramLinkAsync(ProgramLinkDTO programLinkDto);

        /// <summary>
        /// 更新程式链接
        /// </summary>
        /// <param name="programLinkDto">更新的程式链接信息</param>
        /// <returns>更新后的程式链接</returns>
        Task<Result<ProgramLinkDTO>> UpdateProgramLinkAsync(ProgramLinkDTO programLinkDto);

        /// <summary>
        /// 删除程式链接
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>操作结果</returns>
        Task<Result<bool>> DeleteProgramLinkAsync(long id);

        /// <summary>
        /// 获取程式链接中的所有链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <returns>链接项列表</returns>
        Task<Result<IEnumerable<ProgramLinkStepDTO>>> GetProgramLinkItemsAsync(long programLinkId);

        /// <summary>
        /// 添加程式到链接中
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="programId">程式ID</param>
        /// <param name="order">执行顺序（可选）</param>
        /// <returns>添加的链接项</returns>
        Task<Result<ProgramLinkStepDTO>> AddProgramToProgramLinkAsync(long programLinkId, long programId, int? order = null);

        /// <summary>
        /// 从程式链接中移除链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="linkItemId">链接项ID</param>
        /// <returns>操作结果</returns>
        Task<Result<bool>> RemoveLinkItemAsync(long programLinkId, long linkItemId);

        /// <summary>
        /// 调整程式在链接中的执行顺序
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="linkItemId">链接项ID</param>
        /// <param name="newOrder">新的执行顺序</param>
        /// <returns>更新后的链接项</returns>
        Task<Result<ProgramLinkStepDTO>> ReorderLinkItemAsync(long programLinkId, long linkItemId, int newOrder);

        /// <summary>
        /// 批量新增或更新程式链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="items">要新增或更新的链接项集合</param>
        /// <returns>操作结果</returns>
        Task<Result<bool>> BulkUpsertProgramLinkStepItemsAsync(long programLinkId, List<ProgramLinkItemUpsertDto> items);
    }
}