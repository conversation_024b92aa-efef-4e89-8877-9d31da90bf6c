using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using DomainEnums = EnvizonController.Domain.Enums;
using SharedEnums = EnvizonController.Shared.Enums;

namespace EnvizonController.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AlarmsController : ControllerBase
    {
        private readonly IAlarmAppService _alarmService;
        private readonly ILogger<AlarmsController> _logger;

        public AlarmsController(IAlarmAppService alarmService, ILogger<AlarmsController> logger)
        {
            _alarmService = alarmService;
            _logger = logger;
        }

        /// <summary>
        /// 获取报警列表
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>报警列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<AlarmDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<AlarmDTO>>> GetAlarms([FromQuery] AlarmQueryParams queryParams)
        {
            try
            {
                // 跳过分页之前的项
                var pagedAlarms = await _alarmService.GetAlarmsAsync(queryParams);

                return Ok(pagedAlarms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报警列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取报警列表时发生错误");
            }
        }

        /// <summary>
        /// 根据ID获取报警
        /// </summary>
        /// <param name="id">报警ID</param>
        /// <returns>报警详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(AlarmDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<AlarmDTO>> GetAlarm(string id)
        {
            // 注意：这里需要补充实现获取单个报警的方法
            // 由于IAlarmAppService中可能没有直接提供按ID获取单个报警的方法
            // 此处仅为示例，需要根据实际情况调整或扩展IAlarmAppService

            try
            {
                // 这里应该调用服务方法获取单个报警
                // var alarm = await _alarmService.GetAlarmByIdAsync(id);
                
                // 临时替代方案：返回404
                return NotFound($"ID为{id}的报警不存在或暂不支持此操作");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报警 {AlarmId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取报警详情时发生错误");
            }
        }

        /// <summary>
        /// 创建报警
        /// </summary>
        /// <param name="alarmDto">报警信息</param>
        /// <returns>创建的报警</returns>
        [HttpPost]
        [ProducesResponseType(typeof(AlarmDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<AlarmDTO>> CreateAlarm(CreateAlarmDto alarmDto)
        {
            try
            {
                if (alarmDto.TestId == null)
                {
                    return BadRequest("创建报警需要指定测试ID");
                }

                // 创建报警
                var alarm = await _alarmService.CreateAlarmAsync(
                    alarmDto.TestId.Value, 
                    alarmDto.Name, 
                    alarmDto.Message, 
                    alarmDto.Level);

                if (alarm == null)
                {
                    return BadRequest("无法创建报警，可能指定的测试不存在或不在运行中");
                }

                // 手动映射到DTO
                var createdAlarmDto = new AlarmDTO
                {
                    Id = alarm.Id, // 同样，无法访问原始ID
                    TestId = alarmDto.TestId,
                    Name = alarm.Name,
                    Message = alarm.Message,
                    Level = alarm.Level,
                    Status = alarm.Status,
                    ProcessedBy = alarm.ProcessedBy,
                    Timestamp = alarm.Timestamp,
                    LastUpdated = alarm.LastUpdated,
                    ProcessedAt = alarm.ProcessedAt
                };

                return CreatedAtAction(nameof(GetAlarm), new { id = createdAlarmDto.Id }, createdAlarmDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建报警时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "创建报警时发生错误");
            }
        }

        /// <summary>
        /// 处理报警
        /// </summary>
        /// <param name="id">报警ID</param>
        /// <param name="processDto">处理信息</param>
        /// <returns>处理后的报警</returns>
        [HttpPut("{id}/process")]
        [ProducesResponseType(typeof(AlarmDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<AlarmDTO>> ProcessAlarm(long id, ProcessAlarmDto processDto)
        {
            try
            {
                // 处理报警
                var processedAlarm = await _alarmService.ProcessAlarmAsync(id, processDto.ProcessedBy);

                // 手动映射到DTO
                var processedAlarmDto = new AlarmDTO
                {
                    Id = id, // 使用传入的ID
                    TestId = null, // 无法访问测试ID
                    Name = processedAlarm.Name,
                    Message = processedAlarm.Message,
                    Level = processedAlarm.Level,
                    Status = processedAlarm.Status,
                    ProcessedBy = processedAlarm.ProcessedBy,
                    Timestamp = processedAlarm.Timestamp,
                    LastUpdated = processedAlarm.LastUpdated,
                    ProcessedAt = processedAlarm.ProcessedAt
                };

                return Ok(processedAlarmDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理报警 {AlarmId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "处理报警时发生错误");
            }
        }

        /// <summary>
        /// 获取所有活动报警
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>活动报警列表</returns>
        [HttpGet("active")]
        [ProducesResponseType(typeof(PagedResultDto<AlarmDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<AlarmDTO>>> GetActiveAlarms(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var alarms = await _alarmService.GetActiveAlarmsAsync();
                var alarmsList = alarms.ToList();
                
                // 由于AlarmDTO没有Id和TestId字段，我们需要使用一个中间集合
                var alarmDtos = new List<AlarmDTO>();
                
                // 跳过分页之前的项
                var pagedAlarms = alarmsList
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize);
                
                // 手动映射
                foreach (var a in pagedAlarms)
                {
                    alarmDtos.Add(new AlarmDTO
                    {
                        Id = a.Id, 
                        TestId = a.TestId, 
                        Name = a.Name,
                        Message = a.Message,
                        Level = a.Level,
                        Status = a.Status,
                        ProcessedBy = a.ProcessedBy,
                        Timestamp = a.Timestamp,
                        LastUpdated = a.LastUpdated,
                        ProcessedAt = a.ProcessedAt
                    });
                }

                return Ok(new PagedResultDto<AlarmDTO>
                {
                    Items = alarmDtos,
                    TotalCount = alarmsList.Count,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取活动报警列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取活动报警列表时发生错误");
            }
        }

        /// <summary>
        /// 获取特定级别的报警
        /// </summary>
        /// <param name="level">报警级别</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>特定级别的报警列表</returns>
        [HttpGet("by-level/{level}")]
        [ProducesResponseType(typeof(PagedResultDto<AlarmDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<AlarmDTO>>> GetAlarmsByLevel(
            SharedEnums.AlarmSeverity level,
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var alarms = await _alarmService.GetAlarmsByLevelAsync(level);
                var alarmsList = alarms.ToList();
                
                // 由于AlarmDTO没有Id和TestId字段，我们需要使用一个中间集合
                var alarmDtos = new List<AlarmDTO>();
                
                // 跳过分页之前的项
                var pagedAlarms = alarmsList
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize);
                
                // 手动映射
                foreach (var a in pagedAlarms)
                {
                    alarmDtos.Add(new AlarmDTO
                    {
                        Id = a.Id, // 生成临时ID
                        TestId = a.TestId, // 无法访问测试ID
                        Name = a.Name,
                        Message = a.Message,
                        Level = a.Level,
                        Status = a.Status,
                        ProcessedBy = a.ProcessedBy,
                        Timestamp = a.Timestamp,
                        LastUpdated = a.LastUpdated,
                        ProcessedAt = a.ProcessedAt
                    });
                }

                return Ok(new PagedResultDto<AlarmDTO>
                {
                    Items = alarmDtos,
                    TotalCount = alarmsList.Count,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取级别为 {Level} 的报警列表时出错", level);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取特定级别的报警列表时发生错误");
            }
        }
    }
}