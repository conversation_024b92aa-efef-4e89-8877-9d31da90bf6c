using EnvizonController.Mqtt.Server.Handlers;
using EnvizonController.Mqtt.Server.Services;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.Mqtt.Server.DependencyInjection
{
    public static class MqttServerServiceExtensions
    {
        public static IServiceCollection AddMqttServer(this IServiceCollection services)
        {
            // 注册消息处理器
            services.AddSingleton<IMessageProcessor, MessageProcessor>();
            services.AddSingleton<IMqttServerService, MqttServerService>();
            
            // 注册通知服务
            services.AddSingleton<INotificationService, NotificationService>();
            
            // 注册消息处理器
            services.AddTransient<IMessageHandler, AlarmMessageHandler>();
            services.AddTransient<IMessageHandler, DeviceStatusMessageHandler>();
            
            // 注册后台服务
            services.AddHostedService<MqttServerBackgroundService>();
            
            return services;
        }
    }
} 