using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Models;

namespace EnvizonController.Application.DeviceCommands.Interfaces
{
    /// <summary>
    /// 指令执行器接口
    /// </summary>
    public interface ICommandExecutor
    {
        /// <summary>
        /// 执行读取指令
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="client">Modbus客户端</param>
        /// <param name="protocolItem">协议项</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取结果</returns>
        Task<CommandResult<T>> ExecuteReadCommandAsync<T>(IModbusClient client, ProtocolItem protocolItem, 
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行写入指令
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="client">Modbus客户端</param>
        /// <param name="protocolItem">协议项</param>
        /// <param name="value">写入值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入结果</returns>
        Task<CommandResult> ExecuteWriteCommandAsync<T>(IModbusClient client,byte slaveId, ProtocolItem protocolItem, 
            T value, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行自定义指令
        /// </summary>
        /// <param name="client">Modbus客户端</param>
        /// <param name="command">自定义指令</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<CommandResult> ExecuteCustomCommandAsync(IModbusClient client, CustomCommand command, 
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 验证并转换数据类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="value">原始值</param>
        /// <param name="protocolItem">协议项</param>
        /// <returns>转换后的值</returns>
        T ConvertValue<T>(object value, ProtocolItem protocolItem);
        
        /// <summary>
        /// 检查协议项是否支持指定操作
        /// </summary>
        /// <param name="protocolItem">协议项</param>
        /// <param name="commandType">指令类型</param>
        /// <returns>是否支持</returns>
        bool IsCommandTypeSupported(ProtocolItem protocolItem, CommandType commandType);
    }
} 