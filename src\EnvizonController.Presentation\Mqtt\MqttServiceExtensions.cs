using EnvizonController.Configuration.Initializers;
using EnvizonController.Mqtt.Client.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.Presentation.Mqtt;

/// <summary>
///     MQTT服务扩展方法
/// </summary>
public static class MqttServiceExtensions
{
    /// <summary>
    ///     添加MQTT服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合，以支持方法链</returns>
    public static IServiceCollection AddMqttServices(this IServiceCollection services)
    {
        // 注册MQTT连接配置

        services.AddMqttClient(options =>
        {
            options.WithClientId($"{AppConfigurationProvider.ConfigurationAsyncLoaded.MqttSettings.ClientId}")
                .WithTcpServer(AppConfigurationProvider.ConfigurationAsyncLoaded.MqttSettings.ServerHost,
                    AppConfigurationProvider.ConfigurationAsyncLoaded.MqttSettings.ServerPort)
                .WithCleanSession();
        });

        // 注册MQTT连接管理器和主题订阅管理器
        services.AddSingleton<IMqttConnectionManager, MqttConnectionManager>();
        services.AddSingleton<MqttTopicSubscriptionManager>();

        return services;
    }
}