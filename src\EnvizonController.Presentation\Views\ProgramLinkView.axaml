<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramLinkView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="using:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:programViews="using:EnvizonController.Presentation.Views.Program"
    xmlns:views="using:EnvizonController.Presentation.Views"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="768"
    d:DesignWidth="1024"
    x:DataType="vm:ProgramLinkViewModel"
    Background="#050510"
    mc:Ignorable="d">

    <Grid ColumnDefinitions="350,*">
        <!--  左侧程式链接列表  -->
        <Border
            Grid.Column="0"
            Margin="0,0,5,0"
            Classes="cyber-border">
            <Grid RowDefinitions="Auto,*,Auto">
                <TextBlock
                    Grid.Row="0"
                    Margin="15,10"
                    Classes="h3 primary font-cyber"
                    Text="程式链接列表" />

                <ListBox
                    Grid.Row="1"
                    Margin="10"
                    Padding="0"
                    Background="Transparent"
                    ItemsSource="{Binding ProgramLinks}"
                    SelectedItem="{Binding SelectedProgramLink}">
                    <ListBox.Styles>
                        <Style Selector="ListBoxItem">
                            <Setter Property="Padding" Value="10,8" />
                            <Setter Property="Margin" Value="0,2" />
                            <Setter Property="Background" Value="#14131C" />
                            <Setter Property="CornerRadius" Value="4" />
                        </Style>
                        <Style Selector="ListBoxItem:selected">
                            <Setter Property="Background" Value="#252335" />
                            <Setter Property="BorderBrush" Value="#0DF0FF" />
                            <Setter Property="BorderThickness" Value="1" />
                        </Style>
                    </ListBox.Styles>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid ColumnDefinitions="*,Auto">
                                <StackPanel Grid.Column="0">
                                    <TextBlock
                                        Classes="h4 font-cyber"
                                        Foreground="{Binding IsSelected, Converter={StaticResource BoolToSelectedForegroundConverter}}"
                                        Text="{Binding Name, FallbackValue='未命名程式链接'}" />
                                    <TextBlock
                                        Classes="gray"
                                        FontSize="12"
                                        Text="{Binding CreatedAt, StringFormat='创建于: {0:yyyy-MM-dd HH:mm}'}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button
                                        MinWidth="45"
                                        MinHeight="45"
                                        Margin="2,0"
                                        Padding="6"
                                        HorizontalContentAlignment="Center"
                                        VerticalContentAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="#0DF0FF"
                                        BorderThickness="0"
                                        Command="{Binding $parent[UserControl].((vm:ProgramLinkViewModel)DataContext).EditProgramLinkCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4">
                                        <TextBlock
                                            Classes="font-icon primary"
                                            FontSize="18"
                                            Text="&#xf044;" />
                                    </Button>
                                    <Button
                                        MinWidth="45"
                                        MinHeight="45"
                                        Margin="2,0"
                                        Padding="6"
                                        HorizontalContentAlignment="Center"
                                        VerticalContentAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="#FF4556"
                                        BorderThickness="0"
                                        Command="{Binding $parent[UserControl].((vm:ProgramLinkViewModel)DataContext).DeleteProgramLinkCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4">
                                        <TextBlock
                                            Classes="font-icon"
                                            FontSize="18"
                                            Foreground="#FF4556"
                                            Text="&#xf1f8;" />
                                    </Button>
                                </StackPanel>
                            </Grid>

                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <Button
                    Grid.Row="2"
                    Margin="15,10"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Center"
                    Classes="glow2"
                    Command="{Binding AddProgramLinkCommand}"
                    CornerRadius="4">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="0,0,5,0"
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            Text="&#xf067;" />
                        <TextBlock Classes="font-cyber" Text="新建程式链接" />
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!--  右侧内容区域  -->
        <Grid Grid.Column="1" RowDefinitions="Auto,*">
            <!--  右侧导航  -->
            <Border
                Grid.Row="0"
                Height="56"
                Margin="0,0,0,5"
                Classes="cyber-border">
                <Grid ColumnDefinitions="*,Auto">
                    <!--  显示当前选中项的标题  -->
                    <TextBlock
                        Grid.Column="0"
                        Margin="15,0"
                        VerticalAlignment="Center"
                        Classes="h3 primary font-cyber"
                        Text="{Binding SelectedProgramLink.Name, FallbackValue='未选择程式链接'}" />


                    <!--  切换视图的按钮  -->
                    <StackPanel
                        Grid.Column="1"
                        Margin="0,0,15,0"
                        Orientation="Horizontal">
                        <Button
                            Margin="3,0"
                            Classes="nav-button"
                            Classes.selected="{Binding ShowCurveView}"
                            Command="{Binding SwitchToCurveViewCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,5,0"
                                    Classes="font-icon"
                                    Text="&#xf201;" />
                                <TextBlock Classes="font-cyber" Text="曲线视图" />
                            </StackPanel>
                        </Button>

                        <Button
                            Margin="3,0"
                            Classes="nav-button"
                            Classes.selected="{Binding ShowLinkStepsView}"
                            Command="{Binding SwitchToLinkStepsViewCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,5,0"
                                    Classes="font-icon"
                                    Text="&#xf0cb;" />
                                <TextBlock Classes="font-cyber" Text="程式链接" />
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
            <!--  主内容区域  -->
            <Border Grid.Row="1" Classes="cyber-border">
                <Grid>
                    <!--  加载指示器  -->
                    <Panel IsVisible="{Binding IsLoading}" ZIndex="100">
                        <Rectangle Fill="#80000000" />
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" />
                            <TextBlock
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                Classes="primary"
                                Text="加载中..." />
                        </StackPanel>
                    </Panel>

                    <!--  错误信息  -->
                    <Border
                        MaxWidth="550"
                        Margin="10"
                        Padding="10"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        Background="#80FF4556"
                        BorderBrush="#FF4556"
                        BorderThickness="1"
                        CornerRadius="4"
                        IsVisible="{Binding ErrorMessage, Converter={StaticResource StringNotEmptyConverter}}"
                        ZIndex="90">
                        <Grid RowDefinitions="Auto,Auto">
                            <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto">
                                <TextBlock
                                    Grid.Column="0"
                                    MaxWidth="300"
                                    Margin="0,0,10,0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Cursor="Hand"
                                    IsVisible="{Binding !IsErrorExpanded}"
                                    Text="{Binding ErrorMessage}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip.Tip="点击展开完整信息">
                                    <TextBlock.Styles>
                                        <Style Selector="TextBlock:pointerover">
                                            <Setter Property="TextDecorations" Value="Underline" />
                                        </Style>
                                    </TextBlock.Styles>
                                </TextBlock>

                                <Button
                                    Grid.Column="1"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding ToggleErrorExpandCommand}">
                                    <TextBlock
                                        Classes="font-icon"
                                        FontSize="12"
                                        Text="{Binding IsErrorExpanded, Converter={x:Static converters:BoolToExpandIconConverter.Instance}}" />
                                </Button>

                                <Button
                                    Grid.Column="2"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding ClearErrorCommand}">
                                    <TextBlock
                                        Classes="font-icon"
                                        FontSize="12"
                                        Text="&#xf00d;" />
                                </Button>
                            </Grid>

                            <TextBox
                                Grid.Row="1"
                                MaxWidth="500"
                                MaxHeight="200"
                                AcceptsReturn="True"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="White"
                                IsReadOnly="True"
                                IsVisible="{Binding IsErrorExpanded}"
                                ScrollViewer.VerticalScrollBarVisibility="Auto"
                                Text="{Binding ErrorMessage}"
                                TextWrapping="Wrap" />
                        </Grid>
                    </Border>

                    <!--  曲线视图  -->
                    <programViews:ProgramLinkCurveView
                        x:DataType="vm:ProgramLinkViewModel"
                        DataContext="{Binding}"
                        IsVisible="{Binding ShowCurveView}" />

                    <!--  程式链接项列表  -->
                    <views:ProgramLinkStepsView
                        x:DataType="vm:ProgramLinkViewModel"
                        DataContext="{Binding}"
                        IsVisible="{Binding ShowLinkStepsView}" />
                </Grid>
            </Border>
        </Grid>
    </Grid>

</UserControl>