using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace EnvizonController.Configuration.Models;

/// <summary>
/// MQTT客户端连接设置类，用于配置MQTT客户端的连接参数
/// </summary>
public partial class MqttSettings : ObservableObject
{
    /// <summary>
    /// MQTT客户端ID，用于在MQTT服务器上唯一标识此客户端
    /// 默认值生成了一个包含GUID的唯一标识符
    /// </summary>
    [ObservableProperty]
    private string _clientId = $"EnvizonClient-{Guid.NewGuid()}";

    /// <summary>
    /// MQTT服务器地址，可以是域名或IP地址
    /// 默认为本地回环地址127.0.0.1
    /// </summary>
    [ObservableProperty]
    private string _serverHost = "127.0.0.1";

    /// <summary>
    /// MQTT服务器端口号
    /// 默认为标准MQTT端口1883
    /// </summary>
    [ObservableProperty]
    private int _serverPort = 1883;

    /// <summary>
    /// 是否使用清除会话模式
    /// 启用后，客户端断开连接时会清除所有会话状态
    /// 默认为true
    /// </summary>
    [ObservableProperty]
    private bool _useCleanSession = true;

    /// <summary>
    /// MQTT服务器认证的用户名
    /// 如果服务器不需要认证，可留空
    /// </summary>
    [ObservableProperty]
    private string _username = string.Empty;

    /// <summary>
    /// MQTT服务器认证的密码
    /// 如果服务器不需要认证，可留空
    /// </summary>
    [ObservableProperty]
    private string _password = string.Empty;

    /// <summary>
    /// MQTT客户端保活周期，单位为秒
    /// 客户端将按此间隔向服务器发送PING消息，保持连接活跃
    /// 默认为60秒
    /// </summary>
    [ObservableProperty]
    private int _keepAlivePeriod = 60;

    /// <summary>
    /// 是否使用安全连接（SSL/TLS）
    /// 启用后，将通过加密通道与MQTT服务器通信
    /// 默认为false
    /// </summary>
    [ObservableProperty]
    private bool _useSecureConnection = false;

    /// <summary>
    /// 是否在断开连接后自动重连
    /// 默认为true
    /// </summary>
    [ObservableProperty]
    private bool _autoReconnect = true;

    /// <summary>
    /// 是否在应用启动时自动连接到MQTT服务器
    /// 默认为true
    /// </summary>
    [ObservableProperty]
    private bool _autoConnectOnStartup = true;

    /// <summary>
    /// 最大重连尝试次数
    /// 达到此次数后将停止重连尝试
    /// 默认为5次
    /// </summary>
    [ObservableProperty]
    private int _maxAttempts = 5;

    /// <summary>
    /// 初始重连延迟时间，单位为毫秒
    /// 首次重连尝试的等待时间
    /// 默认为2000毫秒（2秒）
    /// </summary>
    [ObservableProperty]
    private int _initialDelayMs = 2000; // 2秒的初始延迟（毫秒）

    /// <summary>
    /// 最大重连延迟时间，单位为毫秒
    /// 重连延迟时间不会超过此值
    /// 默认为30000毫秒（30秒）
    /// </summary>
    [ObservableProperty]
    private int _maxDelayMs = 30000; // 30秒的最大延迟（毫秒）

    /// <summary>
    /// 重连延迟时间的退避乘数
    /// 用于指数退避算法，每次重连失败后，下次等待时间会乘以此系数
    /// 默认为1.5
    /// </summary>
    [ObservableProperty]
    private double _backoffMultiplier = 1.5;

    /// <summary>
    /// 计算重连延迟时间
    /// 使用指数退避算法计算下一次重连的等待时间
    /// </summary>
    /// <param name="attemptCount">当前尝试次数</param>
    /// <returns>应等待的延迟时间（毫秒）</returns>
    public int CalculateDelay(int attemptCount)
    {
        if (attemptCount <= 0)
        {
            return InitialDelayMs;
        }

        // 使用指数退避算法计算延迟时间
        double factor = Math.Pow(BackoffMultiplier, attemptCount - 1);
        int delay = (int)(InitialDelayMs * factor);

        // 限制最大延迟时间
        if (delay > MaxDelayMs)
        {
            return MaxDelayMs;
        }

        return delay;
    }

    /// <summary>
    /// 获取重连延迟时间
    /// 此方法用于兼容现有代码，内部调用CalculateDelay方法
    /// </summary>
    /// <param name="attemptCount">当前尝试次数</param>
    /// <returns>应等待的延迟时间（毫秒）</returns>
    public int GetReconnectDelay(int attemptCount) => CalculateDelay(attemptCount);
}