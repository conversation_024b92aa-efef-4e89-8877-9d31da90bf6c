using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using EnvizonController.Presentation.Initialization;
using Serilog;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 启动屏幕的视图模型，负责显示应用程序初始化进度
    /// </summary>
    public class SplashScreenViewModel : ViewModelBase, IDisposable
    {
        private readonly IAppInitializationService _initializationService;
        private readonly ILogger _logger;
        private CancellationTokenSource _initializationCts;

        private int _progress;
        private string _statusMessage = "正在初始化...";
        private bool _isInitializing = true;
        private bool _hasError;
        private string _errorMessage = string.Empty;

        /// <summary>
        /// 初始化完成时的回调操作
        /// </summary>
        public Action InitializationCompletedCallback { get; set; }

        /// <summary>
        /// 初始化出错时的回调操作
        /// </summary>
        public Action<Exception> InitializationErrorCallback { get; set; }

        /// <summary>
        /// 获取或设置初始化进度（0-100）
        /// </summary>
        public int Progress
        {
            get => _progress;
            set => SetProperty(ref _progress, value);
        }

        /// <summary>
        /// 获取或设置状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 获取或设置一个值，指示是否正在初始化
        /// </summary>
        public bool IsInitializing
        {
            get => _isInitializing;
            set => SetProperty(ref _isInitializing, value);
        }

        /// <summary>
        /// 获取或设置一个值，指示是否发生错误
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// 获取或设置错误消息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// 初始化 <see cref="SplashScreenViewModel"/> 类的新实例
        /// </summary>
        /// <param name="initializationService">应用程序初始化服务</param>
        /// <param name="logger">日志记录器</param>
        public SplashScreenViewModel(IAppInitializationService initializationService = null, ILogger logger = null)
        {
            _initializationService = initializationService;
            _logger = logger ?? Log.ForContext<SplashScreenViewModel>();

            // 设计时数据
            if (_initializationService == null)
            {
                return;
            }

            // 注册初始化进度变化事件
            _initializationService.ProgressChanged += OnInitializationProgressChanged;
        }

        /// <summary>
        /// 开始应用程序初始化
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task StartInitializationAsync()
        {
            if (_initializationService == null)
            {
                // 设计时或未注入服务时的模拟进度
                await SimulateInitializationAsync();
                return;
            }

            try
            {
                IsInitializing = true;
                HasError = false;
                _initializationCts = new CancellationTokenSource();

                // 创建进度报告器
                var progress = new Progress<InitializationProgress>(OnInitializationProgress);

                // 开始初始化
                await _initializationService.InitializeAsync(progress, _initializationCts.Token);
                
                // 初始化完成后，延迟一小段时间让用户看到完成消息
                await Task.Delay(1000);
                
                // 调用完成回调
                InitializationCompletedCallback?.Invoke();
            }
            catch (OperationCanceledException)
            {
                _logger?.Warning("应用程序初始化已取消");
                SetError("初始化已取消");
                // 调用错误回调
                InitializationErrorCallback?.Invoke(new OperationCanceledException("初始化已取消"));
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "应用程序初始化失败");
                SetError($"初始化失败: {ex.Message}");
                // 调用错误回调
                InitializationErrorCallback?.Invoke(ex);
            }
        }

        /// <summary>
        /// 取消应用程序初始化
        /// </summary>
        public void CancelInitialization()
        {
            _initializationCts?.Cancel();
        }

        /// <summary>
        /// 重试应用程序初始化
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        public async Task RetryInitializationAsync()
        {
            if (_initializationService != null)
            {
                _initializationService.Reset();
            }

            await StartInitializationAsync();
        }

        /// <summary>
        /// 更新进度信息
        /// </summary>
        /// <param name="progress">进度值（0-100）</param>
        /// <param name="statusMessage">状态消息</param>
        public void UpdateProgress(int progress, string statusMessage)
        {
            Progress = progress;
            StatusMessage = statusMessage;
        }

        /// <summary>
        /// 设置错误状态
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        public void SetError(string errorMessage)
        {
            HasError = true;
            ErrorMessage = errorMessage;
            IsInitializing = false; // 错误时停止初始化
        }

        /// <summary>
        /// 完成初始化
        /// </summary>
        public void CompleteInitialization()
        {
            IsInitializing = false;
            HasError = false;
            Progress = 100;
            StatusMessage = "初始化完成！";
        }

        /// <summary>
        /// 处理初始化进度变化事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void OnInitializationProgressChanged(object sender, InitializationProgressEventArgs e)
        {
            if (e.Progress == null) return;

            // 更新UI进度
            Progress = e.Progress.PercentComplete;
            StatusMessage = e.Progress.CurrentOperation;

            // 根据初始化阶段更新状态
            if (e.Progress.IsCompleted)
            {
                CompleteInitialization();
            }
            else if (e.Progress.IsFailed)
            {
                SetError(e.Progress.CurrentOperation);
            }
        }

        /// <summary>
        /// 处理初始化进度报告
        /// </summary>
        /// <param name="progress">初始化进度</param>
        private void OnInitializationProgress(InitializationProgress progress)
        {
            if (progress == null) return;

            // 更新UI进度
            Progress = progress.PercentComplete;
            StatusMessage = progress.CurrentOperation;

            // 根据初始化阶段更新状态
            if (progress.IsCompleted)
            {
                CompleteInitialization();
            }
            else if (progress.IsFailed)
            {
                SetError(progress.CurrentOperation);
            }
        }

        /// <summary>
        /// 模拟初始化过程（设计时使用）
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        private async Task SimulateInitializationAsync()
        {
            try
            {
                IsInitializing = true;
                HasError = false;

                // 模拟加载配置
                UpdateProgress(10, "正在加载配置...");
                await Task.Delay(500);

                // 模拟注册服务
                UpdateProgress(30, "正在注册服务...");
                await Task.Delay(700);

                // 模拟连接MQTT
                UpdateProgress(60, "正在连接MQTT服务...");
                await Task.Delay(1000);

                // 初始化完成
                CompleteInitialization();
                
                // 延迟一小段时间让用户看到完成消息
                await Task.Delay(1000);
                
                // 调用完成回调
                InitializationCompletedCallback?.Invoke();
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "模拟初始化失败");
                SetError($"模拟初始化失败: {ex.Message}");
                // 调用错误回调
                InitializationErrorCallback?.Invoke(ex);
            }
        }

        /// <summary>
        /// 当对象被释放时清理资源
        /// </summary>
        public void Dispose()
        {
            if (_initializationService != null)
            {
                _initializationService.ProgressChanged -= OnInitializationProgressChanged;
            }

            _initializationCts?.Dispose();
            _initializationCts = null;
        }
    }
}