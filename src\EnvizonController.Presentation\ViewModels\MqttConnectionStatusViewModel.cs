using System;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.Mqtt;
using EnvizonController.Shared.Enums;
using Serilog;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// MQTT连接状态视图模型
    /// </summary>
    public class MqttConnectionStatusViewModel : ViewModelBase, IRecipient<MqttConnectionStatusChangedMessage>, IDisposable
    {
        private readonly IMqttConnectionManager _connectionManager;
        private readonly ILogger _logger;
        private ConnectionStatus _status;
        private string _statusMessage;
        private bool _isConnecting;
        private bool _isDisposed;

        /// <summary>
        /// MQTT连接状态
        /// </summary>
        public ConnectionStatus Status
        {
            get => _status;
            private set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 状态描述信息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            private set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 是否正在连接
        /// </summary>
        public bool IsConnecting
        {
            get => _isConnecting;
            private set => SetProperty(ref _isConnecting, value);
        }

        /// <summary>
        /// 连接命令
        /// </summary>
        public ICommand ConnectCommand { get; }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        public ICommand DisconnectCommand { get; }

        /// <summary>
        /// 初始化 <see cref="MqttConnectionStatusViewModel"/> 类的新实例
        /// </summary>
        /// <param name="connectionManager">MQTT连接管理器</param>
        /// <param name="logger">日志记录器</param>
        public MqttConnectionStatusViewModel(
            IMqttConnectionManager connectionManager,
            ILogger logger)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _logger = logger?.ForContext<MqttConnectionStatusViewModel>() ?? throw new ArgumentNullException(nameof(logger));
            
            Status = _connectionManager.Status;
            StatusMessage = "MQTT未连接";

            // 注册消息接收
            WeakReferenceMessenger.Default.Register<MqttConnectionStatusChangedMessage>(this);

            // 连接命令
            ConnectCommand = new AsyncRelayCommand(
                ConnectAsync,
                () => Status != ConnectionStatus.Connected && 
                      Status != ConnectionStatus.Connecting && 
                      Status != ConnectionStatus.Reconnecting);

            // 断开连接命令
            DisconnectCommand = new AsyncRelayCommand(
                DisconnectAsync,
                () => Status == ConnectionStatus.Connected);
        }

        /// <summary>
        /// 连接MQTT服务器
        /// </summary>
        private async Task ConnectAsync()
        {
            if (_isDisposed) return;
            
            IsConnecting = true;
            try
            {
                _logger.Information("用户请求连接MQTT服务器");
                await _connectionManager.ConnectAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "连接MQTT服务器失败");
            }
            finally
            {
                IsConnecting = false;
            }
        }

        /// <summary>
        /// 断开MQTT连接
        /// </summary>
        private async Task DisconnectAsync()
        {
            if (_isDisposed) return;
            
            try
            {
                _logger.Information("用户请求断开MQTT连接");
                await _connectionManager.DisconnectAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "断开MQTT连接失败");
            }
        }

        /// <summary>
        /// 接收MQTT连接状态变更消息
        /// </summary>
        /// <param name="message">状态变更消息</param>
        public void Receive(MqttConnectionStatusChangedMessage message)
        {
            if (_isDisposed) return;
            
            Status = message.NewStatus;
            StatusMessage = message.Message;
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;
            
            // 取消消息注册
            WeakReferenceMessenger.Default.Unregister<MqttConnectionStatusChangedMessage>(this);
            
            _isDisposed = true;
        }
    }
}