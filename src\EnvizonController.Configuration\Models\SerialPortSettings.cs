﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Configuration.Enums;

namespace EnvizonController.Configuration.Models
{

    public class SerialPortSettings : ObservableObject
    {
        private string _portName = "COM1";
        private int _baudRate = 9600;
        private int _dataBits = 8;
        private StopBits _stopBits = StopBits.One;
        private Parity _parity = Parity.None;
        private Handshake _handshake = Handshake.None;
        private int _readTimeout = 500;
        private int _writeTimeout = 500;

        public string PortName
        {
            get => _portName;
            set => SetProperty(ref _portName, value);
        }

        public int BaudRate
        {
            get => _baudRate;
            set => SetProperty(ref _baudRate, value);
        }

        public int DataBits
        {
            get => _dataBits;
            set => SetProperty(ref _dataBits, value);
        }

        public StopBits StopBits
        {
            get => _stopBits;
            set => SetProperty(ref _stopBits, value);
        }

        public Parity Parity
        {
            get => _parity;
            set => SetProperty(ref _parity, value);
        }

        public Handshake Handshake
        {
            get => _handshake;
            set => SetProperty(ref _handshake, value);
        }

        public int ReadTimeout
        {
            get => _readTimeout;
            set => SetProperty(ref _readTimeout, value);
        }

        public int WriteTimeout
        {
            get => _writeTimeout;
            set => SetProperty(ref _writeTimeout, value);
        }
    }
}
