﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Configuration.Models
{

    public class NetworkSettings : ObservableObject
    {
        private string _hostAddress = "127.0.0.1";
        private int _port = 502;
        private int _connectionTimeout = 5000;

        public string HostAddress
        {
            get => _hostAddress;
            set => SetProperty(ref _hostAddress, value);
        }

        public int Port
        {
            get => _port;
            set => SetProperty(ref _port, value);
        }

        public int ConnectionTimeout
        {
            get => _connectionTimeout;
            set => SetProperty(ref _connectionTimeout, value);
        }
    }
}
