using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为展开/折叠图标Unicode字符
    /// </summary>
    public class BoolToExpandIconConverter : IValueConverter
    {
        public static BoolToExpandIconConverter Instance { get; } = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isExpanded)
            {
                // 使用FontAwesome字体图标：展开为向上箭头，折叠为向下箭头
                return isExpanded ? "\uf077" : "\uf078"; // up arrow : down arrow
            }
            
            return "\uf078"; // 默认返回向下箭头（折叠状态）
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 