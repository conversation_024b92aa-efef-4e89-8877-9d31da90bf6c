using EnvizonController.Modbus.Abstractions.Interfaces;
using static EnvizonController.Modbus.Client.Extensions.EndianHelper;

namespace EnvizonController.Modbus.Client.Extensions;

/// <summary>
///     Modbus客户端扩展方法
/// </summary>
public static class ModbusClientExtensions
{
    #region 读取Float

    /// <summary>
    ///     读取Float值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Float值</returns>
    public static async Task<float> ReadFloatAsync(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToFloat(registers[0], registers[1], wordOrder);
    }

    #endregion

    #region 读取Double

    /// <summary>
    ///     读取Double值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Double值</returns>
    public static async Task<double> ReadDoubleAsync(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 4, cancellationToken);
        return CombineToDouble(registers, wordOrder);
    }

    #endregion

    #region 读取String

    /// <summary>
    ///     读取字符串
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="length">字符串长度（字符数）</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>字符串值</returns>
    public static async Task<string> ReadStringAsync(this IModbusClient client, byte slaveAddress, ushort address,
        ushort length, ByteOrder byteOrder = ByteOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        // 计算需要读取的寄存器数量（每个寄存器2个字符，向上取整）
        var registerCount = (ushort)Math.Ceiling(length / 2.0);
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, registerCount, cancellationToken);
        return RegistersToString(registers, byteOrder);
    }

    #endregion

    #region 泛型读取方法

    /// <summary>
    ///     读取指定类型的值
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序（对于32位及以上类型）</param>
    /// <param name="byteOrder">字节序（对于字符串类型）</param>
    /// <param name="stringLength">字符串长度（仅对字符串类型有效）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取的值</returns>
    public static async Task<T> ReadAsync<T>(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, ByteOrder byteOrder = ByteOrder.BigEndian,
        ushort stringLength = 10, CancellationToken cancellationToken = default)
    {
        var type = typeof(T);

        if (type == typeof(short))
            return (T)(object)await client.ReadInt16Async(slaveAddress, address, cancellationToken);

        if (type == typeof(ushort))
            return (T)(object)await client.ReadUInt16Async(slaveAddress, address, cancellationToken);

        if (type == typeof(int))
            return (T)(object)await client.ReadInt32Async(slaveAddress, address, wordOrder, cancellationToken);

        if (type == typeof(uint))
            return (T)(object)await client.ReadUInt32Async(slaveAddress, address, wordOrder, cancellationToken);

        if (type == typeof(float))
            return (T)(object)await client.ReadFloatAsync(slaveAddress, address, wordOrder, cancellationToken);

        if (type == typeof(double))
            return (T)(object)await client.ReadDoubleAsync(slaveAddress, address, wordOrder, cancellationToken);

        if (type == typeof(string))
            return (T)(object)await client.ReadStringAsync(slaveAddress, address, stringLength, byteOrder,
                cancellationToken);

        throw new NotSupportedException($"不支持的数据类型: {type.Name}");
    }

    #endregion

    #region 写入Float

    /// <summary>
    ///     写入Float值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteFloatAsync(this IModbusClient client, byte slaveAddress, ushort address,
        float value, WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = SplitFloat(value, wordOrder);
        return client.WriteMultipleRegistersAsync(slaveAddress, address, registers, cancellationToken);
    }

    #endregion

    #region 写入Double

    /// <summary>
    ///     写入Double值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteDoubleAsync(this IModbusClient client, byte slaveAddress, ushort address,
        double value, WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = SplitDouble(value, wordOrder);
        return client.WriteMultipleRegistersAsync(slaveAddress, address, registers, cancellationToken);
    }

    #endregion

    #region 写入String

    /// <summary>
    ///     写入字符串
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的字符串</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteStringAsync(this IModbusClient client, byte slaveAddress, ushort address,
        string value, ByteOrder byteOrder = ByteOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = StringToRegisters(value, byteOrder);
        return client.WriteMultipleRegistersAsync(slaveAddress, address, registers, cancellationToken);
    }

    #endregion

    #region 泛型写入方法

    /// <summary>
    ///     写入指定类型的值
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="wordOrder">字序（对于32位及以上类型）</param>
    /// <param name="byteOrder">字节序（对于字符串类型）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteAsync<T>(this IModbusClient client, byte slaveAddress, ushort address,
        T value, WordOrder wordOrder = WordOrder.BigEndian, ByteOrder byteOrder = ByteOrder.BigEndian,
        CancellationToken cancellationToken = default)
    {
        var type = typeof(T);

        if (type == typeof(short))
            return client.WriteInt16Async(slaveAddress, address, (short)(object)value!, cancellationToken);

        if (type == typeof(ushort))
            return client.WriteUInt16Async(slaveAddress, address, (ushort)(object)value!, cancellationToken);

        if (type == typeof(int))
            return client.WriteInt32Async(slaveAddress, address, (int)(object)value!, wordOrder, cancellationToken);

        if (type == typeof(uint))
            return client.WriteUInt32Async(slaveAddress, address, (uint)(object)value!, wordOrder, cancellationToken);

        if (type == typeof(float))
            return client.WriteFloatAsync(slaveAddress, address, (float)(object)value!, wordOrder, cancellationToken);

        if (type == typeof(double))
            return client.WriteDoubleAsync(slaveAddress, address, (double)(object)value!, wordOrder, cancellationToken);

        if (type == typeof(string))
            return client.WriteStringAsync(slaveAddress, address, (string)(object)value!, byteOrder, cancellationToken);

        throw new NotSupportedException($"不支持的数据类型: {type.Name}");
    }

    #endregion

    #region 读取Int16/UInt16

    /// <summary>
    ///     读取Int16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Int16值</returns>
    public static async Task<short> ReadInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 1, cancellationToken);
        return (short)registers[0];
    }

    /// <summary>
    ///     读取UInt16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>UInt16值</returns>
    public static async Task<ushort> ReadUInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 1, cancellationToken);
        return registers[0];
    }

    #endregion

    #region 读取Int32/UInt32

    /// <summary>
    ///     读取Int32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Int32值</returns>
    public static async Task<int> ReadInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToInt32(registers[0], registers[1], wordOrder);
    }

    /// <summary>
    ///     读取UInt32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>UInt32值</returns>
    public static async Task<uint> ReadUInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadHoldingRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToUInt32(registers[0], registers[1], wordOrder);
    }

    #endregion

    #region 写入Int16/UInt16

    /// <summary>
    ///     写入Int16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        short value, CancellationToken cancellationToken = default)
    {
        return client.WriteSingleRegisterAsync(slaveAddress, address, (ushort)value, cancellationToken);
    }

    /// <summary>
    ///     写入UInt16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteUInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        ushort value, CancellationToken cancellationToken = default)
    {
        return client.WriteSingleRegisterAsync(slaveAddress, address, value, cancellationToken);
    }

    #endregion

    #region 写入Int32/UInt32

    /// <summary>
    ///     写入Int32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        int value, WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = SplitInt32(value, wordOrder);
        return client.WriteMultipleRegistersAsync(slaveAddress, address, registers, cancellationToken);
    }

    /// <summary>
    ///     写入UInt32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="value">要写入的值</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    public static Task<bool> WriteUInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        uint value, WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = SplitUInt32(value, wordOrder);
        return client.WriteMultipleRegistersAsync(slaveAddress, address, registers, cancellationToken);
    }

    #endregion

    #region 输入寄存器读取方法

    /// <summary>
    ///     从输入寄存器读取Int16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Int16值</returns>
    public static async Task<short> ReadInputInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 1, cancellationToken);
        return (short)registers[0];
    }

    /// <summary>
    ///     从输入寄存器读取UInt16值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>UInt16值</returns>
    public static async Task<ushort> ReadInputUInt16Async(this IModbusClient client, byte slaveAddress, ushort address,
        CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 1, cancellationToken);
        return registers[0];
    }

    /// <summary>
    ///     从输入寄存器读取Int32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Int32值</returns>
    public static async Task<int> ReadInputInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToInt32(registers[0], registers[1], wordOrder);
    }

    /// <summary>
    ///     从输入寄存器读取UInt32值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>UInt32值</returns>
    public static async Task<uint> ReadInputUInt32Async(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToUInt32(registers[0], registers[1], wordOrder);
    }

    /// <summary>
    ///     从输入寄存器读取Float值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Float值</returns>
    public static async Task<float> ReadInputFloatAsync(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 2, cancellationToken);
        return CombineToFloat(registers[0], registers[1], wordOrder);
    }

    /// <summary>
    ///     从输入寄存器读取Double值
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="wordOrder">字序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Double值</returns>
    public static async Task<double> ReadInputDoubleAsync(this IModbusClient client, byte slaveAddress, ushort address,
        WordOrder wordOrder = WordOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, 4, cancellationToken);
        return CombineToDouble(registers, wordOrder);
    }

    /// <summary>
    ///     从输入寄存器读取字符串
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">寄存器地址</param>
    /// <param name="length">字符串长度（字符数）</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>字符串值</returns>
    public static async Task<string> ReadInputStringAsync(this IModbusClient client, byte slaveAddress, ushort address,
        ushort length, ByteOrder byteOrder = ByteOrder.BigEndian, CancellationToken cancellationToken = default)
    {
        // 计算需要读取的寄存器数量（每个寄存器2个字符，向上取整）
        var registerCount = (ushort)Math.Ceiling(length / 2.0);
        var registers = await client.ReadInputRegistersAsync(slaveAddress, address, registerCount, cancellationToken);
        return RegistersToString(registers, byteOrder);
    }

    #endregion

    /// <summary>
    /// 读取单个线圈状态
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="address">线圈地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>线圈状态</returns>
    public static async Task<bool> ReadSingleCoilAsync(this IModbusClient client, byte slaveAddress, ushort address,
        CancellationToken cancellationToken = default)
    {
        bool[] values = await client.ReadCoilsAsync(slaveAddress, address, 1, cancellationToken);
        return values[0];
    }
}