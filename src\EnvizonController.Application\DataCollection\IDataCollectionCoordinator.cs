using EnvizonController.Domain.Aggregates;
using System.Collections.Concurrent;

namespace EnvizonController.Application.DataCollection
{
    /// <summary>
    /// 数据采集协调器接口。
    /// 负责管理多个设备的数据采集任务，包括测试采集和常规采集。
    /// </summary>
    /// <remarks>
    /// <para>
    /// 此接口定义了设备数据采集的核心操作，包括：
    /// <list type="bullet">
    ///   <item>测试数据采集的启动/停止</item>
    ///   <item>常规数据采集的启动/停止</item>
    ///   <item>单次数据采集执行</item>
    /// </list>
    /// </para>
    /// <para>
    /// 典型使用场景：
    /// <code>
    /// // 启动设备采集
    /// await coordinator.StartDeviceCollectionAsync(deviceId);
    ///
    /// // 执行采集循环
    /// while (coordinator.ActiveCollectingDevices.ContainsKey(deviceId))
    /// {
    ///     var device = coordinator.ActiveCollectingDevices[deviceId];
    ///     await coordinator.CollectDataAsync(device);
    ///     await Task.Delay(collectionInterval);
    /// }
    /// </code>
    /// </para>
    /// </remarks>
    public interface IDataCollectionCoordinator
    {
        /// <summary>
        /// 获取当前正在进行数据采集的活动设备字典。
        /// </summary>
        /// <value>
        /// 包含活动采集设备的并发字典，键为设备ID(long)，值为设备对象(<see cref="Device"/>)。
        /// </value>
        /// <remarks>
        /// 此字典用于跟踪当前处于采集状态的所有设备。当设备被添加到字典中时，
        /// 表示该设备应开始定期采集数据；当从字典中移除时，表示应停止采集。
        /// </remarks>
        ConcurrentDictionary<long, Device> ActiveCollectingDevices { get; }

        /// <summary>
        /// 启动与设备关联的特定测试的数据采集。
        /// </summary>
        /// <param name="device">要采集数据的设备对象，不可为null。</param>
        /// <returns>
        /// 表示异步操作的任务，任务结果为布尔值：
        /// <c>true</c> - 采集成功启动；
        /// <c>false</c> - 启动失败(如设备为null或已处于采集状态)。
        /// </returns>
        /// <remarks>
        /// 此方法会将设备添加到<see cref="ActiveCollectingDevices"/>字典中，
        /// 但不会立即执行数据采集。实际采集由外部循环调用<see cref="CollectDataAsync"/>完成。
        /// <example>
        /// 示例：
        /// <code>
        /// var device = await deviceService.GetDeviceByIdAsync(1);
        /// if (!await coordinator.StartTestCollectionAsync(device))
        /// {
        ///     logger.Error("无法启动测试采集");
        /// }
        /// </code>
        /// </example>
        /// </remarks>
        /// <exception cref="ArgumentNullException">当device参数为null时可能抛出。</exception>
        Task<bool> StartTestCollectionAsync(Device device);

        /// <summary>
        /// 停止特定测试的数据采集。
        /// </summary>
        /// <param name="deviceId">要停止采集的设备ID。</param>
        /// <returns>
        /// 表示异步操作的任务，任务结果为布尔值：
        /// <c>true</c> - 采集成功停止；
        /// <c>false</c> - 停止过程中发生错误。
        /// </returns>
        /// <remarks>
        /// 此方法会：
        /// <list type="number">
        ///   <item>从<see cref="ActiveCollectingDevices"/>字典中移除设备</item>
        ///   <item>调用测试服务更新测试状态</item>
        /// </list>
        /// </remarks>
        Task<bool> StopTestCollectionAsync(long deviceId);

        /// <summary>
        /// 为指定设备执行一次数据采集。
        /// </summary>
        /// <param name="device">要采集数据的设备对象，不可为null。</param>
        /// <returns>
        /// 表示异步操作的任务，任务结果为布尔值：
        /// <c>true</c> - 数据采集成功；
        /// <c>false</c> - 采集过程中发生错误。
        /// </returns>
        /// <remarks>
        /// <para>
        /// 采集流程包括：
        /// <list type="number">
        ///   <item>检查并建立Modbus连接</item>
        ///   <item>加载设备关联的协议</item>
        ///   <item>使用协议采集数据</item>
        ///   <item>缓冲采集到的数据</item>
        /// </list>
        /// </para>
        /// <para>
        /// 如果采集失败，系统会自动创建采集告警记录。
        /// </para>
        /// </remarks>
        Task<bool> CollectDataAsync(Device device);

        /// <summary>
        /// 启动设备的常规数据采集（非测试模式）。
        /// </summary>
        /// <param name="deviceId">要开始采集的设备ID。</param>
        /// <returns>
        /// 表示异步操作的任务，任务结果为布尔值：
        /// <c>true</c> - 采集成功启动；
        /// <c>false</c> - 启动失败(如设备不存在或已处于采集状态)。
        /// </returns>
        /// <remarks>
        /// 常规采集与测试采集的区别：
        /// <list type="bullet">
        ///   <item>不关联特定测试运行</item>
        ///   <item>采集的数据不会标记为测试数据</item>
        ///   <item>停止时不会更新测试状态</item>
        /// </list>
        /// </remarks>
        Task<bool> StartDeviceCollectionAsync(long deviceId);

        /// <summary>
        /// 停止设备的常规数据采集。
        /// </summary>
        /// <param name="deviceId">要停止采集的设备ID。</param>
        /// <returns>
        /// 表示异步操作的任务，任务结果为布尔值：
        /// <c>true</c> - 采集成功停止；
        /// <c>false</c> - 停止过程中发生错误。
        /// </returns>
        /// <remarks>
        /// 此方法会：
        /// <list type="number">
        ///   <item>从活动采集设备列表中移除设备</item>
        ///   <item>强制处理该设备的所有缓冲数据</item>
        /// </list>
        /// </remarks>
        Task<bool> StopDeviceCollectionAsync(long deviceId);
    }
}