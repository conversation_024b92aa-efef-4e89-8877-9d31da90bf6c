using System;
using System.Collections.Generic;

namespace EnvizonController.Shared.DTOs;

/// <summary>
///     环境控制程式表数据传输对象
/// </summary>
public class ProgramDTO
{
    /// <summary>
    ///     程式表ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     程式名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///     循环次数
    /// </summary>
    public int CycleCount { get; set; }

    /// <summary>
    ///     循环开始
    /// </summary>
    public int CycleStart { get; set; }

    /// <summary>
    ///     循环结束
    /// </summary>
    public int CycleEnd { get; set; }

    /// <summary>
    ///     程式步骤列表
    /// </summary>
    public List<ProgramStepDTO> Steps { get; set; } = new();

    /// <summary>
    ///     步骤数量
    /// </summary>
    public int StepCount => Steps.Count;

    /// <summary>
    ///     总持续时间（秒）
    /// </summary>
    public int TotalDurationSeconds => Steps.Sum(step => step.Duration);

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}