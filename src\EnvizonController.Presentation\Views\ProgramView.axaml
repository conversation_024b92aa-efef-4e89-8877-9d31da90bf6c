<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="using:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="using:EnvizonController.Presentation.Views"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="768"
    d:DesignWidth="1024"
    x:DataType="vm:ProgramViewModel"
    Background="#050510"
    mc:Ignorable="d">


    <Grid Grid.Row="1" ColumnDefinitions="350,*">
        <!--  左侧列表面板  -->
        <views:ProgramListView
            Grid.Column="0"
            Margin="0,0,5,0"
            DataContext="{Binding}" />

        <!--  右侧内容区域  -->
        <Grid Grid.Column="1" RowDefinitions="Auto,*">
            <!--  右侧导航  -->
            <Border
                Grid.Row="0"
                Height="56"
                Margin="0,0,0,5"
                Classes="cyber-border">
                <Grid ColumnDefinitions="*,Auto">
                    <!--  显示当前选中项的标题  -->
                    <TextBlock
                        Grid.Column="0"
                        Margin="15,0"
                        VerticalAlignment="Center"
                        Classes="h3 primary font-cyber"
                        Text="{Binding SelectedProgram.Name, FallbackValue='未选择程式'}" />

                    <!--  切换视图的按钮  -->
                    <StackPanel
                        Grid.Column="1"
                        Margin="0,0,15,0"
                        Orientation="Horizontal">
                        <Button
                            Margin="3,0"
                            Classes="nav-button"
                            Classes.selected="{Binding ShowCurveView}"
                            Command="{Binding SwitchToCurveViewCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,5,0"
                                    Classes="font-icon"
                                    Text="&#xf201;" />
                                <TextBlock Classes="font-cyber" Text="曲线视图" />
                            </StackPanel>
                        </Button>

                        <Button
                            Margin="3,0"
                            Classes="nav-button"
                            Classes.selected="{Binding ShowStepsView}"
                            Command="{Binding SwitchToStepsViewCommand}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,5,0"
                                    Classes="font-icon"
                                    Text="&#xf0cb;" />
                                <TextBlock Classes="font-cyber" Text="步骤管理" />
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!--  主内容区域  -->
            <Border Grid.Row="1" Classes="cyber-border">
                <Grid>
                    <!--  加载指示器  -->
                    <Panel IsVisible="{Binding IsLoading}" ZIndex="100">
                        <Rectangle Fill="#80000000" />
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" />
                            <TextBlock
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                Classes="primary"
                                Text="加载中..." />
                        </StackPanel>
                    </Panel>

                    <!--  错误信息  -->
                    <Border
                        MaxWidth="550"
                        Margin="10"
                        Padding="10"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Top"
                        Background="#80FF4556"
                        BorderBrush="#FF4556"
                        BorderThickness="1"
                        CornerRadius="4"
                        IsVisible="{Binding ErrorMessage, Converter={StaticResource StringNotEmptyConverter}}"
                        ZIndex="90">
                        <Grid RowDefinitions="Auto,Auto">
                            <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto">
                                <TextBlock
                                    Grid.Column="0"
                                    MaxWidth="300"
                                    Margin="0,0,10,0"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Cursor="Hand"
                                    IsVisible="{Binding !IsErrorExpanded}"
                                    Tapped="OnErrorMessageTapped"
                                    Text="{Binding ErrorMessage}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip.Tip="点击展开完整信息">
                                    <TextBlock.Styles>
                                        <Style Selector="TextBlock:pointerover">
                                            <Setter Property="TextDecorations" Value="Underline" />
                                        </Style>
                                    </TextBlock.Styles>
                                </TextBlock>

                                <Button
                                    Grid.Column="1"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding ToggleErrorExpandCommand}">
                                    <TextBlock
                                        Classes="font-icon"
                                        FontSize="12"
                                        Text="{Binding IsErrorExpanded, Converter={x:Static converters:BoolToExpandIconConverter.Instance}}" />
                                </Button>

                                <Button
                                    Grid.Column="2"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding ClearErrorCommand}">
                                    <TextBlock
                                        Classes="font-icon"
                                        FontSize="12"
                                        Text="&#xf00d;" />
                                </Button>
                            </Grid>

                            <TextBox
                                Grid.Row="1"
                                MaxWidth="500"
                                MaxHeight="200"
                                AcceptsReturn="True"
                                Background="Transparent"
                                BorderThickness="0"
                                Foreground="White"
                                IsReadOnly="True"
                                IsVisible="{Binding IsErrorExpanded}"
                                ScrollViewer.VerticalScrollBarVisibility="Auto"
                                Text="{Binding ErrorMessage}"
                                TextWrapping="Wrap" />
                        </Grid>
                    </Border>

                    <!--  曲线视图  -->
                    <views:ProgramCurveView
                        x:DataType="vm:ProgramViewModel"
                        DataContext="{Binding}"
                        IsVisible="{Binding ShowCurveView}" />

                    <!--  步骤管理视图  -->
                    <views:ProgramStepsView
                        x:DataType="vm:ProgramViewModel"
                        DataContext="{Binding}"
                        IsVisible="{Binding ShowStepsView}" />
                </Grid>
            </Border>
        </Grid>
    </Grid>

</UserControl>