using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 设备API服务接口
    /// </summary>
    public interface IDeviceApiService : IApiService
    {
        /// <summary>
        /// 获取所有设备
        /// </summary>
        Task<Result<PagedResultDto<DeviceDto>>> GetDevicesAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        Task<Result<DeviceDto>> GetDeviceAsync(long id);

        /// <summary>
        /// 创建设备
        /// </summary>
        Task<Result<DeviceDto>> CreateDeviceAsync(DeviceDto device);

        /// <summary>
        /// 更新设备
        /// </summary>
        Task<Result<DeviceDto>> UpdateDeviceAsync(long id, DeviceDto device);

        /// <summary>
        /// 删除设备
        /// </summary>
        Task<Result<bool>> DeleteDeviceAsync(long id);
    }
}