﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Shared.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Presentation.ViewModels.DataQuery
{
    /// <summary>
    /// 程式步骤项视图模型
    /// </summary>
    public class TestStepItemViewModel : ObservableObject
    {
        private long _id;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public long Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private long _testId;
        /// <summary>
        /// 所属测试项ID
        /// </summary>
        public long TestId
        {
            get => _testId;
            set => SetProperty(ref _testId, value);
        }

        private double _humidity;
        /// <summary>
        /// 湿度（百分比）
        /// </summary>
        public double Humidity
        {
            get => _humidity;
            set => SetProperty(ref _humidity, value);
        }

        private double _temperature;
        /// <summary>
        /// 温度（摄氏度）
        /// </summary>
        public double Temperature
        {
            get => _temperature;
            set => SetProperty(ref _temperature, value);
        }

        private bool _isLinear;
        /// <summary>
        /// 是否线性变化
        /// </summary>
        public bool IsLinear
        {
            get => _isLinear;
            set => SetProperty(ref _isLinear, value);
        }

        private int _duration;
        /// <summary>
        /// 持续时间（秒）
        /// </summary>
        public int Duration
        {
            get => _duration;
            set => SetProperty(ref _duration, value);
        }

        private int _index;
        /// <summary>
        /// 排序索引 1 开始
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 从DTO创建视图模型
        /// </summary>
        public static TestStepItemViewModel FromDto(TestStepDTO dto)
        {
            return new TestStepItemViewModel
            {
                Id = dto.Id,
                TestId = dto.TestItemId,
                Humidity = dto.Humidity,
                Temperature = dto.Temperature,
                IsLinear = dto.IsLinear,
                Duration = dto.DurationSeconds,
                Index = dto.StepNumber,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt
            };
        }

        /// <summary>
        /// 转换为DTO
        /// </summary>
        public TestStepDTO ToDto()
        {
            return new TestStepDTO
            {
                Id = Id,
                TestItemId = TestId,
                Humidity = Humidity,
                Temperature = Temperature,
                IsLinear = IsLinear,
                DurationSeconds = Duration,
                StepNumber = Index,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}
