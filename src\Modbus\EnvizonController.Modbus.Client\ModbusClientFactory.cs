using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Client
{
    /// <summary>
    /// Modbus客户端工厂
    /// </summary>
    public static class ModbusClientFactory
    {
        /// <summary>
        /// 创建Modbus客户端
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="transportType">传输类型</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        /// <returns>Modbus客户端</returns>
        public static IModbusClient CreateClient(
            IModbusChannel channel,
            ModbusTransportType transportType,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
        {
            return new ModbusClient(channel, transportType, retryCount, retryDelayMilliseconds, responseTimeout);
        }

        /// <summary>
        /// 创建Modbus RTU客户端
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        /// <returns>Modbus RTU客户端</returns>
        public static IModbusClient CreateRtuClient(
            IModbusChannel channel,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
        {
            return CreateClient(channel, ModbusTransportType.Rtu, retryCount, retryDelayMilliseconds, responseTimeout);
        }

        /// <summary>
        /// 创建Modbus ASCII客户端
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        /// <returns>Modbus ASCII客户端</returns>
        public static IModbusClient CreateAsciiClient(
            IModbusChannel channel,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
        {
            return CreateClient(channel, ModbusTransportType.Ascii, retryCount, retryDelayMilliseconds, responseTimeout);
        }

        /// <summary>
        /// 创建Modbus TCP客户端
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        /// <returns>Modbus TCP客户端</returns>
        public static IModbusClient CreateTcpClient(
            IModbusChannel channel,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
        {
            return CreateClient(channel, ModbusTransportType.Tcp, retryCount, retryDelayMilliseconds, responseTimeout);
        }
    }
}
