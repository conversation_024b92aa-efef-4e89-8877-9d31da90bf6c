﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Common\**" />
    <Compile Remove="Data\**" />
    <EmbeddedResource Remove="Common\**" />
    <EmbeddedResource Remove="Data\**" />
    <None Remove="Common\**" />
    <None Remove="Data\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Application\EnvizonController.Application.csproj" />
    <ProjectReference Include="..\EnvizonController.Configuration\EnvizonController.Configuration.csproj" />
    <ProjectReference Include="..\EnvizonController.Domain\EnvizonController.Domain.csproj" />
    <ProjectReference Include="..\EnvizonController.Shared\EnvizonController.Shared.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Abstractions\EnvizonController.Modbus.Abstractions.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Client\EnvizonController.Modbus.Client.csproj" />
    <ProjectReference Include="..\Modbus\EnvizonController.Modbus.Protocol\EnvizonController.Modbus.Protocol.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="System.Reactive" Version="6.0.1" />
  </ItemGroup>

</Project>
