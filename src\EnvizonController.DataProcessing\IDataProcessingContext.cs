using System;
using System.Collections.Generic;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 数据处理上下文接口
    /// </summary>
    public interface IDataProcessingContext
    {
        /// <summary>
        /// 获取或设置原始数据
        /// </summary>
        object RawData { get; set; }
        
        /// <summary>
        /// 获取或设置处理后的数据
        /// </summary>
        object ProcessedData { get; set; }
        
        /// <summary>
        /// 获取或设置数据源标识
        /// </summary>
        string SourceId { get; set; }
        
        /// <summary>
        /// 获取或设置数据类型
        /// </summary>
        string DataType { get; set; }
        
        /// <summary>
        /// 获取或设置时间戳
        /// </summary>
        DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 获取或设置元数据
        /// </summary>
        Dictionary<string, object> Metadata { get; set; }
        
        /// <summary>
        /// 获取处理状态
        /// </summary>
        ProcessingStatus Status { get; set; }
        
        /// <summary>
        /// 获取处理错误信息
        /// </summary>
        List<string> Errors { get; }
        
        /// <summary>
        /// 添加错误信息
        /// </summary>
        void AddError(string error);
        
        /// <summary>
        /// 创建上下文的副本
        /// </summary>
        IDataProcessingContext Clone();
    }

    /// <summary>
    /// 处理状态枚举
    /// </summary>
    public enum ProcessingStatus
    {
        /// <summary>
        /// 未处理
        /// </summary>
        NotProcessed,
        
        /// <summary>
        /// 处理中
        /// </summary>
        Processing,
        
        /// <summary>
        /// 处理成功
        /// </summary>
        Succeeded,
        
        /// <summary>
        /// 处理失败
        /// </summary>
        Failed,
        
        /// <summary>
        /// 已跳过
        /// </summary>
        Skipped
    }
}
