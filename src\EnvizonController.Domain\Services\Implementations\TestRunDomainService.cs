using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;

namespace EnvizonController.Domain.Services.Implementations;

/// <summary>
///     测试项服务实现
/// </summary>
public class TestRunDomainService : ITestRunDomainService
{
    private readonly ITestItemRepository _testItemRepository;
    private readonly IProgramRepository _programRepository;
    private readonly IProgramLinkRepository _programLinkRepository;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="testItemRepository">测试项仓储</param>
    /// <param name="programRepository">程式仓储</param>
    /// <param name="programLinkRepository">程式链接仓储</param>
    public TestRunDomainService(
        ITestItemRepository testItemRepository,
        IProgramRepository programRepository,
        IProgramLinkRepository programLinkRepository)
    {
        _testItemRepository = testItemRepository;
        _programRepository = programRepository;
        _programLinkRepository = programLinkRepository;
    }

    /// <summary>
    ///     创建新的测试项
    /// </summary>
    public async Task<TestRun> CreateTestItemAsync(TestRun testRun)
    {
        // 使用领域实体的方法创建测试项
        var newTestItem = TestRun.Create(testRun.Name, testRun.DeviceId, testRun.Status, testRun.Description);

        return newTestItem;
    }

    /// <summary>
    ///     更新测试项
    /// </summary>
    public async Task<TestRun> UpdateTestItemAsync(TestRun testRun)
    {
        await _testItemRepository.UpdateAsync(testRun);
        return testRun;
    }

    /// <summary>
    ///     删除测试项
    /// </summary>
    public async Task<bool> DeleteTestItemAsync(long id)
    {
        try
        {
            await _testItemRepository.DeleteAsync(id);
            return true;
        }
        catch (KeyNotFoundException)
        {
            return false;
        }
    }

    /// <summary>
    ///     启动测试
    /// </summary>
    public async Task<TestRun?> StartTestAsync(long id)
    {
        try
        {
            var testItem = await _testItemRepository.GetByIdAsync(id);

            // 使用领域实体的方法启动测试
            bool success = testItem.Start();

            if (!success)
            {
                return null;
            }

            // 保存到仓储
            await _testItemRepository.UpdateAsync(testItem);
            return testItem;
        }
        catch (KeyNotFoundException)
        {
            return null;
        }
    }

    /// <summary>
    ///     停止测试
    /// </summary>
    public async Task<TestRun?> StopTestAsync(long id)
    {
        try
        {
            var testItem = await _testItemRepository.GetByIdAsync(id);

            // 使用领域实体的方法停止测试
            bool success = testItem.Stop();

            if (!success)
            {
                return null;
            }

            // 保存到仓储
            await _testItemRepository.UpdateAsync(testItem);
            return testItem;
        }
        catch (KeyNotFoundException)
        {
            return null;
        }
    }

    /// <summary>
    ///     完成测试
    /// </summary>
    public async Task<TestRun?> CompleteTestAsync(long id)
    {
        try
        {
            var testItem = await _testItemRepository.GetByIdAsync(id);

            // 使用领域实体的方法完成测试
            bool success = testItem.Complete();

            if (!success)
            {
                return null;
            }

            // 保存到仓储
            await _testItemRepository.UpdateAsync(testItem);
            return testItem;
        }
        catch (KeyNotFoundException)
        {
            return null;
        }
    }

    /// <summary>
    ///     从程式创建测试项
    /// </summary>
    public async Task<TestRun> CreateTestFromProgramAsync(long programId, string testName, long deviceId, string? description = null)
    {
        // 获取程式及其步骤
        var program = await _programRepository.GetByIdWithStepsAsync(programId);
        if (program == null)
        {
            throw new KeyNotFoundException($"程式 ID: {programId} 不存在");
        }

        // 创建测试项
        var testRun = TestRun.Create(testName, deviceId, "Created", description);

        // 使用程式配置创建测试项的执行配置
        testRun.CreateFromProgram(program);

        return testRun;
    }

    /// <summary>
    ///     从程式链接创建测试项
    /// </summary>
    public async Task<TestRun> CreateTestFromProgramLinkAsync(long programLinkId, string testName, long deviceId, string? description = null)
    {
        // 获取程式链接及其关联的程式
        var programLink = await _programLinkRepository.GetByIdWithItemsAsync(programLinkId);
        if (programLink == null)
        {
            throw new KeyNotFoundException($"程式链接 ID: {programLinkId} 不存在");
        }

        // 创建测试项
        var testRun = TestRun.Create(testName, deviceId, "Created", description);

        // 使用程式链接配置创建测试项的执行配置
        testRun.CreateFromProgramLink(programLink);

        return testRun;
    }

    /// <summary>
    ///     创建新的测试项（同步版本）
    /// </summary>
    public TestRun CreateTestItem(TestRun testRun)
    {
        return CreateTestItemAsync(testRun).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     更新测试项（同步版本）
    /// </summary>
    public TestRun UpdateTestItem(TestRun testRun)
    {
        return UpdateTestItemAsync(testRun).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     删除测试项（同步版本）
    /// </summary>
    public bool DeleteTestItem(long id)
    {
        return DeleteTestItemAsync(id).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     启动测试（同步版本）
    /// </summary>
    public TestRun? StartTest(long id)
    {
        return StartTestAsync(id).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     停止测试（同步版本）
    /// </summary>
    public TestRun? StopTest(long id)
    {
        return StopTestAsync(id).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     完成测试（同步版本）
    /// </summary>
    public TestRun? CompleteTest(long id)
    {
        return CompleteTestAsync(id).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     从程式创建测试项（同步版本）
    /// </summary>
    public TestRun CreateTestFromProgram(long programId, string testName, long deviceId, string? description = null)
    {
        return CreateTestFromProgramAsync(programId, testName, deviceId, description).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     从程式链接创建测试项（同步版本）
    /// </summary>
    public TestRun CreateTestFromProgramLink(long programLinkId, string testName, long deviceId, string? description = null)
    {
        return CreateTestFromProgramLinkAsync(programLinkId, testName, deviceId, description).GetAwaiter().GetResult();
    }
}
