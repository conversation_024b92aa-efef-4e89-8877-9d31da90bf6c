using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Domain.Services;

/// <summary>
///     数据采集服务接口
///     负责处理数据采集的领域逻辑
/// </summary>
public interface IDataCollectionDomainService
{
    /// <summary>
    ///     保存采集的数据点
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="dataJson">采集的数据（JSON格式）</param>
    /// <returns>保存的数据点，如果测试不在运行中则返回null</returns>
    Task<TestDataPoint?> SaveDataPointAsync(long testRunId, TestDataPoint testDataPoint);

    /// <summary>
    ///     根据设备ID保存采集的数据点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="dataJson">采集的数据（JSON格式）</param>
    /// <returns>保存的数据点，如果没有找到运行中的测试项则返回null</returns>
    Task<TestDataPoint?> SaveDataPointByDeviceIdAsync(long deviceId, TestDataPoint testDataPoint);

    /// <summary>
    ///     创建数据采集报警
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="message">报警消息</param>
    /// <returns>创建的报警，如果测试不在运行中则返回null</returns>
    Task<Alarm?> CreateCollectionAlarmAsync(long testRunId, string message);

    /// <summary>
    ///     根据设备ID创建数据采集报警
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="message">报警消息</param>
    /// <returns>创建的报警，如果没有找到运行中的测试项则返回null</returns>
    Task<Alarm?> CreateCollectionAlarmByDeviceIdAsync(long deviceId, string message);
    
    /// <summary>
    ///     根据设备ID保存报警数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="alarms">报警数据列表</param>
    /// <returns>是否保存成功</returns>
    Task<bool> SaveAlarmsByDeviceIdAsync(long deviceId, List<Alarm> alarms);
}