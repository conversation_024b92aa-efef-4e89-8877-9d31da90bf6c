using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Infrastructure.Services.Devices
{
    /// <summary>
    /// 设备服务实现
    /// </summary>
    public class DeviceService : IDeviceService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DeviceService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="unitOfWork">工作单元</param>
        /// <param name="logger">日志记录器</param>
        public DeviceService(IUnitOfWork unitOfWork, ILogger<DeviceService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetAllDevicesAsync()
        {
            try
            {
                return await _unitOfWork.Devices.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备时出错");
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        public async Task<Device?> GetDeviceByIdAsync(long id)
        {
            try
            {
                return await _unitOfWork.Devices.GetByIdAsync(id);
            }
            catch (KeyNotFoundException)
            {
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取设备时出错: {Index}", id);
                throw;
            }
        }

        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        public async Task<Device?> GetDeviceByNameAsync(string name)
        {
            try
            {
                return await _unitOfWork.Devices.GetByNameAsync(name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据名称获取设备时出错: {Name}", name);
                throw;
            }
        }

        /// <summary>
        /// 根据协议ID获取设备列表
        /// </summary>
        public async Task<IEnumerable<Device>> GetDevicesByProtocolIdAsync(long protocolId)
        {
            try
            {
                return await _unitOfWork.Devices.GetByProtocolIdAsync(protocolId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据协议ID获取设备列表时出错: {ProtocolId}", protocolId);
                throw;
            }
        }

        /// <summary>
        /// 根据连接类型获取设备列表
        /// </summary>
        public async Task<IEnumerable<Device>> GetDevicesByConnectionTypeAsync(string connectionType)
        {
            try
            {
                return await _unitOfWork.Devices.GetByConnectionTypeAsync(connectionType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据连接类型获取设备列表时出错: {ConnectionType}", connectionType);
                throw;
            }
        }

        /// <summary>
        /// 获取所有串口设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetSerialDevicesAsync()
        {
            try
            {
                return await _unitOfWork.Devices.GetSerialDevicesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有串口设备时出错");
                throw;
            }
        }

        /// <summary>
        /// 获取所有网络设备
        /// </summary>
        public async Task<IEnumerable<Device>> GetNetworkDevicesAsync()
        {
            try
            {
                return await _unitOfWork.Devices.GetNetworkDevicesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有网络设备时出错");
                throw;
            }
        }

        /// <summary>
        /// 添加设备
        /// </summary>
        public async Task<Device> AddDeviceAsync(Device device)
        {
            try
            {
                // 检查设备名称是否已存在
                var existingDevice = await _unitOfWork.Devices.GetByNameAsync(device.Name);
                if (existingDevice != null)
                {
                    throw new InvalidOperationException($"设备名称 '{device.Name}' 已存在");
                }

                // 设置设备ID
                //if (device.Index == 0)
                //{
                //    device.Index = Guid.NewGuid().to;
                //}

                // 设置创建时间
                device.CreatedAt = DateTime.Now;

                // 添加设备
                await _unitOfWork.Devices.AddAsync(device);
                await _unitOfWork.SaveChangesAsync();

                return device;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加设备时出错: {Name}", device.Name);
                throw;
            }
        }

        /// <summary>
        /// 更新设备
        /// </summary>
        public async Task<Device> UpdateDeviceAsync(Device device)
        {
            try
            {
                // 检查设备是否存在
                var existingDevice = await _unitOfWork.Devices.GetByIdAsync(device.Id);

                // 检查设备名称是否已被其他设备使用
                var deviceWithSameName = await _unitOfWork.Devices.GetByNameAsync(device.Name);
                if (deviceWithSameName != null && deviceWithSameName.Id != device.Id)
                {
                    throw new InvalidOperationException($"设备名称 '{device.Name}' 已被其他设备使用");
                }

                // 设置更新时间
                device.LastUpdatedAt = DateTime.Now;

                // 更新设备
                await _unitOfWork.Devices.UpdateAsync(device);
                await _unitOfWork.SaveChangesAsync();

                return device;
            }
            catch (KeyNotFoundException)
            {
                throw new KeyNotFoundException($"未找到ID为 {device.Id} 的设备");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备时出错: {Index}", device.Id);
                throw;
            }
        }

        /// <summary>
        /// 删除设备
        /// </summary>
        public async Task<bool> DeleteDeviceAsync(long id)
        {
            try
            {
                // 检查设备是否存在
                var device = await _unitOfWork.Devices.GetByIdAsync(id);

                // 删除设备
                await _unitOfWork.Devices.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                return true;
            }
            catch (KeyNotFoundException)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备时出错: {Index}", id);
                throw;
            }
        }

    }
}
