using System;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core.Plugins;
using Avalonia.Markup.Xaml;
using CommunityToolkit.Mvvm.DependencyInjection;
using EnvizonController.ApiClient;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Mqtt.Client.DependencyInjection;
using EnvizonController.Presentation;
using EnvizonController.Presentation.Handlers;
using EnvizonController.Presentation.Initialization;
using EnvizonController.Presentation.Mqtt;
using EnvizonController.Presentation.Services;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Presentation.Views;
using HanumanInstitute.MvvmDialogs;
using HanumanInstitute.MvvmDialogs.Avalonia;
using HanumanInstitute.MvvmDialogs.Avalonia.MessageBox;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace EnvizonController;

public class App : Application
{
    private IConfiguration? _configuration;
    private IServiceProvider? _serviceProvider;

    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 创建配置
        var configBuilder = new ConfigurationBuilder().AddJsonFile("appsettings.json", true, true);

        _configuration = configBuilder.Build();
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(_configuration)
            .CreateLogger();

        // 添加配置服务
        services.AddSingleton(_configuration);
        services.AddSingleton(Log.Logger);
        services.AddLogging(configure => configure.AddSerilog(dispose: true)); // `dispose: true` 会在 ServiceProvider dispose 时也 dispose Serilog.Log.Logger

        //Dialog
        services.AddSingleton<IDialogService>(_ => new DialogService(
            new DialogManager(
                viewLocator: new MvvmDialogsViewLocator(),
                dialogFactory: new DialogFactory().AddDialogHost().AddMessageBox(MessageBoxMode.Popup)
            )
            ,
            viewModelFactory: x => Ioc.Default.GetService(x)));

        //Api
        services.AddApiClientServices();
        services.AddApiClient();

        // 注册IMessenger
        services.AddSingleton<CommunityToolkit.Mvvm.Messaging.IMessenger>(sp => CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Default);
  
        // 注册视图模型
        services.AddSingleton<MainViewModel>();
        services.AddSingleton<DashboardViewModel>();
        services.AddSingleton<SplashScreenViewModel>();
        services.AddSingleton<TestPlanViewModel>();
        services.AddSingleton<ProgramViewModel>();
        services.AddTransient<ProgramItemViewModel>();
        services.AddTransient<ProgramLinkItemViewModel>();
        services.AddSingleton<ProgramLinkViewModel>();
        services.AddSingleton<AlarmViewModel>();
        services.AddSingleton<DataQueryViewModel>();
        services.AddTransient<TestStartDialogViewModel>();
        
        // 注册应用初始化服务
        services.AddSingleton<IAppInitializationService, AppInitializationService>();
        // 手动注册ProtocolAppService，因为它依赖于IProtocolService
        // 添加MQTT客户端

        services.AddMqttServices();
        
        // 添加设备服务
        services.AddDeviceServices();
     
        // 注册DeviceCollectionDetailsMessageHandler
        services.AddTransient<DeviceCollectionDetailsMessageHandler>();

        // 注册窗口
        services.AddTransient<MainWindow>();
        services.AddTransient<SplashScreenWindow>();
    }

    public override void OnFrameworkInitializationCompleted()
    {
        // 配置依赖注入
        var serviceCollection = new ServiceCollection();
        ConfigureServices(serviceCollection); // 单独方法配置服务
        _serviceProvider = serviceCollection.BuildServiceProvider();
        Ioc.Default.ConfigureServices(_serviceProvider);

        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // Line below is needed to remove Avalonia data validation.
            // Without this line you will get duplicate validations from both Avalonia and CT
            BindingPlugins.DataValidators.RemoveAt(0);

            // 创建闪屏窗口并设置为主窗口
            var splashScreenWindow = _serviceProvider.GetRequiredService<SplashScreenWindow>();
            var splashScreenViewModel = _serviceProvider.GetRequiredService<SplashScreenViewModel>();
            splashScreenWindow.DataContext = splashScreenViewModel;

            // 设置闪屏窗口为当前主窗口
            desktop.MainWindow = splashScreenWindow;

            // 设置初始化完成后的回调
            splashScreenViewModel.InitializationCompletedCallback = () =>
            {
                // 从DI容器解析MainWindow
                var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                mainWindow.DataContext = _serviceProvider.GetRequiredService<MainViewModel>();
                desktop.MainWindow = mainWindow;
                mainWindow.Show();
                splashScreenWindow.Close(); // 关闭加载窗口
            };

            // 设置初始化错误的回调
            splashScreenViewModel.InitializationErrorCallback = (ex) =>
            {
                // 在这里处理错误，例如显示一个错误消息框
                var logger = _serviceProvider?.GetService<ILogger>();
                logger?.Error(ex, "应用程序初始化失败");
                // 可以在这里添加错误对话框显示
                desktop.Shutdown(-1); // 错误退出
            };

            // 启动初始化过程
            _ = splashScreenViewModel.StartInitializationAsync();
        }
        else if (ApplicationLifetime is ISingleViewApplicationLifetime singleViewPlatform)
        {
            singleViewPlatform.MainView = new MainView
            {
                DataContext = Ioc.Default.GetRequiredService<MainViewModel>()
            };
        }

        base.OnFrameworkInitializationCompleted();
    }
}