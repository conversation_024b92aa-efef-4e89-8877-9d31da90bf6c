using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Domain.Services.Implementations;

/// <summary>
///     报警领域服务实现
/// </summary>
public class AlarmDomainService : IAlarmDomainService
{
    private readonly IAlarmRepository _alarmRepository;

    public AlarmDomainService(
        IAlarmRepository alarmRepository)
    {
        _alarmRepository = alarmRepository;
    }

    /// <summary>
    ///     创建新的报警
    /// </summary>
    public async Task<Alarm?> CreateAlarmAsync(long testRunId, string name, string message,
        AlarmSeverity level = AlarmSeverity.High)
    {
        // 验证测试运行是否存在

        // 使用TestRun的方法添加报警，它会检查测试是否在运行中
        var alarm = Alarm.AddAlarm(name, message, testRunId);

        // 如果测试不在运行中，AddAlarm会返回null
        if (alarm == null) return null;

        // 设置报警级别
        alarm.Level = (int)level;
        return alarm;
    }

    /// <summary>
    ///     处理报警
    /// </summary>
    public async Task<Alarm> ProcessAlarmAsync(long alarmId, string processedBy)
    {
        var alarm = await _alarmRepository.GetByIdAsync(alarmId);

        // 使用实体的方法处理报警
        alarm.Process(processedBy);
        return alarm;
    }
}