using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Shared.DTOs.Common;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 测试数据点仓储实现
    /// </summary>
    public class TestDataPointRepository : Repository<TestDataPoint, long>, IDataPointRepository
    {
        public TestDataPointRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 获取特定测试运行的所有数据点
        /// </summary>
        public async Task<IEnumerable<TestDataPoint>> GetByTestRunIdAsync(long testRunId)
        {
            return await _dbSet
                .Where(d => d.TestId == testRunId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取特定测试运行的数据点分页列表
        /// </summary>
        public async Task<  PagedResultDto<TestDataPoint>> GetPagedByTestRunIdAsync(long testRunId, int page = 1, int pageSize = 20)
        {
            var query = _dbSet.Where(d => d.TestId == testRunId);
            
            var total = await query.CountAsync();
            var items = await query
                .OrderByDescending(d => d.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResultDto<TestDataPoint>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = total
            };
        }
    }
}
