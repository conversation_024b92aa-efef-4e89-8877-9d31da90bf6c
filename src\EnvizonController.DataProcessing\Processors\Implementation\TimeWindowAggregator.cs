using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Processors.Implementation
{
    /// <summary>
    /// 时间窗口聚合器
    /// </summary>
    public class TimeWindowAggregator : BaseDataProcessor, IDataAggregator
    {
        /// <summary>
        /// 聚合函数
        /// </summary>
        public Func<IEnumerable<object>, object> AggregateFunction { get; }
        
        /// <summary>
        /// 获取聚合窗口大小
        /// </summary>
        public int WindowSize { get; }
        
        /// <summary>
        /// 获取或设置聚合缓冲区
        /// </summary>
        public Queue<object> Buffer { get; private set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="description">处理器描述</param>
        /// <param name="aggregateFunction">聚合函数</param>
        /// <param name="windowSize">窗口大小</param>
        public TimeWindowAggregator(string name, string description, Func<IEnumerable<object>, object> aggregateFunction, int windowSize)
            : base(name, description)
        {
            AggregateFunction = aggregateFunction ?? throw new ArgumentNullException(nameof(aggregateFunction));
            WindowSize = windowSize > 0 ? windowSize : throw new ArgumentException("窗口大小必须大于0", nameof(windowSize));
            Buffer = new Queue<object>();
        }
        
        /// <summary>
        /// 添加数据到缓冲区
        /// </summary>
        /// <param name="data">数据</param>
        public void AddToBuffer(object data)
        {
            Buffer.Enqueue(data);
            
            // 如果缓冲区超过窗口大小，则移除最早的数据
            while (Buffer.Count > WindowSize)
            {
                Buffer.Dequeue();
            }
        }
        
        /// <summary>
        /// 清空缓冲区
        /// </summary>
        public void ClearBuffer()
        {
            Buffer.Clear();
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public override Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            try
            {
                // 添加数据到缓冲区
                AddToBuffer(context.RawData);
                
                // 如果缓冲区已满，则执行聚合
                if (Buffer.Count >= WindowSize)
                {
                    context.ProcessedData = AggregateFunction(Buffer);
                    
                    // 清空缓冲区
                    ClearBuffer();
                }
                else
                {
                    // 缓冲区未满，跳过后续处理
                    context.Status = ProcessingStatus.Skipped;
                }
                
                return Task.FromResult(context);
            }
            catch (Exception ex)
            {
                context.Status = ProcessingStatus.Failed;
                context.AddError($"聚合器 {Name} 处理时出错: {ex.Message}");
                return Task.FromResult(context);
            }
        }
    }
}
