<UserControl
    x:Class="EnvizonController.Presentation.Views.FeatureUnderDevelopmentView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="viewModels:FeatureUnderDevelopmentViewModel"
    mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="TextBlock">
            <Setter Property="Foreground" Value="#FFFFFF" />
        </Style>
        <Style Selector="TextBlock.header">
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="FontSize" Value="24" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,20" />
            <Setter Property="HorizontalAlignment" Value="Center" />
        </Style>
        <Style Selector="TextBlock.message">
            <Setter Property="Foreground" Value="#FFFFFF" />
            <Setter Property="FontSize" Value="18" />
            <Setter Property="TextWrapping" Value="Wrap" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,0,0,30" />
        </Style>
        <Style Selector="TextBlock.info">
            <Setter Property="Foreground" Value="#9DA3AF" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,5,0,5" />
        </Style>
    </UserControl.Styles>

    <Grid Background="#101824" RowDefinitions="*,Auto,*">
        <StackPanel
            Grid.Row="1"
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <!--  图标和标题  -->
            <Border
                Width="120"
                Height="120"
                Margin="0,0,0,30"
                Background="#1A2A3A"
                BorderBrush="#0DF0FF"
                BorderThickness="2"
                CornerRadius="60">
                <PathIcon
                    Width="60"
                    Height="60"
                    Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A1,1 0 0,0 11,7V12.5L15.2,16.7A1,1 0 0,0 16.6,16.7A1,1 0 0,0 16.6,15.3L13,11.7V7A1,1 0 0,0 12,6Z"
                    Foreground="#0DF0FF" />
            </Border>

            <!--  主标题  -->
            <TextBlock Classes="header" Text="{Binding FeatureName}" />

            <!--  主消息  -->
            <TextBlock Classes="message" Text="{Binding Message}" />

        </StackPanel>
    </Grid>
</UserControl> 