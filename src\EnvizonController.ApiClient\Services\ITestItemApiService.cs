using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 测试项API服务接口
    /// </summary>
    public interface ITestItemApiService : IApiService
    {
        /// <summary>
        /// 获取所有测试项
        /// </summary>
        Task<Result<PagedResultDto<TestRunDTO>>> GetTestItemsAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取所有测试项(带查询参数对象)
        /// </summary>
        Task<Result<PagedResultDto<TestRunDTO>>> GetTestItemsAsync(TestItemQueryParams queryParams);

        /// <summary>
        /// 根据ID获取测试项
        /// </summary>
        Task<Result<TestRunDTO>> GetTestItemAsync(long id);

        /// <summary>
        /// 创建测试项
        /// </summary>
        Task<Result<TestRunDTO>> CreateTestItemAsync(TestRunDTO testRun);

        /// <summary>
        /// 更新测试项
        /// </summary>
        Task<Result<TestRunDTO>> UpdateTestItemAsync(long id, TestRunDTO testRun);

        /// <summary>
        /// 删除测试项
        /// </summary>
        Task<Result<bool>> DeleteTestItemAsync(long id);

        /// <summary>
        /// 启动测试
        /// </summary>
        Task<Result<TestRunDTO>> StartTestAsync(long id);

        /// <summary>
        /// 停止测试
        /// </summary>
        Task<Result<TestRunDTO>> StopTestAsync(long id);


        /// <summary>
        /// 删除测试步骤
        /// </summary>
        Task<Result<bool>> RemoveTestStepAsync(long testItemId, int stepNumber);

        /// <summary>
        /// 清空测试步骤
        /// </summary>
        Task<Result<bool>> ClearTestStepsAsync(long testItemId);
    }
} 