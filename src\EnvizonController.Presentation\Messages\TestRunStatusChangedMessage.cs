﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 测试运行状态变更消息
/// </summary>
public class TestRunStatusChangedMessage : ValueChangedMessage<(long TestRunId, TestRunStatus Status)>
{
    /// <summary>
    /// 测试运行ID
    /// </summary>
    public long TestRunId => Value.TestRunId;

    /// <summary>
    /// 测试运行状态
    /// </summary>
    public TestRunStatus Status => Value.Status;

    /// <summary>
    /// 创建测试运行状态变更消息
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <param name="status">测试运行状态</param>
    public TestRunStatusChangedMessage(long testRunId, TestRunStatus status) 
        : base((testRunId, status))
    {
    }
}

/// <summary>
/// 测试运行状态
/// </summary>
public enum TestRunStatus
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted = 0,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 5
}
