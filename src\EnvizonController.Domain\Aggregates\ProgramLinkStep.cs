using System;
using System.ComponentModel.DataAnnotations.Schema;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 程式链接项实体
    /// 表示程式链接中的一个程式项，包含执行顺序等信息
    /// </summary>
    [Table("ProgramLinkSteps")]
    public class ProgramLinkStep : BaseEntity<long>
    {
        /// <summary>
        /// 所属程式链接ID
        /// </summary>
        public long ProgramLinkId { get; set; }

        /// <summary>
        /// 关联的程式ID
        /// </summary>
        public long ProgramId { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int ExecutionOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 关联的程式链接
        /// </summary>
        public virtual ProgramLink? ProgramLink { get; set; }

        /// <summary>
        /// 关联的程式
        /// </summary>
        public virtual Program? Program { get; set; }
    }
}