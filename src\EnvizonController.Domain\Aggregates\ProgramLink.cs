using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 程式链接聚合根
    /// 表示一个将多个程式串联在一起的测试流程
    /// </summary>
    [Table("ProgramLinks")]
    public class ProgramLink : BaseEntity<long>
    {
        /// <summary>
        /// 程式链接名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 循环次数
        /// </summary>
        public int CycleCount { get; set; } = 1;

        /// <summary>
        /// 程式链接项集合
        /// </summary>
        public List<ProgramLinkStep> Items { get; set; } = new List<ProgramLinkStep>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加程式到链接中
        /// </summary>
        /// <param name="program">要添加的程式</param>
        /// <param name="order">执行顺序(可选，默认添加到末尾)</param>
        /// <returns>添加的链接项</returns>
        public ProgramLinkStep AddProgram(Program program, int? order = null)
        {
            // 确定插入顺序
            int insertOrder = order ?? (Items.Count > 0 ? Items.Max(i => i.ExecutionOrder) + 1 : 1);
            
            // 如果指定了顺序，需要将该位置及之后的项目顺序加1
            if (order.HasValue)
            {
                foreach (var item in Items.Where(i => i.ExecutionOrder >= insertOrder))
                {
                    item.ExecutionOrder++;
                }
            }
            
            // 创建新的链接项
            var linkItem = new ProgramLinkStep
            {
                ProgramLinkId = this.Id,
                ProgramId = program.Id,
                Program = program,
                ExecutionOrder = insertOrder,
                CreatedAt = DateTime.Now
            };
            
            Items.Add(linkItem);
            UpdatedAt = DateTime.Now;
            
            return linkItem;
        }

        /// <summary>
        /// 移除程式链接项
        /// </summary>
        /// <param name="linkItem">要移除的链接项</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveLinkItem(ProgramLinkStep linkItem)
        {
            bool result = Items.Remove(linkItem);
            if (result)
            {
                // 重新排序剩余项
                int order = 1;
                foreach (var item in GetOrderedItems())
                {
                    item.ExecutionOrder = order++;
                }
                UpdatedAt = DateTime.Now;
            }
            return result;
        }

        /// <summary>
        /// 根据程式ID移除链接项
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveProgramById(long programId)
        {
            var item = Items.FirstOrDefault(i => i.ProgramId == programId);
            if (item != null)
            {
                return RemoveLinkItem(item);
            }
            return false;
        }

        /// <summary>
        /// 清空程式链接项集合
        /// </summary>
        public void ClearItems()
        {
            Items.Clear();
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 根据执行顺序查找程式链接项
        /// </summary>
        /// <param name="order">执行顺序</param>
        /// <returns>程式链接项，如果未找到则返回null</returns>
        public ProgramLinkStep? FindItemByOrder(int order)
        {
            return Items.FirstOrDefault(item => item.ExecutionOrder == order);
        }
        
        /// <summary>
        /// 根据程式ID查找程式链接项
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>程式链接项，如果未找到则返回null</returns>
        public ProgramLinkStep? FindItemByProgramId(long programId)
        {
            return Items.FirstOrDefault(item => item.ProgramId == programId);
        }
        
        /// <summary>
        /// 获取排序后的程式链接项列表
        /// </summary>
        /// <returns>按执行顺序排序的程式链接项列表</returns>
        public List<ProgramLinkStep> GetOrderedItems()
        {
            return Items.OrderBy(item => item.ExecutionOrder).ToList();
        }

        /// <summary>
        /// 调整程式在链接中的执行顺序
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <param name="newOrder">新的执行顺序</param>
        /// <returns>是否成功调整</returns>
        public bool ReorderProgram(long programId, int newOrder)
        {
            var item = FindItemByProgramId(programId);
            if (item == null || newOrder < 1 || newOrder > Items.Count)
            {
                return false;
            }

            int oldOrder = item.ExecutionOrder;
            if (oldOrder == newOrder)
            {
                return true; // 顺序没有变化
            }

            // 调整其他项目的顺序
            if (oldOrder < newOrder)
            {
                // 向后移动
                foreach (var affectedItem in Items.Where(i => i.ExecutionOrder > oldOrder && i.ExecutionOrder <= newOrder))
                {
                    affectedItem.ExecutionOrder--;
                }
            }
            else
            {
                // 向前移动
                foreach (var affectedItem in Items.Where(i => i.ExecutionOrder >= newOrder && i.ExecutionOrder < oldOrder))
                {
                    affectedItem.ExecutionOrder++;
                }
            }

            // 设置新顺序
            item.ExecutionOrder = newOrder;
            UpdatedAt = DateTime.Now;
            
            return true;
        }

        /// <summary>
        /// 获取链接中的所有程式
        /// </summary>
        /// <returns>按执行顺序排序的程式列表</returns>
        public List<Program> GetLinkedPrograms()
        {
            return GetOrderedItems()
                .Select(item => item.Program)
                .Where(program => program != null)
                .ToList();
        }

        /// <summary>
        /// 验证程式链接是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool Validate()
        {
            // 确保至少有一个程式
            if (Items.Count == 0)
            {
                return false;
            }
            
            // 确保所有链接项都有关联的程式
            if (Items.Any(item => item.Program == null))
            {
                return false;
            }
            
            // 确保执行顺序是连续的
            var orders = Items.Select(item => item.ExecutionOrder).OrderBy(o => o).ToList();
            for (int i = 0; i < orders.Count; i++)
            {
                if (orders[i] != i + 1)
                {
                    return false;
                }
            }
            
            return true;
        }
    }
}