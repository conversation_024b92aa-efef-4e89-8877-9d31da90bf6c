<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-android</TargetFramework>
    <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
    <Nullable>enable</Nullable>
    <ApplicationId>com.CompanyName.EnvizonController</ApplicationId>
    <ApplicationVersion>1</ApplicationVersion>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <AndroidPackageFormat>apk</AndroidPackageFormat>
    <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
  </PropertyGroup>

  <ItemGroup>
    <AndroidResource Include="Icon.png">
      <Link>Resources\drawable\Icon.png</Link>
    </AndroidResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia.Android" Version="$(AvaloniaVersion)" />
    <PackageReference Include="Xamarin.AndroidX.Core.SplashScreen" Version="1.0.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\EnvizonController\EnvizonController.csproj" />
  </ItemGroup>
</Project>
