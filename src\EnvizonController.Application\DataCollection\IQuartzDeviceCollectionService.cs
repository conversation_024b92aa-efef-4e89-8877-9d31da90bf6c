using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Application.DataCollection;

/// <summary>
/// Quartz设备数据采集服务接口
/// </summary>
public interface IQuartzDeviceCollectionService
{
    /// <summary>
    /// 启动调度器
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止调度器
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 为设备安排数据采集任务
    /// </summary>
    Task<bool> ScheduleDeviceCollectionTask(Device device);
    
    /// <summary>
    /// 取消设备数据采集任务
    /// </summary>
    Task<bool> UnscheduleDeviceCollectionTask(long deviceId);
    
    /// <summary>
    /// 更新设备数据采集任务
    /// </summary>
    Task<bool> UpdateDeviceCollectionTask(Device device);
} 