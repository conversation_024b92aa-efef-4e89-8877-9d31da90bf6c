# EnvizonController.Server API设计方案

## 目录

- [1. 系统现状分析](#1-系统现状分析)
- [2. Web API接口设计](#2-web-api接口设计)
  - [2.1 设备管理API](#21-设备管理api)
  - [2.2 报警管理API](#22-报警管理api)
  - [2.3 数据采集API](#23-数据采集api)
- [3. 数据查询优化方案](#3-数据查询优化方案)
  - [3.1 数据库查询优化](#31-数据库查询优化)
  - [3.2 架构优化](#32-架构优化)
  - [3.3 针对时序数据的特殊优化](#33-针对时序数据的特殊优化)
  - [3.4 考虑使用专业时序数据库](#34-考虑使用专业时序数据库)
- [4. API实现技术选择](#4-api实现技术选择)
  - [4.1 接口实现方式](#41-接口实现方式)
  - [4.2 数据传输](#42-数据传输)
- [5. 安全考虑](#5-安全考虑)
- [6. 具体实现计划](#6-具体实现计划)

## 1. 系统现状分析

通过对现有代码的分析，EnvizonController系统已有良好的领域模型设计和应用服务实现，但缺乏Web API接口。主要需要支持以下实体的增删改查：

- **设备(Device)**：通信设备的配置和状态信息
- **报警(Alarm)**：系统发出的异常警报
- **数据采集(TestDataPoint)**：设备采集的监测数据点

当前系统已通过服务层实现了核心业务逻辑：

- `DeviceAppService`：提供设备管理功能
- `AlarmAppService`：提供报警管理功能
- `DataCollectionAppService`：提供数据采集管理功能

但尚未将这些功能通过API暴露给客户端使用。

## 2. Web API接口设计

我们将设计一个RESTful风格的API，使用标准HTTP方法表示操作意图。

### 2.1 设备管理API

| 方法 | 路径 | 描述 | 参数 | 返回 |
|------|------|------|------|------|
| GET | /api/devices | 获取设备列表 | 可选：page, pageSize, sortBy, filter | 设备列表、总数和分页信息 |
| GET | /api/devices/{id} | 获取特定设备详情 | id: 设备ID | 设备详情 |
| POST | /api/devices | 创建新设备 | 设备信息JSON | 创建的设备信息 |
| PUT | /api/devices/{id} | 更新设备信息 | id: 设备ID, 更新的设备信息JSON | 更新后的设备信息 |
| DELETE | /api/devices/{id} | 删除设备 | id: 设备ID | 操作结果 |

#### 示例请求和响应

**获取设备列表:**
```http
GET /api/devices?page=1&pageSize=10
```

**响应:**
```json
{
  "items": [
    {
      "id": 1,
      "name": "默认测试设备",
      "slaveId": 5,
      "connectionType": "Serial",
      "transportType": "RTU",
      "status": "Normal",
      "connectionStatus": "Disconnected",
      "createdAt": "2025-05-10T08:15:30Z",
      "lastUpdatedAt": null,
      "autoStart": true,
      "collectionIntervalMs": 1000
    },
    ...
  ],
  "totalCount": 42,
  "page": 1,
  "pageSize": 10,
  "totalPages": 5
}
```

**创建设备:**
```http
POST /api/devices
Content-Type: application/json

{
  "name": "新测试设备",
  "slaveId": 10,
  "connectionType": "TCP",
  "transportType": "TCP",
  "hostAddress": "*************",
  "port": 502,
  "status": "Normal",
  "autoStart": true,
  "collectionIntervalMs": 2000,
  "protocolId": 1
}
```

### 2.2 报警管理API

| 方法 | 路径 | 描述 | 参数 | 返回 |
|------|------|------|------|------|
| GET | /api/alarms | 获取报警列表 | 可选：page, pageSize, status, level, startDate, endDate | 报警列表、总数和分页信息 |
| GET | /api/alarms/{id} | 获取特定报警详情 | id: 报警ID | 报警详情 |
| POST | /api/alarms | 创建新报警 | 报警信息JSON | 创建的报警信息 |
| PUT | /api/alarms/{id}/process | 处理特定报警 | id: 报警ID, 处理信息JSON | 处理后的报警信息 |
| GET | /api/alarms/active | 获取所有活动报警 | 可选：page, pageSize | 活动报警列表 |
| GET | /api/alarms/by-level/{level} | 获取特定级别的报警 | level: 报警级别, 可选：page, pageSize | 指定级别的报警列表 |

#### 示例请求和响应

**获取活动报警:**
```http
GET /api/alarms/active?page=1&pageSize=10
```

**响应:**
```json
{
  "items": [
    {
      "id": "a1b2c3d4",
      "testId": 5,
      "name": "温度超限",
      "message": "设备温度超过预设阈值",
      "level": 2,
      "status": 1,
      "timestamp": "2025-05-12T02:15:30Z",
      "lastUpdated": "2025-05-12T02:15:30Z",
      "processedAt": null,
      "processedBy": null
    },
    ...
  ],
  "totalCount": 3,
  "page": 1,
  "pageSize": 10,
  "totalPages": 1
}
```

**处理报警:**
```http
PUT /api/alarms/a1b2c3d4/process
Content-Type: application/json

{
  "processedBy": "操作员张三"
}
```

### 2.3 数据采集API

| 方法 | 路径 | 描述 | 参数 | 返回 |
|------|------|------|------|------|
| GET | /api/data-points | 获取数据点列表 | 可选：page, pageSize, startTime, endTime | 数据点列表、总数和分页信息 |
| GET | /api/data-points/{id} | 获取特定数据点详情 | id: 数据点ID | 数据点详情 |
| GET | /api/data-points/by-test/{testId} | 获取特定测试的数据点 | testId: 测试ID, 可选：page, pageSize, startTime, endTime | 测试相关的数据点列表 |
| GET | /api/data-points/by-device/{deviceId} | 获取特定设备的数据点 | deviceId: 设备ID, 可选：page, pageSize, startTime, endTime | 设备相关的数据点列表 |
| GET | /api/data-points/aggregate | 获取聚合数据 | 必选：deviceId/testId, startTime, endTime, interval; 可选：aggregateFunc (avg/min/max/sum) | 聚合后的数据点列表 |

#### 示例请求和响应

**获取设备数据点:**
```http
GET /api/data-points/by-device/1?startTime=2025-05-01T00:00:00Z&endTime=2025-05-12T23:59:59Z&page=1&pageSize=100
```

**响应:**
```json
{
  "items": [
    {
      "id": 10045,
      "testId": 3,
      "timestamp": "2025-05-12T10:15:30Z",
      "values": [
        {
          "protocolIndex": 1,
          "value": "27.5"
        },
        {
          "protocolIndex": 2,
          "value": "75.2"
        }
      ]
    },
    ...
  ],
  "totalCount": 8640,
  "page": 1,
  "pageSize": 100,
  "totalPages": 87
}
```

**获取聚合数据:**
```http
GET /api/data-points/aggregate?deviceId=1&startTime=2025-05-01T00:00:00Z&endTime=2025-05-12T23:59:59Z&interval=hour&aggregateFunc=avg
```

## 3. 数据查询优化方案

针对数据采集量大的情况，我们提出以下优化方案：

### 3.1 数据库查询优化

1. **分页优化**：
   - 改进当前分页实现，使用数据库级别分页而非内存分页
   - 实现高效的SQL查询，直接在数据库层面进行`Skip/Take`操作
   - 使用`IQueryable`延迟执行而非即时加载全部数据

2. **索引优化**：
   - 对频繁查询的字段创建索引：
     * TestId
     * DeviceId (通过关联查询)
     * Timestamp
   - 对组合查询条件创建复合索引
   - 考虑使用覆盖索引减少IO操作

3. **查询条件优化**：
   - 支持更多过滤条件，减少需传输的数据量
   - 在数据库层面实现过滤，避免全表扫描
   - 使用参数化查询避免SQL注入并提高查询缓存命中率

4. **数据库层面分区**：
   - 按时间范围对数据表进行分区
   - 历史数据和实时数据使用不同的存储策略

```sql
-- 优化后的查询示例（使用时间范围和分页）
SELECT dp.* FROM TestDataPoints dp
INNER JOIN TestItems ti ON dp.TestId = ti.Id
WHERE ti.DeviceId = @deviceId
  AND dp.Timestamp BETWEEN @startTime AND @endTime
ORDER BY dp.Timestamp DESC
OFFSET @offset ROWS
FETCH NEXT @pageSize ROWS ONLY;
```

### 3.2 架构优化

1. **缓存策略**：
   - 实现多级缓存机制：
     * 一级缓存：应用内存缓存（短时间内的频繁查询）
     * 二级缓存：分布式缓存如Redis（热点数据）
   - 针对不同查询场景配置不同缓存策略：
     * 设备列表：长时间缓存，变更时失效
     * 实时数据：短时间缓存，定期刷新
     * 历史聚合数据：长时间缓存

```mermaid
graph TD
    Client[客户端] --> API[API层]
    API --> Cache[缓存层]
    Cache --> Service[服务层]
    Service --> DB[(数据库)]
    
    subgraph 缓存策略
    MemCache[内存缓存<br>短期有效]
    DistCache[分布式缓存<br>中长期有效]
    end
    
    Cache --> MemCache
    Cache --> DistCache
```

2. **聚合查询**：
   - 实现数据聚合API，提供以下聚合功能：
     * 按时间间隔（分钟、小时、天、月）聚合
     * 聚合函数：平均值、最大值、最小值、总和、计数
   - 预计算常用时间范围的聚合结果并缓存

3. **异步处理**：
   - 大型查询使用异步处理，避免阻塞
   - 考虑实现后台作业生成报表，结果通过API获取

### 3.3 针对时序数据的特殊优化

1. **降采样存储**：
   - 实现多级数据保留策略：
     * 原始数据：保留30天
     * 5分钟聚合数据：保留90天
     * 1小时聚合数据：保留1年
     * 1天聚合数据：永久保留
   - 自动数据生命周期管理，定期清理或归档

```mermaid
graph LR
    RawData[原始数据收集] --> Processing[数据处理]
    Processing --> HotStorage[热数据存储<br>最近30天]
    Processing --> Aggregation[数据聚合]
    Aggregation --> ColdStorage[冷数据存储<br>降采样历史数据]
    HotStorage --> Query[查询服务]
    ColdStorage --> Query
```

2. **冷热数据分离**：
   - 热数据（最近30天）：使用高性能存储，优化读写速度
   - 冷数据（历史数据）：使用大容量存储，优化存储成本
   - 查询时自动从适当的存储读取数据

3. **压缩策略**：
   - 对历史数据使用压缩存储
   - 实现智能数据压缩算法，保留关键变化点

### 3.4 考虑使用专业时序数据库

对于大量时序数据，考虑引入专业时序数据库：

1. **InfluxDB**：
   - 针对时间序列数据优化的开源数据库
   - 内置的降采样和数据保留策略
   - 高效的压缩算法
   - 强大的查询语言（Flux/InfluxQL）

2. **TimescaleDB**：
   - PostgreSQL的时序数据扩展
   - 兼容SQL，易于集成
   - 自动分区和索引优化
   - 支持连续聚合

3. **集成方案**：
   - 保留现有EF Core数据访问层用于元数据管理
   - 时序数据存储在专用时序数据库
   - 通过仓储模式屏蔽底层实现细节

```mermaid
graph TB
    Device[设备] --> Collector[数据采集服务]
    Collector --> DataProcessor[数据处理]
    DataProcessor --> MetaDB[(关系数据库<br>设备/报警管理)]
    DataProcessor --> TSDB[(时序数据库<br>测量数据)]
    API[API服务] --> MetaDB
    API --> TSDB
    API --> Client[客户端]
```

## 4. API实现技术选择

### 4.1 接口实现方式

1. **Controller方式**：
   - 使用ASP.NET Core MVC模式
   - 每个主要实体创建一个Controller类：
     * `DevicesController`
     * `AlarmsController`
     * `DataPointsController`
   - 示例实现：

```csharp
[ApiController]
[Route("api/[controller]")]
public class DevicesController : ControllerBase
{
    private readonly IDeviceAppService _deviceService;
    
    public DevicesController(IDeviceAppService deviceService)
    {
        _deviceService = deviceService;
    }
    
    [HttpGet]
    public async Task<ActionResult<PagedResult<DeviceDto>>> GetDevices(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        var devices = await _deviceService.GetAllDevicesAsync();
        // 应用分页逻辑
        return Ok(new PagedResult<DeviceDto>
        {
            Items = devices.Skip((page - 1) * pageSize).Take(pageSize),
            TotalCount = devices.Count(),
            Page = page,
            PageSize = pageSize
        });
    }
    
    [HttpGet("{id}")]
    public async Task<ActionResult<DeviceDto>> GetDevice(long id)
    {
        var device = await _deviceService.GetDeviceByIdAsync(id);
        if (device == null)
        {
            return NotFound();
        }
        return Ok(device);
    }
    
    // 其他CRUD操作...
}
```

2. **Minimal API方式**：
   - 使用ASP.NET Core的Minimal API
   - 简洁的Lambda表达式定义端点
   - 更轻量级，适合简单API场景
   - 示例实现：

```csharp
app.MapGet("/api/devices", async (
    IDeviceAppService deviceService,
    int page = 1,
    int pageSize = is) =>
{
    var devices = await deviceService.GetAllDevicesAsync();
    // 应用分页逻辑
    return Results.Ok(new PagedResult<DeviceDto>
    {
        Items = devices.Skip((page - 1) * pageSize).Take(pageSize),
        TotalCount = devices.Count(),
        Page = page,
        PageSize = pageSize
    });
});

app.MapGet("/api/devices/{id}", async (
    IDeviceAppService deviceService,
    long id) =>
{
    var device = await deviceService.GetDeviceByIdAsync(id);
    return device is null ? Results.NotFound() : Results.Ok(device);
});

// 其他端点定义...
```

### 4.2 数据传输

1. **DTO模式**：
   - 定义专用的数据传输对象，用于API数据交换
   - 使用AutoMapper进行对象映射
   - 确保API契约与内部模型解耦

```csharp
// DTO类定义
public class DeviceDto
{
    public long Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public byte SlaveId { get; set; }
    public string ConnectionType { get; set; } = string.Empty;
    public string TransportType { get; set; } = string.Empty;
    public DeviceStatus Status { get; set; }
    public ConnectionStatus ConnectionStatus { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdatedAt { get; set; }
    public bool AutoStart { get; set; }
    public int CollectionIntervalMs { get; set; }
    // 移除敏感字段或内部字段
}

// AutoMapper配置
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<Device, DeviceDto>();
        CreateMap<CreateDeviceDto, Device>();
        CreateMap<UpdateDeviceDto, Device>();
        // 其他映射...
    }
}
```

2. **API文档**：
   - 使用Swagger/OpenAPI生成接口文档
   - 配置详细的API描述、参数说明和示例
   - 支持客户端代码自动生成

```csharp
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "EnvizonController API",
        Version = "v1",
        Description = "EnvizonController系统的API接口"
    });
    
    // 添加XML注释
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);
});
```

## 5. 安全考虑

1. **认证与授权**：
   - 实现JWT认证：
     * 用户登录获取令牌
     * API请求需携带有效令牌
   - 基于角色的权限控制：
     * 管理员：完全访问权限
     * 操作员：只读或有限写入权限
     * 访客：只读权限

```csharp
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("ReadOnly", policy => policy.RequireRole("Guest", "Operator", "Admin"));
    options.AddPolicy("WriteAccess", policy => policy.RequireRole("Operator", "Admin"));
});
```

2. **输入验证**：
   - 使用FluentValidation验证请求
   - 防止SQL注入和XSS攻击
   - 限制请求大小和频率

```csharp
public class CreateDeviceValidator : AbstractValidator<CreateDeviceDto>
{
    public CreateDeviceValidator()
    {
        RuleFor(x => x.Name).NotEmpty().MaximumLength(100);
        RuleFor(x => x.SlaveId).InclusiveBetween((byte)1, (byte)247);
        RuleFor(x => x.ProtocolId).GreaterThan(0);
        RuleFor(x => x.ConnectionType).NotEmpty().MaximumLength(50);
        RuleFor(x => x.TransportType).NotEmpty().MaximumLength(50);
        // 其他验证规则...
    }
}

// 注册验证器
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssemblyContaining<CreateDeviceValidator>();
```

3. **CORS配置**：
   - 配置跨域资源共享策略
   - 限制允许的来源、方法和头信息

```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("DefaultPolicy", policy =>
    {
        policy.WithOrigins(
                "http://localhost:5000",
                "https://yourfrontenddomain.com")
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 在请求管道中启用CORS
app.UseCors("DefaultPolicy");
```

4. **安全HTTP头**：
   - 配置内容安全策略
   - 启用XSS保护
   - 防止点击劫持

```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    await next();
});
```

## 6. 具体实现计划

### 第一阶段：基础API实现（2周）

1. 配置API基础架构（3天）
   - 添加必要的NuGet包
   - 配置Swagger文档
   - 设置认证和授权
   - 定义DTO类和AutoMapper配置

2. 实现设备API（4天）
   - 添加DevicesController
   - 实现CRUD操作
   - 添加输入验证
   - 编写单元测试

3. 实现报警API（4天）
   - 添加AlarmsController
   - 实现CRUD和处理操作
   - 支持各种查询方式
   - 编写单元测试

4. 实现基础数据点API（3天）
   - 添加DataPointsController
   - 实现标准查询操作
   - 编写单元测试

### 第二阶段：数据采集API优化（3周）

1. 数据库优化（1周）
   - 创建和优化索引
   - 重构仓储查询
   - 实现高效分页

2. 缓存层实现（1周）
   - 添加内存缓存
   - 配置Redis分布式缓存
   - 实现缓存策略

3. 数据聚合API（1周）
   - 实现聚合查询端点
   - 支持时间区间统计
   - 优化大数据量查询性能

### 第三阶段：高级功能（2周）

1. 实时数据推送（1周）
   - 实现SignalR Hub
   - 配置连接和消息传递
   - 设计客户端集成方式

2. 批量操作和高级功能（1周）
   - 实现批量导入/导出
   - 批量处理报警
   - 高级查询和过滤功能

### 第四阶段：测试和文档（1周）

1. 全面测试
   - 单元测试
   - 集成测试
   - 负载测试

2. 文档完善
   - API文档更新
   - 使用示例
   - 部署指南