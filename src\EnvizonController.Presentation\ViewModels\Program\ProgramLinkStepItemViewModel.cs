﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.ViewModels.Program;

/// <summary>
///     程式联动步骤项视图模型
/// </summary>
public class ProgramLinkStepItemViewModel : ObservableObject
{
    private int _executionOrder;
    private long _id;


    private bool _isSelected;

    private long _programLinkId;

    /// <summary>
    ///     步骤ID
    /// </summary>
    public long Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    /// <summary>
    ///     所属程式联动表ID
    /// </summary>
    public long ProgramLinkId
    {
        get => _programLinkId;
        set => SetProperty(ref _programLinkId, value);
    }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    private ProgramDTO _program;

    public ProgramDTO Program
    {
        get => ProgramLinkViewModel.StaticAvailablePrograms.FirstOrDefault(s => s.Id == _program.Id).Program;
        set
        {
            SetProperty(ref _program, value);
        }
    }

    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    ///     排序索引 1 开始
    /// </summary>
    public int ExecutionOrder
    {
        get => _executionOrder;
        set => SetProperty(ref _executionOrder, value);
    }

    /// <summary>
    ///     从DTO创建视图模型
    /// </summary>
    public static ProgramLinkStepItemViewModel FromDto(ProgramLinkStepDTO dto)
    {
        return new ProgramLinkStepItemViewModel
        {
            Id = dto.Id,
            ProgramLinkId = dto.ProgramLinkId,
            ExecutionOrder = dto.ExecutionOrder,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            Program = dto.Program
        };
    }

    /// <summary>
    ///     转换为DTO
    /// </summary>
    public ProgramLinkStepDTO ToDto()
    {
        return new ProgramLinkStepDTO
        {
            Id = Id,
            ProgramLinkId = ProgramLinkId,
            ExecutionOrder = ExecutionOrder,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            Program = Program
        };
    }
}