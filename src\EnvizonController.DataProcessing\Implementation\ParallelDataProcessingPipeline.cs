using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Implementation
{
    /// <summary>
    /// 并行数据处理管道
    /// </summary>
    public class ParallelDataProcessingPipeline : IDataProcessingPipeline
    {
        private readonly List<IDataProcessor> _processors = new();
        
        /// <summary>
        /// 获取管道名称
        /// </summary>
        public string Name { get; }
        
        /// <summary>
        /// 获取管道描述
        /// </summary>
        public string Description { get; }
        
        /// <summary>
        /// 获取处理器列表
        /// </summary>
        public IReadOnlyList<IDataProcessor> Processors => _processors.AsReadOnly();
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <param name="description">管道描述</param>
        public ParallelDataProcessingPipeline(string name, string description = "")
        {
            Name = name;
            Description = description;
        }
        
        /// <summary>
        /// 添加处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        public IDataProcessingPipeline AddProcessor(IDataProcessor processor)
        {
            _processors.Add(processor);
            return this;
        }
        
        /// <summary>
        /// 移除处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        public IDataProcessingPipeline RemoveProcessor(IDataProcessor processor)
        {
            _processors.Remove(processor);
            return this;
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            context.Status = ProcessingStatus.Processing;
            
            // 创建上下文副本列表
            var contextCopies = new List<IDataProcessingContext>();
            var tasks = new List<Task<IDataProcessingContext>>();
            
            // 为每个处理器创建上下文副本并启动处理任务
            foreach (var processor in _processors)
            {
                if (processor.CanProcess(context))
                {
                    var contextCopy = context.Clone();
                    contextCopies.Add(contextCopy);
                    tasks.Add(processor.ProcessAsync(contextCopy));
                }
            }
            
            // 等待所有任务完成
            await Task.WhenAll(tasks);
            
            // 合并处理结果
            var mergedContext = context;
            mergedContext.ProcessedData = tasks.Select(t => t.Result.ProcessedData).ToList();
            
            // 检查是否有任务失败
            var failedContexts = contextCopies.Where(c => c.Status == ProcessingStatus.Failed).ToList();
            if (failedContexts.Any())
            {
                mergedContext.Status = ProcessingStatus.Failed;
                foreach (var failedContext in failedContexts)
                {
                    foreach (var error in failedContext.Errors)
                    {
                        mergedContext.AddError(error);
                    }
                }
            }
            else
            {
                mergedContext.Status = ProcessingStatus.Succeeded;
            }
            
            return mergedContext;
        }
        
        /// <summary>
        /// 清空管道
        /// </summary>
        public void Clear()
        {
            _processors.Clear();
        }
    }
}
