﻿using System.Collections.Concurrent;
using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces.Cache;
using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Infrastructure.Services.Devices;

public class ModbusDeviceServiceCache : IModbusDeviceServiceCache
{
    // Define constants for connection types to avoid magic strings and improve maintainability
    private const string
        ConnectionTypeSerial = "Serial"; // Assuming "Serial" is a possible value for device.ConnectionType

    private const string
        ConnectionTypeNetwork = "Network"; // Assuming "Network" is a possible value for device.ConnectionType

    private readonly ConcurrentDictionary<string, IModbusDeviceService> _deviceServices = new();
    private readonly ModbusDeviceServiceFactory _factory;
    private IServiceProvider _serviceProvider;

    public ModbusDeviceServiceCache(ModbusDeviceServiceFactory factory, IServiceProvider serviceProvider)
    {
        _factory = factory;
        _serviceProvider = serviceProvider;
    }

    public IModbusDeviceService GetOrCreate(Device device)
    {
        var cacheKey = GetConnectionCacheKey(device);
        return _deviceServices.GetOrAdd(cacheKey, _ => _factory.CreateDeviceService(device));
    }

    public void Remove(Device device) // Changed signature from long deviceId to Device device
    {
        var cacheKey = GetConnectionCacheKey(device);
        if (_deviceServices.TryRemove(cacheKey, out var service) && service is IDisposable disposable)
            disposable.Dispose();
    }

    private string GetConnectionCacheKey(Device device)
    {
        if (device == null) throw new ArgumentNullException(nameof(device));

        // Using OrdinalIgnoreCase for robust comparison of ConnectionType
        // Constants ConnectionTypeSerial and ConnectionTypeNetwork are defined above
        if (string.Equals(device.ConnectionType, ConnectionTypeSerial, StringComparison.OrdinalIgnoreCase))
            // Key format: SERIAL::{PortName}::{BaudRate}::{DataBits}::{Parity}::{StopBits}::{TransportType}::{SlaveId}
            return
                $"SERIAL::{device.PortName ?? string.Empty}::{device.BaudRate}::{device.DataBits}::{device.Parity}::{device.StopBits}::{device.TransportType ?? string.Empty}";

        if (string.Equals(device.ConnectionType, ConnectionTypeNetwork, StringComparison.OrdinalIgnoreCase))
            // Key format: NETWORK::{HostAddress}::{Port}::{TransportType}::{SlaveId}
            return
                $"NETWORK::{device.HostAddress ?? string.Empty}::{device.Port}::{device.TransportType ?? string.Empty}";

        // Fallback or throw an exception for unknown connection types
        // Consider throwing new NotSupportedException($"Unsupported connection type: {device.ConnectionType}");
        // Using a prefix to distinguish from valid keys and including more info for debugging.
        return
            $"UNKNOWN_CONNECTION::{device.ConnectionType ?? "NULL_CONN_TYPE"}::ID::{device.Id}::SLAVE::{device.SlaveId}";
    }
}