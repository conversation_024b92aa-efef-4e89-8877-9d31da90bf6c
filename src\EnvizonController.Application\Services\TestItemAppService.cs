﻿using AutoMapper;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EnvizonController.Application.Services
{
    /// <summary>
    /// 测试项应用服务实现
    /// 负责在应用层处理DTO转换
    /// </summary>
    public class TestItemAppService : ITestItemAppService
    {
        private readonly ITestRunDomainService _testRunService;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TestItemAppService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="testRunService">测试项领域服务</param>
        /// <param name="mapper">AutoMapper映射器</param>
        /// <param name="unitOfWork">工作单元</param>
        /// <param name="logger">日志记录器</param>
        public TestItemAppService(
            ITestRunDomainService testRunService,
            IMapper mapper,
            IUnitOfWork unitOfWork,
            ILogger<TestItemAppService> logger)
        {
            _testRunService = testRunService;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有测试项
        /// </summary>
        public async Task<IEnumerable<TestRunDTO>> GetAllTestItemsAsync()
        {
            try
            {
                var testItems = await _unitOfWork.TestItems.GetAllAsync();
                return _mapper.Map<IEnumerable<TestRunDTO>>(testItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有测试项时出错");
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取测试项
        /// </summary>
        public async Task<TestRunDTO?> GetTestRunByIdAsync(long id)
        {
            try
            {
                var testItem = await _unitOfWork.TestItems.GetByIdAsync(id);
                return _mapper.Map<TestRunDTO>(testItem);
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("未找到ID为{Index}的测试项", id);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取ID为{Index}的测试项时出错", id);
                throw;
            }
        }

        /// <summary>
        /// 根据名称查找测试项
        /// </summary>
        public async Task<IEnumerable<TestRunDTO>> FindTestItemsByNameAsync(string name)
        {
            try
            {
                var testItems = await _unitOfWork.TestItems.FindByNameAsync(name);
                return _mapper.Map<IEnumerable<TestRunDTO>>(testItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据名称{Name}查找测试项时出错", name);
                throw;
            }
        }

        /// <summary>
        /// 创建新的测试项
        /// </summary>
        public async Task<TestRunDTO> CreateTestItemAsync(TestRunDTO testRunDTO)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                TestRun createdTestItem;

                // 根据ExecutionId和ExecutionType判断使用哪种创建方式
                if (testRunDTO.ExecutionId.HasValue && !string.IsNullOrEmpty(testRunDTO.ExecutionType))
                {
                    if (testRunDTO.ExecutionType == "Program")
                    {
                        // 从程式创建测试项
                        createdTestItem = await _testRunService.CreateTestFromProgramAsync(
                            testRunDTO.ExecutionId.Value,
                            testRunDTO.Name,
                            testRunDTO.DeviceId,
                            testRunDTO.Description);
                    }
                    else if (testRunDTO.ExecutionType == "ProgramLink")
                    {
                        // 从程式链接创建测试项
                        createdTestItem = await _testRunService.CreateTestFromProgramLinkAsync(
                            testRunDTO.ExecutionId.Value,
                            testRunDTO.Name,
                            testRunDTO.DeviceId,
                            testRunDTO.Description);
                    }
                    else
                    {
                        throw new ArgumentException($"不支持的执行类型: {testRunDTO.ExecutionType}");
                    }
                }
                else
                {
                    // 传统方式创建测试项
                    var testItem = new TestRun
                    {
                        Name = testRunDTO.Name,
                        Description = testRunDTO.Description,
                        DeviceId = testRunDTO.DeviceId,
                        Status = "Running"
                    };

                    // 使用领域服务创建测试项
                    createdTestItem = await _testRunService.CreateTestItemAsync(testItem);
                }

                // 保存到数据库
                await _unitOfWork.TestItems.AddAsync(createdTestItem);
                await _unitOfWork.SaveChangesAsync();

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                // 将创建的实体转换回DTO
                return _mapper.Map<TestRunDTO>(createdTestItem);
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "创建测试项时出错");
                throw;
            }
        }

        /// <summary>
        /// 更新测试项
        /// </summary>
        public async Task<TestRunDTO> UpdateTestItemAsync(TestRunDTO testRunDTO)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                // 获取现有测试项
                var existingTestItem = await _unitOfWork.TestItems.GetByIdAsync(testRunDTO.Id);

                // 更新属性
                existingTestItem.Name = testRunDTO.Name;
                existingTestItem.Description = testRunDTO.Description;
                existingTestItem.DeviceId = testRunDTO.DeviceId;

                // 使用领域服务更新测试项
                var updatedTestItem = await _testRunService.UpdateTestItemAsync(existingTestItem);

                // 保存更改
                await _unitOfWork.SaveChangesAsync();

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                // 将更新后的实体转换回DTO
                return _mapper.Map<TestRunDTO>(updatedTestItem);
            }
            catch (KeyNotFoundException)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogWarning("更新测试项时未找到ID为{Index}的测试项", testRunDTO.Id);
                throw;
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "更新ID为{Index}的测试项时出错", testRunDTO.Id);
                throw;
            }
        }

        /// <summary>
        /// 删除测试项
        /// </summary>
        public async Task<bool> DeleteTestItemAsync(long id)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                // 使用领域服务删除测试项
                bool result = await _testRunService.DeleteTestItemAsync(id);

                // 保存更改
                await _unitOfWork.SaveChangesAsync();

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                return result;
            }
            catch (KeyNotFoundException)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogWarning("删除测试项时未找到ID为{Index}的测试项", id);
                return false;
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "删除ID为{Index}的测试项时出错", id);
                throw;
            }
        }

        /// <summary>
        /// 启动测试
        /// </summary>
        public async Task<TestRunDTO?> StartTestAsync(long id)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                // 使用领域服务启动测试
                var testItem = await _testRunService.StartTestAsync(id);

                // 如果启动失败，返回null
                if (testItem == null)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return null;
                }

                // 保存更改
                await _unitOfWork.SaveChangesAsync();

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                // 将实体转换为DTO
                return _mapper.Map<TestRunDTO>(testItem);
            }
            catch (KeyNotFoundException)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogWarning("启动测试时未找到ID为{Index}的测试项", id);
                return null;
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "启动ID为{Index}的测试时出错", id);
                throw;
            }
        }

        /// <summary>
        /// 获取测试项列表（支持查询参数和分页）
        /// </summary>
        /// <param name="queryParams">查询参数，包括分页、名称、日期范围、设备ID、状态等</param>
        /// <returns>分页的测试项DTO列表</returns>
        public async Task<PagedResultDto<TestRunDTO>> GetTestItemsAsync(TestItemQueryParams queryParams)
        {
            try
            {
                // 从查询参数中提取过滤条件
                var page = queryParams.Page <= 0 ? 1 : queryParams.Page;
                var pageSize = queryParams.PageSize <= 0 ? 20 : queryParams.PageSize;
                
                _logger.LogInformation("获取测试项列表，页码：{Page}，每页大小：{PageSize}", page, pageSize);
                
                // 调用仓储的条件分页查询方法
                var (testItems, totalCount) = await _unitOfWork.TestItems.GetPagedTestItemsAsync(
                    page,
                    pageSize,
                    queryParams.SearchText,
                    queryParams.Name,
                    queryParams.Status,
                    queryParams.StartDate,
                    queryParams.EndDate,
                    queryParams.DeviceId);
                
                // 转换为DTO
                var testItemDtos = _mapper.Map<List<TestRunDTO>>(testItems);
                
                // 构造分页结果
                return new PagedResultDto<TestRunDTO>
                {
                    Items = testItemDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试项列表（带查询参数）时出错");
                throw;
            }
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        public async Task<TestRunDTO?> StopTestAsync(long id)
        {
            try
            {
                // 开始事务
                await _unitOfWork.BeginTransactionAsync();

                // 使用领域服务停止测试
                var testItem = await _testRunService.StopTestAsync(id);

                // 如果停止失败，返回null
                if (testItem == null)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    return null;
                }

                // 保存更改
                await _unitOfWork.SaveChangesAsync();

                // 提交事务
                await _unitOfWork.CommitTransactionAsync();

                // 将实体转换为DTO
                return _mapper.Map<TestRunDTO>(testItem);
            }
            catch (KeyNotFoundException)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogWarning("停止测试时未找到ID为{Index}的测试项", id);
                return null;
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "停止ID为{Index}的测试时出错", id);
                throw;
            }
        }

        /// <summary>
        /// 初始化默认测试项
        /// </summary>
        /// <returns>初始化的默认测试项列表</returns>
        public async Task<IEnumerable<TestRunDTO>> InitializeDefaultTestItemsAsync()
        {
            try
            {
                // 检查是否已有测试项
                var existingTestItems = await GetAllTestItemsAsync();
                if (existingTestItems.Any())
                {
                    _logger.LogInformation("已存在{Count}个测试项，跳过初始化", existingTestItems.Count());
                    return existingTestItems;
                }

                _logger.LogInformation("数据库中没有测试项，创建默认测试项");
                
                var defaultTestItems = new List<TestRunDTO>
                {
                    new TestRunDTO
                    {
                        Name = "压力测试",
                        Description = "环境压力测试项目",
                        Status = "Running",
                        DeviceId = 1,
                        ExecutionId = 1,
                        ExecutionType = "Program"
                    },
                    new TestRunDTO
                    {
                        Name = "温度测试",
                        Description = "环境温度测试项目",
                        Status = "Ready",
                        DeviceId = 1,
                    },
                    new TestRunDTO
                    {
                        Name = "湿度测试",
                        Description = "环境湿度测试项目",
                        Status = "Ready",
                        DeviceId = 1
                    },
                };
                
                var createdItems = new List<TestRunDTO>();
                
                foreach (var testItemDto in defaultTestItems)
                {
                    var createdItem = await CreateTestItemAsync(testItemDto);
                    createdItems.Add(createdItem);
                    _logger.LogInformation("已创建默认测试项，ID: {Index}, 名称: {Name}", createdItem.Id, createdItem.Name);
                }
                
                
                return createdItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化默认测试项时出错");
                return new List<TestRunDTO>();
            }
        }
    }
}
