﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Shared.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EnvizonController.Presentation.ViewModels.Dashboard;

namespace EnvizonController.Presentation.Messages
{

    /// <summary>
    /// 数据采集状态变更消息
    /// </summary>
    public class DeviceItemViewModelChangedMessage : ValueChangedMessage<DeviceItemViewModel>
    {
        /// <summary>
        /// 数据采集状态
        /// </summary>
        public DeviceItemViewModel ViewModel => Value;

        /// <summary>
        /// 创建数据采集状态变更消息
        /// </summary>
        /// <param name="viewModel"></param>
        public DeviceItemViewModelChangedMessage(DeviceItemViewModel viewModel) : base(viewModel)
        {
        }
    }
}
