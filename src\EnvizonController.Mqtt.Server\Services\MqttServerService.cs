using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Protocol;
using MQTTnet.Server;
using System; // For Array.Empty
using System.Text; // For Encoding
using System.Threading; // For CancellationToken
using System.Threading.Tasks; // For Task

namespace EnvizonController.Mqtt.Server.Services
{
    public class MqttServerService : IMqttServerService 
    {
        private readonly MqttServer _mqttServer;
        private readonly IMessageProcessor _messageProcessor;
        private readonly ILogger<MqttServerService> _logger;

        public MqttServerService(IMessageProcessor messageProcessor, ILogger<MqttServerService> logger)
        {
            _messageProcessor = messageProcessor;
            _logger = logger;
            // 配置服务器选项
            var optionsBuilder = new MqttServerOptionsBuilder()
                .WithDefaultEndpoint()
                .WithDefaultEndpointPort(1883);

            var mqttFactory = new MqttFactory();
            _mqttServer = mqttFactory.CreateMqttServer(optionsBuilder.Build());
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 注册消息接收处理
                _mqttServer.InterceptingPublishAsync += HandleInterceptingPublishAsync;
                _mqttServer.ClientConnectedAsync += HandleClientConnectedAsync;
                _mqttServer.ClientDisconnectedAsync += HandleClientDisconnectedAsync;

                // 新增：注册客户端订阅主题事件处理
                _mqttServer.ClientSubscribedTopicAsync += HandleClientSubscribedTopicAsync;
                // 新增：注册客户端取消订阅主题事件处理
                _mqttServer.ClientUnsubscribedTopicAsync += HandleClientUnsubscribedTopicAsync;


                // 启动服务器
                await _mqttServer.StartAsync();
                _logger.LogInformation("MQTT服务器已启动");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动MQTT服务器时发生错误");
                throw;
            }
        }

        private Task HandleClientConnectedAsync(ClientConnectedEventArgs args)
        {
            _logger.LogInformation($"客户端已连接: ClientId={args.ClientId}");
            return Task.CompletedTask;
        }

        private Task HandleClientDisconnectedAsync(ClientDisconnectedEventArgs args)
        {
            _logger.LogInformation($"客户端已断开连接: ClientId={args.ClientId}, 原因={args.DisconnectType}");
            return Task.CompletedTask;
        }

        // 新增：处理客户端订阅主题事件
        private Task HandleClientSubscribedTopicAsync(ClientSubscribedTopicEventArgs args)
        {
            _logger.LogInformation($"客户端已订阅主题: ClientId='{args.ClientId}', TopicFilter='{args.TopicFilter.Topic}', QoS='{args.TopicFilter.QualityOfServiceLevel}'");
            // 注意: args.TopicFilter 包含客户端请求订阅的主题过滤器，可能包含通配符
            // 如果需要对订阅进行验证或修改，可以使用 _mqttServer.InterceptingSubscriptionAsync 事件
            return Task.CompletedTask;
        }

        // 新增：处理客户端取消订阅主题事件
        private Task HandleClientUnsubscribedTopicAsync(ClientUnsubscribedTopicEventArgs args)
        {
            _logger.LogInformation($"客户端已取消订阅主题: ClientId='{args.ClientId}', TopicFilter='{args.TopicFilter}'");
            return Task.CompletedTask;
        }


        private Task HandleInterceptingPublishAsync(InterceptingPublishEventArgs args)
        {
            try
            {
                var topic = args.ApplicationMessage.Topic;
                var payloadSegment = args.ApplicationMessage.PayloadSegment; // Changed variable name for clarity
                var message = System.Text.Encoding.UTF8.GetString(payloadSegment.Array ?? Array.Empty<byte>(), payloadSegment.Offset, payloadSegment.Count);

                _logger.LogDebug($"收到消息: ClientId='{args.ClientId ?? "unknown"}', Topic='{topic}', Message='{message}'");
                _messageProcessor.ProcessMessage(topic, message, args.ClientId ?? "unknown");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理MQTT消息时发生错误");
            }

            return Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            if (_mqttServer.IsStarted)
            {
                await _mqttServer.StopAsync();
            }
            // 取消注册事件处理器，以防内存泄漏（如果服务实例会被重新创建和启动）
            _mqttServer.InterceptingPublishAsync -= HandleInterceptingPublishAsync;
            _mqttServer.ClientConnectedAsync -= HandleClientConnectedAsync;
            _mqttServer.ClientDisconnectedAsync -= HandleClientDisconnectedAsync;
            _mqttServer.ClientSubscribedTopicAsync -= HandleClientSubscribedTopicAsync;
            _mqttServer.ClientUnsubscribedTopicAsync -= HandleClientUnsubscribedTopicAsync;

            _logger.LogInformation("MQTT服务器已停止");
        }

        public async Task PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            if (!_mqttServer.IsStarted)
            {
                _logger.LogWarning("MQTT服务器未启动，无法发布消息。");
                return;
            }
            var applicationMessage = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(message)
                .WithQualityOfServiceLevel(qos)
                .WithRetainFlag(false)
                .Build();

            // MqttApplicationMessage is already the correct type. No need to cast.
            await _mqttServer.InjectApplicationMessage(
                new InjectedMqttApplicationMessage(applicationMessage) // Pass MqttApplicationMessage directly
                {
                    SenderClientId = "Server" // Optional: identify server as sender
                });

            _logger.LogDebug($"服务器发布消息: Topic={topic}, Message={message}");
        }
    }
}