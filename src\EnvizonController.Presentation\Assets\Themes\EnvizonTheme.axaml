﻿<Styles
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:helpers="clr-namespace:EnvizonController.Presentation.Helpers"
    xmlns:controls="clr-namespace:EnvizonController.Presentation.Controls">

    <!--  基础文本样式  -->
    <Style Selector="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource Rajdhani}" />
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}" />
    </Style>
    <Style Selector="Window">
        <Setter Property="Foreground" Value="White" />
    </Style>
    <!--  标题层级  -->
    <Style Selector="TextBlock.h1">
        <Setter Property="FontSize" Value="{DynamicResource H1FontSize}" />
        <Setter Property="FontWeight" Value="Bold" />
    </Style>
    <Style Selector="TextBlock.h1.bottom">
        <Setter Property="Margin" Value="0 0 0 10" />
    </Style>

    <Style Selector="TextBlock.h2">
        <Setter Property="FontSize" Value="{DynamicResource H2FontSize}" />
        <Setter Property="FontWeight" Value="SemiBold" />
    </Style>
    <Style Selector="TextBlock.h2.bottom">
        <Setter Property="Margin" Value="0 0 0 8" />
    </Style>
    <Style Selector="TextBlock.h3">
        <Setter Property="FontSize" Value="{DynamicResource H3FontSize}" />
        <Setter Property="FontWeight" Value="Bold" />
    </Style>

    <Style Selector="TextBlock.h3.bottom">
        <Setter Property="Margin" Value="0 0 0 6" />
    </Style>
    <Style Selector="TextBlock.h4">
        <Setter Property="FontSize" Value="{DynamicResource H4FontSize}" />
    </Style>

    <Style Selector="TextBlock.h4.bottom">
        <Setter Property="Margin" Value="0 0 0 16" />
        <Setter Property="FontWeight" Value="Bold" />
    </Style>
    <!--  正文层级  -->
    <Style Selector="TextBlock.body">
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}" />
        <Setter Property="TextWrapping" Value="Wrap" />
    </Style>

    <Style Selector="TextBlock.small">
        <Setter Property="FontSize" Value="{DynamicResource SmallFontSize}" />
        <!--<Setter Property="Foreground" Value="{DynamicResource TextBrush3}" />-->
    </Style>

    <!--  特殊变体（可组合使用）  -->
    <Style Selector="TextBlock.glow-primary">
        <Setter Property="Effect" Value="{DynamicResource GlowEffect}" />
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
    </Style>
    <Style Selector="TextBlock.primary">
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
    </Style>
    <Style Selector="TextBlock.white">
        <Setter Property="Foreground" Value="White" />
    </Style>
    <Style Selector="TextBlock.gray">
        <Setter Property="Foreground" Value="#9DA3AF" />
    </Style>
    <Style Selector="TextBlock.gray2">
        <Setter Property="Foreground" Value="#6b7280" />
    </Style>
    <Style Selector="TextBlock.glow-white">
        <Setter Property="Effect" Value="{DynamicResource WhiteGlowEffect}" />
        <Setter Property="Foreground" Value="White" />
    </Style>
    <Style Selector="TextBlock.font-icon">
        <Setter Property="FontFamily" Value="{DynamicResource FontAwesomeSolid}" />
    </Style>
    <Style Selector="TextBlock.font-cyber">
        <Setter Property="FontFamily" Value="{DynamicResource Orbitron}" />
    </Style>
    <Style Selector="TextBlock.font-SansSerif">
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
    </Style>
    <Style Selector="TextBlock.font-value">
        <Setter Property="FontFamily" Value="{DynamicResource Rajdhani}" />
    </Style>
    <!--  1. TextBlock Styles  -->
    <!--
    -->
    <!--  - 基础样式：应用于基本类的样式  -->
    <!--
    -->
    <!--  - 特定样式：应用于多个类的组合  -->
    <!--
    -->
    <!--  基础发光文本样式（可独立使用）  -->
    <!--
    <Style Selector="TextBlock.glow-text">
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
        <Setter Property="Effect" Value="{DynamicResource GlowEffect}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    -->
    <!--  白色发光变体  -->
    <!--
    <Style Selector="TextBlock.white-glow-text">
        <Setter Property="Foreground" Value="White" />
        <Setter Property="Effect" Value="{DynamicResource GlowEffect}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    -->
    <!--  赛博风格文本（无发光效果）  -->
    <!--
    <Style Selector="TextBlock.cyber-text">
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    -->
    <!--  数值显示专用样式  -->
    <!--
    <Style Selector="TextBlock.value-text">
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
        <Setter Property="FontFamily" Value="{DynamicResource Rajdhani}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    -->
    <!--  图标扩展样式（需与基础样式组合使用）  -->
    <!--
    <Style Selector="TextBlock.glow-text.fa-icon">
        <Setter Property="FontFamily" Value="{DynamicResource FontAwesomeSolid}" />
        <Setter Property="FontSize" Value="24" />
    </Style>

    <Style Selector="TextBlock.glow-text.icon">
        <Setter Property="FontFamily" Value="{DynamicResource FontAwesomeSolid}" />
        <Setter Property="FontSize" Value="18" />
    </Style>

    -->
    <!--  标题扩展样式（需与基础样式组合使用）  -->
    <!--
    <Style Selector="TextBlock.orbitron-font">
        <Setter Property="FontFamily" Value="{DynamicResource Orbitron}" />
        <Setter Property="FontSize" Value="23" />
        <Setter Property="FontWeight" Value="Bold" />
    </Style>-->

    <!--  2. Border Styles  -->
    <!--  - 基础样式：应用于基本类的样式  -->
    <!--  - 特定样式：应用于多个类的组合  -->
    <Style Selector="Border.cyber-border">
        <Setter Property="Background" Value="#0B0A1A" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="6" />
        <Setter Property="Effect" Value="{DynamicResource GlowEffect}" />
    </Style>
    <Style Selector="Border.cyber-noGlow-border">
        <Setter Property="Background" Value="#0B0A1A" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="6" />
    </Style>
    <Style Selector="Border.card">
        <Setter Property="Padding" Value="15" />
        <Setter Property="Margin" Value="3" />
    </Style>
    <Style Selector="Grid.card-in">
        <Setter Property="Margin" Value="15" />
    </Style>
    <Style Selector="Grid.bigCard-in">
        <Setter Property="Margin" Value="24" />
    </Style>
    <Style Selector="Border.bigCard">
        <Setter Property="Padding" Value="24" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="CornerRadius" Value="9" />
    </Style>

    <!--  3. Button Styles  -->
    <!--  - 基础样式：应用于基本类的样式，包括模板  -->
    <!--  - 状态样式：应用于特定状态，如 :pointerover 或 .selected  -->
    <Style Selector="Button.nav-button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="#9DA3AF" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="10,5" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    x:Name="border"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        x:Name="NavButtonContentPresenter"
                        Margin="{TemplateBinding Padding}"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>

    <!--  基础样式 for ContentPresenter in Button.nav-button  -->
    <Style Selector="Button.nav-button /template/ ContentPresenter#NavButtonContentPresenter">
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="0" Color="#00000000" />
            </Setter.Value>
        </Setter>
        <Setter Property="Transitions">
            <Transitions>
                <EffectTransition Property="Effect" Duration="0.1" />
            </Transitions>
        </Setter>
    </Style>

    <!--  状态样式：Selected  -->
    <Style Selector="Button.nav-button.selected">
        <Setter Property="Foreground" Value="#0DF0FF" />
    </Style>

    <!--  状态样式：Selected and Pointer Over for ContentPresenter  -->
    <Style Selector="Button.nav-button:pointerover /template/ ContentPresenter#NavButtonContentPresenter">
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="10"
                OffsetX="0.5"
                OffsetY="0.5"
                Opacity="0.6"
                Color="#0DF0FF" />
        </Setter>
        <Setter Property="Foreground" Value="#0DF0FF" />
    </Style>

    <!--  状态样式：Selected but not Pointer Over for ContentPresenter  -->
    <Style Selector="Button.nav-button.selected:not(:pointerover) /template/ ContentPresenter#NavButtonContentPresenter">
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="0" Color="#00000000" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  状态样式：Not Selected for ContentPresenter  -->
    <Style Selector="Button.nav-button:not(.selected):not(:pointerover) /template/ ContentPresenter#NavButtonContentPresenter">
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect BlurRadius="0" Color="#00000000" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  禁用状态样式  -->
    <Style Selector="Button.nav-button:disabled">
        <!--  降低不透明度  -->
        <Setter Property="Opacity" Value="0.5" />
    </Style>
    <!--  禁用状态样式  -->
    <Style Selector="Button.nav-button:pressed">
        <!--  降低不透明度  -->
        <Setter Property="Margin" Value="3 3 -3 -3" />
    </Style>
    <Style Selector="Button.glow">
        <!--  降低不透明度  -->
        <Setter Property="Padding" Value="9" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="#56EFFE" />
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="CornerRadius" Value="5" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect
                    BlurRadius="10"
                    OffsetX="0"
                    OffsetY="0"
                    Color="#56EFFE" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style Selector="Button.glow2">
        <Setter Property="helpers:CommonHelper.CommonBrush" Value="#05D9E8" />
        <Setter Property="Foreground" Value="{DynamicResource GlowColorBrush}" />
        <Setter Property="CornerRadius" Value="1" />
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border
                        Name="BackgroundBorder"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding helpers:CommonHelper.CommonBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <ContentPresenter
                            x:Name="NavButtonContentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />
                    </Border>
                    <Border
                        BorderBrush="{TemplateBinding helpers:CommonHelper.CommonBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Effect>
                            <BlurEffect Radius="21" />
                        </Border.Effect>
                    </Border>
                    <Border
                        BorderBrush="{TemplateBinding helpers:CommonHelper.CommonBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Effect>
                            <BlurEffect Radius="21" />
                        </Border.Effect>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.3" />
            </Transitions>
        </Setter>
    </Style>
    <Style Selector="Button.glow2:pointerover">
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="30"
                OffsetX="0.5"
                OffsetY="0.5"
                Opacity="1"
                Color="#4DD8E8" />
        </Setter>
        <Setter Property="Foreground" Value="Black" />
        <Setter Property="Background" Value="#4DD8E8" />

    </Style>
    <Style Selector="Button.glow2:pressed">
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="30"
                OffsetX="0.5"
                OffsetY="0.5"
                Opacity="1"
                Color="#4DD8E8" />
        </Setter>
        <Setter Property="Foreground" Value="Black" />
        <Setter Property="Background" Value="#4DD8E8" />

    </Style>
    <Style Selector="Button.glow2:not(:pointerover)">

        <Setter Property="Background" Value="Transparent" />
    </Style>


    <Style Selector="Button.font-icon /template/ ContentPresenter > TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource FontAwesomeSolid}" />
    </Style>

    <!-- 赛博朋克风格ListBox样式 -->
    <Style Selector="ListBox.cyber">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
    </Style>

    <!-- ListBoxItem基础样式 -->
    <Style Selector="ListBox.cyber > ListBoxItem">
        <Setter Property="Padding" Value="10,8" />
        <Setter Property="Margin" Value="0,2" />
        <Setter Property="Background" Value="#14131C" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
    </Style>

    <!-- 鼠标悬停状态 -->
    <Style Selector="ListBox.cyber > ListBoxItem:pointerover">
        <Setter Property="Background" Value="#1A1927" />
        <Setter Property="BorderBrush" Value="#56EFFE" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="5"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.5"
                Color="#56EFFE" />
        </Setter>
    </Style>

    <!-- 选中状态 -->
    <Style Selector="ListBox.cyber > ListBoxItem:selected">
        <Setter Property="Background" Value="#252335" />
        <Setter Property="BorderBrush" Value="#0DF0FF" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="10"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.7"
                Color="#0DF0FF" />
        </Setter>
    </Style>

    <!-- 选中且鼠标悬停状态 -->
    <Style Selector="ListBox.cyber > ListBoxItem:selected:pointerover">
        <Setter Property="BorderBrush" Value="#56EFFE" />
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="15"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.8"
                Color="#56EFFE" />
        </Setter>
    </Style>

    <!-- 按下状态 -->
    <Style Selector="ListBox.cyber > ListBoxItem:pressed">
        <Setter Property="Background" Value="#1F1D2E" />
        <Setter Property="RenderTransform" Value="scale(0.98)" />
    </Style>

</Styles>
