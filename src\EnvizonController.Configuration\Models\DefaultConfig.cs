﻿// YourApp.Core/Configuration/Models/Base/DefaultConfig.cs

using System.Text.Json.Serialization;
using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Configuration.Enums;

namespace EnvizonController.Configuration.Models;

public class DefaultConfig : ObservableObject
{
    private int _configVersion = 1; // 版本号，便于迁移升级
    private DisplayConfig _display = new();
    private bool _enableAutoUpdate = true;

    // 新增属性（演示前向兼容）
    private bool _enableLogging;
    private SerialPortSettings _serialPortSettings = new SerialPortSettings();
    private NetworkSettings _networkSettings = new NetworkSettings();
    private LogSettings _logSettings = new LogSettings();
    private MqttSettings _mqttSettings = new MqttSettings();
    private ApiClientSettings _apiClientSettings = new ApiClientSettings();

    // 属性重命名演示
    [JsonPropertyName("EnableAutoCheckForUpdates")] // 兼容老配置字段
    public bool EnableAutoUpdate
    {
        get => _enableAutoUpdate;
        set => SetProperty(ref _enableAutoUpdate, value);
    }

    public DisplayConfig Display
    {
        get => _display;
        set => SetProperty(ref _display, value);
    }

    public bool EnableLogging
    {
        get => _enableLogging;
        set => SetProperty(ref _enableLogging, value);
    }

    public int ConfigVersion
    {
        get => _configVersion;
        protected set => SetProperty(ref _configVersion, value);
    }

    public SerialPortSettings SerialPortSettings
    {
        get => _serialPortSettings;
        set => SetProperty(ref _serialPortSettings, value);
    }

    public NetworkSettings NetworkSettings
    {
        get => _networkSettings;
        set => SetProperty(ref _networkSettings, value);
    }

    public LogSettings LogSettings
    {
        get => _logSettings;
        set => SetProperty(ref _logSettings, value);
    }

    public MqttSettings MqttSettings
    {
        get => _mqttSettings;
        set => SetProperty(ref _mqttSettings, value);
    }

    public ApiClientSettings ApiClientSettings
    {
        get => _apiClientSettings;
        set => SetProperty(ref _apiClientSettings, value);
    }

    /// <summary>
    ///     预留迁移逻辑，加载旧配置后调用（由 ConfigurationManager 调用）。
    /// </summary>
    public virtual void MigrateIfNecessary()
    {
        if (ConfigVersion < 2)
        {
            // 这里可以做版本迁移逻辑
            // ConfigVersion = 2;
            // OnPropertyChanged(nameof(ConfigVersion));
        }
        // 未来版本此处扩展
    }
}


