using EnvizonController.ApiClient.Results;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;

namespace EnvizonController.ApiClient.Http
{
    /// <summary>
    /// HTTP客户端接口的默认实现
    /// </summary>
    public class HttpClientAdapter : IHttpClient
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly JsonSerializerOptions _jsonOptions;

        public HttpClientAdapter(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        /// <inheritdoc />
        public async Task<Result<T>> GetAsync<T>(string url, IDictionary<string, string> queryParams = null)
        {
            try
            {
                var requestUrl = BuildUrl(url, queryParams);
                var client = _httpClientFactory.CreateClient("ApiClient");
                var response = await client.GetAsync(requestUrl);
                return await ProcessResponseAsync<T>(response);

            }
            catch (Exception ex)
            {
                return Result<T>.Failure(ex.Message, 500);
            }
        }

        /// <inheritdoc />
        public async Task<Result<T>> PostAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var client = _httpClientFactory.CreateClient("ApiClient");
                var response = await client.PostAsJsonAsync(url, content, _jsonOptions);

                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return Result<T>.Failure(ex.Message, 500);
            }
        }

        /// <inheritdoc />
        public async Task<Result<T>> PutAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var client = _httpClientFactory.CreateClient("ApiClient");
                var response = await client.PutAsJsonAsync(url, content, _jsonOptions);

                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return Result<T>.Failure(ex.Message, 500);
            }
        }

        /// <inheritdoc />
        public async Task<Result<T>> DeleteAsync<T>(string url)
        {
            try
            {
                var client = _httpClientFactory.CreateClient("ApiClient");
                var response = await client.DeleteAsync(url);

                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return Result<T>.Failure(ex.Message, 500);
            }
        }

        /// <summary>
        /// 处理HTTP响应
        /// </summary>
        private async Task<Result<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            var statusCode = (int)response.StatusCode;
            
            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var data = await response.Content.ReadFromJsonAsync<T>(_jsonOptions);
                    return Result<T>.Success(data, statusCode);
                }
                catch (JsonException ex)
                {
                    return Result<T>.Failure($"JSON解析错误: {ex.Message}", statusCode);
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                return Result<T>.Failure(string.IsNullOrEmpty(errorContent) 
                    ? response.ReasonPhrase 
                    : errorContent, statusCode);
            }
        }

        /// <summary>
        /// 构建URL（包含查询参数）
        /// </summary>
        private string BuildUrl(string url, IDictionary<string, string> queryParams)
        {
            if (queryParams == null || queryParams.Count == 0)
            {
                return url;
            }

            var builder = new StringBuilder(url);
            
            if (!url.Contains("?"))
            {
                builder.Append('?');
            }
            else if (!url.EndsWith("?") && !url.EndsWith("&"))
            {
                builder.Append('&');
            }

            var isFirst = true;
            foreach (var param in queryParams)
            {
                if (!isFirst)
                {
                    builder.Append('&');
                }

                builder.Append(HttpUtility.UrlEncode(param.Key));
                builder.Append('=');
                builder.Append(HttpUtility.UrlEncode(param.Value));
                
                isFirst = false;
            }

            return builder.ToString();
        }
    }
}