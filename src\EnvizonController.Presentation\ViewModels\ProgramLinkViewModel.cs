using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.ApiClient.Services;
using EnvizonController.Presentation.Extensions;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Shared.DTOs;
using HanumanInstitute.MvvmDialogs;
using HanumanInstitute.MvvmDialogs.FrameworkDialogs;

namespace EnvizonController.Presentation.ViewModels;

/// <summary>
///     程式链接管理视图模型
/// </summary>
public class ProgramLinkViewModel : ViewModelBase, IRecipient<ProgramChangedMessage>
{
    private readonly IDialogService _dialogService;
    private readonly IProgramApiService _programApiService;
    private readonly IProgramLinkApiService _programLinkApiService;
    private readonly IMessenger _messenger;

    public ProgramLinkViewModel(IProgramLinkApiService programLinkApiService, IProgramApiService programApiService,
        IDialogService dialogService, IMessenger messenger)
    {
        _programLinkApiService = programLinkApiService;
        _programApiService = programApiService;
        _dialogService = dialogService;
        _messenger = messenger;

        // 初始化命令
        AddProgramLinkCommand = new AsyncRelayCommand(AddProgramLinkAsync);
        EditProgramLinkCommand = new AsyncRelayCommand<ProgramLinkItemViewModel>(EditProgramLink);
        DeleteProgramLinkCommand = new AsyncRelayCommand<ProgramLinkItemViewModel>(DeleteProgramLinkAsync);
        RefreshProgramLinksCommand = new AsyncRelayCommand(LoadProgramLinksAsync);

        // 程式链接项命令
        RefreshProgramLinkStepsCommand = new AsyncRelayCommand(RefreshProgramLinkStepsAsync, CanManageProgramLinkItems);
        AddProgramToLinkCommand = new AsyncRelayCommand(AddProgramToLinkAsync, CanManageProgramLinkItems);
        RemoveProgramFromLinkCommand = new AsyncRelayCommand<ProgramLinkStepItemViewModel>(RemoveProgramFromLinkAsync);
        MoveLinkItemUpCommand = new RelayCommand(MoveLinkItemUp, CanMoveProgramLinkStepUp);
        MoveLinkItemDownCommand = new RelayCommand(MoveLinkItemDown, CanMoveProgramLinkStepDown);

        // 添加确认删除链接步骤命令
        ConfirmDeleteLinkStepCommand = new AsyncRelayCommand<ProgramLinkStepItemViewModel>(ConfirmDeleteLinkStep);

        // 保存程式链接项修改命令
        SaveAllStepsCommand = new AsyncRelayCommand(SaveProgramLinkStepsAsync, CanSaveProgramLinkItems);

        // 错误消息展开切换命令
        ToggleErrorExpandCommand = new RelayCommand(ToggleErrorExpand);

        // 清除错误消息命令
        ClearErrorCommand = new RelayCommand(ClearError);

        // 视图切换命令
        SwitchToCurveViewCommand = new RelayCommand(SwitchToCurveView);
        SwitchToLinkStepsViewCommand = new RelayCommand(SwitchToLinkStepsView);

        // 初始视图状态，默认显示步骤视图
        _showLinkStepsView = true;
        _showCurveView = false;

        // 注册程序变更消息接收器
        _messenger.Register(this);

        // 初始加载数据
        _ = LoadProgramLinksAsync();
        _ = LoadAvailableProgramsAsync();
    }

    // 实现接口方法，接收程序变更消息
    public void Receive(ProgramChangedMessage message)
    {
        _ = LoadProgramLinksAsync();
        _ = LoadAvailableProgramsAsync();
    }

    private bool CanMoveProgramLinkStepUp()
    {
        return SelectedLinkStepItem is { ExecutionOrder: > 1 };
    }

    private bool CanMoveProgramLinkStepDown()
    {
        return SelectedLinkStepItem != null &&
               ProgramLinkStepItems.Count > 0 &&
               SelectedLinkStepItem.ExecutionOrder < ProgramLinkStepItems.Max(s => s.ExecutionOrder);
    }
    public static ObservableCollection<ProgramItemViewModel> StaticAvailablePrograms { get; set; }

    #region 属性

    // 程式链接列表
    public ObservableCollection<ProgramLinkItemViewModel> ProgramLinks { get; } = new();

    // 可选程式列表
    public ObservableCollection<ProgramItemViewModel> AvailablePrograms { get; } = new();

    // 当前选中的程式链接
    private ProgramLinkItemViewModel? _selectedProgramLink;

    public ProgramLinkItemViewModel? SelectedProgramLink
    {
        get => _selectedProgramLink;
        set
        {
            if (SetProperty(ref _selectedProgramLink, value))
            {
                if (value != null)
                    _ = LoadProgramLinkStepItemsAsync(value.Id);
                else
                    ProgramLinkStepItems.Clear();
            }

            RefreshProgramLinkStepsCommand.NotifyCanExecuteChanged();
            AddProgramToLinkCommand.NotifyCanExecuteChanged();
            SaveAllStepsCommand.NotifyCanExecuteChanged();
        }
    }

    // 当前选中的程式链接项
    private ProgramLinkStepItemViewModel? _selectedLinkStepStepItem;

    public ProgramLinkStepItemViewModel? SelectedLinkStepItem
    {
        get => _selectedLinkStepStepItem;
        set
        {
            SetProperty(ref _selectedLinkStepStepItem, value);
            RemoveProgramFromLinkCommand.NotifyCanExecuteChanged();
            MoveLinkItemUpCommand.NotifyCanExecuteChanged();
            MoveLinkItemDownCommand.NotifyCanExecuteChanged();
        }
    }

    // 当前选中的可用程式
    private ProgramItemViewModel? _selectedProgram;

    public ProgramItemViewModel? SelectedProgram
    {
        get => _selectedProgram;
        set => SetProperty(ref _selectedProgram, value);
    }

    // 程式链接项列表
    public ObservableCollection<ProgramLinkStepItemViewModel> ProgramLinkStepItems { get; } = new();

    // 加载状态
    private bool _isLoading;

    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    // 错误信息
    private string _errorMessage;

    public string ErrorMessage
    {
        get => _errorMessage;
        set => SetProperty(ref _errorMessage, value);
    }

    // 错误信息是否展开
    private bool _isErrorExpanded;

    public bool IsErrorExpanded
    {
        get => _isErrorExpanded;
        set => SetProperty(ref _isErrorExpanded, value);
    }

    // 视图切换属性
    private bool _showCurveView;

    public bool ShowCurveView
    {
        get => _showCurveView;
        set => SetProperty(ref _showCurveView, value);
    }

    private bool _showLinkStepsView;

    public bool ShowLinkStepsView
    {
        get => _showLinkStepsView;
        set => SetProperty(ref _showLinkStepsView, value);
    }

    #endregion

    #region 命令

    // 程式链接命令
    public IAsyncRelayCommand AddProgramLinkCommand { get; }
    public IAsyncRelayCommand<ProgramLinkItemViewModel> EditProgramLinkCommand { get; }
    public IAsyncRelayCommand<ProgramLinkItemViewModel> DeleteProgramLinkCommand { get; }
    public IAsyncRelayCommand RefreshProgramLinksCommand { get; }

    // 程式链接项命令
    public IAsyncRelayCommand RefreshProgramLinkStepsCommand { get; }
    public IAsyncRelayCommand AddProgramToLinkCommand { get; }
    public IAsyncRelayCommand<ProgramLinkStepItemViewModel> RemoveProgramFromLinkCommand { get; }
    public IRelayCommand MoveLinkItemUpCommand { get; }
    public IRelayCommand MoveLinkItemDownCommand { get; }

    // 确认删除链接步骤命令
    public IAsyncRelayCommand<ProgramLinkStepItemViewModel> ConfirmDeleteLinkStepCommand { get; }

    // 保存程式链接项修改命令
    public IAsyncRelayCommand SaveAllStepsCommand { get; }

    // 错误消息展开切换命令
    public IRelayCommand ToggleErrorExpandCommand { get; }

    // 清除错误消息命令
    public IRelayCommand ClearErrorCommand { get; }

    // 视图切换命令
    public IRelayCommand SwitchToCurveViewCommand { get; }
    public IRelayCommand SwitchToLinkStepsViewCommand { get; }

    #endregion

    #region 程式链接方法

    public async Task LoadProgramLinksAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _programLinkApiService.GetProgramLinksAsync();

            if (result.IsSuccess)
            {
                ProgramLinks.Clear();
                foreach (var programLinkDto in result.Data.Items)
                    ProgramLinks.Add(ProgramLinkItemViewModel.FromDto(programLinkDto));

                // 如果有数据且没有选中项，则选中第一项
                if (ProgramLinks.Any() && SelectedProgramLink == null) SelectedProgramLink = ProgramLinks.First();
            }
            else
            {
                ErrorMessage = $"加载程式链接失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"加载程式链接异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadProgramLinkStepItemsAsync(long programLinkId)
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _programLinkApiService.GetProgramLinkItemsAsync(programLinkId);

            if (result.IsSuccess)
            {
                ProgramLinkStepItems.Clear();
                foreach (var itemDto in result.Data)
                {
                    var linkItemViewModel = ProgramLinkStepItemViewModel.FromDto(itemDto);
                    ProgramLinkStepItems.Add(linkItemViewModel);
                }

                // 排序
                var sortedItems = ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder).ToList();
                ProgramLinkStepItems.Clear();
                foreach (var item in sortedItems) ProgramLinkStepItems.Add(item);

                // 确保在加载后更新CanExecute状态
                SaveAllStepsCommand.NotifyCanExecuteChanged();
            }
            else
            {
                ErrorMessage = $"加载程式链接项失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"加载程式链接项异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadAvailableProgramsAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _programApiService.GetProgramsAsync();

            if (result.IsSuccess)
            {
                AvailablePrograms.Clear();
                foreach (var programDto in result.Data.Items)
                    AvailablePrograms.Add(ProgramItemViewModel.FromDto(programDto));

                StaticAvailablePrograms = AvailablePrograms;
            }
            else
            {
                ErrorMessage = $"加载可用程式失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"加载可用程式异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task AddProgramLinkAsync()
    {
        var programItemViewModel = await _dialogService.AddProgramLinkAsync(this);

        // 在实际应用中，这里应该调用对话框进行编辑然后保存到数据库
        // 这里简化为直接添加到集合
        if (programItemViewModel != null)
        {
            ProgramLinks.Add(programItemViewModel);
            SelectedProgramLink = programItemViewModel;
        }
    }

    private async Task EditProgramLink(ProgramLinkItemViewModel programLinkItemViewModel)
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // 获取完整的程式链接信息
            var getResult = await _programLinkApiService.GetProgramLinkAsync(programLinkItemViewModel.Id);

            if (getResult.IsSuccess)
            {
                // 从DTO创建视图模型以确保拥有完整数据
                var completeViewModel = ProgramLinkItemViewModel.FromDto(getResult.Data);
                
                // 使用对话框服务打开编辑对话框
                var editedViewModel = await _dialogService.EditProgramLinkAsync(this, completeViewModel);
                
                if (editedViewModel != null)
                {
                    // 用户保存了修改，更新列表
                    await LoadProgramLinksAsync();
                    // 重新选中编辑后的项
                    SelectedProgramLink = ProgramLinks.FirstOrDefault(p => p.Id == editedViewModel.Id);
                }
            }
            else
            {
                ErrorMessage = $"获取程式链接详情失败: {getResult.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"编辑程式链接异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task DeleteProgramLinkAsync(ProgramLinkItemViewModel programLinkItemViewModel)
    {
        // 显示确认对话框
        var confirmResult = await _dialogService.ShowMessageBoxAsync(
            $"确定要删除程式链接 '{programLinkItemViewModel?.Name}' 吗？此操作不可恢复。",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (confirmResult != true) return; // User clicked No or closed the dialog

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // 调用API删除程式链接
            var response = await _programLinkApiService.DeleteProgramLinkAsync(programLinkItemViewModel.Id);

            if (response.IsSuccess)
            {
                // 从集合中移除已删除的程式链接
                var programLinkToRemove = programLinkItemViewModel;
                ProgramLinks.Remove(programLinkToRemove);
                SelectedProgramLink = ProgramLinks.FirstOrDefault();

                // 显示成功消息
                await _dialogService.ShowMessageBoxAsync(
                    $"程式链接 '{programLinkToRemove?.Name}' 已成功删除。",
                    "删除成功",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Information);
            }
            else
            {
                ErrorMessage = $"删除程式链接失败: {response.ErrorMessage}";
                await _dialogService.ShowMessageBoxAsync(
                    ErrorMessage,
                    "删除失败",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"删除程式链接异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                ErrorMessage,
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    #endregion

    #region 程式链接项方法

    private async Task RefreshProgramLinkStepsAsync()
    {
        if (SelectedProgramLink != null) await LoadProgramLinkStepItemsAsync(SelectedProgramLink.Id);
    }

    private async Task SaveProgramLinkStepsAsync()
    {
        if (SelectedProgramLink == null)
        {
            await _dialogService.ShowMessageBoxAsync(
                "没有选中的程式链接。",
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
            return;
        }

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;
            await Task.Delay(500);
            var dtosToSave = ProgramLinkStepItems.Select(vm => new ProgramLinkItemUpsertDto
            {
                Id = vm.Id, // vm.Id 为 0 表示新项目
                ProgramId = vm.Program.Id,
                ExecutionOrder = vm.ExecutionOrder
            }).ToList();

            // 假设 API 服务中存在此方法用于批量更新/插入
            // 这个API应该能够处理Id为0的新项的创建，并更新现有项
            var result =
                await _programLinkApiService.BulkUpsertProgramLinkStepItemsAsync(SelectedProgramLink.Id, dtosToSave);

            if (result.IsSuccess)
            {
                await _dialogService.ShowMessageBoxAsync(
                    "程式链接项已成功保存。",
                    "保存成功",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Information);
                // 重新加载以获取任何新的 ID 并确认顺序
                await LoadProgramLinkStepItemsAsync(SelectedProgramLink.Id);
            }
            else
            {
                ErrorMessage = $"保存程式链接项失败: {result.ErrorMessage}";
                await _dialogService.ShowMessageBoxAsync(
                    ErrorMessage,
                    "保存失败",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"保存程式链接项异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                ErrorMessage,
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
            SaveAllStepsCommand.NotifyCanExecuteChanged(); // 更新按钮状态
        }
    }

    private bool CanSaveProgramLinkItems()
    {
        // 仅当有选中的程式链接并且链接项列表不为空时才允许保存
        // 可以进一步扩展此逻辑，例如检查是否有未保存的更改
        return SelectedProgramLink != null && ProgramLinkStepItems.Any();
    }

    private async Task AddProgramToLinkAsync()
    {
        if (SelectedProgramLink == null || SelectedProgram == null)
        {
            await _dialogService.ShowMessageBoxAsync(
                "请先选择一个程式链接和要添加的程式",
                "添加失败",
                MessageBoxButton.Ok,
                MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // 计算新的执行顺序
            var newOrder = 1; // 默认添加到末尾
            if (ProgramLinkStepItems.Any()) newOrder = ProgramLinkStepItems.Max(i => i.ExecutionOrder) + 1;

            // 本地创建新的 ProgramLinkStepItemViewModel
            var newLinkItem = new ProgramLinkStepItemViewModel
            {
                // 这里需要根据实际情况填充 Id，或者如果不需要立即使用 Id，可以暂时留空或设为临时值
                // Id = 0, // 假设本地添加时 Id 可以为 0 或临时值
                ProgramLinkId = SelectedProgram.Id,
                Program = SelectedProgram.ToDto(), // 直接使用选中的 ProgramItemViewModel
                ExecutionOrder = newOrder
                // 其他属性根据需要设置
            };

            ProgramLinkStepItems.Add(newLinkItem);

            // 排序
            var order = 1;
            foreach (var item in ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder)
                         .ThenBy(i => i.Program?.Name ?? string.Empty /* 处理临时添加项可能没有完整Program对象的情况 */))
                item.ExecutionOrder = order++;

            // 重新填充以确保UI更新排序
            var sortedItems = ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder).ToList();
            ProgramLinkStepItems.Clear();
            foreach (var item in sortedItems) ProgramLinkStepItems.Add(item);

            // 选中新添加的项
            SelectedLinkStepItem = newLinkItem;

            await _dialogService.ShowMessageBoxAsync(
                $"程式 '{SelectedProgram.Name}' 已成功添加到链接中 (本地)。请记得保存更改。",
                "添加成功 (本地)",
                MessageBoxButton.Ok,
                MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            ErrorMessage = $"本地添加程式到链接异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                ErrorMessage,
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
            SaveAllStepsCommand.NotifyCanExecuteChanged();
        }
    }

    private async Task RemoveProgramFromLinkAsync(ProgramLinkStepItemViewModel linkItem)
    {
        if (SelectedProgramLink == null || linkItem == null) return;

        // 显示确认对话框
        var confirmResult = await _dialogService.ShowMessageBoxAsync(
            $"确定要从程式链接中移除程式 '{linkItem.Program?.Name}' 吗？",
            "确认移除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (confirmResult != true) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // 本地移除
            ProgramLinkStepItems.Remove(linkItem);
            SelectedLinkStepItem = ProgramLinkStepItems.FirstOrDefault();

            // 重新计算剩余项的 ExecutionOrder
            var currentOrder = 1;
            foreach (var item in ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder))
                item.ExecutionOrder = currentOrder++;

            // 重新填充以确保UI更新排序 (如果需要)
            var tempList = ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder).ToList();
            ProgramLinkStepItems.Clear();
            foreach (var item in tempList) ProgramLinkStepItems.Add(item);

            await _dialogService.ShowMessageBoxAsync(
                $"程式 '{linkItem.Program?.Name}' 已成功从链接中移除 (本地)。请记得保存更改。",
                "移除成功 (本地)",
                MessageBoxButton.Ok,
                MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            ErrorMessage = $"本地移除程式从链接异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                ErrorMessage,
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
            SaveAllStepsCommand.NotifyCanExecuteChanged();
        }
    }

    private void MoveLinkItemUp()
    {
        if (SelectedLinkStepItem is { ExecutionOrder: > 0 })
        {
            var currentIndex = SelectedLinkStepItem.ExecutionOrder;
            var stepAbove = ProgramLinkStepItems.FirstOrDefault(s => s.ExecutionOrder == currentIndex - 1);

            if (stepAbove != null)
            {
                // 交换索引
                stepAbove.ExecutionOrder = currentIndex;
                SelectedLinkStepItem.ExecutionOrder = currentIndex - 1;

                // 重新排序显示
                var sortedSteps = ProgramLinkStepItems.OrderBy(s => s.ExecutionOrder).ToList();
                ProgramLinkStepItems.Clear();
                foreach (var step in sortedSteps) ProgramLinkStepItems.Add(step);

                // 确保选中的步骤仍然被选中
                SelectedLinkStepItem = ProgramLinkStepItems.FirstOrDefault(s => s.ExecutionOrder == currentIndex - 1);


                // 更新命令可执行状态
                MoveLinkItemDownCommand.NotifyCanExecuteChanged();
                MoveLinkItemUpCommand.NotifyCanExecuteChanged();
            }
        }
    }

    private void MoveLinkItemDown()
    {
        if (SelectedLinkStepItem != null)
        {
            var currentIndex = SelectedLinkStepItem.ExecutionOrder;
            var maxIndex = ProgramLinkStepItems.Count > 0 ? ProgramLinkStepItems.Max(s => s.ExecutionOrder) : 0;

            if (currentIndex < maxIndex)
            {
                var stepBelow = ProgramLinkStepItems.FirstOrDefault(s => s.ExecutionOrder == currentIndex + 1);

                if (stepBelow != null)
                {
                    // 交换索引
                    stepBelow.ExecutionOrder = currentIndex;
                    SelectedLinkStepItem.ExecutionOrder = currentIndex + 1;

                    // 重新排序显示
                    var sortedSteps = ProgramLinkStepItems.OrderBy(s => s.ExecutionOrder).ToList();
                    ProgramLinkStepItems.Clear();
                    foreach (var step in sortedSteps) ProgramLinkStepItems.Add(step);

                    // 确保选中的步骤仍然被选中
                    SelectedLinkStepItem =
                        ProgramLinkStepItems.FirstOrDefault(s => s.ExecutionOrder == currentIndex + 1);

                    // 更新命令可执行状态
                    MoveLinkItemDownCommand.NotifyCanExecuteChanged();
                    MoveLinkItemUpCommand.NotifyCanExecuteChanged();
                }
            }
        }
    }

    private async Task ReorderLinkItemAsync(ProgramLinkStepItemViewModel linkItem, int newOrder)
    {
        if (SelectedProgramLink == null || linkItem == null) return;

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _programLinkApiService.ReorderLinkItemAsync(
                SelectedProgramLink.Id, linkItem.Id, newOrder);

            if (result.IsSuccess)
            {
                // 刷新链接项列表
                await LoadProgramLinkStepItemsAsync(SelectedProgramLink.Id);

                // 重新选中项 (LoadProgramLinkStepItemsAsync 会清空并重新填充，所以需要重新查找)
                SelectedLinkStepItem = ProgramLinkStepItems.FirstOrDefault(i => i.Id == linkItem.Id);
            }
            else
            {
                ErrorMessage = $"调整程式链接项顺序失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"调整程式链接项顺序异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
            SaveAllStepsCommand.NotifyCanExecuteChanged();
        }
    }

    private bool CanManageProgramLinkItems()
    {
        return SelectedProgramLink != null;
    }

    #endregion

    #region 视图切换方法

    private void SwitchToCurveView()
    {
        ShowCurveView = true;
        ShowLinkStepsView = false;
    }

    private void SwitchToLinkStepsView()
    {
        ShowCurveView = false;
        ShowLinkStepsView = true;
    }

    #endregion

    #region 错误处理方法

    private void ToggleErrorExpand()
    {
        IsErrorExpanded = !IsErrorExpanded;
    }

    private void ClearError()
    {
        ErrorMessage = string.Empty;
        IsErrorExpanded = false;
    }

    #endregion

    #region 确认删除链接步骤方法

    private async Task ConfirmDeleteLinkStep(ProgramLinkStepItemViewModel linkStepItem)
    {
        if (SelectedProgramLink == null || linkStepItem == null) return;

        // 显示确认对话框
        var confirmResult = await _dialogService.ShowMessageBoxAsync(
            $"确定要删除此程式链接步骤吗？",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (confirmResult == true)
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                // 本地移除链接步骤
                ProgramLinkStepItems.Remove(linkStepItem);
                SelectedLinkStepItem = ProgramLinkStepItems.FirstOrDefault();

                // 重新计算剩余项的 ExecutionOrder
                var currentOrder = 1;
                foreach (var item in ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder))
                    item.ExecutionOrder = currentOrder++;

                // 重新填充以确保UI更新排序
                var tempList = ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder).ToList();
                ProgramLinkStepItems.Clear();
                foreach (var item in tempList) ProgramLinkStepItems.Add(item);

                // 通知命令状态变更
                SaveAllStepsCommand.NotifyCanExecuteChanged();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"删除程式链接步骤异常: {ex.Message}";
                await _dialogService.ShowMessageBoxAsync(
                    ErrorMessage,
                    "错误",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
    }

    #endregion
}