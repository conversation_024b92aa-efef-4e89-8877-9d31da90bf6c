using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.Presentation.ViewModels.Dashboard;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Services;
using Serilog;
using EnvizonController.Presentation.Controls.Models;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 报警管理页面视图模型
    /// </summary>
    public partial class AlarmViewModel : ObservableObject
    {
        #region 属性

        #region 测试项相关

        /// <summary>
        /// 测试项搜索文本
        /// </summary>
        [ObservableProperty]
        private string _testItemSearchText = string.Empty;

        /// <summary>
        /// 测试项名称精确筛选
        /// </summary>
        [ObservableProperty]
        private string _testItemNameFilter = string.Empty;

        /// <summary>
        /// 测试项开始日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _testItemStartDate;

        /// <summary>
        /// 测试项结束日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _testItemEndDate;

        /// <summary>
        /// 是否打开筛选面板
        /// </summary>
        [ObservableProperty]
        private bool _isFilterPanelOpen;

        /// <summary>
        /// 是否有活动的测试项筛选条件
        /// </summary>
        [ObservableProperty]
        private bool _hasActiveTestItemFilters;

        /// <summary>
        /// 所有测试项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TestItemViewModel> _testItems = new();
        
        /// <summary>
        /// 当前选中的测试项
        /// </summary>
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(SelectedTestItemDisplay))]
        private TestItemViewModel? _selectedTestItem;

        partial void OnSelectedTestItemChanged(TestItemViewModel? value)
        {
            // 当选中的测试项改变时，清空报警筛选条件并刷新报警列表
            AlarmSearchText = string.Empty;
            // 异步加载报警数据
            Task.Run(async () =>
            {
                ClearAlarmFilterValues();
                await ApplyAlarmFilters();
            });
        }

        /// <summary>
        /// 当前选中测试项的显示文本
        /// </summary>
        public string SelectedTestItemDisplay => SelectedTestItem != null ? 
            $"测试项: {SelectedTestItem.Name}" : "全部测试项";

        #endregion

        #region 报警相关

        /// <summary>
        /// 报警搜索文本
        /// </summary>
        [ObservableProperty]
        private string _alarmSearchText = string.Empty;

        /// <summary>
        /// 设备ID筛选
        /// </summary>
        [ObservableProperty]
        private long? _deviceIdFilter;

        /// <summary>
        /// 处理人筛选
        /// </summary>
        [ObservableProperty]
        private string _processedByFilter = string.Empty;

        /// <summary>
        /// 报警开始日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _alarmStartDate;

        /// <summary>
        /// 报警结束日期筛选
        /// </summary>
        [ObservableProperty]
        private DateTime? _alarmEndDate;

        /// <summary>
        /// 是否打开报警筛选面板
        /// </summary>
        [ObservableProperty]
        private bool _isAlarmFilterPanelOpen;

        /// <summary>
        /// 所有报警
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<AlarmItemViewModel> _alarms = new();


        /// <summary>
        /// J报警级别列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<AlarmSeverity> _alarmLevels = new(Enum.GetValues<AlarmSeverity>());

        /// <summary>
        /// 当前选中的报警级别
        /// </summary>
        [ObservableProperty]
        private AlarmSeverity? _selectedAlarmLevel;

        /// <summary>
        /// 报警状态列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<AlarmStatus> _alarmStatuses = new(Enum.GetValues<AlarmStatus>());

        /// <summary>
        /// 当前选中的报警状态
        /// </summary>
        [ObservableProperty]
        private AlarmStatus? _selectedAlarmStatus = null;

        #endregion

        #region 分页相关

        /// <summary>
        /// 分页信息
        /// </summary>
        private EnvizonController.Presentation.Controls.Models.PaginationInfo _paginationInfo = new EnvizonController.Presentation.Controls.Models.PaginationInfo();
        public EnvizonController.Presentation.Controls.Models.PaginationInfo PaginationInfo
        {
            get => _paginationInfo;
            set => SetProperty(ref _paginationInfo, value);
        }

        /// <summary>
        /// 页码变更命令
        /// </summary>
        public IRelayCommand PageChangedCommand => new RelayCommand<PaginationInfo>(OnPageChanged);

        private void OnPageChanged(PaginationInfo page)
        {
            _ = ApplyAlarmFilters();
        }

        [ObservableProperty]
        private bool _isLoading;
        #endregion

#endregion 
        #region 构造函数

        /// <summary>
        /// 全部测试项对象（特殊对象，ID为0）
        /// </summary>
        private TestItemViewModel _allTestItemsObject = new TestItemViewModel 
        { 
            Id = 0, 
            Name = "全部测试项", 
            ExecutionTime = DateTime.Now 
        };

        private readonly IAlarmApiService _alarmApiService;
        private readonly ITestItemApiService _testItemApiService;
        /// <summary>
        /// 初始化
        /// </summary>
        public AlarmViewModel(IAlarmApiService alarmApiService, ITestItemApiService testItemApiService)
        {
            _alarmApiService = alarmApiService;
            _testItemApiService = testItemApiService;
            
            // 为报警状态列表添加"全部"选项
            // 由于枚举不支持添加额外值，我们使用null表示全部
            _selectedAlarmStatus = null; // 默认选中全部
            
            // 初始化数据
            Task.Run(async () => await Refresh());
        }

        #endregion

        #region 命令

        /// <summary>
        /// 切换筛选面板命令
        /// </summary>
        [RelayCommand]
        private void ToggleFilterPanel()
        {
            IsFilterPanelOpen = !IsFilterPanelOpen;
        }

        /// <summary>
        /// 应用测试项筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ApplyTestItemFiltersAsync()
        {
            HasActiveTestItemFilters = !string.IsNullOrEmpty(TestItemNameFilter) ||
                                      TestItemStartDate.HasValue ||
                                      TestItemEndDate.HasValue;
            
            await LoadTestItemsAsync();
        }

        /// <summary>
        /// 清除测试项筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ClearTestItemFiltersAsync()
        {
            TestItemNameFilter = string.Empty;
            TestItemStartDate = null;
            TestItemEndDate = null;
            TestItemSearchText = string.Empty;
            HasActiveTestItemFilters = false;
            
            await LoadTestItemsAsync();
        }

        /// <summary>
        /// 切换报警筛选面板命令
        /// </summary>
        [RelayCommand]
        private void ToggleAlarmFilterPanel()
        {
            IsAlarmFilterPanelOpen = !IsAlarmFilterPanelOpen;
        }

        /// <summary>
        /// 应用报警筛选命令
        /// </summary>
        [RelayCommand]
        private async Task ApplyAlarmFilters()
        {
            IsLoading = true;
            try
            {

                await LoadDataAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 清除报警筛选命令（清空筛选并刷新数据）
        /// </summary>
        [RelayCommand]
        private async Task ClearAlarmFilters()
        {
            ClearAlarmFilterValues();
            await ApplyAlarmFilters();
        }

        /// <summary>
        /// 只清空报警筛选条件，不刷新数据
        /// </summary>
        private void ClearAlarmFilterValues()
        {
            DeviceIdFilter = null;
            ProcessedByFilter = string.Empty;
            AlarmStartDate = null;
            AlarmEndDate = null;
            SelectedAlarmLevel = null;
            SelectedAlarmStatus = null;
        }

        /// <summary>
        /// 前往上一页命令
        /// </summary>
        [RelayCommand]
        private async Task PreviousPage()
        {
            PaginationInfo.CurrentPage--;
            await LoadDataAsync();
        }

        /// <summary>
        /// 前往下一页命令
        /// </summary>
        [RelayCommand]
        private async Task NextPage()
        {
            PaginationInfo.CurrentPage++;
            await LoadDataAsync();
        }

        /// <summary>
        /// 前往指定页码命令
        /// </summary>
        [RelayCommand]
        private async Task GoToPage(int pageNumber)
        {
            if (pageNumber >= 1 && pageNumber <= PaginationInfo.TotalPages)
            {
                PaginationInfo.CurrentPage = pageNumber;
                await LoadDataAsync();
            }
        }

        /// <summary>
        /// 处理报警命令
        /// </summary>
        [RelayCommand]
        private async Task ProcessAlarm(AlarmItemViewModel alarm)
        {
            if (alarm == null) return;

            IsLoading = true;
            try
            {
                // 实际应用中应从身份验证系统获取当前操作员
                var processedBy = "当前操作员";

                // 调用API处理报警
                var result = await _alarmApiService.ProcessAlarmAsync(alarm.Id, processedBy);

                if (result.IsSuccess && result.Data != null)
                {
                    // 用API返回的数据更新本地视图模型
                    alarm.Status = result.Data.Status;
                    alarm.ProcessedBy = result.Data.ProcessedBy;
                    alarm.ProcessedAt = result.Data.ProcessedAt;
                    alarm.IsExpanded = false; // 关闭展开状态
                }
                else
                {
                    // 可根据需要弹窗或日志提示
                    Log.Error("处理报警失败: {Message}", result.ErrorMessage);
                }

                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理报警异常");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 筛选全部报警命令
        /// </summary>
        [RelayCommand]
        private async Task FilterAllAlarms()
        {
            SelectedAlarmStatus = null;
            await ApplyAlarmFilters();
        }

        /// <summary>
        /// 筛选待处理报警命令
        /// </summary>
        [RelayCommand]
        private async Task FilterActiveAlarms()
        {
            SelectedAlarmStatus = AlarmStatus.Active;
            await ApplyAlarmFilters();
        }

        /// <summary>
        /// 筛选已处理报警命令
        /// </summary>
        [RelayCommand]
        private async Task FilterProcessedAlarms()
        {
            SelectedAlarmStatus = AlarmStatus.Processed;
            await ApplyAlarmFilters();
        }

        #endregion

        #region 私有方法

        private async Task Refresh()
        {
            IsLoading = true;
            try
            {
                await LoadTestItemsAsync();
                await LoadDataAsync();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 根据加载测试数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            var result = await _alarmApiService.GetAlarmsAsync(new AlarmQueryParams
            {
                Page = PaginationInfo.CurrentPage,
                PageSize = PaginationInfo.PageSize, // 可根据实际需求调整
                Status = SelectedAlarmStatus,
                Level = SelectedAlarmLevel,
                StartDate = AlarmStartDate,
                EndDate = AlarmEndDate,
                TestId = SelectedTestItem?.Id,
                DeviceId = DeviceIdFilter
            });
            if (!result.IsSuccess)
            {
                Log.Logger.Error("获取报警数据失败: {Message}", result.ErrorMessage);
                return;
            }

            var alarms = result.Data.Items
                .Select(dto => new AlarmItemViewModel(dto))
                .ToList();

            // 计算"全部测试项"的活动报警数
            _allTestItemsObject.ActiveAlarmCount = alarms.Count(a => a.Status == AlarmStatus.Active);

            Alarms = new ObservableCollection<AlarmItemViewModel>(alarms);

            // 计算总页数
            PaginationInfo.TotalCount = result.Data.TotalCount;
        }

        /// <summary>
        /// 加载测试项数据
        /// </summary>
        private async Task LoadTestItemsAsync()
        {// 构建查询参数
            var queryParams = new TestItemQueryParams();

            // 应用名称搜索
            if (!string.IsNullOrEmpty(TestItemSearchText))
            {
                queryParams.SearchText = TestItemSearchText;
            }

            // 应用名称精确筛选
            if (HasActiveTestItemFilters && !string.IsNullOrEmpty(TestItemNameFilter))
            {
                queryParams.Name = TestItemNameFilter;
            }

            // 应用开始日期筛选
            if (HasActiveTestItemFilters && TestItemStartDate.HasValue)
            {
                queryParams.StartDate = TestItemStartDate.Value;
            }

            // 应用结束日期筛选
            if (HasActiveTestItemFilters && TestItemEndDate.HasValue)
            {
                queryParams.EndDate = TestItemEndDate.Value;
            }

            // 设置页码和每页大小
            queryParams.Page = 1;
            queryParams.PageSize = 100;

            // 调用API获取筛选后的测试项
            var result = await _testItemApiService.GetTestItemsAsync(queryParams);

            if (!result.IsSuccess)
            {
                Log.Error("获取测试项失败: {Message}", result.ErrorMessage);
                return;
            }

            var testItems = new List<TestItemViewModel>
            {
                _allTestItemsObject // 添加"全部测试项"作为第一个选项
            };

            // 将API返回的测试项转换为视图模型
            foreach (var item in result.Data.Items)
            {
                testItems.Add(new TestItemViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    ExecutionTime = item.CreatedAt,
                    // 此处可以添加活动报警计数的逻辑，例如从报警服务获取
                    ActiveAlarmCount = 0 // 暂时设为0，后续可以通过接口获取
                });
            }

            TestItems = new ObservableCollection<TestItemViewModel>(testItems);

            // 默认选择全部测试项
            if (SelectedTestItem == null)
            {
                SelectedTestItem = _allTestItemsObject;
            }
        }

        /// <summary>
        /// 应用测试项筛选条件
        /// </summary>
        private IEnumerable<TestItemViewModel> ApplyTestItemFilters(IEnumerable<TestItemViewModel> items, string searchText)
        {
            var query = items.AsQueryable();

            // 应用搜索文本筛选
            if (!string.IsNullOrEmpty(searchText))
            {
                query = query.Where(x => x.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            }

            // 应用名称精确筛选
            if (HasActiveTestItemFilters && !string.IsNullOrEmpty(TestItemNameFilter))
            {
                query = query.Where(x => x.Name.Equals(TestItemNameFilter, StringComparison.OrdinalIgnoreCase));
            }

            // 应用开始日期筛选
            if (HasActiveTestItemFilters && TestItemStartDate.HasValue)
            {
                query = query.Where(x => x.ExecutionTime >= TestItemStartDate.Value);
            }

            // 应用结束日期筛选
            if (HasActiveTestItemFilters && TestItemEndDate.HasValue)
            {
                query = query.Where(x => x.ExecutionTime <= TestItemEndDate.Value.AddDays(1).AddSeconds(-1));
            }

            return query.ToList();
        }

   
        #endregion
    }
} 