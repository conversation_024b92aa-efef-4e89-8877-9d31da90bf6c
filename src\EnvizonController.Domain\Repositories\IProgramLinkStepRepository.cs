using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 链接步骤仓储接口
    /// </summary>
    public interface IProgramLinkStepRepository : IRepository<ProgramLinkStep, long>
    {
        /// <summary>
        /// 根据程式ID获取所有链接程式步骤
        /// </summary>
        /// <param name="programLinkId">链接ID</param>
        /// <returns>程式步骤集合</returns>
        Task<IEnumerable<ProgramLinkStep>> GetByProgramLinkIdAsync(long programLinkId);

        /// <summary>
        /// 根据链接ID和步骤索引获取链接步骤
        /// </summary>
        /// <param name="programLinkId">链接ID</param>
        /// <param name="index">步骤索引</param>
        /// <returns>程式步骤，如果未找到则返回null</returns>
        Task<ProgramLinkStep?> GetByProgramLinkIdAndIndexAsync(long programLinkId, int index);

        /// <summary>
        /// 根据链接ID删除所有链接步骤
        /// </summary>
        /// <param name="programLinkId">链接ID</param>
        /// <returns>操作任务</returns>
        Task DeleteByProgramLinkIdAsync(long programLinkId);
    }
} 