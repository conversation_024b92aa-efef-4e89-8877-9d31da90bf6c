using EnvizonController.Mqtt.Client.Models;
using EnvizonController.Mqtt.Client.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EnvizonController.Mqtt.Client.Handlers
{
    public class AlarmMessageHandler : IMessageHandler
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<AlarmMessageHandler> _logger;

        public AlarmMessageHandler(INotificationService notificationService, ILogger<AlarmMessageHandler> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        public bool CanHandle(string topic)
        {
            return topic.StartsWith("alarms/");
        }

        public Task HandleMessageAsync(string topic, string message)
        {
            try
            {
                var alarm = JsonSerializer.Deserialize<AlarmNotification>(message);
                if (alarm == null)
                {
                    _logger.LogWarning($"无法解析告警消息: {message}");
                    return Task.CompletedTask;
                }

                _logger.LogInformation($"处理告警消息: {alarm.Message}");
                
                // 发布通知
                _notificationService.Publish(alarm);
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理告警消息时发生错误");
                return Task.FromException(ex);
            }
        }
    }
} 