using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写多个线圈请求
    /// </summary>
    public class WriteMultipleCoilsRequest : ModbusRequest
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; set; }

        /// <summary>
        /// 线圈值
        /// </summary>
        public bool[] CoilValues { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="coilValues">线圈值</param>
        public WriteMultipleCoilsRequest(byte slaveAddress, ushort startAddress, bool[] coilValues)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.WriteMultipleCoils;
            StartAddress = startAddress;
            CoilValues = coilValues ?? throw new ArgumentNullException(nameof(coilValues));
            
            if (coilValues.Length > 1968) // Modbus规范限制
                throw new ArgumentException("线圈数量超过Modbus规范限制(1968)", nameof(coilValues));
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            // 计算字节数
            int byteCount = (CoilValues.Length + 7) / 8;
            
            var data = new byte[5 + byteCount];
            data[0] = (byte)(StartAddress >> 8);    // 起始地址高字节
            data[1] = (byte)(StartAddress & 0xFF);  // 起始地址低字节
            data[2] = (byte)(CoilValues.Length >> 8);   // 线圈数量高字节
            data[3] = (byte)(CoilValues.Length & 0xFF); // 线圈数量低字节
            data[4] = (byte)byteCount;              // 字节计数
            
            // 打包线圈值
            for (int i = 0; i < CoilValues.Length; i++)
            {
                if (CoilValues[i])
                {
                    int byteIndex = 5 + (i / 8);
                    int bitIndex = i % 8;
                    data[byteIndex] |= (byte)(1 << bitIndex);
                }
            }
            
            return data;
        }
    }
}
