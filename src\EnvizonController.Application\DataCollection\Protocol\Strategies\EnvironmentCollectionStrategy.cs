﻿using System.Globalization;
using EnvizonController.Application.DataCollection.Protocol;
using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Modbus.Client.Extensions;
using EnvizonController.Shared.Enums;
using Serilog;
using static EnvizonController.Application.DataCollection.Protocol.ProtocolDataCollector;

namespace EnvizonController.Application.DataCollection.Strategies;

public class EnvironmentCollectionStrategy : ICollectionStrategy
{
    private readonly ILogger _logger;

    public EnvironmentCollectionStrategy(ILogger logger)
    {
        _logger = logger.ForContext<EnvironmentCollectionStrategy>();
    }

    public bool CanHandle(ProtocolItem item)
    {
        return item.GroupName == "环境";
    }

    public async Task<Dictionary<string, object>> CollectAsync(DeviceCollectionDetails deviceCollectionDetails,
        ProtocolItem item, Device device, IModbusDeviceService modbusDeviceService)
    {
        _logger.Debug(
            "Collecting ENVIRONMENT data for item: {ItemName} from device: {DeviceName} using address {Address}",
            item.Name, device.Name, item.Address);

        double rawValue;

        switch (item.DataType)
        {
            case DataType.Float:
                rawValue = await modbusDeviceService.ModbusClient.ReadFloatAsync(device.SlaveId, item.Address,
                    EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                break;
            case DataType.Double:
                rawValue = await modbusDeviceService.ModbusClient.ReadDoubleAsync(device.SlaveId, item.Address,
                    EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                break;
            case DataType.Int16:
                rawValue = await modbusDeviceService.ModbusClient.ReadInt16Async(device.SlaveId, item.Address,
                    CancellationToken.None);
                break;
            case DataType.UInt16:
                rawValue = await modbusDeviceService.ModbusClient.ReadUInt16Async(device.SlaveId, item.Address,
                    CancellationToken.None);
                break;
            case DataType.Int32:
                rawValue = await modbusDeviceService.ModbusClient.ReadInt32Async(device.SlaveId, item.Address,
                    EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                break;
            case DataType.UInt32:
                rawValue = await modbusDeviceService.ModbusClient.ReadUInt32Async(device.SlaveId, item.Address,
                    EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                break;
            case DataType.Bool:
                var coils = await modbusDeviceService.ModbusClient.ReadCoilsAsync(device.SlaveId, item.Address, 1,
                    CancellationToken.None);
                rawValue = coils[0] ? 1.0 : 0.0;
                break;
            default:
                _logger.Warning("不支持的数据类型: {DataType}，使用默认Float类型", item.DataType);
                rawValue = await modbusDeviceService.ModbusClient.ReadFloatAsync(device.SlaveId, item.Address,
                    EndianHelper.WordOrder.BigEndian, CancellationToken.None);
                break;
        }

        var scaledValue = rawValue * (item.ScaleFactor != 0 ? item.ScaleFactor : 1.0) + item.Offset;

        deviceCollectionDetails.ValueDataList.Add(new ValueData
            { ProtocolIndex = item.Index, Value = scaledValue.ToString(CultureInfo.InvariantCulture) });
        var collectedData = new Dictionary<string, object>
        {
            { "value", scaledValue },
            { "unit", item.Unit ?? "N/A" },
            { "name", item.DisplayName },
            { "description", $"Simulated environment data for {item.Name}" }
        };

        deviceCollectionDetails.EnvironmentalData[item.Name] = collectedData;
        return collectedData;
    }
}