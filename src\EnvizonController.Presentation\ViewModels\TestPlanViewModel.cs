using CommunityToolkit.Mvvm.DependencyInjection;
using CommunityToolkit.Mvvm.Input;

namespace EnvizonController.Presentation.ViewModels;

public class TestPlanViewModel : ViewModelBase
{
    private bool _isLinkSettingsSelected;

    // 当前选中的标签页
    private bool _isProgramDesignSelected = true;


    public TestPlanViewModel()
    {
        // 初始化命令
        SwitchToProgramDesignCommand = new RelayCommand(() => { IsProgramDesignSelected = true; });
        SwitchToLinkSettingsCommand = new RelayCommand(() => { IsLinkSettingsSelected = true; });
        ProgramLinkViewModel = Ioc.Default.GetService<ProgramLinkViewModel>();
        ProgramViewModel = Ioc.Default.GetService<ProgramViewModel>();
        RefreshCommand = new AsyncRelayCommand(RefreshAsync);
    }
    private async Task RefreshAsync()
    {
        if (IsProgramDesignSelected)
        {
            await ProgramViewModel.LoadProgramsAsync();
        }
        else
        {
           await ProgramLinkViewModel.LoadProgramLinksAsync();
        }
    }
    public IAsyncRelayCommand RefreshCommand { get; }
    public bool IsProgramDesignSelected
    {
        get => _isProgramDesignSelected;
        set
        {
            if (SetProperty(ref _isProgramDesignSelected, value) && value) IsLinkSettingsSelected = false;
        }
    }

    public bool IsLinkSettingsSelected
    {
        get => _isLinkSettingsSelected;
        set
        {
            if (SetProperty(ref _isLinkSettingsSelected, value) && value) IsProgramDesignSelected = false;
        }
    }


    // 命令
    public IRelayCommand SwitchToProgramDesignCommand { get; }
    public IRelayCommand SwitchToLinkSettingsCommand { get; }

    public ProgramLinkViewModel ProgramLinkViewModel { get; }
    public ProgramViewModel ProgramViewModel { get; }
}