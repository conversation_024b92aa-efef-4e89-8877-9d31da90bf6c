using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;

namespace EnvizonController.Presentation.Models
{
    /// <summary>
    /// 测试配置类
    /// </summary>
    public partial class TestConfiguration : ObservableValidator
    {
        /// <summary>
        /// 测试名称
        /// </summary>
        [ObservableProperty]
        [Required(ErrorMessage = "测试名称不能为空")]
        private string _testName = string.Empty;

        /// <summary>
        /// 测试备注
        /// </summary>
        [ObservableProperty]
        private string? _testRemark;

        /// <summary>
        /// 测试模式
        /// </summary>
        [ObservableProperty]
        private TestMode _testMode = TestMode.FixedValue;

        /// <summary>
        /// 定值模式配置 - 始终保持实例
        /// </summary>
        public FixedValueModeConfig FixedValueConfig { get; } = new();

        /// <summary>
        /// 定时模式配置 - 始终保持实例
        /// </summary>
        public TimerModeConfig TimerConfig { get; } = new();

        /// <summary>
        /// 程式模式配置 - 始终保持实例
        /// </summary>
        public ProgramModeConfig ProgramConfig { get; } = new();

        /// <summary>
        /// 链接模式配置 - 始终保持实例
        /// </summary>
        public LinkModeConfig LinkConfig { get; } = new();

        /// <summary>
        /// 获取当前选中模式的配置对象
        /// </summary>
        public object GetCurrentModeConfig()
        {
            return TestMode switch
            {
                TestMode.FixedValue => FixedValueConfig,
                TestMode.Timer => TimerConfig,
                TestMode.Program => ProgramConfig,
                TestMode.Link => LinkConfig,
                _ => FixedValueConfig
            };
        }
    }

    /// <summary>
    /// 定值模式配置
    /// </summary>
    public class FixedValueModeConfig
    {
        /// <summary>
        /// 启动模式
        /// </summary>
        public StartupMode StartupMode { get; set; } = StartupMode.ColdStart;

        /// <summary>
        /// 温度设定值
        /// </summary>
        [Range(-100, 200, ErrorMessage = "温度设定值必须在-100到200之间")]
        public double Temperature { get; set; } = 25.0;

        /// <summary>
        /// 湿度设定值
        /// </summary>
        [Range(0, 100, ErrorMessage = "湿度设定值必须在0到100之间")]
        public double Humidity { get; set; } = 50.0;
    }

    /// <summary>
    /// 定时模式配置
    /// </summary>
    public class TimerModeConfig
    {
        /// <summary>
        /// 启动模式
        /// </summary>
        public StartupMode StartupMode { get; set; } = StartupMode.ColdStart;

        /// <summary>
        /// 温度设定值
        /// </summary>
        [Range(-100, 200, ErrorMessage = "温度设定值必须在-100到200之间")]
        public double Temperature { get; set; } = 25.0;

        /// <summary>
        /// 湿度设定值
        /// </summary>
        [Range(0, 100, ErrorMessage = "湿度设定值必须在0到100之间")]
        public double Humidity { get; set; } = 50.0;

        /// <summary>
        /// 开始计时范围
        /// </summary>
        public string TimingRange { get; set; } = string.Empty;

        /// <summary>
        /// 定时天数
        /// </summary>
        [Range(0, 365, ErrorMessage = "天数必须在0到365之间")]
        public int Days { get; set; } = 0;

        /// <summary>
        /// 定时小时数
        /// </summary>
        [Range(0, 23, ErrorMessage = "小时数必须在0到23之间")]
        public int Hours { get; set; } = 1;

        /// <summary>
        /// 定时分钟数
        /// </summary>
        [Range(0, 59, ErrorMessage = "分钟数必须在0到59之间")]
        public int Minutes { get; set; } = 0;
    }

    /// <summary>
    /// 程式模式配置
    /// </summary>
    public class ProgramModeConfig
    {
        /// <summary>
        /// 启动模式
        /// </summary>
        public StartupMode StartupMode { get; set; } = StartupMode.ColdStart;

        /// <summary>
        /// 开始计时范围
        /// </summary>
        public string TimingRange { get; set; } = string.Empty;

        /// <summary>
        /// 选择的程式ID
        /// </summary>
        [Required(ErrorMessage = "请选择一个程式")]
        public long? SelectedProgramId { get; set; }

        /// <summary>
        /// 选择的程式名称
        /// </summary>
        public string SelectedProgramName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 链接模式配置
    /// </summary>
    public class LinkModeConfig
    {
        /// <summary>
        /// 启动模式
        /// </summary>
        public StartupMode StartupMode { get; set; } = StartupMode.ColdStart;

        /// <summary>
        /// 开始计时范围
        /// </summary>
        public string TimingRange { get; set; } = string.Empty;

        /// <summary>
        /// 选择的链接ID
        /// </summary>
        [Required(ErrorMessage = "请选择一个链接")]
        public long? SelectedLinkId { get; set; }

        /// <summary>
        /// 选择的链接名称
        /// </summary>
        public string SelectedLinkName { get; set; } = string.Empty;
    }
}
