﻿using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Repositories;
using EnvizonController.Infrastructure.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace EnvizonController.Infrastructure.Persistence
{
    /// <summary>
    /// 工作单元实现
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly AppDbContext _dbContext;
        private IDbContextTransaction? _transaction;
        private bool _disposed;

        private IAlarmRepository? _alarmRepository;
        private ITestItemRepository? _testItemRepository;
        private IDataPointRepository? _testDataPointRepository;
        private IDeviceRepository? _deviceRepository;
        private IProtocolRepository? _protocolRepository;
        private IProgramRepository? _programRepository;
        private IProgramStepRepository? _programStepRepository;
        private IProgramLinkRepository? _programLinkRepository;
        private IProgramLinkStepRepository? _programLinkStepRepository;
        public UnitOfWork(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 报警仓储
        /// </summary>
        public IAlarmRepository Alarms => _alarmRepository ??= new AlarmRepository(_dbContext);

        /// <summary>
        /// 测试项仓储
        /// </summary>
        public ITestItemRepository TestItems => _testItemRepository ??= new TestItemRepository(_dbContext);

        /// <summary>
        /// 测试数据点仓储
        /// </summary>
        public IDataPointRepository TestDataPoints => _testDataPointRepository ??= new TestDataPointRepository(_dbContext);

        /// <summary>
        /// 设备仓储
        /// </summary>
        public IDeviceRepository Devices => _deviceRepository ??= new DeviceRepository(_dbContext);

        /// <summary>
        /// 协议仓储
        /// </summary>
        public IProtocolRepository Protocols => _protocolRepository ??= new ProtocolRepository(_dbContext);

        /// <summary>
        /// 程式表仓储
        /// </summary>
        public IProgramRepository Programs => _programRepository ??= new ProgramRepository(_dbContext);

        /// <summary>
        /// 链接表仓储
        /// </summary>
        public IProgramLinkRepository ProgramLinks => _programLinkRepository ??= new ProgramLinkRepository(_dbContext);

        /// <summary>
        /// 程式步骤仓储
        /// </summary>
        public IProgramStepRepository ProgramSteps => _programStepRepository ??= new ProgramStepRepository(_dbContext);

        /// <summary>
        /// 链接步骤仓储
        /// </summary>
        public IProgramLinkStepRepository ProgramLinkSteps => _programLinkStepRepository ??= new ProgramLinkStepRepository(_dbContext);

        /// <summary>
        /// 保存所有更改
        /// </summary>
        public async Task<int> SaveChangesAsync()
        {
            return await _dbContext.SaveChangesAsync();
        }

        /// <summary>
        /// 开始事务
        /// </summary>
        public async Task BeginTransactionAsync()
        {
            _transaction = await _dbContext.Database.BeginTransactionAsync();
        }

        /// <summary>
        /// 提交事务
        /// </summary>
        public async Task CommitTransactionAsync()
        {
            try
            {
                await SaveChangesAsync();

                if (_transaction != null)
                {
                    await _transaction.CommitAsync();
                }
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        /// <summary>
        /// 回滚事务
        /// </summary>
        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _transaction?.Dispose();
                _dbContext.Dispose();
                _disposed = true;
            }
        }
    }
}
