using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 读取输入寄存器请求
    /// </summary>
    public class ReadInputRegistersRequest : ModbusRequest
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; set; }

        /// <summary>
        /// 寄存器数量
        /// </summary>
        public ushort RegisterCount { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="registerCount">寄存器数量</param>
        public ReadInputRegistersRequest(byte slaveAddress, ushort startAddress, ushort registerCount)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.ReadInputRegisters;
            StartAddress = startAddress;
            RegisterCount = registerCount;
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            var data = new byte[4];
            data[0] = (byte)(StartAddress >> 8);    // 高字节
            data[1] = (byte)(StartAddress & 0xFF);  // 低字节
            data[2] = (byte)(RegisterCount >> 8);   // 高字节
            data[3] = (byte)(RegisterCount & 0xFF); // 低字节
            return data;
        }
    }
}
