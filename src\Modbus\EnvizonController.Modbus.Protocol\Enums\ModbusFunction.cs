namespace EnvizonController.Modbus.Protocol.Enums
{
    /// <summary>
    /// Modbus 功能码枚举
    /// </summary>
    public enum ModbusFunction : byte
    {
        /// <summary>
        /// 读取线圈状态 (Read Coil Status)
        /// </summary>
        ReadCoils = 0x01,

        /// <summary>
        /// 读取离散输入状态 (Read Discrete Inputs)
        /// </summary>
        ReadDiscreteInputs = 0x02,

        /// <summary>
        /// 读取保持寄存器 (Read Holding Registers)
        /// </summary>
        ReadHoldingRegisters = 0x03,

        /// <summary>
        /// 读取输入寄存器 (Read Input Registers)
        /// </summary>
        ReadInputRegisters = 0x04,

        /// <summary>
        /// 写单个线圈 (Write Single Coil)
        /// </summary>
        WriteSingleCoil = 0x05,

        /// <summary>
        /// 写单个寄存器 (Write Single Register)
        /// </summary>
        WriteSingleRegister = 0x06,

        /// <summary>
        /// 读取异常状态 (Read Exception Status)
        /// </summary>
        ReadExceptionStatus = 0x07,

        /// <summary>
        /// 诊断 (Diagnostics)
        /// </summary>
        Diagnostics = 0x08,

        /// <summary>
        /// 获取计数器 (Get Comm Event Counter)
        /// </summary>
        GetCommEventCounter = 0x0B,

        /// <summary>
        /// 获取事件日志 (Get Comm Event Log)
        /// </summary>
        GetCommEventLog = 0x0C,

        /// <summary>
        /// 写多个线圈 (Write Multiple Coils)
        /// </summary>
        WriteMultipleCoils = 0x0F,

        /// <summary>
        /// 写多个寄存器 (Write Multiple Registers)
        /// </summary>
        WriteMultipleRegisters = 0x10,

        /// <summary>
        /// 报告从站ID (Report Slave ID)
        /// </summary>
        ReportSlaveId = 0x11,

        /// <summary>
        /// 读写多个寄存器 (Read/Write Multiple Registers)
        /// </summary>
        ReadWriteMultipleRegisters = 0x17,

        /// <summary>
        /// 读取文件记录 (Read File Record)
        /// </summary>
        ReadFileRecord = 0x14,

        /// <summary>
        /// 写文件记录 (Write File Record)
        /// </summary>
        WriteFileRecord = 0x15,

        /// <summary>
        /// 屏蔽写寄存器 (Mask Write Register)
        /// </summary>
        MaskWriteRegister = 0x16,

        /// <summary>
        /// 读取FIFO队列 (Read FIFO Queue)
        /// </summary>
        ReadFifoQueue = 0x18,

        /// <summary>
        /// 封装接口传输 (Encapsulated Interface Transport)
        /// </summary>
        EncapsulatedInterfaceTransport = 0x2B
    }
}
