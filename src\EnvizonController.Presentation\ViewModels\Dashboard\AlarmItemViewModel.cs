using System;
using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.ViewModels.Dashboard
{
    /// <summary>
    /// 报警信息视图模型
    /// </summary>
    public partial class AlarmItemViewModel : ObservableObject
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        [ObservableProperty] private long _id;

        /// <summary>
        /// 设备ID
        /// </summary>
        [ObservableProperty] private long _deviceId;

        /// <summary>
        /// 协议ID
        /// </summary>
        [ObservableProperty] private long? _protocolId;

        /// <summary>
        /// 协议项索引
        /// </summary>
        [ObservableProperty] private int? _protocolItemIndex;

        /// <summary>
        /// 测试ID
        /// </summary>
        [ObservableProperty] private long? _testId;

        /// <summary>
        /// 报警名称
        /// </summary>
        [ObservableProperty] private string _name = string.Empty;

        /// <summary>
        /// 报警消息
        /// </summary>
        [ObservableProperty] private string _message = string.Empty;

        /// <summary>
        /// 报警级别
        /// </summary>
        [ObservableProperty] private AlarmSeverity _level;

        /// <summary>
        /// 报警状态
        /// </summary>
        [ObservableProperty] private AlarmStatus _status;

        /// <summary>
        /// 处理人
        /// </summary>
        [ObservableProperty] private string? _processedBy;

        /// <summary>
        /// 报警发生时间
        /// </summary>
        [ObservableProperty] private DateTime _timestamp;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty] private DateTime? _lastUpdated;

        /// <summary>
        /// 处理时间
        /// </summary>
        [ObservableProperty] private DateTime? _processedAt;
        
        /// <summary>
        /// 是否展开详情视图
        /// </summary>
        [ObservableProperty] private bool _isExpanded;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AlarmItemViewModel()
        {
        }

        /// <summary>
        /// 从AlarmDTO创建报警视图模型
        /// </summary>
        /// <param name="alarmDto">报警DTO</param>
        public AlarmItemViewModel(AlarmDTO alarmDto)
        {
            Id = alarmDto.Id;
            DeviceId = alarmDto.DeviceId;
            TestId = alarmDto.TestId;
            Name = alarmDto.Name;
            Message = alarmDto.Message;
            Level = alarmDto.Level;
            Status = alarmDto.Status;
            ProcessedBy = alarmDto.ProcessedBy;
            Timestamp = alarmDto.Timestamp;
            LastUpdated = alarmDto.LastUpdated;
            ProcessedAt = alarmDto.ProcessedAt;
        }

        /// <summary>
        /// 获取报警级别的显示文本
        /// </summary>
        public string LevelDisplayText => Level switch
        {
            AlarmSeverity.Low => "低",
            AlarmSeverity.Medium => "中",
            AlarmSeverity.High => "紧急",
            _ => "未知"
        };

        /// <summary>
        /// 获取报警状态的显示文本
        /// </summary>
        public string StatusDisplayText => Status switch
        {
            AlarmStatus.Active => "活动",
            AlarmStatus.Processed => "已处理",
            _ => "未知"
        };
    }
}