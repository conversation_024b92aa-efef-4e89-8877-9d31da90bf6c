using EnvizonController.Mqtt.Server.Handlers;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Mqtt.Server.Services
{
    public class MessageProcessor : IMessageProcessor
    {
        private readonly Dictionary<string, List<IMessageHandler>> _topicHandlers = new();
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MessageProcessor> _logger;

        public MessageProcessor(IServiceProvider serviceProvider, ILogger<MessageProcessor> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public void RegisterHandler(string topicPattern, IMessageHandler handler)
        {
            if (!_topicHandlers.ContainsKey(topicPattern))
            {
                _topicHandlers[topicPattern] = new List<IMessageHandler>();
            }
            _topicHandlers[topicPattern].Add(handler);
        }

        public void ProcessMessage(string topic, string message, string clientId)
        {
            foreach (var entry in _topicHandlers)
            {
                if (TopicMatchesPattern(topic, entry.Key))
                {
                    foreach (var handler in entry.Value)
                    {
                        try
                        {
                            handler.HandleMessage(topic, message, clientId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"处理消息时出错: Topic={topic}, Handler={handler.GetType().Name}");
                        }
                    }
                }
            }
        }

        private bool TopicMatchesPattern(string topic, string pattern)
        {
            // 实现MQTT主题匹配逻辑
            // 例如: "sensors/+/temperature" 匹配 "sensors/living-room/temperature"
            if (topic == pattern)
            {
                return true;
            }

            string[] topicLevels = topic.Split('/');
            string[] patternLevels = pattern.Split('/');

            // '#'通配符允许匹配零个或多个级别
            if (patternLevels[patternLevels.Length - 1] == "#")
            {
                // 检查前面的级别是否匹配
                for (int i = 0; i < patternLevels.Length - 1; i++)
                {
                    if (i >= topicLevels.Length)
                    {
                        return false;
                    }

                    if (patternLevels[i] != "+" && patternLevels[i] != topicLevels[i])
                    {
                        return false;
                    }
                }
                return true;
            }

            // 如果级别数不同且没有'#'通配符，则不匹配
            if (topicLevels.Length != patternLevels.Length)
            {
                return false;
            }

            // 检查每个级别
            for (int i = 0; i < topicLevels.Length; i++)
            {
                // '+'通配符匹配当前级别中的任何内容
                if (patternLevels[i] != "+" && patternLevels[i] != topicLevels[i])
                {
                    return false;
                }
            }

            return true;
        }
    }
} 