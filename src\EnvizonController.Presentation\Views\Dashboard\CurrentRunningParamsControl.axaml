<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.CurrentRunningParamsControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="150"
    d:DesignWidth="300"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <StackPanel Orientation="Horizontal">
            <TextBlock
                Margin="0,0,6,0"
                VerticalAlignment="Center"
                Classes="gray font-icon small"
                Text="&#xf085;" />
            <TextBlock
                Margin="0"
                VerticalAlignment="Center"
                Classes="gray bottom small"
                Text="当前运行参数" />
        </StackPanel>
        <Grid
            Grid.Row="1"
            Margin="0,6,0,0"
            RowDefinitions="Auto,Auto">
            <Grid ColumnDefinitions="*,*" RowDefinitions="*,*">
                <Border Grid.Column="0" Classes="cyber-border card">
                    <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,*,Auto">
                        <StackPanel Margin="0,0,0,9" Orientation="Horizontal">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,6,0"
                                    VerticalAlignment="Center"
                                    Classes="font-icon small"
                                    Foreground="#E62FF3"
                                    Text="&#xf06d;" />
                                <TextBlock
                                    Margin="0"
                                    VerticalAlignment="Center"
                                    Classes="gray bottom small"
                                    Text="{Binding CurrentDevice.HeatingOutput.Name}" />
                            </StackPanel>
                        </StackPanel>
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Classes="white h3 font-cyber"
                            Text="{Binding CurrentDevice.HeatingOutput.Value}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Classes="small gray2"
                            Text="{Binding CurrentDevice.HeatingOutput.Status}" />
                    </Grid>
                </Border>
                <Border Grid.Column="1" Classes="cyber-border card">
                    <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,*,Auto">
                        <StackPanel Margin="0,0,0,9" Orientation="Horizontal">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,6,0"
                                    VerticalAlignment="Center"
                                    Classes="font-icon small"
                                    Foreground="#E62FF3"
                                    Text="&#xf06d;" />
                                <TextBlock
                                    Margin="0"
                                    VerticalAlignment="Center"
                                    Classes="gray bottom small"
                                    Text="{Binding CurrentDevice.CoolingOutput.Name}" />
                            </StackPanel>
                        </StackPanel>
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Classes="white h3 font-cyber"
                            Text="{Binding CurrentDevice.CoolingOutput.Value}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Classes="small gray2"
                            Text="{Binding CurrentDevice.CoolingOutput.Status}" />
                    </Grid>
                </Border>
                <Border
                    Grid.Row="1"
                    Grid.Column="0"
                    Classes="cyber-border card">
                    <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,*,Auto">
                        <StackPanel Margin="0,0,0,9" Orientation="Horizontal">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,6,0"
                                    VerticalAlignment="Center"
                                    Classes="font-icon small"
                                    Foreground="#5ADCFE"
                                    Text="&#xf043;" />
                                <TextBlock
                                    Margin="0"
                                    VerticalAlignment="Center"
                                    Classes="gray bottom small"
                                    Text="{Binding CurrentDevice.HumidificationOutput.Name}" />
                            </StackPanel>
                        </StackPanel>
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Classes="white h3 font-cyber"
                            Text="{Binding CurrentDevice.HumidificationOutput.Value}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Classes="small gray2"
                            Text="{Binding CurrentDevice.HumidificationOutput.Status}" />
                    </Grid>
                </Border>
                <Border
                    Grid.Row="1"
                    Grid.Column="1"
                    Classes="cyber-border card">
                    <Grid ColumnDefinitions="*,Auto" RowDefinitions="Auto,*,Auto">
                        <StackPanel Margin="0,0,0,9" Orientation="Horizontal">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Margin="0,0,6,0"
                                    VerticalAlignment="Center"
                                    Classes="font-icon small"
                                    Foreground="#5ADCFE"
                                    Text="&#xf043;" />
                                <TextBlock
                                    Margin="0"
                                    VerticalAlignment="Center"
                                    Classes="gray bottom small"
                                    Text="{Binding CurrentDevice.DehumidificationOutput.Name}" />
                            </StackPanel>
                        </StackPanel>
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Classes="white h3 font-cyber"
                            Text="{Binding CurrentDevice.DehumidificationOutput.Value}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Classes="small gray2"
                            Text="{Binding CurrentDevice.DehumidificationOutput.Status}" />
                    </Grid>
                </Border>

            </Grid>
        </Grid>
    </Grid>
</UserControl>