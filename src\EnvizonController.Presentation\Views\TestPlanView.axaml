<UserControl
    x:Class="EnvizonController.Presentation.Views.TestPlanView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AvaloniaLineSeriesChart.Controls;assembly=AvaloniaLineSeriesChart"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:EnvizonController.Presentation.Views"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="768"
    d:DesignWidth="1024"
    x:DataType="vm:TestPlanViewModel"
    Background="#050510"
    mc:Ignorable="d">

    <Grid Margin="15" RowDefinitions="Auto,*">
        <!--  标签导航  -->
        <Border
            Grid.Row="0"
            Height="56"
            Margin="0,0,0,5"
            Classes="cyber-border">
            <Grid Margin="15,0" ColumnDefinitions="Auto,Auto,*">
                <Button
                    Grid.Column="0"
                    Margin="0,0,20,0"
                    Classes="nav-button"
                    Classes.selected="{Binding IsProgramDesignSelected}"
                    Command="{Binding SwitchToProgramDesignCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="0,0,10,0"
                            Classes="font-icon"
                            Text="&#xf085;" />
                        <TextBlock Classes="font-cyber" Text="程式设计" />
                    </StackPanel>
                </Button>

                <Button
                    Grid.Column="1"
                    Classes="nav-button"
                    Classes.selected="{Binding IsLinkSettingsSelected}"
                    Command="{Binding SwitchToLinkSettingsCommand}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="0,0,10,0"
                            Classes="font-icon"
                            Text="&#xf0c1;" />
                        <TextBlock Classes="font-cyber" Text="链接设置" />
                    </StackPanel>
                </Button>

                <Button
                    Grid.Column="2"
                    Margin="0,0,15,0"
                    Padding="12,6"
                    HorizontalAlignment="Right"
                    Background="Transparent"
                    BorderBrush="#0DF0FF"
                    BorderThickness="1"
                    Command="{Binding RefreshCommand}"
                    CornerRadius="4">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="0,0,5,0"
                            Classes="font-icon primary"
                            Text="&#xf021;" />
                        <TextBlock Classes="primary font-cyber" Text="刷新" />
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <views:ProgramView
            Grid.Row="1"
            DataContext="{Binding ProgramViewModel}"
            IsVisible="{Binding $parent[Grid].((vm:TestPlanViewModel)DataContext).IsProgramDesignSelected}" />
        <views:ProgramLinkView
            Grid.Row="1"
            DataContext="{Binding ProgramLinkViewModel}"
            IsVisible="{Binding $parent[Grid].((vm:TestPlanViewModel)DataContext).IsLinkSettingsSelected}" />
    </Grid>
</UserControl> 