using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 报警API服务实现
    /// </summary>
    public class AlarmApiService : IAlarmApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public AlarmApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        public async Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<AlarmDTO>>($"api/alarms", queryParams);
        }
        public async Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsAsync(AlarmQueryParams queryParams)
        {
            var queryDict = new Dictionary<string, string>
            {
                ["page"] = queryParams.Page.ToString(),
                ["pageSize"] = queryParams.PageSize.ToString()
            };


            if (queryParams.Status.HasValue)
                queryDict["status"] = queryParams.Status.Value.ToString();

            if (queryParams.Level.HasValue)
                queryDict["level"] = queryParams.Level.Value.ToString();

            if (queryParams.StartDate.HasValue)
                queryDict["startDate"] = queryParams.StartDate.Value.ToString("o", CultureInfo.InvariantCulture);

            if (queryParams.EndDate.HasValue)
                queryDict["endDate"] = queryParams.EndDate.Value.ToString("o", CultureInfo.InvariantCulture);

            if (queryParams.TestId.HasValue && queryParams.TestId != 0)
                queryDict["testId"] = queryParams.TestId.Value.ToString();

            if (queryParams.DeviceId.HasValue && queryParams.DeviceId != 0)
                queryDict["deviceId"] = queryParams.DeviceId.Value.ToString(); 

            return await _httpClient.GetAsync<PagedResultDto<AlarmDTO>>("api/alarms", queryDict);
        }

        public async Task<Result<PagedResultDto<AlarmDTO>>> GetActiveAlarmsAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString(),
                ["active"] = "true"
            };

            return await _httpClient.GetAsync<PagedResultDto<AlarmDTO>>($"api/alarms/active", queryParams);
        }

        public async Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsByLevelAsync(AlarmSeverity level, int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString(),
                ["level"] = level.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<AlarmDTO>>($"api/alarms/by-level", queryParams);
        }

        public async Task<Result<AlarmDTO>> GetAlarmAsync(string id)
        {
            return await _httpClient.GetAsync<AlarmDTO>($"api/alarms/{id}");
        }

        public async Task<Result<AlarmDTO>> CreateAlarmAsync(AlarmDTO alarm)
        {
            return await _httpClient.PostAsync<AlarmDTO, AlarmDTO>($"api/alarms", alarm);
        }

        public async Task<Result<AlarmDTO>> ProcessAlarmAsync(long id, string processedBy)
        {
            var data = new { ProcessedBy = processedBy };
            return await _httpClient.PutAsync<AlarmDTO, object>($"api/alarms/{id}/process", data);
        }
    }
}