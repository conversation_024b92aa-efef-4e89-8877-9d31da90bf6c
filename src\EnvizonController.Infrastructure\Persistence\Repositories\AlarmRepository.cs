using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Enums;
using EnvizonController.Domain.Repositories;
using EnvizonController.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 报警仓储实现
    /// </summary>
    public class AlarmRepository : Repository<Alarm, long>, IAlarmRepository
    {
        public AlarmRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 获取特定测试运行的所有报警
        /// </summary>
        public async Task<IEnumerable<Alarm>> GetAlarmsByTestRunIdAsync(long testRunId)
        {
            return await _dbSet
                .Where(a => a.TestId == testRunId)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// 获取活动状态的报警
        /// </summary>
        public async Task<IEnumerable<Alarm>> GetActiveAlarmsAsync()
        {
            return await _dbSet
                .Where(a => a.Status == (int)AlarmStatus.Active)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// 获取特定级别的报警
        /// </summary>
        public async Task<IEnumerable<Alarm>> GetAlarmsByLevelAsync(AlarmSeverity level)
        {
            return await _dbSet
                .Where(a => a.Level == (int)level)
                .OrderByDescending(a => a.Timestamp)
                .ToListAsync();
        }
        
        /// <summary>
        /// 获取分页的报警列表（支持多参数查询）
        /// </summary>
        public async Task<(IEnumerable<Alarm> Alarms, int TotalCount)> GetPagedAlarmsAsync(
            int page, 
            int pageSize, 
            AlarmStatus? status = null, 
            AlarmSeverity? level = null, 
            DateTime? startDate = null, 
            DateTime? endDate = null,
            long? testRunId = null,
            long? deviceId = null)
        {
            var query = _dbSet.AsQueryable();

            // 应用筛选条件
            if (status.HasValue)
            {
                query = query.Where(a => a.Status == (int)status.Value);
            }

            if (level.HasValue)
            {
                query = query.Where(a => a.Level == (int)level.Value);
            }

            if (startDate.HasValue)
            {
                query = query.Where(a => a.Timestamp >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(a => a.Timestamp <= endDate.Value);
            }

            if (testRunId.HasValue)
            {
                query = query.Where(a => a.TestId == testRunId.Value);
            }

            if (deviceId.HasValue)
            {
                query = query.Where(a => a.DeviceId == deviceId.Value);
            }

            // 获取总记录数
            var totalCount = await query.CountAsync();

            // 应用分页
            var alarms = await query
                .OrderByDescending(a => a.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (alarms, totalCount);
        }
    }
}
