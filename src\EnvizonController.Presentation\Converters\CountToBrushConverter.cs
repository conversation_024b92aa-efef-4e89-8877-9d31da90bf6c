using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警数量转换为对应的颜色画刷（用于活动报警计数器背景）
    /// </summary>
    public class CountToBrushConverter : IValueConverter
    {
        /// <summary>
        /// 将报警数量转换为对应的颜色画刷
        /// </summary>
        /// <param name="value">报警数量</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>对应的颜色画刷</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not int count) return new SolidColorBrush(Colors.Gray);

            // 根据报警数量设置不同的颜色
            return count switch
            {
                <= 0 => new SolidColorBrush(Colors.Gray),
                <= 3 => new SolidColorBrush(Color.Parse("#4CAF50")),  // 绿色 (少量)
                <= 10 => new SolidColorBrush(Color.Parse("#FFA000")), // 橙色 (中量)
                _ => new SolidColorBrush(Color.Parse("#E53935"))      // 红色 (大量)
            };
        }

        /// <summary>
        /// 将颜色画刷转换回报警数量（不支持）
        /// </summary>
        /// <param name="value">颜色画刷</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 