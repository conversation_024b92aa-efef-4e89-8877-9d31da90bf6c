using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.VisualTree;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.ViewModels;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;

namespace EnvizonController.Presentation.Views
{
    public partial class MainView : UserControl
    {
        public MainView()
        {
            InitializeComponent();
            AttachedToVisualTree += MainView_AttachedToVisualTree;
       
        }

        private void MainView_AttachedToVisualTree(object? sender, Avalonia.VisualTreeAttachmentEventArgs e)
        {
            //if (this.GetVisualRoot() is not Window)
            //    WindowButtonPanel.IsVisible = false;
      
        }

        // 标题栏拖动窗口事件处理
        private void TitleBar_PointerPressed(object sender, PointerPressedEventArgs e)
        {
            var window = this.GetVisualRoot() as Window;
            if (window != null && e.GetCurrentPoint(this).Properties.IsLeftButtonPressed)
            {
                window.BeginMoveDrag(e);
            }
        }
        
        // 窗口控制按钮事件处理方法
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            var window = this.GetVisualRoot() as Window;
            if (window != null)
            {
                window.WindowState = WindowState.Minimized;
            }
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            var window = this.GetVisualRoot() as Window;
            if (window != null)
            {
                window.WindowState = window.WindowState == WindowState.Maximized
                    ? WindowState.Normal
                    : WindowState.Maximized;
                
                // 更新最大化/还原按钮图标
                var maximizeIcon = this.FindControl<TextBlock>("MaximizeIcon");
                if (maximizeIcon != null)
                {
                    maximizeIcon.Text = window.WindowState == WindowState.Maximized 
                        ? "\uE923"  // 还原图标
                        : "\uE922"; // 最大化图标
                }
            }
        
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            var window = this.GetVisualRoot() as Window;
            window?.Close();
        }
    }
}