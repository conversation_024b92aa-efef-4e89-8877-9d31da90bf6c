﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 数据采集状态变更消息
/// </summary>
public class DataCollectionStatusChangedMessage : ValueChangedMessage<DataCollectionStatus>
{
    /// <summary>
    /// 数据采集状态
    /// </summary>
    public DataCollectionStatus Status => Value;

    /// <summary>
    /// 创建数据采集状态变更消息
    /// </summary>
    /// <param name="status">数据采集状态</param>
    public DataCollectionStatusChangedMessage(DataCollectionStatus status) : base(status)
    {
    }
}


/// <summary>
/// 数据采集状态
/// </summary>
public enum DataCollectionStatus
{
    /// <summary>
    /// 已停止
    /// </summary>
    Stopped = 0,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused = 2,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 3
}
