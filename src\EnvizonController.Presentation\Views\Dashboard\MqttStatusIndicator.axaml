<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.MqttStatusIndicator"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:enums="clr-namespace:EnvizonController.Shared.Enums"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="40"
    d:DesignWidth="200"
    x:DataType="vm:MqttConnectionStatusViewModel"
    mc:Ignorable="d">

    <Grid Margin="5">
        <Border
            Padding="5"
            Background="#20000000"
            CornerRadius="5">
            <StackPanel
                VerticalAlignment="Center"
                Orientation="Horizontal"
                Spacing="5">
                <Ellipse
                    Name="StatusIndicator"
                    Width="10"
                    Height="10" />
                <TextBlock VerticalAlignment="Center" Text="{Binding StatusMessage}" />
            </StackPanel>
        </Border>
    </Grid>
</UserControl>