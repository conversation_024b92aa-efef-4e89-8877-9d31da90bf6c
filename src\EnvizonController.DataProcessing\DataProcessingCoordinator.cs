using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.DataPush;
using Microsoft.Extensions.Logging;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 数据处理协调器
    /// </summary>
    public class DataProcessingCoordinator : IDisposable
    {
        private readonly IDataProcessingService _processingService;
        private readonly IDataPushService _pushService;
        private readonly ILogger<DataProcessingCoordinator> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="processingService">数据处理服务</param>
        /// <param name="pushService">数据推送服务</param>
        /// <param name="logger">日志记录器</param>
        public DataProcessingCoordinator(
            IDataProcessingService processingService,
            IDataPushService pushService,
            ILogger<DataProcessingCoordinator> logger)
        {
            _processingService = processingService ?? throw new ArgumentNullException(nameof(processingService));
            _pushService = pushService ?? throw new ArgumentNullException(nameof(pushService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 初始化
        /// </summary>
        public async Task InitializeAsync()
        {
            // 连接推送服务
            await _pushService.ConnectAsync();
            _logger.LogInformation("数据处理协调器已初始化");
        }
        
        /// <summary>
        /// 处理数据并推送
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="rawData">原始数据</param>
        /// <param name="topic">推送主题</param>
        /// <param name="messageType">消息类型</param>
        /// <param name="metadata">元数据</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessAndPushAsync(
            string pipelineName, 
            object rawData, 
            string topic, 
            MessageType messageType, 
            Dictionary<string, object> metadata = null)
        {
            try
            {
                // 处理数据
                var result = await _processingService.ProcessDataAsync(pipelineName, rawData, metadata);
                
                // 检查处理结果
                if (result.Status == ProcessingStatus.Succeeded)
                {
                    // 推送处理后的数据
                    await _pushService.PushAsync(topic, result.ProcessedData, messageType);
                    _logger.LogInformation("已推送处理后的数据到主题 {Topic}", topic);
                }
                else
                {
                    _logger.LogWarning("数据处理失败，未推送数据。错误: {Errors}", string.Join(", ", result.Errors));
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理并推送数据时出错");
                throw;
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public async void Dispose()
        {
            // 断开推送服务
            await _pushService.DisconnectAsync();
            _logger.LogInformation("数据处理协调器已释放资源");
        }
    }
}
