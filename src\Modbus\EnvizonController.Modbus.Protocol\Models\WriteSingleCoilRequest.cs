using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写单个线圈请求
    /// </summary>
    public class WriteSingleCoilRequest : ModbusRequest
    {
        /// <summary>
        /// 线圈地址
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// 线圈值
        /// </summary>
        public bool Value { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="address">线圈地址</param>
        /// <param name="value">线圈值</param>
        public WriteSingleCoilRequest(byte slaveAddress, ushort address, bool value)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.WriteSingleCoil;
            Address = address;
            Value = value;
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            var data = new byte[4];
            data[0] = (byte)(Address >> 8);    // 高字节
            data[1] = (byte)(Address & 0xFF);  // 低字节
            
            // 线圈值：ON=0xFF00，OFF=0x0000
            if (Value)
            {
                data[2] = 0xFF;
                data[3] = 0x00;
            }
            else
            {
                data[2] = 0x00;
                data[3] = 0x00;
            }
            
            return data;
        }
    }
}
