<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Border Padding="20">
            <StackPanel Spacing="10">
                <ProgressBar
                    Width="300"
                    Height="25"
                    Classes="custom-gradient-glow"
                    Maximum="100"
                    Minimum="0"
                    Value="65" />
                <ProgressBar
                    Width="300"
                    Height="25"
                    Classes="custom-gradient-glow"
                    Maximum="100"
                    Minimum="0"
                    Value="30" />
            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <!--  Style for the Custom Gradient Glow ProgressBar  -->
    <Style Selector="ProgressBar.custom-gradient-glow">
        <!--  Define the complex gradient for the indicator part  -->
        <!--  This is set on the Background property which the template will use  -->
        <Setter Property="Background">
            <LinearGradientBrush StartPoint="0%,50%" EndPoint="100%,50%">
                <!--  Adjust colors and offsets to match the image precisely  -->
                <GradientStop Offset="0.0" Color="#FF008B8B" />
                <!--  Dark Teal/Cyan Start  -->
                <GradientStop Offset="0.2" Color="#FF48D1CC" />
                <!--  Medium Turquoise  -->
                <GradientStop Offset="0.4" Color="#FF7FFFD4" />
                <!--  Aquamarine/Bright Cyan  -->
                <GradientStop Offset="0.6" Color="#FFAFEEEE" />
                <!--  Pale Turquoise/Light Blue  -->
                <GradientStop Offset="0.8" Color="#FFB19CD9" />
                <!--  Light Pastel Purple  -->
                <GradientStop Offset="1.0" Color="#FF8A2BE2" />
                <!--  Blue Violet End  -->
            </LinearGradientBrush>
        </Setter>

        <!--  Default visual properties  -->
        <Setter Property="Height" Value="20" />
        <Setter Property="MinHeight" Value="10" />
        <Setter Property="Padding" Value="6" />
        <Setter Property="Margin" Value="-6" />
        <Setter Property="CornerRadius" Value="10" />
        <!--  Default CornerRadius for track/indicator  -->

        <!--  Define the ControlTemplate  -->
        <Setter Property="Template">
            <ControlTemplate>
                <!--  PART_Track: Defines the background/container of the progress bar  -->
                <Panel>
                    <Border
                        Name="PART_Track"
                        Margin="{TemplateBinding Padding}"
                        Background="#2A3441"
                        ClipToBounds="True"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <!--  Important to clip the indicator  -->


                    </Border>
                    <!--  PART_Indicator: Represents the filled portion  -->
                    <Border
                        Name="PART_Indicator"
                        Margin="{TemplateBinding Padding}"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Stretch"
                        Background="{TemplateBinding Background}"
                        Classes="Effect-Blink"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Styles>
                            <Style Selector="Border">
                                <Setter Property="BoxShadow">
                                    <Setter.Value>
                                        0 0 12 0 #90A0E0F0
                                    </Setter.Value>
                                </Setter>
                                <Style.Animations>
                                    <Animation
                                        IterationCount="INFINITE"
                                        PlaybackDirection="Alternate"
                                        Duration="0:0:1">
                                        <KeyFrame Cue="0%">
                                            <Setter Property="BoxShadow">
                                                <Setter.Value>
                                                    0 0 12 0 #00A0E0F0
                                                </Setter.Value>
                                            </Setter>
                                        </KeyFrame>
                                        <KeyFrame Cue="100%">
                                            <Setter Property="BoxShadow">
                                                <Setter.Value>
                                                    0 0 12 0 #FFA0E0F0
                                                </Setter.Value>
                                            </Setter>
                                        </KeyFrame>
                                    </Animation>
                                </Style.Animations>
                            </Style>
                        </Border.Styles>
                        <!--  Apply an Effect to simulate the glow  -->
                        <!--  DropShadowEffect with 0 depth acts like a glow  -->
                        <!--  Adjust Color, BlurRadius, Opacity for desired look  -->

                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

    </Style>
    <!--<Style Selector="Border.Effect-Blink">
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowDirectionEffect
                    BlurRadius="12"
                    Direction="270"
                    Opacity="0.7"
                    ShadowDepth="0"
                    Color="#A0E0F0">
                    <DropShadowDirectionEffect.Transitions>
                        <DoubleTransition
                            Property="Opacity"
                            From="0.5"
                            Duration="0:0:0.2" />
                    </DropShadowDirectionEffect.Transitions>
                </DropShadowDirectionEffect>
            </Setter.Value>
        </Setter>

    </Style>-->
</Styles>