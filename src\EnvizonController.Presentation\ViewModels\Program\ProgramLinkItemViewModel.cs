using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using CommunityToolkit.Mvvm.Input;
using EnvizonController.ApiClient.Results;
using EnvizonController.ApiClient.Services;
using EnvizonController.Shared.DTOs;
using HanumanInstitute.MvvmDialogs;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;

namespace EnvizonController.Presentation.ViewModels.Program;

/// <summary>
///     程式链接项视图模型
/// </summary>
public class ProgramLinkItemViewModel : ObservableObject, IModalDialogViewModel, ICloseable
{
    private int _cycleCount = 1;
    private long _id;
    private string _name = string.Empty;

    /// <summary>
    ///     构造函数
    /// </summary>
    public ProgramLinkItemViewModel()
    {
        // 初始化命令
        SaveCommand = new AsyncRelayCommand(SaveProgram);
        CancelCommand = new RelayCommand(CancelEdit);
    }

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    /// <summary>
    ///     循环次数
    /// </summary>
    public int CycleCount
    {
        get => _cycleCount;
        set => SetProperty(ref _cycleCount, value);
    }

    /// <summary>
    ///     程式链接项ID
    /// </summary>
    public long Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    private bool _isSelected;
    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }
    
    /// <summary>
    ///     程式步骤列表
    /// </summary>
    public List<ProgramLinkStepItemViewModel> Items { get; set; } = new();

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }


    /// <summary>
    ///     是否为编辑模式
    /// </summary>
    public bool IsEditMode => Id > 0;

    /// <summary>
    ///     保存命令
    /// </summary>
    public IRelayCommand SaveCommand { get; }

    /// <summary>
    ///     取消命令
    /// </summary>
    public IRelayCommand CancelCommand { get; }

    public event EventHandler? RequestClose;
    public bool? DialogResult { get; set; }

    /// <summary>
    ///     从DTO创建视图模型
    /// </summary>
    public static ProgramLinkItemViewModel FromDto(ProgramLinkDTO dto)
    {
        var viewModel = new ProgramLinkItemViewModel
        {
            Id = dto.Id,
            CycleCount = dto.CycleCount,
            Name = dto.Name,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt
        };

        foreach (var stepDto in dto.Items) viewModel.Items.Add(ProgramLinkStepItemViewModel.FromDto(stepDto));

        return viewModel;
    }


    /// <summary>
    ///     转换为DTO
    /// </summary>
    public ProgramLinkDTO ToDto()
    {
        return new ProgramLinkDTO
        {
            Id = Id,
            Name = Name,
            CycleCount = CycleCount,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            Items = Items.Select(s => s.ToDto()).ToList()
        };
    }

    /// <summary>
    ///     保存程式
    /// </summary>
    private async Task SaveProgram()
    {
        // 验证输入
        if (string.IsNullOrWhiteSpace(Name))
        {
            var nameBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "程式名称不能为空",
                    ButtonEnum.Ok, Icon.Warning);
            await nameBox.ShowAsync();
            return;
        }

        if (Name.Length < 2 || Name.Length > 50)
        {
            var lengthBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "程式名称长度必须为2-50个字符",
                    ButtonEnum.Ok, Icon.Warning);
            await lengthBox.ShowAsync();
            return;
        }


        if (CycleCount <= 0)
        {
            var countBox = MessageBoxManager
                .GetMessageBoxStandard("验证失败", "循环次数必须大于0",
                    ButtonEnum.Ok, Icon.Warning);
            await countBox.ShowAsync();
            return;
        }

        try
        {
            Result<ProgramLinkDTO> result;
            var programLinkApiService = Ioc.Default.GetRequiredService<IProgramLinkApiService>();

            if (IsEditMode)
                // 更新现有程序
                result = await programLinkApiService.UpdateProgramLinkAsync(ToDto());
            else
                // 创建新程序
                result = await programLinkApiService.CreateProgramLinkAsync(ToDto());

            if (result.IsSuccess)
            {
                // 使用返回的DTO更新当前ViewModel，以获取正确的ID和时间戳
                var updatedDto = result.Data;
                Id = updatedDto.Id;
                CreatedAt = updatedDto.CreatedAt;
                UpdatedAt = updatedDto.UpdatedAt;

                // 更新步骤的ID和关联
                if (updatedDto.Items.Any())
                {
                    Items.Clear();
                    foreach (var stepDto in updatedDto.Items) Items.Add(ProgramLinkStepItemViewModel.FromDto(stepDto));
                }

                DialogResult = true;
                RequestClose?.Invoke(this, EventArgs.Empty);
            }
            else
            {
                var operation = IsEditMode ? "更新" : "创建";
                var errorBox = MessageBoxManager
                    .GetMessageBoxStandard($"{operation}失败", $"{operation}程式时发生错误，请检查输入并重试。错误信息：" + result.ErrorMessage,
                        ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }
        catch (Exception ex)
        {
            var operation = IsEditMode ? "更新" : "创建";
            var exceptionBox = MessageBoxManager
                .GetMessageBoxStandard($"{operation}异常", $"{operation}过程中发生异常：{ex.Message}",
                    ButtonEnum.Ok, Icon.Error);
            await exceptionBox.ShowAsync();
        }
    }

    /// <summary>
    ///     取消编辑
    /// </summary>
    private void CancelEdit()
    {
        // 通知对话框关闭并返回空结果

        DialogResult = false;
        RequestClose?.Invoke(this, EventArgs.Empty);
    }
}