﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Shared
{
    public class ObservableDictionary<TKey, TValue> :
        ObservableCollection<KeyValuePair<TKey, TValue>>
    {
        public void Add(TKey key, TValue value)
        {
            var existingItem = this.FirstOrDefault(kvp =>
                EqualityComparer<TKey>.Default.Equals(kvp.Key, key));

            if (existingItem.Key != null &&
                EqualityComparer<TKey>.Default.Equals(existingItem.Key, key))
            {
                // 更新现有值
                this.Remove(existingItem);
                this.Add(new KeyValuePair<TKey, TValue>(key, value));
            }
            else
            {
                // 添加新项
                this.Add(new KeyValuePair<TKey, TValue>(key, value));
            }
        }

        public TValue this[TKey key]
        {
            get
            {
                var item = this.FirstOrDefault(kvp =>
                    EqualityComparer<TKey>.Default.Equals(kvp.Key, key));
                if (EqualityComparer<TKey>.Default.Equals(item.Key, key))
                    return item.Value;
                throw new KeyNotFoundException($"Key '{key}' not found");
            }
            set
            {
                Add(key, value);
            }
        }

        public bool ContainsKey(TKey key)
        {
            return this.Any(kvp => EqualityComparer<TKey>.Default.Equals(kvp.Key, key));
        }
    }

}
