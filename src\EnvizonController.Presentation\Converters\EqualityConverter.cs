using Avalonia.Data.Converters;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 值相等性检查转换器（用于页码选中状态）
    /// </summary>
    public class EqualityConverter : IValueConverter
    {
        /// <summary>
        /// 检查值是否与参数相等
        /// </summary>
        /// <param name="value">待检查的值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">用于比较的值</param>
        /// <param name="culture">区域信息</param>
        /// <returns>若相等则返回true，否则返回false</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;

            return value.Equals(parameter);
        }

        /// <summary>
        /// 将布尔值转换回原始值（不支持）
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
    public class EqualityMultiConverter : IMultiValueConverter
    {
        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values.Count < 2) return false;
            return Equals(values[0], values[1]);
        }
    }
    public class NotEqualityMultiConverter : IMultiValueConverter
    {
        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values.Count < 2) return false;
            return !Equals(values[0], values[1]);
        }
    }
    public class NotEqualityConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return !object.Equals(value, parameter);
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 