using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Application.Interfaces.Cache
{
    /// <summary>
    /// Modbus设备缓存接口
    /// </summary>
    public interface IModbusDeviceServiceCache
    {
        /// <summary>
        /// 获取或创建设备服务
        /// </summary>
        /// <param name="device">设备</param>
        /// <returns>Modbus设备服务</returns>
        IModbusDeviceService GetOrCreate(Device device);

        /// 移除设备服务
        /// </summary>
        /// <param name="device">设备对象</param>
        void Remove(Device device);
    }
}