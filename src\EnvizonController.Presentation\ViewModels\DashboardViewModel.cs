﻿using System.Collections.ObjectModel;
using System.Diagnostics;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Mqtt.Client.Services;
using EnvizonController.Presentation.Handlers;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.Models;
using EnvizonController.Presentation.Services;
using EnvizonController.Presentation.ViewModels.Dashboard;
using EnvizonController.Shared.DTOs;
using System.Linq;
using HanumanInstitute.MvvmDialogs;
using EnvizonController.Presentation.Extensions;
using HanumanInstitute.MvvmDialogs.FrameworkDialogs;
using EnvizonController.ApiClient.Services;

namespace EnvizonController.Presentation.ViewModels;

/// <summary>
///     仪表盘视图模型
/// </summary>
public partial class DashboardViewModel : ViewModelBase
{
    private readonly IDeviceService _deviceService;
    private readonly INotificationService _notificationService;
    private readonly IDialogService _dialogService;
    private readonly IDeviceTestApiService _deviceTestService;


    #region 属性

    /// <summary>
    ///     控制面板是否展开
    /// </summary>
    [ObservableProperty] private bool _isControlPanelExpanded = true;

    /// <summary>
    ///     设备列表面板是否展开
    /// </summary>
    [ObservableProperty] private bool _isDeviceListPanelExpanded = false;

    /// <summary>
    ///     环境数据 - 温度
    /// </summary>
    [ObservableProperty] private double _temperature = 25.0;

    /// <summary>
    ///     环境数据 - 湿度
    /// </summary>
    [ObservableProperty] private double _humidity = 50.0;

    /// <summary>
    ///     从API获取的设备列表
    /// </summary>
    [ObservableProperty] private ObservableCollection<DeviceListItemViewModel> _apiDevices = new();

    [ObservableProperty] private DeviceListItemViewModel? _currentDeviceDto ;


    /// <summary>
    ///     数据加载中
    /// </summary>
    [ObservableProperty] private bool _isLoading;

    /// <summary>
    ///     上次刷新时间
    /// </summary>
    [ObservableProperty] private DateTime? _lastRefreshTime;

    /// <summary>
    ///     环境数据历史记录
    /// </summary>
    [ObservableProperty] private ObservableCollection<EnvironmentDataPoint> _environmentHistory = new();

    /// <summary>
    ///     测试进度
    /// </summary>
    [ObservableProperty] private double _testProgress;

    /// <summary>
    ///     测试状态
    /// </summary>
    [ObservableProperty] private string _testStatus = "就绪";

    /// <summary>
    ///     测试剩余时间（分钟）
    /// </summary>
    [ObservableProperty] private int _testRemainingMinutes;

    /// <summary>
    ///     当前运行参数
    /// </summary>
    [ObservableProperty] private ObservableCollection<RunningParameter> _runningParameters = new();

    /// <summary>
    ///     设备状态
    /// </summary>
    [ObservableProperty] private ObservableCollection<DeviceStatus> _deviceStatuses = new();

    /// <summary>
    ///     系统参数
    /// </summary>
    [ObservableProperty] private ObservableCollection<SystemParameter> _systemParameters = new();

    [ObservableProperty] private DeviceItemViewModel? _currentDevice = new DeviceItemViewModel();

    #endregion
    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="notificationService">通知服务</param>
    /// <param name="deviceService">设备服务</param>
    /// <param name="dialogService">对话服务</param>
    public DashboardViewModel(INotificationService notificationService, IDeviceService deviceService, IDialogService dialogService,
        IDeviceTestApiService deviceTestService)
    {
        _notificationService = notificationService;
        _deviceService = deviceService;
        _dialogService = dialogService;
        _deviceTestService = deviceTestService;

        // 订阅设备数据更新
        _notificationService.Subscribe<BatchDeviceCollectionDetails>(OnDeviceCollectionReceived);

        // 初始化测试数据
        InitializeTestData();

        // 加载设备列表
        _ = LoadDevicesAsync();
    }

    /// <summary>
    ///     从API加载设备列表
    /// </summary>
    public async Task LoadDevicesAsync()
    {
        try
        {
            IsLoading = true;

            var result = await _deviceService.GetDevicesAsync();

            if (result.IsSuccess && result.Data != null)
            {
                ApiDevices = new ObservableCollection<DeviceListItemViewModel>(result.Data.Items.Select(s=> new DeviceListItemViewModel(s)));
                LastRefreshTime = DateTime.Now;

                // 默认选择之前选中的设备，或者第一个设备
                var deviceToSelect = ApiDevices.FirstOrDefault(s =>
                    CurrentDeviceDto != null && CurrentDeviceDto.Id == s.Id)
                    ?? ApiDevices.FirstOrDefault();

                if (deviceToSelect != null)
                {
                    SelectDevice(deviceToSelect);

                }
            }
            else
            {
                Debug.WriteLine($"加载设备列表失败: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"加载设备列表时发生异常: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    ///     设备数据接收处理
    /// </summary>
    private async void OnDeviceCollectionReceived(BatchDeviceCollectionDetails data)
    {
        try
        {

            var detailsReceivedDTO =
                data.Items.FirstOrDefault(s =>
                    CurrentDevice == null || CurrentDevice.DeviceDTO == null || s.DeviceId == CurrentDevice.DeviceDTO.DeviceId);
           await CurrentDevice.InitAsync(detailsReceivedDTO);

            WeakReferenceMessenger.Default.Send(
                new DeviceItemViewModelChangedMessage(CurrentDevice));
        }
        catch (Exception e)
        {
            Debug.WriteLine(e.Message);
#if DEBUG

            throw;
#endif
        }

        //// 更新环境数据
        //if (data.EnvironmentData != null)
        //{
        //    Temperature = data.EnvironmentData.Temperature;
        //    Humidity = data.EnvironmentData.Humidity;

        //    // 添加到历史记录
        //    EnvironmentHistory.Add(new EnvironmentDataPoint
        //    {
        //        Timestamp = DateTime.Now,
        //        Temperature = Temperature,
        //        Humidity = Humidity
        //    });

        //    // 保持历史记录不超过100条
        //    while (EnvironmentHistory.Count > 100)
        //    {
        //        EnvironmentHistory.RemoveAt(0);
        //    }
        //}

        // 不再需要注释的代码

        //// 更新设备状态
        //if (data.StatusData != null)
        //{
        //    // 这里需要根据实际数据结构进行更新
        //}
    }

    /// <summary>
    ///     初始化测试数据
    /// </summary>
    private void InitializeTestData()
    {
        // 添加一些测试数据
        for (var i = 0; i < 10; i++)
            EnvironmentHistory.Add(new EnvironmentDataPoint
            {
                Timestamp = DateTime.Now.AddMinutes(-10 + i),
                Temperature = 25 + Math.Sin(i * 0.5) * 2,
                Humidity = 50 + Math.Cos(i * 0.5) * 5
            });



        // 添加运行参数
        RunningParameters.Add(new RunningParameter { Name = "温度设定值", Value = "30.0 °C" });
        RunningParameters.Add(new RunningParameter { Name = "湿度设定值", Value = "60.0 %" });
        RunningParameters.Add(new RunningParameter { Name = "运行时间", Value = "02:30:00" });

        // 添加设备状态
        DeviceStatuses.Add(new DeviceStatus { Name = "温控器", Status = "运行中", IsOnline = true });
        DeviceStatuses.Add(new DeviceStatus { Name = "湿度控制器", Status = "运行中", IsOnline = true });
        DeviceStatuses.Add(new DeviceStatus { Name = "压力传感器", Status = "待机", IsOnline = true });

        // 添加系统参数
        SystemParameters.Add(new SystemParameter { Name = "CPU使用率", Value = "32%" });
        SystemParameters.Add(new SystemParameter { Name = "内存使用率", Value = "45%" });
        SystemParameters.Add(new SystemParameter { Name = "存储空间", Value = "256GB/500GB" });
    }

    /// <summary>
    ///     切换控制面板显示状态
    /// </summary>
    [RelayCommand]
    private void ToggleControlPanel()
    {
        IsControlPanelExpanded = !IsControlPanelExpanded;
    }

    /// <summary>
    ///     切换设备列表面板显示状态
    /// </summary>
    [RelayCommand]
    private void ToggleDeviceListPanel()
    {
        IsDeviceListPanelExpanded = !IsDeviceListPanelExpanded;
    }

    /// <summary>
    ///     刷新设备列表命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshDevicesAsync()
    {
        await LoadDevicesAsync();
    }

    /// <summary>
    ///     选择设备命令
    /// </summary>
    [RelayCommand]
    private void SelectDevice(DeviceListItemViewModel device)
    {
        if (device == null) return;

        // 如果点击已选中的设备，则保持选中状态不变
        if (device.IsSelected)
            return;

        // 清除所有设备的选中状态
        foreach (var deviceItem in ApiDevices)
        {
            deviceItem.IsSelected = false;
        }

        // 设置当前设备为选中状态
        device.IsSelected = true;
        CurrentDeviceDto = device;

        // 发送消息通知其他组件
        WeakReferenceMessenger.Default.Send(new CurrentDeviceDtoChangedMessage(CurrentDeviceDto.DeviceDto));
    }

    #region 测试控制命令

    /// <summary>
    /// 启动测试命令
    /// </summary>
    [RelayCommand]
    private async Task StartTestAsync()
    {
        try
        {
            // 显示启动测试对话框
            var testConfig = await _dialogService.ShowTestStartDialogAsync(this);
            if (testConfig == null)
                return; // 用户取消

            // 检查是否选择了设备
            if (CurrentDeviceDto?.DeviceDto?.Id == null)
            {
                await _dialogService.ShowErrorAsync("错误", "请先选择一个设备");
                return;
            }

            TestStatus = "正在启动...";

            // 根据测试模式调用相应的启动方法
            var success = await StartTestWithConfigurationAsync(testConfig);

            if (success)
            {
                TestStatus = "运行中";
                await _dialogService.ShowMessageBoxAsync($"测试\"{testConfig.TestName}\"已成功启动", "启动成功");
            }
            else
            {
                TestStatus = "就绪";
                await _dialogService.ShowErrorAsync("启动失败", "测试启动失败，请检查配置和设备状态");
            }
        }
        catch (Exception ex)
        {
            TestStatus = "就绪";
            await _dialogService.ShowErrorAsync("启动失败", $"启动测试时发生错误：{ex.Message}");
        }
    }

    /// <summary>
    /// 根据测试配置启动测试
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>是否启动成功</returns>
    private async Task<bool> StartTestWithConfigurationAsync(TestConfiguration config)
    {
        try
        {
            // 根据测试模式构建启动请求
            var request = BuildDeviceTestStartRequest(config);

            // 调用设备测试服务启动测试
            var result = await _deviceTestService.StartDeviceTestAsync(request);

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"启动测试配置时发生异常: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 根据测试配置构建设备测试启动请求
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>设备测试启动请求</returns>
    private DeviceTestStartRequest BuildDeviceTestStartRequest(TestConfiguration config)
    {
        var request = new DeviceTestStartRequest
        {
            DeviceId = CurrentDeviceDto!.DeviceDto!.Id,
            TestName = config.TestName,
            Description = config.TestRemark ?? string.Empty
        };

        // 根据测试模式设置执行类型和配置
        switch (config.TestMode)
        {
            case TestMode.FixedValue:
                request.ExecutionType = "manual";
                request.ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = config.FixedValueConfig.StartupMode.ToString(),
                    ["temperature"] = config.FixedValueConfig.Temperature,
                    ["humidity"] = config.FixedValueConfig.Humidity
                };
                break;

            case TestMode.Timer:
                request.ExecutionType = "manual";
                var totalSeconds = config.TimerConfig.Days * 24 * 3600 +
                                 config.TimerConfig.Hours * 3600 +
                                 config.TimerConfig.Minutes * 60;
                request.ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = config.TimerConfig.StartupMode.ToString(),
                    ["temperature"] = config.TimerConfig.Temperature,
                    ["humidity"] = config.TimerConfig.Humidity,
                    ["duration"] = totalSeconds,
                    ["timingRange"] = config.TimerConfig.TimingRange
                };
                break;

            case TestMode.Program:
                request.ExecutionType = "program";
                request.ExecutionId = config.ProgramConfig.SelectedProgramId;
                request.ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = config.ProgramConfig.StartupMode.ToString(),
                    ["timingRange"] = config.ProgramConfig.TimingRange
                };
                break;

            case TestMode.Link:
                request.ExecutionType = "programlink";
                request.ExecutionId = config.LinkConfig.SelectedLinkId;
                request.ExtraConfig = new Dictionary<string, object>
                {
                    ["startupMode"] = config.LinkConfig.StartupMode.ToString(),
                    ["timingRange"] = config.LinkConfig.TimingRange
                };
                break;
        }

        return request;
    }

    /// <summary>
    /// 暂停测试命令
    /// </summary>
    [RelayCommand]
    private async Task PauseTestAsync()
    {
        try
        {
            var result = await _dialogService.ShowMessageBoxAsync("确定要暂停当前测试吗？", "确认暂停",
                MessageBoxButton.YesNo, MessageBoxImage.Information);

            if (result != true)
                return;

            // 调用暂停测试的 API
            if (CurrentDeviceDto?.DeviceDto?.Id == null)
            {
                await _dialogService.ShowErrorAsync("错误", "请先选择一个设备");
                return;
            }

            var apiResult = await _deviceTestService.PauseDeviceTestAsync(CurrentDeviceDto.DeviceDto.Id);

            if (apiResult.IsSuccess)
            {
                TestStatus = "暂停中";
                await _dialogService.ShowMessageBoxAsync("测试已暂停", "暂停成功");
            }
            else
            {
                await _dialogService.ShowErrorAsync("暂停失败", apiResult.ErrorMessage ?? "未知错误");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync("暂停失败", $"暂停测试时发生错误：{ex.Message}");
        }
    }


    /// <summary>
    /// 停止测试命令
    /// </summary>
    [RelayCommand]
    private async Task StopTestAsync()
    {
        try
        {
            var result = await _dialogService.ShowMessageBoxAsync("确定要停止当前测试吗？", "确认停止",
                MessageBoxButton.YesNo, MessageBoxImage.Information);

            if (result != true)
                return;

            // 调用继续测试的 API
            if (CurrentDeviceDto?.DeviceDto?.Id == null)
            {
                await _dialogService.ShowErrorAsync("错误", "请先选择一个设备");
                return;
            }

            var apiResult = await _deviceTestService.StopDeviceTestAsync(CurrentDeviceDto.DeviceDto.Id);

            if (apiResult.IsSuccess)
            {
                TestStatus = "停止中";
                await _dialogService.ShowMessageBoxAsync("测试已停止", "停止成功");
            }
            else
            {
                await _dialogService.ShowErrorAsync("停止失败", apiResult.ErrorMessage ?? "未知错误");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync("停止失败", $"停止测试时发生错误：{ex.Message}");
        }
    }

    /// <summary>
    /// 继续测试命令
    /// </summary>
    [RelayCommand]
    private async Task ResumeTestAsync()
    {
        try
        {
            var result = await _dialogService.ShowMessageBoxAsync("确定要继续当前测试吗？", "确认继续",
                MessageBoxButton.YesNo, MessageBoxImage.Information);

            if (result != true)
                return;

            // 调用继续测试的 API
            if (CurrentDeviceDto?.DeviceDto?.Id == null)
            {
                await _dialogService.ShowErrorAsync("错误", "请先选择一个设备");
                return;
            }

            var apiResult = await _deviceTestService.ResumeDeviceTestAsync(CurrentDeviceDto.DeviceDto.Id);

            if (apiResult.IsSuccess)
            {
                TestStatus = "运行中";
                await _dialogService.ShowMessageBoxAsync("测试已继续", "继续成功");
            }
            else
            {
                await _dialogService.ShowErrorAsync("继续失败", apiResult.ErrorMessage ?? "未知错误");
            }
        }
        catch (Exception ex)
        {
            await _dialogService.ShowErrorAsync("继续失败", $"继续测试时发生错误：{ex.Message}");
        }
    }

    #endregion
}

/// <summary>
///     环境数据点
/// </summary>
public class EnvironmentDataPoint
{
    public DateTime Timestamp { get; set; }
    public double Temperature { get; set; }
    public double Humidity { get; set; }
}

/// <summary>
///     运行参数
/// </summary>
public class RunningParameter
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

/// <summary>
///     设备状态
/// </summary>
public class DeviceStatus
{
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsOnline { get; set; }
}

/// <summary>
///     系统参数
/// </summary>
public class SystemParameter
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}