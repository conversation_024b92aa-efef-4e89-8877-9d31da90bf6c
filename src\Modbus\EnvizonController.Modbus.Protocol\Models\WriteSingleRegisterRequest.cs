using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写单个寄存器请求
    /// </summary>
    public class WriteSingleRegisterRequest : ModbusRequest
    {
        /// <summary>
        /// 寄存器地址
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// 寄存器值
        /// </summary>
        public ushort Value { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="address">寄存器地址</param>
        /// <param name="value">寄存器值</param>
        public WriteSingleRegisterRequest(byte slaveAddress, ushort address, ushort value)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.WriteSingleRegister;
            Address = address;
            Value = value;
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            var data = new byte[4];
            data[0] = (byte)(Address >> 8);    // 地址高字节
            data[1] = (byte)(Address & 0xFF);  // 地址低字节
            data[2] = (byte)(Value >> 8);      // 值高字节
            data[3] = (byte)(Value & 0xFF);    // 值低字节
            return data;
        }
    }
}
