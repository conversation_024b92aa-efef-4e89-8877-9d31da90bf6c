using EnvizonController.ApiClient.Results;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 报警API服务接口
    /// </summary>
    public interface IAlarmApiService : IApiService
    {
        /// <summary>
        /// 获取报警列表
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsAsync(int page = 1, int pageSize = 20);
        /// <summary>
        /// 使用查询参数获取报警列表
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsAsync(AlarmQueryParams queryParams);
      
        /// <summary>
        /// 获取活动报警
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetActiveAlarmsAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取指定级别的报警
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsByLevelAsync(AlarmSeverity level, int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取报警
        /// </summary>
        Task<Result<AlarmDTO>> GetAlarmAsync(string id);

        /// <summary>
        /// 创建报警
        /// </summary>
        Task<Result<AlarmDTO>> CreateAlarmAsync(AlarmDTO alarm);

        /// <summary>
        /// 处理报警
        /// </summary>
        Task<Result<AlarmDTO>> ProcessAlarmAsync(long id, string processedBy);
    }
}