<UserControl
    x:Class="EnvizonController.Modbus.SerialTester.Views.SerialConfigView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:EnvizonController.Modbus.SerialTester.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Modbus.SerialTester.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:SerialConfigViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="300"
    DataContext="{Binding SerialConfigViewModel}"
    mc:Ignorable="d">
    <StackPanel Margin="10">
        <GroupBox Margin="0,5" Header="串口设置">
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <Label
                    Grid.Row="0"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="端口:" />
                <ComboBox
                    Grid.Row="0"
                    Grid.Column="1"
                    Margin="5"
                    ItemsSource="{Binding AvailablePorts}"
                    SelectedItem="{Binding SelectedPort}" />

                <Label
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="波特率:" />
                <ComboBox
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="5"
                    ItemsSource="{Binding BaudRates}"
                    SelectedItem="{Binding SelectedBaudRate}" />

                <Label
                    Grid.Row="2"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="数据位:" />
                <ComboBox
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="5"
                    ItemsSource="{Binding DataBits}"
                    SelectedItem="{Binding SelectedDataBits}" />

                <Label
                    Grid.Row="3"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="校验位:" />
                <ComboBox
                    Grid.Row="3"
                    Grid.Column="1"
                    Margin="5"
                    ItemsSource="{Binding Parities}"
                    SelectedItem="{Binding SelectedParity}" />

                <Label
                    Grid.Row="4"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="停止位:" />
                <ComboBox
                    Grid.Row="4"
                    Grid.Column="1"
                    Margin="5"
                    ItemsSource="{Binding StopBits}"
                    SelectedItem="{Binding SelectedStopBits}" />

                <Button
                    Grid.Row="5"
                    Grid.Column="1"
                    Margin="5,10"
                    Click="RefreshPorts_Click"
                    Content="刷新端口列表" />
            </Grid>
        </GroupBox>
    </StackPanel>
</UserControl>
