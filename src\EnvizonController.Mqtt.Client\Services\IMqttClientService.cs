using MQTTnet.Protocol;
using System;
using EnvizonController.Mqtt.Client.Events;

namespace EnvizonController.Mqtt.Client.Services
{
    public interface IMqttClientService
    {
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<MqttConnectionStatusEventArgs> ConnectionStatusChanged;
        
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        bool IsConnected { get; }
        
        Task ConnectAsync();
        Task DisconnectAsync();
        Task PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce);
        Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce);
        Task UnsubscribeAsync(string topic);
    }
} 