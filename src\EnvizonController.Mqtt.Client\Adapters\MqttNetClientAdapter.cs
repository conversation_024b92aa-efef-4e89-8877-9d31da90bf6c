using EnvizonController.Mqtt.Client.Factories;
using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;

namespace EnvizonController.Mqtt.Client.Adapters
{
    public class MqttNetClientAdapter : IMqttClientAdapter, IDisposable
    {
        private readonly IMqttClient _mqttClient;
        private readonly MqttClientOptions _options;
        private readonly ILogger<MqttNetClientAdapter> _logger;
        private bool _isConnected;
        private readonly List<string> _subscribedTopics = new();

        public event Func<string, string, Task> MessageReceived;

        public MqttNetClientAdapter(
            IMqttClientFactory mqttClientFactory,
            MqttClientOptions options,
            ILogger<MqttNetClientAdapter> logger)
        {
            _mqttClient = mqttClientFactory.CreateClient();
            _options = options;
            _logger = logger;

            _mqttClient.ApplicationMessageReceivedAsync += HandleApplicationMessageReceivedAsync;
            _mqttClient.DisconnectedAsync += HandleDisconnectedAsync;
        }

        public async Task ConnectAsync()
        {
            try
            {
                var result = await _mqttClient.ConnectAsync(_options);
                _isConnected = result.ResultCode == MqttClientConnectResultCode.Success;

                if (_isConnected)
                {
                    _logger.LogInformation("已连接到MQTT服务器");
                    // 重新订阅之前的主题
                    foreach (var topic in _subscribedTopics)
                    {
                        await SubscribeAsync(topic);
                    }
                }
                else
                {
                    _logger.LogWarning($"连接MQTT服务器失败: {result.ResultCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接MQTT服务器时发生错误");
                throw;
            }
        }

        public async Task DisconnectAsync()
        {
            if (_mqttClient.IsConnected)
            {
                await _mqttClient.DisconnectAsync();
                _isConnected = false;
                _logger.LogInformation("已断开与MQTT服务器的连接");
            }
        }

        public async Task PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试发布消息但未连接到MQTT服务器");
                await ConnectAsync();
            }

            var applicationMessage = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(message)
                .WithQualityOfServiceLevel(qos)
                .WithRetainFlag(false)
                .Build();

            await _mqttClient.PublishAsync(applicationMessage);
            _logger.LogDebug($"已发布消息: Topic={topic}, Message={message}");
        }

        public async Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试订阅主题但未连接到MQTT服务器");
                await ConnectAsync();
            }

            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter(f => f.WithTopic(topic).WithQualityOfServiceLevel(qos))
                .Build();

            await _mqttClient.SubscribeAsync(subscribeOptions);
            
            if (!_subscribedTopics.Contains(topic))
            {
                _subscribedTopics.Add(topic);
            }
            
            _logger.LogInformation($"已订阅主题: {topic}");
        }

        public async Task UnsubscribeAsync(string topic)
        {
            if (!_isConnected)
            {
                _logger.LogWarning("尝试取消订阅主题但未连接到MQTT服务器");
                return;
            }

            var unsubscribeOptions = new MqttClientUnsubscribeOptionsBuilder()
                .WithTopicFilter(topic)
                .Build();

            await _mqttClient.UnsubscribeAsync(unsubscribeOptions);
            _subscribedTopics.Remove(topic);
            _logger.LogInformation($"已取消订阅主题: {topic}");
        }

        private async Task HandleApplicationMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs args)
        {
            try
            {
                var topic = args.ApplicationMessage.Topic;
                var payload = args.ApplicationMessage.PayloadSegment;
                var message = System.Text.Encoding.UTF8.GetString(payload.Array ?? Array.Empty<byte>(), payload.Offset, payload.Count);

                _logger.LogDebug($"收到消息: Topic={topic}, Message={message}");

                if (MessageReceived != null)
                {
                    await MessageReceived.Invoke(topic, message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理MQTT消息时发生错误");
            }
        }

        private async Task HandleDisconnectedAsync(MqttClientDisconnectedEventArgs args)
        {
            _logger.LogWarning($"与MQTT服务器断开连接: {args.Reason}");
            _isConnected = false;

            // 实现重连逻辑
            await Task.Delay(TimeSpan.FromSeconds(5));
            await ConnectAsync();
        }

        public void Dispose()
        {
            _mqttClient?.Dispose();
        }
    }
} 