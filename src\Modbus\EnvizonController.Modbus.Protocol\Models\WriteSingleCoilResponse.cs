using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写单个线圈响应
    /// </summary>
    public class WriteSingleCoilResponse : ModbusResponse
    {
        /// <summary>
        /// 线圈地址
        /// </summary>
        public ushort Address { get; private set; }

        /// <summary>
        /// 线圈值
        /// </summary>
        public bool Value { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public WriteSingleCoilResponse()
        {
            FunctionCode = ModbusFunction.WriteSingleCoil;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 6)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            Address = (ushort)((frame[2] << 8) | frame[3]);
            
            // 解析线圈值：0xFF00=ON，0x0000=OFF
            ushort valueWord = (ushort)((frame[4] << 8) | frame[5]);
            Value = valueWord == 0xFF00;
        }
    }
}
