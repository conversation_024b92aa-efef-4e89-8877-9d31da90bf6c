using EnvizonController.Modbus.Protocol.Models;

namespace EnvizonController.Modbus.Abstractions.Interfaces
{
    /// <summary>
    /// Modbus客户端接口
    /// </summary>
    public interface IModbusClient : IDisposable
    {
        /// <summary>
        /// 获取客户端是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接到Modbus设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task ConnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 断开与Modbus设备的连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送Modbus请求并接收响应
        /// </summary>
        /// <param name="request">Modbus请求</param>
        /// <param name="response">Modbus响应</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功接收响应</returns>
        Task<bool> SendRequestAsync(ModbusRequest request, ModbusResponse response, CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取保持寄存器
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="registerCount">寄存器数量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>寄存器值</returns>
        Task<ushort[]> ReadHoldingRegistersAsync(byte slaveAddress, ushort startAddress, ushort registerCount, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写入多个寄存器
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">寄存器值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        Task<bool> WriteMultipleRegistersAsync(byte slaveAddress, ushort startAddress, ushort[] values, CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取输入寄存器
        /// </summary>
        Task<ushort[]> ReadInputRegistersAsync(byte slaveAddress, ushort startAddress, ushort registerCount, CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取线圈状态
        /// </summary>
        Task<bool[]> ReadCoilsAsync(byte slaveAddress, ushort startAddress, ushort coilCount, CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取离散输入状态
        /// </summary>
        Task<bool[]> ReadDiscreteInputsAsync(byte slaveAddress, ushort startAddress, ushort inputCount, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写单个线圈
        /// </summary>
        Task<bool> WriteSingleCoilAsync(byte slaveAddress, ushort address, bool value, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写单个寄存器
        /// </summary>
        Task<bool> WriteSingleRegisterAsync(byte slaveAddress, ushort address, ushort value, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写多个线圈
        /// </summary>
        Task<bool> WriteMultipleCoilsAsync(byte slaveAddress, ushort startAddress, bool[] values, CancellationToken cancellationToken = default);
    }
}
