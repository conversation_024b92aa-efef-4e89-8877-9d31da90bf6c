using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Abstractions.Enums;

namespace EnvizonController.Infrastructure.Services.Devices
{
    /// <summary>
    /// Modbus通道工厂接口
    /// </summary>
    public interface IModbusChannelFactory
    {
        /// <summary>
        /// 创建串口通道
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBits">数据位</param>
        /// <param name="parity">校验位</param>
        /// <param name="stopBits">停止位</param>
        /// <param name="readTimeout">读取超时</param>
        /// <param name="writeTimeout">写入超时</param>
        /// <returns>Modbus通道</returns>
        IModbusChannel CreateSerialChannel(
            string portName, 
            int baudRate, 
            int dataBits, 
            Parity parity, 
            StopBits stopBits, 
            int readTimeout, 
            int writeTimeout);

        /// <summary>
        /// 创建网络通道
        /// </summary>
        /// <param name="hostAddress">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="connectionTimeout">连接超时</param>
        /// <returns>Modbus通道</returns>
        IModbusChannel CreateNetworkChannel(
            string hostAddress, 
            int port, 
            int connectionTimeout);
    }
} 