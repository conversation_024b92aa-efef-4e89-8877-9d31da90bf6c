{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "DataReceiver": {"DefaultServiceType": "MVVM", "ConnectionSettings": {"MqttServer": "localhost", "MqttPort": 1883, "MqttClientId": "EnvizonController", "MqttTopic": "envizon/#"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=envizon.db"}, "Kestrel": {"EndpointDefaults": {"Protocols": "Http1AndHttp2"}}}