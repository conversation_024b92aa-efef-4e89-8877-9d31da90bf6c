using Microsoft.Extensions.DependencyInjection;
using System.Windows;
using EnvizonController.Modbus.SerialTester.ViewModels;
using EnvizonController.Modbus.SerialTester.Views;

namespace EnvizonController.Modbus.SerialTester
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;

        public App()
        {
            ServiceCollection services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
        }

        private void ConfigureServices(ServiceCollection services)
        {
            // 注册视图模型
            services.AddSingleton<MainViewModel>();
            services.AddTransient<SerialConfigViewModel>();
            services.AddTransient<ModbusTestViewModel>();
            services.AddTransient<LogViewModel>();

            // 注册视图
            services.AddTransient<SerialConfigView>();
            services.AddTransient<ModbusTestView>();
            services.AddTransient<LogView>();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            var mainWindow = new MainWindow
            {
                DataContext = _serviceProvider.GetRequiredService<MainViewModel>()
            };

            // 设置视图
            var mainViewModel = _serviceProvider.GetRequiredService<MainViewModel>();
            var serialConfigView = _serviceProvider.GetRequiredService<SerialConfigView>();
            var modbusTestView = _serviceProvider.GetRequiredService<ModbusTestView>();
            var logView = _serviceProvider.GetRequiredService<LogView>();

            serialConfigView.DataContext = mainViewModel.SerialConfigViewModel;
            modbusTestView.DataContext = mainViewModel.ModbusTestViewModel;
            logView.DataContext = mainViewModel.LogViewModel;

            mainWindow.Show();
        }
    }
}
