﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 报警触发消息
/// </summary>
public class AlarmRaisedMessage : ValueChangedMessage<AlarmDTO>
{
    /// <summary>
    /// 报警项
    /// </summary>
    public AlarmDTO Alarm => Value;

    /// <summary>
    /// 创建报警触发消息
    /// </summary>
    /// <param name="alarm">报警项</param>
    public AlarmRaisedMessage(AlarmDTO alarm) : base(alarm)
    {
    }
}
