# EnvizonController Modbus 集成

本文档描述了 EnvizonController 项目中 Modbus 通信框架的集成方式。

## 架构概述

Modbus 通信框架采用分层设计，与 EnvizonController 项目的整体架构保持一致：

1. **领域层 (Domain)**：定义设备状态和连接状态的枚举。

2. **应用层 (Application)**：定义 `IModbusDeviceService` 接口和相关的数据模型。

3. **基础设施层 (Infrastructure)**：实现 `ModbusDeviceService` 和 `ModbusDeviceServiceFactory`，负责与底层 Modbus 框架交互。

4. **API 层 (API.Http)**：提供 `ModbusDeviceController`，用于通过 HTTP API 访问 Modbus 设备。

## 配置

Modbus 设备的配置存储在 `appsettings.Modbus.json` 文件中，包括以下内容：

- 设备基本信息（ID、名称）
- 通信参数（从站地址、传输类型、连接类型）
- 串口设置（端口名称、波特率、数据位、校验位、停止位）
- 网络设置（主机地址、端口、连接超时）
- 重试策略（重试次数、重试延迟、响应超时）
- 自动重连策略（是否启用、重连延迟、最大尝试次数）
- 参数映射（名称、地址、类型、缩放因子、偏移量、单位、描述、是否可写、最小值、最大值）

## 使用方式

### 依赖注入

Modbus 设备服务已通过依赖注入注册到服务容器中，可以在需要的地方注入 `IModbusDeviceService` 接口：

```csharp
public class MyService
{
    private readonly IModbusDeviceService _deviceService;

    public MyService(IModbusDeviceService deviceService)
    {
        _deviceService = deviceService;
    }

    public async Task DoSomethingAsync()
    {
        // 连接到设备
        await _deviceService.ConnectAsync();

        // 读取参数
        ushort temperature = await _deviceService.ReadParameterAsync(0);

        // 写入参数
        await _deviceService.WriteParameterAsync(100, 250);

        // 断开连接
        await _deviceService.DisconnectAsync();
    }
}
```

### HTTP API

Modbus 设备服务也可以通过 HTTP API 访问：

- `GET /api/ModbusDevice/status`：获取设备状态
- `POST /api/ModbusDevice/connect`：连接设备
- `POST /api/ModbusDevice/disconnect`：断开设备连接
- `POST /api/ModbusDevice/refresh`：刷新设备状态
- `GET /api/ModbusDevice/parameters/{address}`：读取设备参数
- `PUT /api/ModbusDevice/parameters/{address}`：写入设备参数
- `GET /api/ModbusDevice/parameters?startAddress={startAddress}&count={count}`：读取多个设备参数
- `PUT /api/ModbusDevice/parameters?startAddress={startAddress}`：写入多个设备参数

### 设备状态流

`IModbusDeviceService` 提供了一个 `DeviceStateStream` 属性，可以订阅设备状态的变化：

```csharp
// 订阅设备状态变化
_deviceService.DeviceStateStream
    .Subscribe(deviceState =>
    {
        Console.WriteLine($"设备状态: {deviceState.ConnectionStatus} - {deviceState.StatusMessage}");
    });
```

## 扩展性

### 添加新的设备类型

如果需要添加新的设备类型，可以通过以下步骤实现：

1. 在 `ModbusDeviceSettings` 中添加新的参数映射。
2. 在 `ModbusDeviceService` 中实现对应的读写逻辑。
3. 如果需要，可以创建新的控制器或服务来提供特定于设备的功能。

### 添加新的通信通道

如果需要添加新的通信通道，可以通过以下步骤实现：

1. 创建新的通道实现类，继承自 `ModbusChannelBase` 或实现 `IModbusChannel` 接口。
2. 在 `ModbusDeviceService` 的 `CreateChannel` 方法中添加对新通道的支持。
3. 在 `ModbusDeviceSettings` 中添加对应的配置项。

## 故障排除

### 连接问题

- 检查设备是否已连接并通电。
- 检查通信参数（端口名称、波特率、数据位、校验位、停止位）是否正确。
- 检查从站地址是否正确。
- 检查网络连接是否正常（如果使用 Modbus TCP）。

### 读写问题

- 检查参数地址是否正确。
- 检查设备是否支持对应的功能码。
- 检查参数是否可写（如果尝试写入）。
- 检查写入的值是否在允许的范围内。

### 性能问题

- 减少不必要的读写操作。
- 使用批量读写操作代替单个读写操作。
- 调整超时和重试参数。
- 考虑使用缓存机制。
