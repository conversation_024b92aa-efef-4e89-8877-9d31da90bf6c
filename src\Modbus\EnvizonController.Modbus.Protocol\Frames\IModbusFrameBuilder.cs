using EnvizonController.Modbus.Protocol.Models;

namespace EnvizonController.Modbus.Protocol.Frames
{
    /// <summary>
    /// Modbus帧构建器接口
    /// </summary>
    public interface IModbusFrameBuilder
    {
        /// <summary>
        /// 构建请求帧
        /// </summary>
        /// <param name="request">Modbus请求</param>
        /// <returns>完整的请求帧字节数组</returns>
        byte[] BuildRequestFrame(ModbusRequest request);

        /// <summary>
        /// 解析响应帧
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <param name="response">Modbus响应对象</param>
        /// <returns>是否成功解析</returns>
        bool ParseResponseFrame(byte[] responseFrame, ModbusResponse response);

        /// <summary>
        /// 验证响应帧的有效性
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <returns>响应帧是否有效</returns>
        bool ValidateResponseFrame(byte[] responseFrame);
    }
}
