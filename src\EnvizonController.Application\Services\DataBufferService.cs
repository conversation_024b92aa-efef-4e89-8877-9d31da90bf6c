using System.Collections.Concurrent;
using System.Text.Json;
using EnvizonController.Application.DataCollection.Protocol;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Mqtt.Server.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MQTTnet.Protocol;
using Serilog;
using System.Collections.Generic;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;
using System.Linq;

namespace EnvizonController.Application.Services;

/// <summary>
///     数据缓冲服务，用于缓存数据并定时批量处理（推送和保存）
/// </summary>
public class DataBufferService : IDisposable
{
    // 设备ID -> 测试数据点列表（为每个设备存储多个数据点）
    private readonly ConcurrentDictionary<long, List<TestDataPoint>> _testDataPointCache = new();
    // 设备ID -> 采集数据详情
    private readonly ConcurrentDictionary<long, DeviceCollectionDetailsDTO> _collectionDetailsCache = new();
    // 设备ID -> 报警数据列表 (用于缓存待处理的报警)
    private readonly ConcurrentDictionary<long, List<AlarmDTO>> _alarmCache = new();
    // 设备ID -> 当前活动的报警标识符集合 (用于过滤重复报警)
    private readonly ConcurrentDictionary<long, HashSet<string>> _activeDeviceAlarms = new();
    private readonly bool _enableBatchProcessing;
    private readonly ILogger _logger;
    private readonly IMqttServerService _mqttServerService;

    // 配置参数
    private readonly int _processingIntervalSeconds;
    private readonly int _batchSize; // 添加批处理大小参数
    private readonly int _alarmProcessingIntervalSeconds; // 报警处理间隔

    // 批量处理的计时器
    private readonly Timer _processingTimer;
    // 报警处理的计时器
    private readonly Timer _alarmProcessingTimer;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public DataBufferService(
        IServiceScopeFactory serviceScopeFactory,
        IMqttServerService mqttServerService,
        ILogger logger,
        IConfiguration configuration)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _mqttServerService = mqttServerService;
        _logger = logger;

        // 从配置中加载参数
        var bufferSettings = configuration.GetSection("DataCollection:Buffer").Get<DataBufferSettings>()
                             ?? new DataBufferSettings();

        _processingIntervalSeconds = bufferSettings.ProcessingIntervalSeconds;
        _enableBatchProcessing = bufferSettings.EnableBatchProcessing;
        _batchSize = bufferSettings.BatchSize;
        _alarmProcessingIntervalSeconds = bufferSettings.AlarmProcessingIntervalSeconds;

        // 创建定时器，定期处理缓存中的数据
        _processingTimer = new Timer(
            ProcessBufferedData,
            null,
            TimeSpan.FromSeconds(_processingIntervalSeconds),
            TimeSpan.FromSeconds(_processingIntervalSeconds));
            
        // 创建报警处理定时器
        _alarmProcessingTimer = new Timer(
            ProcessBufferedAlarms,
            null,
            TimeSpan.FromSeconds(_alarmProcessingIntervalSeconds),
            TimeSpan.FromSeconds(_alarmProcessingIntervalSeconds));

        _logger.Information(
            "数据缓冲服务已初始化. 批处理: {EnableBatch}, 数据处理间隔: {Interval}秒, 报警处理间隔: {AlarmInterval}秒",
            _enableBatchProcessing,
            _processingIntervalSeconds,
            _alarmProcessingIntervalSeconds);
    }

    public void Dispose()
    {
        _processingTimer?.Dispose();
        _alarmProcessingTimer?.Dispose();
    }

    // 使用对象锁防止同时处理同一设备的数据
    private readonly object _processLock = new();

    /// <summary>
    ///     将测试数据点添加到缓存
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="testDataPoint">测试数据点</param>
    public void BufferTestDataPoint(long deviceId, TestDataPoint testDataPoint)
    {
        // 添加到缓存中设备的测试数据点列表（不替换旧的）
        _testDataPointCache.AddOrUpdate(
            deviceId,
            // 如果设备ID不存在，创建新列表并添加数据点
            _ => new List<TestDataPoint> { testDataPoint },
            // 如果设备ID已存在，将数据点添加到现有列表
            (_, existingList) =>
            {
                lock (existingList)
                {
                    existingList.Add(testDataPoint);
                    return existingList;
                }
            });

        // 如果未启用批处理，则立即处理
        if (!_enableBatchProcessing)
        {
            ProcessImmediatelyIfNeeded(deviceId);
        }
    }

    /// <summary>
    ///     将采集数据详情添加到缓存
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="collectionDetails">采集数据详情</param>
    public void BufferCollectionDetails(long deviceId, DeviceCollectionDetailsDTO collectionDetails)
    {
        // 更新缓存中设备的最新采集数据详情（替换旧的）
        _collectionDetailsCache[deviceId] = collectionDetails;

        // 如果未启用批处理，则立即处理
        if (!_enableBatchProcessing)
        {
            ProcessImmediatelyIfNeeded(deviceId);
        }
    }
    
    /// <summary>
    ///     将报警数据添加到缓存
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="alarms">报警数据列表</param>
    public void BufferAlarms(long deviceId, List<AlarmDTO> alarms)
    {
        if (alarms == null)
        {
            alarms = new List<AlarmDTO>(); // 处理null情况，视为空列表
        }

        var activeAlarmsForDevice = _activeDeviceAlarms.GetOrAdd(deviceId, _ => new HashSet<string>());
        var currentAlarmKeysFromDevice = new HashSet<string>(alarms.Select(GenerateAlarmKey));
        
        var newAlarmsToProcess = new List<AlarmDTO>();
        var alarmsThatHaveCleared = new List<string>();

        lock (activeAlarmsForDevice) // 同步对特定设备活动报警集合的访问
        {
            // 识别已清除的报警
            foreach (var activeKey in activeAlarmsForDevice.ToList()) // ToList() 允许在迭代时修改集合
            {
                if (!currentAlarmKeysFromDevice.Contains(activeKey))
                {
                    alarmsThatHaveCleared.Add(activeKey);
                }
            }

            // 从活动报警集合中移除已清除的报警
            foreach (var clearedKey in alarmsThatHaveCleared)
            {
                activeAlarmsForDevice.Remove(clearedKey);
                _logger.Information("设备 {DeviceId} 的报警 {AlarmKey} 已清除", deviceId, clearedKey);
            }

            // 识别新的或重新激活的报警
            foreach (var alarm in alarms)
            {
                var alarmKey = GenerateAlarmKey(alarm);
                if (activeAlarmsForDevice.Add(alarmKey)) // Add返回true表示报警是新的
                {
                    newAlarmsToProcess.Add(alarm);
                    _logger.Information("设备 {DeviceId} 检测到新的或重新激活的报警: {AlarmKey} (原始信息: {AlarmName})", deviceId, alarmKey, alarm.Name);
                }
            }
        }

        if (!newAlarmsToProcess.Any())
        {
            return; // 没有新的报警需要处理
        }
        
        // 根据报警优先级决定处理方式 (使用 newAlarmsToProcess 替换 alarms)
        var highPriorityAlarms = newAlarmsToProcess.Where(a => a.Level == AlarmSeverity.High).ToList();
        var otherAlarms = newAlarmsToProcess.Where(a => a.Level != AlarmSeverity.High).ToList();
        
        // 高优先级报警立即处理
        if (highPriorityAlarms.Any())
        {
            _ = ProcessHighPriorityAlarmsAsync(deviceId, highPriorityAlarms);
        }
        
        // 中低优先级报警放入缓存
        if (otherAlarms.Any())
        {
            _alarmCache.AddOrUpdate(
                deviceId,
                otherAlarms,
                (_, existingAlarms) =>
                {
                    existingAlarms.AddRange(otherAlarms);
                    return existingAlarms;
                });
                
            // 如果未启用批处理，则立即处理中低优先级报警
            if (!_enableBatchProcessing)
            {
                ProcessAlarmImmediately(deviceId);
            }
        }
    }
    /// <summary>
    ///     清理指定设备的活动报警缓存
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    public void ClearDeviceAlarms(long deviceId)
    {
        if (_activeDeviceAlarms.TryRemove(deviceId, out var activeAlarms))
        {
            _logger.Information("已清理设备 {DeviceId} 的活动报警缓存", deviceId);
        }
        else
        {
            _logger.Debug("未找到设备 {DeviceId} 的活动报警缓存，无需清理", deviceId);
        }
    }

    /// <summary>
    ///     立即处理高优先级报警
    /// </summary>
    private async Task ProcessHighPriorityAlarmsAsync(long deviceId, List<AlarmDTO> highPriorityAlarms)
    {
        try
        {
            _logger.Information("立即处理设备 {DeviceId} 的 {Count} 个高优先级报警", deviceId, highPriorityAlarms.Count);
            
            // 批量发布高优先级报警
            await PublishAlarmsAsync(highPriorityAlarms);
            
            // 保存高优先级报警到数据库
            await SaveAlarmsAsync(deviceId, highPriorityAlarms);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理设备 {DeviceId} 的高优先级报警时发生错误", deviceId);
        }
    }
    
    /// <summary>
    ///     立即处理单个设备的报警数据
    /// </summary>
    private void ProcessAlarmImmediately(long deviceId)
    {
        // 使用锁确保一次只处理一个设备的报警数据
        lock (_processLock)
        {
            if (_alarmCache.TryRemove(deviceId, out var alarms) && alarms.Any())
            {
                _ = ProcessDeviceAlarmsAsync(deviceId, alarms);
            }
        }
    }
    
    /// <summary>
    ///     异步处理设备报警数据
    /// </summary>
    private async Task ProcessDeviceAlarmsAsync(long deviceId, List<AlarmDTO> alarms)
    {
        if (!alarms.Any()) return;
        
        try
        {
            _logger.Debug("处理设备 {DeviceId} 的 {Count} 个报警数据", deviceId, alarms.Count);
            
            // 发布报警数据
            await PublishAlarmsAsync(alarms);
            
            // 保存报警数据到数据库
            await SaveAlarmsAsync(deviceId, alarms);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理设备 {DeviceId} 的报警数据时发生错误", deviceId);
        }
    }

    /// <summary>
    ///     根据缓存中的可用数据处理设备数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private void ProcessImmediatelyIfNeeded(long deviceId)
    {
        // 使用锁确保一次只处理一个设备的数据
        lock (_processLock)
        {
            bool hasTestData = _testDataPointCache.TryGetValue(deviceId, out var testDataPoints) && testDataPoints.Any();
            bool hasCollectionDetails = _collectionDetailsCache.TryGetValue(deviceId, out var collectionDetails);

            if (hasTestData && hasCollectionDetails)
            {
                // 同时有两种数据，一起处理
                _ = ProcessDataImmediatelyAsync(deviceId, testDataPoints!, collectionDetails!);
            }
            else if (hasTestData)
            {
                // 只有测试数据
                _ = SaveDataPointsAsync(deviceId, testDataPoints!);
            }
            else if (hasCollectionDetails)
            {
                // 只有采集详情
                // 单条处理时仍使用单条发送方法
                _ = PublishDataAsync(collectionDetails!);
            }
        }
    }

    /// <summary>
    ///     立即处理单个设备数据（当禁用批处理时）
    /// </summary>
    private async Task ProcessDataImmediatelyAsync(long deviceId, List<TestDataPoint> testDataPoints,
        DeviceCollectionDetailsDTO collectionDetails)
    {
        try
        {
            // 保存数据点到数据库
            await SaveDataPointsAsync(deviceId, testDataPoints);

            // 推送数据到MQTT
            await PublishDataAsync(collectionDetails);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "立即处理设备 {DeviceId} 的数据时发生错误", deviceId);
        }
    }

    /// <summary>
    ///     定时批量处理缓存中的数据
    /// </summary>
    private async void ProcessBufferedData(object? state)
    {
        // 检查是否有数据需要处理
        if (!_testDataPointCache.Any() && !_collectionDetailsCache.Any()) return;

        int deviceCount = Math.Max(_testDataPointCache.Count, _collectionDetailsCache.Count);
        _logger.Debug("开始批量处理缓存数据, 共 {Count} 个设备", deviceCount);

        try
        {
            // 使用锁确保批处理过程中不会有即时处理的干扰
            lock (_processLock)
            {
                // 创建数据快照，以避免处理过程中的并发修改
                var testDataPointSnapshot = new Dictionary<long, List<TestDataPoint>>();
                foreach (var kvp in _testDataPointCache)
                {
                    testDataPointSnapshot[kvp.Key] = new List<TestDataPoint>(kvp.Value);
                }
                var collectionDetailsSnapshot = new Dictionary<long, DeviceCollectionDetailsDTO>(_collectionDetailsCache);

                // 提前清空缓存，防止处理过程中出现异常导致部分数据未被处理
                foreach (var deviceId in testDataPointSnapshot.Keys)
                {
                    _testDataPointCache.TryRemove(deviceId, out _);
                }

                foreach (var deviceId in collectionDetailsSnapshot.Keys)
                {
                    _collectionDetailsCache.TryRemove(deviceId, out _);
                }

                // 异步处理所有设备数据
                _ = ProcessAllDevicesDataAsync(testDataPointSnapshot, collectionDetailsSnapshot);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "批量处理缓存数据时发生错误");
        }
    }

    /// <summary>
    ///     异步处理所有设备的数据
    /// </summary>
    private async Task ProcessAllDevicesDataAsync(
        Dictionary<long, List<TestDataPoint>> testDataPoints,
        Dictionary<long, DeviceCollectionDetailsDTO> collectionDetails)
    {
        try
        {
            // 获取所有需要处理的设备ID
            var allDeviceIds = testDataPoints.Keys.Union(collectionDetails.Keys).Distinct().ToList();
            
            // 批量处理测试数据点
            var dataPointTasks = new List<Task>();
            foreach (var deviceId in allDeviceIds)
            {
                // 保存测试数据点到数据库
                if (testDataPoints.TryGetValue(deviceId, out var testDataPointList) && testDataPointList.Any())
                {
                    dataPointTasks.Add(SaveDataPointsAsync(deviceId, testDataPointList));
                }
            }
            
            // 等待所有测试数据点保存完成
            if (dataPointTasks.Any())
            {
                await Task.WhenAll(dataPointTasks);
            }

            // 批量发布采集详情到MQTT
            if (collectionDetails.Any())
            {
                await BatchPublishDataAsync(collectionDetails.Values.ToList());
            }

            _logger.Debug("批量处理完成, 已处理 {Count} 个设备的数据", allDeviceIds.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "异步处理设备数据时发生错误: {Message}", ex.Message);
            // 考虑在严重错误时通知监控系统
        }
    }

    /// <summary>
    ///     保存数据点到数据库
    /// </summary>
    /// <summary>
    ///     保存多个数据点到数据库
    /// </summary>
    private async Task<List<TestDataPoint>> SaveDataPointsAsync(long deviceId, List<TestDataPoint> testDataPoints)
    {
        if (testDataPoints == null || !testDataPoints.Any())
        {
            return new List<TestDataPoint>();
        }

        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dataCollectionAppService = scope.ServiceProvider.GetRequiredService<IDataCollectionAppService>();
            
            var results = new List<TestDataPoint>();
            // 逐个处理每个数据点，确保全部数据都被处理
            foreach (var dataPoint in testDataPoints)
            {
                var result = await dataCollectionAppService.SaveDataPointByDeviceIdAsync(deviceId, dataPoint);
                if (result != null)
                {
                    results.Add(result);
                }
                else
                {
                    _logger.Warning("无法保存数据点: 设备 {DeviceId} 没有关联的运行中测试项", deviceId);
                }
            }

            _logger.Debug("设备 {DeviceId} 的 {Count}/{Total} 个数据点已成功保存",
                deviceId, results.Count, testDataPoints.Count);
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "保存设备 {DeviceId} 的 {Count} 个数据点时发生错误",
                deviceId, testDataPoints.Count);
            return new List<TestDataPoint>();
        }
    }

    /// <summary>
    ///     保存单个数据点到数据库（兼容性方法）
    /// </summary>
    private async Task<TestDataPoint?> SaveDataPointAsync(long deviceId, TestDataPoint testDataPoint)
    {
        if (testDataPoint == null)
        {
            return null;
        }

        var results = await SaveDataPointsAsync(deviceId, new List<TestDataPoint> { testDataPoint });
        return results.FirstOrDefault();
    }

    /// <summary>
    ///     批量推送数据到MQTT
    /// </summary>
    /// <param name="collectionDetailsList">需要推送的设备采集数据列表</param>
    private async Task BatchPublishDataAsync(List<DeviceCollectionDetailsDTO> collectionDetailsList)
    {
        if (collectionDetailsList == null || !collectionDetailsList.Any())
        {
            return;
        }

        try
        {
            // 分批处理，避免消息过大
            int effectiveBatchSize = _batchSize > 0 ? _batchSize : 100; // 使用配置的批处理大小，默认100
            
            for (int i = 0; i < collectionDetailsList.Count; i += effectiveBatchSize)
            {
                var batch = collectionDetailsList.Skip(i).Take(effectiveBatchSize).ToList();
                
                // 创建批量数据包
                var batchData = new
                {
                    BatchType = "DeviceCollectionDetails",
                    BatchSize = batch.Count,
                    Timestamp = DateTime.UtcNow,
                    Items = batch
                };
                
                var dataJson = JsonSerializer.Serialize(batchData);
                var topic = "device/data/batch";

                await _mqttServerService.PublishAsync(
                    topic,
                    dataJson,
                    MqttQualityOfServiceLevel.AtLeastOnce);
                
                _logger.Debug("批量推送了 {Count} 个设备的采集数据", batch.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "批量推送MQTT数据失败: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     推送单个设备数据到MQTT
    /// </summary>
    private async Task PublishDataAsync(DeviceCollectionDetailsDTO collectionDetails)
    {
        try
        {
            var dataJson = JsonSerializer.Serialize(collectionDetails);
            var topic = "device/data";

            await _mqttServerService.PublishAsync(
                topic,
                dataJson,
                MqttQualityOfServiceLevel.AtLeastOnce);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "通过MQTT发送数据失败: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     强制立即处理所有缓存的数据
    /// </summary>
    public async Task ForceProcessAllAsync()
    {
        // 使用Task.Run确保不阻塞调用线程
        await Task.Run(() =>
        {
            ProcessBufferedData(null);
            ProcessBufferedAlarms(null);
        });
    }
    
    /// <summary>
    ///     定时批量处理缓存中的报警数据
    /// </summary>
    private async void ProcessBufferedAlarms(object? state)
    {
        // 检查是否有报警数据需要处理
        if (!_alarmCache.Any()) return;
        
        int deviceCount = _alarmCache.Count;
        _logger.Debug("开始批量处理缓存报警数据, 共 {Count} 个设备", deviceCount);
        
        try
        {
            // 使用锁确保批处理过程中不会有即时处理的干扰
            lock (_processLock)
            {
                // 创建数据快照，以避免处理过程中的并发修改
                var alarmSnapshot = new Dictionary<long, List<AlarmDTO>>();
                foreach (var kvp in _alarmCache)
                {
                    alarmSnapshot[kvp.Key] = new List<AlarmDTO>(kvp.Value);
                }
                
                // 提前清空缓存
                _alarmCache.Clear();
                
                // 异步处理所有设备的报警数据
                _ = ProcessAllDeviceAlarmsAsync(alarmSnapshot);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "批量处理缓存报警数据时发生错误");
        }
    }
    
    /// <summary>
    ///     异步处理所有设备的报警数据
    /// </summary>
    private async Task ProcessAllDeviceAlarmsAsync(Dictionary<long, List<AlarmDTO>> alarmsDict)
    {
        if (!alarmsDict.Any()) return;
        
        try
        {
            // 合并所有报警到一个列表
            var allAlarms = new List<AlarmDTO>();
            foreach (var deviceAlarms in alarmsDict.Values)
            {
                allAlarms.AddRange(deviceAlarms);
            }
            
            if (!allAlarms.Any()) return;
            
            // 批量发布报警数据
            await PublishAlarmsAsync(allAlarms);
            
            // 分别保存每个设备的报警数据
            var saveTasks = new List<Task>();
            foreach (var kvp in alarmsDict)
            {
                saveTasks.Add(SaveAlarmsAsync(kvp.Key, kvp.Value));
            }
            
            // 等待所有保存任务完成
            if (saveTasks.Any())
            {
                await Task.WhenAll(saveTasks);
            }
            
            _logger.Debug("批量处理完成, 已处理 {Count} 个设备的 {AlarmCount} 个报警数据", 
                alarmsDict.Count, allAlarms.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "批量处理报警数据时发生错误: {Message}", ex.Message);
        }
    }
    
    /// <summary>
    ///     发布报警数据到MQTT
    /// </summary>
    private async Task PublishAlarmsAsync(List<AlarmDTO> alarms)
    {
        if (!alarms.Any()) return;
        
        try
        {
            // 创建报警数据包
            var alarmsData = new
            {
                BatchType = "Alarms",
                BatchSize = alarms.Count,
                Timestamp = DateTime.UtcNow,
                Items = alarms
            };
            
            var dataJson = JsonSerializer.Serialize(alarmsData);
            var topic = "device/alarms";
            
            await _mqttServerService.PublishAsync(
                topic,
                dataJson,
                MqttQualityOfServiceLevel.AtLeastOnce);
                
            _logger.Debug("已发布 {Count} 个报警数据", alarms.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "发布报警数据失败: {Message}", ex.Message);
        }
    }
    
    /// <summary>
    ///     保存报警数据到数据库
    /// </summary>
    private async Task SaveAlarmsAsync(long deviceId, List<AlarmDTO> alarms)
    {
        if (!alarms.Any()) return;
        
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dataCollectionService = scope.ServiceProvider.GetRequiredService<IDataCollectionAppService>();
            await dataCollectionService.SaveAlarmsAsync(deviceId, alarms);
            
            _logger.Debug("已保存设备 {DeviceId} 的 {Count} 个报警数据", deviceId, alarms.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "保存设备 {DeviceId} 的报警数据时发生错误", deviceId);
        }
    }
    /// <summary>
    ///     根据报警对象生成唯一标识
    /// </summary>
    /// <param name="alarm">报警对象</param>
    /// <returns>报警唯一标识</returns>
    private string GenerateAlarmKey(AlarmDTO alarm)
    {
        // 组合设备ID、协议ID和报警代码等生成唯一标识
        // 根据实际的AlarmDTO结构进行调整
        return $"{alarm.TestId}_{alarm.DeviceId}_{alarm.ProtocolId}_{alarm.ProtocolItemIndex}_{alarm.Name}";
    }
   
}

/// <summary>
///     数据缓冲配置项
/// </summary>
public class DataBufferSettings
{
    /// <summary>
    ///     是否启用批处理
    /// </summary>
    public bool EnableBatchProcessing { get; set; } = true;

    /// <summary>
    ///     处理间隔（秒）
    /// </summary>
    public int ProcessingIntervalSeconds { get; set; } = 30;
    
    /// <summary>
    ///     批处理大小 - 每批最多处理的设备数
    /// </summary>
    public int BatchSize { get; set; } = 100;
    
    /// <summary>
    ///     报警处理间隔（秒）
    /// </summary>
    public int AlarmProcessingIntervalSeconds { get; set; } = 15;
}