using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using AvaloniaLineSeriesChart.Data;
using AvaloniaLineSeriesChart.Data.Enums;
using EnvizonController.Presentation.ViewModels; // 假设 ProgramViewModel 在这里
using Avalonia.Threading;
using System.Collections.Generic;
using System.Linq;
using EnvizonController.Presentation.ViewModels.Program;

namespace EnvizonController.Presentation.Views
{
    public partial class ProgramCurveView : UserControl
    {
        public ProgramCurveView()
        {
            InitializeComponent();
            InitializeChart();
            // 订阅 ViewModel 的 PropertyChanged 事件，以便在 SelectedProgram 更改时更新图表
            DataContextChanged += OnDataContextChanged;
        }

        private void OnDataContextChanged(object sender, EventArgs e)
        {
            if (DataContext is ProgramViewModel vm)
            {
                vm.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(ProgramViewModel.SelectedProgram))
                    {
                        UpdateChart();
                    }
                };
                // 初始加载
                UpdateChart();
            }
        }

        private void InitializeChart()
        {
            var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
            if (lineChart == null) return;

            lineChart.SetTheme(new ChartTheme
            {
                BackgroundColor = Colors.Transparent,
                TextColor = Colors.White,
                AxisColor = Color.FromRgb(0x66, 0x66, 0x66),
                GridLineColor = Color.FromRgb(0x44, 0x44, 0x44),
                DefaultSeriesColors = new Color[]
                {
                    Color.FromRgb(0xe6, 0x2f, 0xf3), // 默认颜色1
                    Color.FromRgb(0x0d, 0xf0, 0xff), // 默认颜色2
                    Colors.LawnGreen,
                    Colors.Gold,
                    Colors.DeepPink
                },
                TooltipBackgroundColor = Color.FromRgb(50, 50, 50)
            });
            lineChart.SetXAxisType(XAxisType.DateTime); // X轴代表累计时间
            lineChart.SetXTickCount(5);
            lineChart.SetXAxisLabelFormatter(s =>
            {
                // 格式化X轴标签为时间格式，适用于长时间运行
                return DateTime.Now.AddSeconds(Convert.ToDouble(s)).ToString("ddd HH:mm:ss");

            });
        }


        private void UpdateChart()
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                // 获取图表控件
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                // 清除旧的曲线数据
                lineChart.ClearSeries();

                // 检查数据上下文和选中的程序是否有效
                if (DataContext is not ProgramViewModel vm || vm.SelectedProgram == null)
                {
                    lineChart.Refresh(); // 即使没有数据，也刷新图表以清空内容
                    return;
                }
                var program = vm.SelectedProgram;

                // 创建新的温度数据系列
                var tempSeries = new LineSeriesData
                {
                    Name = $"{program.Name ?? "Program"} - 温度", // 更新名称
                    YAxisPosition = YAxisPosition.Left
                };
                lineChart.AddSeries(tempSeries);
                tempSeries.SetXDataType<double>(); // 设置X轴为累计时间类型

                // 创建新的湿度数据系列
                var humiditySeries = new LineSeriesData
                {
                    Name = $"{program.Name ?? "Program"} - 湿度", // 更新名称
                    YAxisPosition = YAxisPosition.Right // 分配给右Y轴
                };
                lineChart.AddSeries(humiditySeries);
                humiditySeries.SetXDataType<double>(); // 设置X轴为累计时间类型

                // 构建有效的步骤列表，考虑循环配置
                List<ProgramStepItemViewModel> effectiveSteps = new List<ProgramStepItemViewModel>();
                if (program.Steps != null && program.Steps.Any())
                {
                    var originalSteps = program.Steps.ToList(); // 确保步骤为列表以便索引访问
                                                                // 验证循环参数，防止越界访问
                    bool hasValidCycleConfig = program.CycleCount > 0 &&
                                               program.CycleEnd > 0 &&
                                               program.CycleStart > 0 &&
                                               program.CycleStart <= program.CycleEnd &&
                                               program.CycleStart <= originalSteps.Count && // CycleStart 是基于1的索引
                                               program.CycleEnd <= originalSteps.Count;   // CycleEnd 是基于1的索引

                    for (int i = 0; i < originalSteps.Count; i++)
                    {
                        effectiveSteps.Add(originalSteps[i]);
                        // 如果当前步骤是循环块的结束，并且循环配置有效
                        if (hasValidCycleConfig && (i + 1) == program.CycleEnd)
                        {
                            for (int c = 0; c < program.CycleCount; c++) // 循环次数为额外重复的次数
                            {
                                for (int j = program.CycleStart - 1; j < program.CycleEnd; j++) // 将1基索引转换为0基索引
                                {
                                    effectiveSteps.Add(originalSteps[j]);
                                }
                            }
                        }
                    }
                }

                // 如果处理后的步骤列表为空，添加默认点以避免图表为空
                if (!effectiveSteps.Any())
                {
                    tempSeries.Points.Add(new Point(0, 0)); // 为温度添加默认点
                    humiditySeries.Points.Add(new Point(0, 0)); // 为湿度添加默认点
                    lineChart.Refresh();
                    return;
                }

                double currentTime = 0; // 当前时间
                double tempYLevel = effectiveSteps.First().Temperature; // 初始化温度Y轴值
                double humidityYLevel = effectiveSteps.First().Humidity; // 初始化湿度Y轴值

                // 添加初始点 (T=0, Y=初始值)
                tempSeries.Points.Add(new Point(currentTime, tempYLevel));
                humiditySeries.Points.Add(new Point(currentTime, humidityYLevel));

                // 遍历处理后的步骤列表
                foreach (var step in effectiveSteps)
                {
                    if (step.IsLinear)
                    {
                        // 线性变化
                        currentTime += step.Duration; // 更新时间
                        tempYLevel = step.Temperature; // 更新温度Y轴值
                        humidityYLevel = step.Humidity; // 更新湿度Y轴值
                        tempSeries.Points.Add(new Point(currentTime, tempYLevel));
                        humiditySeries.Points.Add(new Point(currentTime, humidityYLevel));
                    }
                    else
                    {
                        // 非线性变化：瞬时跳变后保持
                        if (tempYLevel != step.Temperature)
                        {
                            // 添加温度跳变前的点
                            tempSeries.Points.Add(new Point(currentTime, tempYLevel));
                            // 更新温度Y轴值为跳变后的目标值
                            tempYLevel = step.Temperature;
                            // 添加温度跳变点
                            tempSeries.Points.Add(new Point(currentTime, tempYLevel));
                        }

                        if (humidityYLevel != step.Humidity)
                        {
                            // 添加湿度跳变前的点
                            humiditySeries.Points.Add(new Point(currentTime, humidityYLevel));
                            // 更新湿度Y轴值为跳变后的目标值
                            humidityYLevel = step.Humidity;
                            // 添加湿度跳变点
                            humiditySeries.Points.Add(new Point(currentTime, humidityYLevel));
                        }
                        
                        // 持续保持当前值
                        currentTime += step.Duration;
                        tempSeries.Points.Add(new Point(currentTime, tempYLevel));
                        humiditySeries.Points.Add(new Point(currentTime, humidityYLevel));
                    }
                }

                // 如果没有添加任何点（极少发生），确保至少有一个默认点
                if (!tempSeries.Points.Any())
                {
                    tempSeries.Points.Add(new Point(0, 0));
                }
                if (!humiditySeries.Points.Any())
                {
                    humiditySeries.Points.Add(new Point(0, 0));
                }

                // 刷新图表以显示更新后的数据
                lineChart.Refresh();
            });
        }
    }
} 