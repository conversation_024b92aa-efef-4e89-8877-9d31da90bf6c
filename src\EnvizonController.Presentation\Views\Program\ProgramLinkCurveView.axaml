<UserControl
    x:Class="EnvizonController.Presentation.Views.Program.ProgramLinkCurveView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AvaloniaLineSeriesChart.Controls;assembly=AvaloniaLineSeriesChart"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:program="clr-namespace:EnvizonController.Presentation.Views.Program"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="600"
    x:DataType="vm:ProgramLinkViewModel"
    mc:Ignorable="d">

    <Grid Margin="15" RowDefinitions="Auto,*,Auto">
        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,15"
            Classes="h3 glow-primary font-cyber"
            Text="程式链接曲线视图" />

        <Border
            Grid.Row="1"
            Background="#0F0E1D"
            Classes="cyber-noGlow-border">
            <Grid>
                <!--  图表区域  -->
                <controls:LineChartControl Name="LineChart" VerticalAlignment="Stretch" />
            </Grid>
        </Border>

        <!--  图例区域  -->
        <Border
            Grid.Row="2"
            Margin="0,10,0,0"
            Padding="10"
            Background="#0F0E1D"
            Classes="cyber-noGlow-border">
            <ItemsControl Name="LegendItems">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="program:LegendItem">
                        <StackPanel Margin="5" Orientation="Horizontal">
                            <Rectangle
                                Width="15"
                                Height="15"
                                Margin="0,0,5,0"
                                Fill="{Binding Color}" />
                            <TextBlock VerticalAlignment="Center" Text="{Binding Name}" />
                        </StackPanel>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Border>
    </Grid>
</UserControl>