using System;
using System.Collections.Generic;

namespace EnvizonController.DataProcessing.Processors
{
    /// <summary>
    /// 数据聚合器接口
    /// </summary>
    public interface IDataAggregator : IDataProcessor
    {
        /// <summary>
        /// 聚合函数
        /// </summary>
        Func<IEnumerable<object>, object> AggregateFunction { get; }
        
        /// <summary>
        /// 获取聚合窗口大小
        /// </summary>
        int WindowSize { get; }
        
        /// <summary>
        /// 获取或设置聚合缓冲区
        /// </summary>
        Queue<object> Buffer { get; }
        
        /// <summary>
        /// 添加数据到缓冲区
        /// </summary>
        /// <param name="data">数据</param>
        void AddToBuffer(object data);
        
        /// <summary>
        /// 清空缓冲区
        /// </summary>
        void ClearBuffer();
    }
}
