using System.Collections.ObjectModel;
using System.Diagnostics;
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Mqtt.Client.Services;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.Mqtt;
using EnvizonController.Presentation.Views;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;

namespace EnvizonController.Presentation.ViewModels;

public partial class MainViewModel : ObservableRecipient, IRecipient<NavigateMessage>,
    IRecipient<MqttConnectionStatusChangedMessage>
{
    private readonly ILogger<MainViewModel> _logger;
    private readonly Dictionary<string, Func<ObservableObject>> _viewModelFactories;
    [ObservableProperty] private ConnectionStatus _connectionStatus;
    [ObservableProperty] private object _content = null!;
    [ObservableProperty] private DeviceDto? _device;
    [ObservableProperty] private ObservableCollection<NavigationItemViewModel> _navigationItems;
    [ObservableProperty] private NavigationItemViewModel? _selectedNavigationItem;

    public MainViewModel(INotificationService notificationService, IMqttConnectionManager mqttConnectionManager,
        IServiceProvider serviceProvider,ILogger<MainViewModel> logger)
    {
        _logger = logger;
        _navigationItems = new ObservableCollection<NavigationItemViewModel>(GetInitialNavigationItems());
        // Initialize ViewModel factories
        _viewModelFactories = new Dictionary<string, Func<ObservableObject>>
        {
            { NavigationKeys.Dashboard, () => serviceProvider.GetRequiredService<DashboardViewModel>() },
            { NavigationKeys.TestPlan, () => serviceProvider.GetRequiredService<TestPlanViewModel>() },
            { NavigationKeys.AlarmRecord, () => serviceProvider.GetRequiredService<AlarmViewModel>() },
            { NavigationKeys.HistoricalData, () => serviceProvider.GetRequiredService<DataQueryViewModel>() },
            
            // { NavigationKeys.AlarmRecord, () => _serviceProvider.GetRequiredService<AlarmRecordViewModel>() },
            // { NavigationKeys.HistoricalData, () => _serviceProvider.GetRequiredService<HistoricalDataViewModel>() },
            // { NavigationKeys.SystemSettings, () => _serviceProvider.GetRequiredService<SystemSettingsViewModel>() }
        };

        WeakReferenceMessenger.Default.Register<NavigateMessage>(this);
        WeakReferenceMessenger.Default.Register<MqttConnectionStatusChangedMessage>(this);
        
        RequestInitialNavigation();

        notificationService.Subscribe<DeviceCollectionDetailsDTO>(s => { });

        if (AppConfigurationProvider.ConfigurationAsyncLoaded.MqttSettings.AutoConnectOnStartup)
            _ = mqttConnectionManager.ConnectAsync();

    }

    public void Receive(MqttConnectionStatusChangedMessage message)
    {
        ConnectionStatus = message.NewStatus;
    }

    public void Receive(NavigateMessage message)
    {
        var key = message.NavigationKey;
        var targetItem = NavigationItems.FirstOrDefault(item => item.TargetViewKey == key);
        if (targetItem != null)
        {
            if (SelectedNavigationItem != null) SelectedNavigationItem.IsSelected = false;
            targetItem.IsSelected = true;
            SelectedNavigationItem = targetItem;
        }

        if (_viewModelFactories.TryGetValue(key, out var viewModelFactory))
        {
            Content = viewModelFactory.Invoke();
        }
        else
        {
            // 对于未实现的导航键，显示功能开发中提示界面
            _logger.LogInformation($"导航到未实现的功能: {key}，显示开发中提示界面");

            string featureName = "功能开发中";
            string estimatedDate = "未定";

            if (targetItem != null)
            {
                featureName = !string.IsNullOrEmpty(targetItem.FeatureName) ? targetItem.FeatureName : featureName;
                estimatedDate = !string.IsNullOrEmpty(targetItem.EstimatedDate) ? targetItem.EstimatedDate : estimatedDate;
            }
            
            Content = new FeatureUnderDevelopmentViewModel(
                featureName, 
                "此功能正在开发中，敬请期待", 
                estimatedDate);
        }
    }

    private void RequestInitialNavigation()
    {
        var initialNavItem = NavigationItems.FirstOrDefault(ni => ni.TargetViewKey == NavigationKeys.Dashboard);
        if (initialNavItem != null)
            TryNavigateTo(initialNavItem);
        else
            Debug.WriteLine("Warning: Dashboard navigation item not found for initial navigation.");
    }

    private IEnumerable<NavigationItemViewModel> GetInitialNavigationItems()
    {
        return new List<NavigationItemViewModel>
        {
            new("仪表盘", NavigationKeys.Dashboard, "仪表盘"), // 仪表盘通常是已实现的，所以日期留空
            new("测试方案", NavigationKeys.TestPlan, "测试方案", "2023年第四季度"),
            new("报警记录", NavigationKeys.AlarmRecord, "报警记录", "2024年第一季度"),
            new("历史数据", NavigationKeys.HistoricalData, "历史数据", "2024年第一季度"),
            new("系统设置", NavigationKeys.SystemSettings, "系统设置", "2023年第四季度")
        };
    }

    [RelayCommand]
    private void TryNavigateTo(NavigationItemViewModel navigationItemViewModel)
    {
        WeakReferenceMessenger.Default.Send<NavigateMessage>(
            new NavigateMessage(navigationItemViewModel.TargetViewKey)
        );
    }
}