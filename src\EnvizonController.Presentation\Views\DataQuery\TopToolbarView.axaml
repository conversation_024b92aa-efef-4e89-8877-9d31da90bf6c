<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQuery.TopToolbarView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="100"
    d:DesignWidth="800"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">
    
    <Border
        Margin="0"
        Padding="15,10"
        Background="#0B0A1A"
        BorderBrush="#224F5F"
        BorderThickness="0,0,0,1">

        <Grid RowDefinitions="Auto,Auto,Auto">
            <!--  当前选中测试项提示  -->
            <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto">
                <TextBlock
                    Grid.Column="0"
                    Margin="0,0,0,10"
                    Classes="glow-primary"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Text="{Binding SelectedTestItemDisplay}" />
            </Grid>

            <!--  报警搜索和筛选区  -->
            <Grid Grid.Row="1" ColumnDefinitions="*,Auto,Auto">
                <!--  搜索框  -->
                <TextBox
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    Text="{Binding TestDataSearchText}"
                    Watermark="搜索报警名称或消息内容..." />
                <Button
                    Grid.Column="1"
                    Margin="5,0"
                    VerticalAlignment="Stretch"
                    Classes="glow2"
                    Command="{Binding ApplyTestDataFiltersCommand}"
                    DockPanel.Dock="Right"
                    ToolTip.Tip="精确搜索">
                    <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                        <TextBlock
                            Width="16"
                            Height="16"
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            Text="&#xf002;" />
                        <TextBlock
                            Margin="3,0,0,0"
                            VerticalAlignment="Center"
                            Text="搜索" />
                    </StackPanel>
                </Button>
                <!--  高级报警筛选按钮  -->
                <Button
                    Grid.Column="2"
                    Classes="glow2"
                    Command="{Binding ToggleTestDataFilterPanelCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Width="16"
                                Height="16"
                                Margin="0,0,5,0"
                                Classes="font-icon"
                                Foreground="{Binding $parent[Button].Foreground}"
                                Text="&#xf0b0;" />
                            <TextBlock Text="高级筛选" />
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
            
            <!--  高级筛选面板  -->
            <Border
                Grid.Row="2"
                Margin="0,10,0,0"
                Padding="15"
                Classes="cyber-noGlow-border"
                IsVisible="{Binding IsTestDataFilterPanelOpen}">
                <Grid ColumnDefinitions="*,*,Auto" RowDefinitions="Auto,Auto">
                    <!--  时间范围选择  -->
                    <StackPanel
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="0,0,10,0">
                        <TextBlock
                            Margin="0,0,0,5"
                            Foreground="White"
                            Text="开始时间" />
                        <DatePicker SelectedDate="{Binding StartDate}" />
                    </StackPanel>

                    <StackPanel
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="0,0,10,0">
                        <TextBlock
                            Margin="0,0,0,5"
                            Foreground="White"
                            Text="结束时间" />
                        <DatePicker SelectedDate="{Binding EndDate}" />
                    </StackPanel>

                    <!--  数据状态筛选  -->
                    <StackPanel
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,10,10,0">
                        <TextBlock
                            Margin="0,0,0,5"
                            Foreground="White"
                            Text="数据状态" />
                        <ComboBox
                            HorizontalAlignment="Stretch"
                            ItemsSource="{Binding StatusOptions}"
                            SelectedItem="{Binding SelectedStatus}" />
                    </StackPanel>

                    <!--  应用筛选按钮  -->
                    <Button
                        Grid.Row="1"
                        Grid.Column="2"
                        VerticalAlignment="Bottom"
                        Classes="glow2"
                        Command="{Binding ApplyTestDataFiltersCommand}">
                        <TextBlock Text="应用筛选" />
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl> 