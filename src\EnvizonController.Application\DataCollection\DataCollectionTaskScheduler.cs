using EnvizonController.Application.Devices;
using EnvizonController.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Application.DataCollection;

/// <summary>
///     数据采集任务调度器实现
/// </summary>
public class DataCollectionTaskScheduler : IDataCollectionTaskScheduler
{
    private readonly IConfiguration _configuration;
    private readonly IDataCollectionCoordinator _coordinator;
    private readonly ILogger<DataCollectionTaskScheduler> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _schedulerTask;

    public DataCollectionTaskScheduler(
        IDataCollectionCoordinator coordinator,
        IConfiguration configuration,
        ILogger<DataCollectionTaskScheduler> logger,
        IServiceScopeFactory serviceScopeFactory)
    {
        _coordinator = coordinator;
        _configuration = configuration;
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;

        // 从配置中加载采集间隔
        var settings = _configuration.GetSection("DataCollection").Get<DataCollectionSettings>();
        CollectionIntervalMs = settings?.CollectionIntervalMs ?? 1000; // 默认1秒
    }

    /// <summary>
    ///     采集间隔（毫秒）
    /// </summary>
    public int CollectionIntervalMs { get; private set; }

    /// <summary>
    ///     调度器是否正在运行
    /// </summary>
    public bool IsRunning => _schedulerTask != null && !_schedulerTask.IsCompleted;

    /// <summary>
    ///     启动调度器
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (IsRunning)
        {
            _logger.LogWarning("数据采集调度器已在运行中");
            return Task.CompletedTask;
        }

        _logger.LogInformation("启动数据采集调度器，采集间隔：{IntervalMs}毫秒", CollectionIntervalMs);

        _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _schedulerTask = Task.Run(() => RunSchedulerAsync(_cancellationTokenSource.Token),
            _cancellationTokenSource.Token);

        return Task.CompletedTask;
    }

    /// <summary>
    ///     停止调度器
    /// </summary>
    public async Task StopAsync()
    {
        if (!IsRunning)
        {
            _logger.LogWarning("数据采集调度器未运行");
            return;
        }

        _logger.LogInformation("停止数据采集调度器");

        try
        {
            // 取消调度器任务
            _cancellationTokenSource?.Cancel();

            // 等待任务完成
            if (_schedulerTask != null) await _schedulerTask;
        }
        catch (OperationCanceledException)
        {
            // 预期的异常，忽略
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止数据采集调度器时出错：{Message}", ex.Message);
        }
        finally
        {
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _schedulerTask = null;
        }
    }

    /// <summary>
    ///     设置采集间隔
    /// </summary>
    public void SetCollectionInterval(int intervalMs)
    {
        if (intervalMs < 100) throw new ArgumentException("采集间隔不能小于100毫秒", nameof(intervalMs));

        CollectionIntervalMs = intervalMs;
        _logger.LogInformation("已设置数据采集间隔为：{IntervalMs}毫秒", intervalMs);
    }

    /// <summary>
    ///     运行调度器（启动时启动所有任务）
    /// </summary>
    private async Task RunSchedulerAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("数据采集调度器已启动");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // 获取所有设备
                using var scope = _serviceScopeFactory.CreateScope();
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();
                var devices = await deviceService.GetAllDevicesAsync();

                // 筛选出自动启动的设备
                var autoStartDevices = devices.Where(d => d.AutoStart).ToList();

                // 对每个自动启动的设备执行数据采集
                foreach (var device in autoStartDevices) await _coordinator.StartCollectionForDeviceAsync(device.Id);

                // 执行所有活动测试运行的数据采集
                if (_coordinator.ActiveTestRuns.Count > 0)
                {
                    var results = await _coordinator.CollectAllDataAsync();

                    // 记录采集结果
                    var successCount = results.Count(r => r.Value);
                    var failureCount = results.Count - successCount;

                    if (failureCount > 0)
                        _logger.LogWarning("数据采集完成：{SuccessCount}个成功，{FailureCount}个失败",
                            successCount, failureCount);
                    else if (successCount > 0) _logger.LogDebug("数据采集完成：{SuccessCount}个成功", successCount);
                }

                // 等待下一个采集周期
                await Task.Delay(CollectionIntervalMs, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 预期的异常，忽略
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据采集调度器运行时出错：{Message}", ex.Message);
        }
        finally
        {
            _logger.LogInformation("数据采集调度器已停止");
        }
    }
}