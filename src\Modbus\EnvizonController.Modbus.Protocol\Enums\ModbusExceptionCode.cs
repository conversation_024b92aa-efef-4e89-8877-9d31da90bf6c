namespace EnvizonController.Modbus.Protocol.Enums
{
    /// <summary>
    /// Modbus 异常码枚举
    /// </summary>
    public enum ModbusExceptionCode : byte
    {
        /// <summary>
        /// 非法功能 (Illegal Function)
        /// </summary>
        IllegalFunction = 0x01,

        /// <summary>
        /// 非法数据地址 (Illegal Data Address)
        /// </summary>
        IllegalDataAddress = 0x02,

        /// <summary>
        /// 非法数据值 (Illegal Data Value)
        /// </summary>
        IllegalDataValue = 0x03,

        /// <summary>
        /// 从站设备故障 (Slave Device Failure)
        /// </summary>
        SlaveDeviceFailure = 0x04,

        /// <summary>
        /// 确认 (Acknowledge)
        /// </summary>
        Acknowledge = 0x05,

        /// <summary>
        /// 从站设备忙 (Slave Device Busy)
        /// </summary>
        SlaveDeviceBusy = 0x06,

        /// <summary>
        /// 内存奇偶校验错误 (Memory Parity Error)
        /// </summary>
        MemoryParityError = 0x08,

        /// <summary>
        /// 网关路径不可用 (Gateway Path Unavailable)
        /// </summary>
        GatewayPathUnavailable = 0x0A,

        /// <summary>
        /// 网关目标设备响应失败 (Gateway Target Device Failed to Respond)
        /// </summary>
        GatewayTargetDeviceFailedToRespond = 0x0B
    }
}
