using EnvizonController.Modbus.Tests.Mocks;
using Xunit;

namespace EnvizonController.Modbus.Tests.Channels
{
    public class ModbusChannelTests
    {
        [Fact]
        public async Task ConnectAsync_SetsIsConnectedToTrue()
        {
            // Arrange
            var channel = new MockModbusChannel();
            
            // Act
            await channel.ConnectAsync();
            
            // Assert
            Assert.True(channel.IsConnected);
        }
        
        [Fact]
        public async Task DisconnectAsync_SetsIsConnectedToFalse()
        {
            // Arrange
            var channel = new MockModbusChannel();
            await channel.ConnectAsync();
            
            // Act
            await channel.DisconnectAsync();
            
            // Assert
            Assert.False(channel.IsConnected);
        }
        
        [Fact]
        public async Task SendAsync_WhenConnected_AddsSentData()
        {
            // Arrange
            var channel = new MockModbusChannel();
            await channel.ConnectAsync();
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act
            await channel.SendAsync(data);
            
            // Assert
            Assert.Single(channel.SentData);
            Assert.Equal(data, channel.SentData[0]);
        }
        
        [Fact]
        public async Task SendAsync_WhenNotConnected_ThrowsInvalidOperationException()
        {
            // Arrange
            var channel = new MockModbusChannel();
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                channel.SendAsync(data));
        }
        
        [Fact]
        public async Task ReceiveAsync_WhenConnectedAndResponseAvailable_ReturnsResponse()
        {
            // Arrange
            var channel = new MockModbusChannel();
            await channel.ConnectAsync();
            byte[] expectedResponse = new byte[] { 0x01, 0x03, 0x14, 0x00, 0x01 };
            channel.EnqueueResponse(expectedResponse);
            
            // Act
            byte[] actualResponse = await channel.ReceiveAsync();
            
            // Assert
            Assert.Equal(expectedResponse, actualResponse);
        }
        
        [Fact]
        public async Task ReceiveAsync_WhenConnectedAndNoResponseAvailable_ThrowsTimeoutException()
        {
            // Arrange
            var channel = new MockModbusChannel();
            await channel.ConnectAsync();
            
            // Act & Assert
            await Assert.ThrowsAsync<TimeoutException>(() => 
                channel.ReceiveAsync());
        }
        
        [Fact]
        public async Task ReceiveAsync_WhenNotConnected_ThrowsInvalidOperationException()
        {
            // Arrange
            var channel = new MockModbusChannel();
            
            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => 
                channel.ReceiveAsync());
        }
        
        [Fact]
        public async Task ClearReceiveBufferAsync_ClearsQueuedResponses()
        {
            // Arrange
            var channel = new MockModbusChannel();
            await channel.ConnectAsync();
            channel.EnqueueResponse(new byte[] { 0x01 });
            channel.EnqueueResponse(new byte[] { 0x02 });
            
            // Act
            await channel.ClearReceiveBufferAsync();
            
            // Act & Assert - 如果队列已清空，应该抛出超时异常
            await Assert.ThrowsAsync<TimeoutException>(() => 
                channel.ReceiveAsync());
        }
        
        [Fact]
        public void Dispose_SetsIsConnectedToFalse()
        {
            // Arrange
            var channel = new MockModbusChannel();
            channel.ConnectAsync().GetAwaiter().GetResult();
            
            // Act
            channel.Dispose();
            
            // Assert
            Assert.False(channel.IsConnected);
        }
    }
}
