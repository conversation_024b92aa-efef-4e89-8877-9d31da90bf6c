using EnvizonController.Shared.DTOs;

namespace EnvizonController.Application.Interfaces.Services
{
    /// <summary>
    /// 程式表应用服务接口
    /// 提供程式表和程式步骤的创建、查询、更新和删除功能
    /// </summary>
    public interface IProgramAppService
    {
        /// <summary>
        /// 获取所有程式表
        /// </summary>
        /// <returns>程式表集合</returns>
        Task<IEnumerable<ProgramDTO>> GetAllProgramsAsync();

        /// <summary>
        /// 根据ID获取程式表
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>程式表，如果未找到则返回null</returns>
        Task<ProgramDTO?> GetProgramByIdAsync(long id);

        /// <summary>
        /// 根据名称获取程式表
        /// </summary>
        /// <param name="name">程式表名称</param>
        /// <returns>程式表，如果未找到则返回null</returns>
        Task<ProgramDTO?> GetProgramByNameAsync(string name);

        /// <summary>
        /// 创建程式表
        /// </summary>
        /// <param name="programDto">程式表DTO</param>
        /// <returns>创建后的程式表</returns>
        Task<ProgramDTO> CreateProgramAsync(ProgramDTO programDto);

        /// <summary>
        /// 更新程式表
        /// </summary>
        /// <param name="programDto">程式表DTO</param>
        /// <returns>更新后的程式表</returns>
        Task<ProgramDTO> UpdateProgramAsync(ProgramDTO programDto);

        /// <summary>
        /// 删除程式表
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>是否成功删除</returns>
        Task<bool> DeleteProgramAsync(long id);

        /// <summary>
        /// 获取程式表的所有步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <returns>程式步骤集合</returns>
        Task<IEnumerable<ProgramStepDTO>> GetProgramStepsAsync(long programId);

        /// <summary>
        /// 创建程式步骤
        /// </summary>
        /// <param name="stepDto">程式步骤DTO</param>
        /// <returns>创建后的程式步骤</returns>
        Task<ProgramStepDTO> CreateProgramStepAsync(ProgramStepDTO stepDto);

        /// <summary>
        /// 更新程式步骤
        /// </summary>
        /// <param name="stepDto">程式步骤DTO</param>
        /// <returns>更新后的程式步骤</returns>
        Task<ProgramStepDTO> UpdateProgramStepAsync(ProgramStepDTO stepDto);

        /// <summary>
        /// 删除程式步骤
        /// </summary>
        /// <param name="id">程式步骤ID</param>
        /// <returns>是否成功删除</returns>
        Task<bool> DeleteProgramStepAsync(long id);

        /// <summary>
        /// 批量更新程式步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <param name="stepDtos">要更新的程式步骤DTO集合</param>
        /// <returns>更新后的程式步骤集合</returns>
        Task<IEnumerable<ProgramStepDTO>> BatchUpdateProgramStepsAsync(long programId, IEnumerable<ProgramStepDTO> stepDtos);

        /// <summary>
        /// 初始化默认 Program 数据
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        Task InitializeDefaultProgramsAsync();
    }
}