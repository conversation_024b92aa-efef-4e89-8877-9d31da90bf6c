using EnvizonController.Modbus.Abstractions.Channels;
using System.IO.Ports;

namespace EnvizonController.Modbus.Adapters.Desktop
{
    /// <summary>
    /// 基于System.IO.Ports.SerialPort的串口通道
    /// </summary>
    public class SerialPortChannel : ModbusChannelBase
    {
        private readonly SerialPort _serialPort;
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private bool _isDisposed;

        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        public override bool IsConnected => _serialPort.IsOpen;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="portName">端口名称</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBits">数据位</param>
        /// <param name="parity">校验位</param>
        /// <param name="stopBits">停止位</param>
        /// <param name="readTimeout">读取超时（毫秒）</param>
        /// <param name="writeTimeout">写入超时（毫秒）</param>
        public SerialPortChannel(
            string portName,
            int baudRate = 9600,
            int dataBits = 8,
            EnvizonController.Modbus.Abstractions.Enums. Parity parity = EnvizonController.Modbus.Abstractions.Enums.Parity.None,
            EnvizonController.Modbus.Abstractions.Enums.StopBits stopBits = EnvizonController.Modbus.Abstractions.Enums.StopBits.One,
            int readTimeout = 1000,
            int writeTimeout = 1000)
        {
            _serialPort = new SerialPort
            {
                PortName = portName,
                BaudRate = baudRate,
                DataBits = dataBits,
                Parity = (Parity)parity,
                StopBits = (StopBits)stopBits,
                ReadTimeout = readTimeout,
                WriteTimeout = writeTimeout
            };
        }

        /// <summary>
        /// 连接到通道
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!_serialPort.IsOpen)
                {
                    _serialPort.Open();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 断开通道连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null || data.Length == 0)
                return;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!_serialPort.IsOpen)
                    throw new InvalidOperationException("串口未打开");

                // 使用Task.Run将同步操作包装为异步操作
                await Task.Run(() =>
                {
                    _serialPort.Write(data, 0, data.Length);
                }, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收到的数据</returns>
        public override async Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!_serialPort.IsOpen)
                    throw new InvalidOperationException("串口未打开");

                // 保存原始超时设置
                int originalTimeout = _serialPort.ReadTimeout;
                _serialPort.ReadTimeout = timeout;

                try
                {
                    // 使用Task.Run将同步操作包装为异步操作
                    return await Task.Run(() =>
                    {
                        // 等待数据到达
                        int startTime = Environment.TickCount;
                        while (_serialPort.BytesToRead == 0)
                        {
                            if (Environment.TickCount - startTime > timeout)
                                throw new TimeoutException("接收数据超时");

                            Thread.Sleep(10);
                            cancellationToken.ThrowIfCancellationRequested();
                        }

                        // 给予一些时间让所有数据到达
                        Thread.Sleep(50);

                        // 读取所有可用数据
                        byte[] buffer = new byte[_serialPort.BytesToRead];
                        _serialPort.Read(buffer, 0, buffer.Length);
                        return buffer;
                    }, cancellationToken);
                }
                finally
                {
                    // 恢复原始超时设置
                    _serialPort.ReadTimeout = originalTimeout;
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        /// <returns>异步任务</returns>
        public override async Task ClearReceiveBufferAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.DiscardInBuffer();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 释放托管资源
                _semaphore.Dispose();
                _serialPort.Dispose();
            }

            _isDisposed = true;
            base.Dispose(disposing);
        }
    }
}
