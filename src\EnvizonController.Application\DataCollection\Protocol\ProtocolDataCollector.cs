using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
using EnvizonController.Application.DataCollection.Strategies;
using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces.Cache;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using static EnvizonController.Application.DataCollection.Protocol.ProtocolDataCollector;
using AutoMapper;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.DataCollection.Protocol
{
    /// <summary>
    /// 协议数据收集器，负责使用不同的收集策略从设备收集数据
    /// </summary>
    public class ProtocolDataCollector
    {
        private readonly IEnumerable<ICollectionStrategy> _strategies;
        private readonly ILogger _logger;
        private readonly IModbusDeviceServiceCache _modbusDeviceServiceCache;
        private readonly IMapper _mapper;
        /// <summary>
        /// 构造函数，初始化收集策略和日志记录器
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public ProtocolDataCollector(IServiceProvider serviceProvider)
        {
            // 从服务提供者获取所有已注册的收集策略
            _strategies = serviceProvider.GetServices<ICollectionStrategy>() ?? Enumerable.Empty<ICollectionStrategy>();
            // 从服务提供者获取日志记录器并为此类添加上下文
            _logger = serviceProvider.GetRequiredService<ILogger>().ForContext<ProtocolDataCollector>();

            if (!_strategies.Any())
            {
                _logger.Warning(
                    "未找到或注册任何收集策略。数据收集可能无法按预期工作。");
            }

            _modbusDeviceServiceCache = serviceProvider.GetRequiredService<IModbusDeviceServiceCache>();
            _mapper = serviceProvider.GetRequiredService<IMapper>();
        }

        /// <summary>
        /// 用于根据协议项目类型进行分类的常量。
        /// 您需要确保 ProtocolItem 的 Type 属性与这些值匹配。
        /// </summary>
        public static class ProtocolItemTypes
        {
            public const string Environmental = "环境"; // 示例：环境数据类型
            public const string Alarm = "报警";                 // 示例：报警数据类型
            public const string Status = "运行设置";               // 示例：状态数据类型
            // 根据需要添加更多类型
        }


      

        /// <summary>
        /// 异步收集设备数据，根据协议中的项目和适用的策略
        /// </summary>
        /// <param name="protocol">要使用的协议</param>
        /// <param name="device">要收集数据的设备</param>
        /// <returns>收集到的数据字典，键为协议项目名称</returns>
        public async Task<DeviceCollectionDetails> CollectGroupedDataAsync(Domain.Aggregates.Protocol protocol, Device device, IModbusDeviceService modbusDeviceService)
        {
            var deviceCollectionDetails = new DeviceCollectionDetails(_mapper);

            deviceCollectionDetails.DeviceId = device.Id;
            deviceCollectionDetails.TestId = device.TestId;
            if (device.TestId.HasValue)
                deviceCollectionDetails.TestDataPoint.TestId = device.TestId.Value;
            // 验证协议不为空
            if (protocol == null)
            {
                _logger.Error("数据收集的协议不能为空。");
                // 或者填充 report 并返回
                deviceCollectionDetails.DataCollectionStatus = "Failure";
                deviceCollectionDetails.OverallDeviceHealth = "Critical";
                deviceCollectionDetails.CollectionErrors.Add(new ItemErrorInfo { ItemName = "N/A", ErrorMessage = "协议不能为空。" });
                return deviceCollectionDetails; // 或者继续抛出异常，取决于您的错误处理策略
                // throw new ArgumentNullException(nameof(protocol)); 
            }

            // 验证设备不为空
            if (device == null)
            {
                _logger.Error("数据收集的设备不能为空。");
                deviceCollectionDetails.DataCollectionStatus = "Failure";
                deviceCollectionDetails.OverallDeviceHealth = "Critical";
                deviceCollectionDetails.CollectionErrors.Add(new ItemErrorInfo { ItemName = "N/A", ErrorMessage = "设备不能为空。" });
                return deviceCollectionDetails; // 或者继续抛出异常
                // throw new ArgumentNullException(nameof(device));
            }
            
            // 检查协议是否包含项目
            if (protocol.Items == null || !protocol.Items.Any())
            {
                _logger.Information(
                    "协议 {ProtocolName}（ID：{ProtocolId}）没有为设备 {DeviceName}（ID：{DeviceId}）收集的项目",
                    protocol.Name, protocol.Id, device.Name, device.Id);
                deviceCollectionDetails.DataCollectionStatus = "Success (No Items)"; // 表示成功，但没有项目可收集
                deviceCollectionDetails.OverallDeviceHealth = "Healthy"; // 没有项目，设备健康状况视为良好
                return deviceCollectionDetails;
            }

            _logger.Debug(
                "开始为设备 {DeviceName} 收集协议 {ProtocolName} 的数据。项目数量：{ItemCount}",
                protocol.Name, device.Name, protocol.Items.Count);

            var validItems = protocol.Items.Where(i => i != null).ToList();
            int totalAttemptedItems = validItems.Count;
            int successfulItemsCount = 0;

            // 遍历协议中的每个项目
            foreach (var protocolItem in validItems)
            {
                // 跳过空项目 - 已通过 validItems 过滤，但保留以防万一
                if (protocolItem == null) 
                {
                    _logger.Warning(
                        "为设备 {DeviceName} 处理协议 {ProtocolName} 时遇到意外的空协议项目（应已被过滤）。",
                        protocol.Name, device.Name);
                    continue; 
                }
                
                string itemName = protocolItem.Name ?? $"未命名项目_{protocolItem.Index}";

                // 寻找能处理当前项目的策略
                ICollectionStrategy? chosenStrategy = null;
                foreach (var strategy in _strategies)
                {
                    if (strategy.CanHandle(protocolItem))
                    {
                        chosenStrategy = strategy;
                        break;
                    }
                }

                // 如果找到适用的策略，则使用它收集数据
                if (chosenStrategy != null)
                {
                    try
                    {
                        _logger.Debug("为项目 {ItemName}（类型：{ItemType}）使用策略 {StrategyType}",
                            chosenStrategy.GetType().Name, itemName, protocolItem.GroupName);
                        var itemData = await chosenStrategy.CollectAsync(deviceCollectionDetails,protocolItem, device, modbusDeviceService);
                        if (itemData != null)
                        {
                            // 根据 item.Type 将数据分类存储
                            // 您需要确保 item.Type 的值与 ProtocolItemTypes 中定义的常量匹配
                            //if (protocolItem.GroupName == ProtocolItemTypes.Environmental)
                            //{
                            //    deviceCollectionDetails.TestDataPoint.Values.Add(new ValueData(){ ProtocolItemIndex = protocolItem.Index , Value = });
                            //    deviceCollectionDetails.EnvironmentalData[itemName] = itemData;
                            //}
                            //else if (protocolItem.GroupName == ProtocolItemTypes.Alarm)
                            //{
                            //    deviceCollectionDetails.AlarmData[itemName] = itemData;
                            //}
                            //else if (protocolItem.GroupName == ProtocolItemTypes.Status)
                            //{
                            //    deviceCollectionDetails.DeviceStateData[itemName] = itemData;
                            //}
                            //else
                            //{
                            //    deviceCollectionDetails.OtherData[itemName] = itemData;
                            //}
                            successfulItemsCount++;
                        }
                        else
                        {
                            _logger.Warning("策略 {StrategyType} 为项目 {ItemName} 返回了空数据",
                                chosenStrategy.GetType().Name, itemName);
                            deviceCollectionDetails.CollectionErrors.Add(new ItemErrorInfo
                            {
                                ItemName = itemName,
                                Type = protocolItem.GroupName,
                                ErrorMessage = "策略返回了空数据"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录收集过程中的异常
                        _logger.Error(ex,
                            "使用策略 {StrategyType} 收集项目 {ItemName}（类型：{ItemType}）数据时出错：{ErrorMessage}",
                            itemName, protocolItem.GroupName, chosenStrategy.GetType().Name, ex.Message);
                        deviceCollectionDetails.CollectionErrors.Add(new ItemErrorInfo
                        {
                            ItemName = itemName,
                            Type = protocolItem.GroupName,
                            ErrorMessage = $"收集失败：{ex.Message}"
                        });
                    }
                }
                else
                {
                    // 如果没有找到适用的策略，记录警告并添加到错误列表
                    _logger.Warning(
                        "未找到设备 {DeviceName} 上协议项目的收集策略：{ItemName}（ID：{ItemId}，类型：{ItemType}）",
                        itemName, protocolItem.Index, protocolItem.GroupName, device.Name);
                    deviceCollectionDetails.CollectionErrors.Add(new ItemErrorInfo
                    {
                        ItemName = itemName,
                        Type = protocolItem.GroupName,
                        ErrorMessage = $"未找到项目类型 '{protocolItem.GroupName}' 的收集策略"
                    });
                }
            }

            // 设置最终的收集状态和设备健康状况
            if (totalAttemptedItems == 0) // 再次检查，以防 validItems 为空但 protocol.Items 不为 null 但全为 null
            {
                deviceCollectionDetails.DataCollectionStatus = "Success (No Valid Items)";
                deviceCollectionDetails.OverallDeviceHealth = "Healthy";
            }
            else if (deviceCollectionDetails.CollectionErrors.Count == 0 && successfulItemsCount == totalAttemptedItems)
            {
                deviceCollectionDetails.DataCollectionStatus = "Success";
                deviceCollectionDetails.OverallDeviceHealth = "Healthy"; // 可根据 AlarmData 内容进一步细化
            }
            else if (successfulItemsCount > 0 || deviceCollectionDetails.CollectionErrors.Count < totalAttemptedItems)
            {
                deviceCollectionDetails.DataCollectionStatus = "Partial Success";
                deviceCollectionDetails.OverallDeviceHealth = "Warnings";
            }
            else
            {
                deviceCollectionDetails.DataCollectionStatus = "Failure";
                deviceCollectionDetails.OverallDeviceHealth = "Critical";
            }
            if(device.TestId != null)
            {
                deviceCollectionDetails.TestDataPoint.Values = deviceCollectionDetails.ValueDataList;
                deviceCollectionDetails.TestDataPoint.TestId = device.TestId.Value;
                deviceCollectionDetails.TestDataPoint.Timestamp = DateTime.UtcNow;
            }
                // 如果 AlarmData 中有实际报警，也可能将 OverallDeviceHealth 设为 "Warnings" 或 "Critical"
                // 示例：if (report.AlarmData.Any(kvp => Convert.ToBoolean(kvp.Value))) report.OverallDeviceHealth = "Critical";
                // 这需要对 AlarmData 的结构有更多了解。

                _logger.Debug(
                "已完成设备 {DeviceName} 上协议 {ProtocolName} 的数据收集。状态：{Status}，健康状况：{Health}，收集到 {CollectedItemCount} 个项目的数据，错误：{ErrorCount} 个。",
                protocol.Name, device.Name, deviceCollectionDetails.DataCollectionStatus, deviceCollectionDetails.OverallDeviceHealth, successfulItemsCount, deviceCollectionDetails.CollectionErrors.Count);
            
            return deviceCollectionDetails;
        }


    }  /// <summary>
    /// 包含从设备收集的详细数据，已分类并包含错误和状态信息。
    /// </summary>
    public class DeviceCollectionDetails
    {
        private readonly IMapper? _mapper;

        public long DeviceId { get; set; }
        public long? TestId { get; set; }
        public Dictionary<string, Dictionary<string, object>> EnvironmentalData { get; private set; } = new Dictionary<string, Dictionary<string, object>>();
        public TestDataPoint TestDataPoint { get; private set; } = new TestDataPoint();
        public List<ValueData> ValueDataList { get; private set; } = new List<ValueData>();
        public Dictionary<string, Dictionary<string, object>> DeviceStateData { get; private set; } = new Dictionary<string, Dictionary<string, object>>();
        //public Dictionary<string, object> OtherData { get; private set; } = new Dictionary<string, object>();

        public List<ItemErrorInfo> CollectionErrors { get; private set; } = new List<ItemErrorInfo>();
        public List<Alarm> Alarms { get; private set; } = new List<Alarm>();

        /// <summary>
        /// 数据收集过程的整体状态 (例如："Success", "Partial Success", "Failure")。
        /// </summary>
        public string DataCollectionStatus { get; set; } = "Pending";

        /// <summary>
        /// 设备的整体健康状况评估 (例如："Healthy", "Warnings", "Critical")。
        /// 此评估基于收集错误和潜在的报警数据。
        /// </summary>
        public string OverallDeviceHealth { get; set; } = "Unknown";

        /// <summary>
        /// 设备运行状态 (运行、暂停、停止)
        /// </summary>
        public DeviceOperatingStatus OperatingStatus { get; set; } = DeviceOperatingStatus.Unknown;

        /// <summary>
        /// 当前程式步骤
        /// </summary>
        public int CurrentProgramStep { get; set; } = 0;

        /// <summary>
        /// 当前程式步骤是否完成
        /// </summary>
        public bool IsCurrentProgramStepCompleted { get; set; } = false;

        // 默认构造函数
        public DeviceCollectionDetails()
        {
        }

        // 带有IMapper参数的构造函数
        public DeviceCollectionDetails(IMapper mapper)
        {
            _mapper = mapper;
        }

        public DeviceCollectionDetailsDTO CreateDeviceCollectionDetailsDTO()
        {
            var dto = new DeviceCollectionDetailsDTO
            {
                DeviceId = this.DeviceId,
                TestId = this.TestId,
                CollectionErrors = this.CollectionErrors,
                // 将EnvironmentalData字典转换为List<EnvironmentalDataGroup>
                EnvironmentalData = EnvironmentalData,
                DeviceStateData = DeviceStateData,
                // 设备状态相关属性
                OperatingStatus = this.OperatingStatus,
                CurrentProgramStep = this.CurrentProgramStep,
                IsCurrentProgramStepCompleted = this.IsCurrentProgramStepCompleted
            };


            // 如果有映射器可用，使用映射器转换Alarm到AlarmDTO
            if (_mapper != null)
            {
                dto.Alarms = _mapper.Map<List<AlarmDTO>>(this.Alarms);
            }
            else
            {
                // 如果没有映射器，可以记录警告或抛出异常
                // 这里简单地不设置Alarms，或者可以根据需求采取其他处理方式
            }

            return dto;
        }
    }


}