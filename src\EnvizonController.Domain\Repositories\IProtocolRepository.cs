using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 协议仓储接口
    /// </summary>
    public interface IProtocolRepository : IRepository<Protocol, long>
    {
        /// <summary>
        /// 根据名称获取协议
        /// </summary>
        /// <param name="name">协议名称</param>
        /// <returns>协议，如果未找到则返回null</returns>
        Task<Protocol?> GetByNameAsync(string name);
    }
}
