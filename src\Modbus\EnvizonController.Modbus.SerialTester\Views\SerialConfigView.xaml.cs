using EnvizonController.Modbus.SerialTester.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace EnvizonController.Modbus.SerialTester.Views
{
    /// <summary>
    /// SerialConfigView.xaml 的交互逻辑
    /// </summary>
    public partial class SerialConfigView : UserControl
    {
        public SerialConfigView()
        {
            InitializeComponent();
        }

        private void RefreshPorts_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is SerialConfigViewModel viewModel)
            {
                viewModel.RefreshPorts();
            }
        }
    }
}
