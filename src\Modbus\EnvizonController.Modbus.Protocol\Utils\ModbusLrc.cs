namespace EnvizonController.Modbus.Protocol.Utils
{
    /// <summary>
    /// Modbus LRC计算工具类
    /// </summary>
    public static class ModbusLrc
    {
        /// <summary>
        /// 计算Modbus LRC校验码
        /// </summary>
        /// <param name="data">需要计算LRC的数据</param>
        /// <returns>LRC校验码（1字节）</returns>
        public static byte CalculateLrc(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            byte lrc = 0;
            
            foreach (byte b in data)
            {
                lrc += b;
            }
            
            // 取反加1
            lrc = (byte)((lrc ^ 0xFF) + 1);
            
            return lrc;
        }

        /// <summary>
        /// 验证Modbus LRC校验码
        /// </summary>
        /// <param name="data">包含LRC的完整数据</param>
        /// <returns>LRC校验是否正确</returns>
        public static bool ValidateLrc(byte[] data)
        {
            if (data == null || data.Length < 1)
                return false;

            // 计算除LRC外的数据的LRC
            byte[] dataWithoutLrc = new byte[data.Length - 1];
            Array.Copy(data, dataWithoutLrc, data.Length - 1);
            
            byte calculatedLrc = CalculateLrc(dataWithoutLrc);
            
            // 比较计算的LRC与数据中的LRC
            return data[data.Length - 1] == calculatedLrc;
        }

        /// <summary>
        /// 添加LRC校验码到数据末尾
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>添加了LRC的数据</returns>
        public static byte[] AppendLrc(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            byte lrc = CalculateLrc(data);
            byte[] result = new byte[data.Length + 1];
            
            Array.Copy(data, result, data.Length);
            result[data.Length] = lrc;
            
            return result;
        }

        /// <summary>
        /// 将字节数组转换为ASCII字符串
        /// </summary>
        /// <param name="data">字节数组</param>
        /// <returns>ASCII字符串</returns>
        public static string BytesToAscii(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            char[] asciiChars = new char[data.Length * 2];
            
            for (int i = 0; i < data.Length; i++)
            {
                byte highNibble = (byte)((data[i] >> 4) & 0x0F);
                byte lowNibble = (byte)(data[i] & 0x0F);
                
                asciiChars[i * 2] = NibbleToAscii(highNibble);
                asciiChars[i * 2 + 1] = NibbleToAscii(lowNibble);
            }
            
            return new string(asciiChars);
        }

        /// <summary>
        /// 将ASCII字符串转换为字节数组
        /// </summary>
        /// <param name="ascii">ASCII字符串</param>
        /// <returns>字节数组</returns>
        public static byte[] AsciiToBytes(string ascii)
        {
            if (string.IsNullOrEmpty(ascii))
                throw new ArgumentNullException(nameof(ascii));

            if (ascii.Length % 2 != 0)
                throw new ArgumentException("ASCII字符串长度必须为偶数", nameof(ascii));

            byte[] data = new byte[ascii.Length / 2];
            
            for (int i = 0; i < data.Length; i++)
            {
                byte highNibble = AsciiToNibble(ascii[i * 2]);
                byte lowNibble = AsciiToNibble(ascii[i * 2 + 1]);
                
                data[i] = (byte)((highNibble << 4) | lowNibble);
            }
            
            return data;
        }

        /// <summary>
        /// 将半字节转换为ASCII字符
        /// </summary>
        /// <param name="nibble">半字节</param>
        /// <returns>ASCII字符</returns>
        private static char NibbleToAscii(byte nibble)
        {
            if (nibble < 10)
                return (char)('0' + nibble);
            else
                return (char)('A' + (nibble - 10));
        }

        /// <summary>
        /// 将ASCII字符转换为半字节
        /// </summary>
        /// <param name="ascii">ASCII字符</param>
        /// <returns>半字节</returns>
        private static byte AsciiToNibble(char ascii)
        {
            if (ascii >= '0' && ascii <= '9')
                return (byte)(ascii - '0');
            else if (ascii >= 'A' && ascii <= 'F')
                return (byte)(ascii - 'A' + 10);
            else if (ascii >= 'a' && ascii <= 'f')
                return (byte)(ascii - 'a' + 10);
            else
                throw new ArgumentException($"无效的ASCII字符: {ascii}", nameof(ascii));
        }
    }
}
