using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    public class AlarmStatusToForeground : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "活跃" => new SolidColorBrush(Color.Parse("#FF5555")),  // 活跃报警：红色文本
                    "已确认" => new SolidColorBrush(Color.Parse("#FFCC55")), // 已确认报警：黄色文本
                    "已解除" => new SolidColorBrush(Color.Parse("#55CC55")), // 已解除报警：绿色文本
                    "已清除" => new SolidColorBrush(Color.Parse("#55CC55")), // 已清除报警：同样使用绿色文本
                    _ => new SolidColorBrush(Color.Parse("#AAAAAA")),      // 默认灰色文本
                };
            }

            return new SolidColorBrush(Color.Parse("#FFFFFF"));
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 