﻿using EnvizonController.Domain.Aggregates;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Application.Interfaces
{
    /// <summary>
    /// 协议应用服务接口
    /// </summary>
    public interface IProtocolAppService
    {
        /// <summary>
        /// 获取所有协议
        /// </summary>
        /// <returns>协议列表</returns>
        List<Protocol> GetAllProtocols();

        /// <summary>
        /// 根据ID获取协议
        /// </summary>
        /// <param name="id">协议ID</param>
        /// <returns>协议，如果未找到则返回null</returns>
        Protocol? GetProtocolById(long id);

        /// <summary>
        /// 根据名称获取协议
        /// </summary>
        /// <param name="name">协议名称</param>
        /// <returns>协议，如果未找到则返回null</returns>
        Protocol? GetProtocolByName(string name);

        /// <summary>
        /// 添加协议
        /// </summary>
        /// <param name="protocol">协议</param>
        void AddProtocol(Protocol protocol);

        /// <summary>
        /// 更新协议
        /// </summary>
        /// <param name="protocol">协议</param>
        /// <returns>是否成功更新</returns>
        bool UpdateProtocol(Protocol protocol);

        /// <summary>
        /// 删除协议
        /// </summary>
        /// <param name="id">协议ID</param>
        /// <returns>是否成功删除</returns>
        bool DeleteProtocol(long id);

        /// <summary>
        /// 初始化默认协议
        /// </summary>
        void InitializeDefaultProtocols();
    }
}
