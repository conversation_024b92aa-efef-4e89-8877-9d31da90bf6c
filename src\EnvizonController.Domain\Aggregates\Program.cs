using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates
{
    /// <summary>
    /// 程式表聚合根
    /// 表示一个完整的程式定义，包含多个程式步骤
    /// </summary>
    [Table("Programs")]
    public class Program : BaseEntity<long>
    {
        /// <summary>
        /// 程式名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 循环次数
        /// </summary>
        public int CycleCount { get; set; }

        /// <summary>
        /// 循环开始
        /// </summary>
        public int CycleStart { get; set; }

        /// <summary>
        /// 循环结束
        /// </summary>
        public int CycleEnd { get; set; }

        /// <summary>
        /// 程式步骤集合
        /// </summary>
        public List<ProgramStep> Steps { get; set; } = new List<ProgramStep>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加程式步骤
        /// </summary>
        /// <param name="step">程式步骤</param>
        public void AddStep(ProgramStep step)
        {
            Steps.Add(step);
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 移除程式步骤
        /// </summary>
        /// <param name="step">程式步骤</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveStep(ProgramStep step)
        {
            bool result = Steps.Remove(step);
            if (result)
            {
                UpdatedAt = DateTime.Now;
            }
            return result;
        }

        /// <summary>
        /// 清空程式步骤集合
        /// </summary>
        public void ClearSteps(){
            Steps.Clear();
            
        }

        /// <summary>
        /// 根据索引查找程式步骤
        /// </summary>
        /// <param name="index">步骤索引</param>
        /// <returns>程式步骤，如果未找到则返回null</returns>
        public ProgramStep? FindStep(int index)
        {
            return Steps.FirstOrDefault(step => step.Index == index);
        }
        
        /// <summary>
        /// 根据ID查找程式步骤
        /// </summary>
        /// <param name="id">步骤ID</param>
        /// <returns>程式步骤，如果未找到则返回null</returns>
        public ProgramStep? FindStepById(long id)
        {
            return Steps.FirstOrDefault(step => step.Id == id);
        }
        
        /// <summary>
        /// 获取排序后的程式步骤列表
        /// </summary>
        /// <returns>按索引排序的程式步骤列表</returns>
        public List<ProgramStep> GetOrderedSteps()
        {
            return Steps.OrderBy(step => step.Index).ToList();
        }
    }
}