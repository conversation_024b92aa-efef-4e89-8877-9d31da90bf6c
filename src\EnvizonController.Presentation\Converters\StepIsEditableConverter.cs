using System;
using System.Globalization;
using Avalonia.Data.Converters;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Program;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 检查步骤是否可编辑的转换器 - 仅当步骤与当前编辑的步骤匹配时才返回可编辑状态
    /// </summary>
    public class StepIsEditableConverter : IValueConverter
    {
        /// <summary>
        /// 如果传入的步骤与当前编辑的步骤不同，返回true（即该步骤为只读）
        /// 如果传入的步骤就是当前编辑的步骤，返回false（即该步骤可编辑）
        /// </summary>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            // 获取当前行的步骤数据
            var step = value as ProgramStepItemViewModel;
            if (step == null)
                return true; // 不是步骤，则为只读

            // 获取视图模型
            var programViewModel = parameter as ProgramViewModel;
            if (programViewModel == null)
                return true; // 没有视图模型，则为只读

            // 如果没有正在编辑的步骤或当前步骤不是正在编辑的步骤，则为只读
            return programViewModel.EditingProgramStep == null ||
                   !ReferenceEquals(programViewModel.EditingProgramStep, step);
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}