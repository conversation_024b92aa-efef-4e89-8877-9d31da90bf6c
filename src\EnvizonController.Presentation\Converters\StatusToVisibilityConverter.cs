using Avalonia.Data.Converters;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警状态转换为可见性（用于控制处理按钮的显示）
    /// </summary>
    public class StatusToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 将报警状态转换为可见性
        /// </summary>
        /// <param name="value">报警状态</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>若状态为活动则返回true，否则返回false</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not AlarmStatus status) return false;

            // 活动状态的报警需要显示处理按钮
            return status == AlarmStatus.Active;
        }

        /// <summary>
        /// 将可见性转换回报警状态（不支持）
        /// </summary>
        /// <param name="value">可见性</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 