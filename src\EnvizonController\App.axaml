<Application
    x:Class="EnvizonController.App"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:EnvizonController.Presentation.Converters;assembly=EnvizonController.Presentation"
    xmlns:local="using:EnvizonController"
    xmlns:presentation="clr-namespace:EnvizonController.Presentation;assembly=EnvizonController.Presentation"
    xmlns:uikit="https://github.com/avaloniaui/avaloniauikit"
    RequestedThemeVariant="Default">
    <!--  "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options.  -->
    <Application.DataTemplates>
        <presentation:MvvmDialogsViewLocator />
    </Application.DataTemplates>

    <Application.Styles>
        <FluentTheme />
        <uikit:UiKitTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml" />
        <StyleInclude Source="avares://EnvizonController.Presentation/Assets/Themes/EnvizonTheme.axaml" />
        <StyleInclude Source="avares://EnvizonController.Presentation/Assets/Themes/Controls/ProgressBar.axaml" />
        <StyleInclude Source="avares://EnvizonController.Presentation/Controls/PaginationControlStyles.axaml" />

        <StyleInclude Source="avares://DialogHost.Avalonia/Styles.xaml" />
        <!--<StyleInclude Source="avares://EnvizonController.Presentation/Assets/Themes/Controls/Dialog.axaml" />-->
    </Application.Styles>

    <Application.Resources>
        <ResourceDictionary>
            <converters:PowerStateTextConverter x:Key="PowerStateTextConverter" />
            <converters:PowerStateBackgroundConverter x:Key="PowerStateBackgroundConverter" />
            <converters:AlarmBackgroundConverter x:Key="AlarmBackgroundConverter" />
            <converters:EventTypeBackgroundConverter x:Key="EventTypeBackgroundConverter" />
            <converters:StringNotEmptyConverter x:Key="StringNotEmptyConverter" />
            <converters:PercentageToWidthConverter x:Key="PercentageToWidthConverter" />
            <converters:StatusToColorConverter x:Key="StatusToColorConverter" />
            <converters:BoolToHighlightBrush x:Key="BoolToHighlightBrush" />
            <converters:AlarmLevelToBrush x:Key="AlarmLevelToBrush" />
            <converters:AlarmLevelToForeground x:Key="AlarmLevelToForeground" />
            <converters:BoolToGlowEffect x:Key="BoolToGlowEffect" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:StringEqualsConverter x:Key="StringEqualsConverter" />
            <converters:DoubleParseConverter x:Key="DoubleParseConverter" />
            <converters:EnumEqualsConverter x:Key="EnumEqualsConverter" />
            <converters:GreaterThanConverter x:Key="GreaterThanConverter" />
            <converters:LessThanConverter x:Key="LessThanConverter" />
            <converters:BetweenConverter x:Key="BetweenConverter" />
            <converters:AlarmLevelToStringConverter x:Key="AlarmLevelToStringConverter" />
            <converters:AlarmLevelToBrushConverter x:Key="AlarmLevelToBrushConverter" />
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters:BoolToSelectedBackgroundConverter x:Key="BoolToSelectedBackgroundConverter" />
            <converters:BoolToSelectedBorderConverter x:Key="BoolToSelectedBorderConverter" />
            <converters:BoolToSelectedForegroundConverter x:Key="BoolToSelectedForegroundConverter" />
            <converters:BoolToSelectedIndexBackgroundConverter x:Key="BoolToSelectedIndexBackgroundConverter" />
            <converters:BoolToSelectedIndexForegroundConverter x:Key="BoolToSelectedIndexForegroundConverter" />
            <converters:HexColorToSolidColorBrushConverter x:Key="HexColorToSolidColorBrushConverter" />
            <converters:DateTimeOffsetToDateTimeConverter x:Key="DateTimeOffsetToDateTimeConverter" />
            <FontFamily x:Key="FontAwesomeSolid">avares://EnvizonController.Presentation/Assets/Fonts/FontAwesome6Free-Solid-900.otf#Font Awesome 6 Free Solid</FontFamily>
            <FontFamily x:Key="Orbitron">avares://EnvizonController.Presentation/Assets/Fonts/Orbitron-Regular.ttf#Orbitron</FontFamily>
            <FontFamily x:Key="SansSerif">avares://EnvizonController.Presentation/Assets/Fonts/SansSerif.ttf#SansSerif</FontFamily>
            <FontFamily x:Key="Rajdhani">avares://EnvizonController.Presentation/Assets/Fonts/Rajdhani-Regular-2.ttf#Rajdhani</FontFamily>

            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="avares://EnvizonController.Presentation/Assets/Themes/Brushes.axaml" />
                <ResourceInclude Source="avares://EnvizonController.Presentation/Assets/Geometries/GeometryResources.axaml" />
                <ResourceInclude Source="avares://EnvizonController.Presentation/Controls/ControlResources.axaml" />
                <!--<ResourceInclude Source="avares://EnvizonController.Presentation/Assets/Themes/CustomDialogTemplates.axaml" />-->
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>