﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Configuration.Enums;

namespace EnvizonController.Configuration.Models;

public class DisplayConfig : ObservableObject
{
    private int _fontSize = 12;
    private Theme _theme = Theme.Light;

    public Theme Theme
    {
        get => _theme;
        set => SetProperty(ref _theme, value);
    }

    public int FontSize
    {
        get => _fontSize;
        set => SetProperty(ref _fontSize, value);
    }
}