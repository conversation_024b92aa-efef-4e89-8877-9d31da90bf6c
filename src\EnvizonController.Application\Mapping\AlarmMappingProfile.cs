using AutoMapper;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.Enums;
using System;

namespace EnvizonController.Application.Mapping
{
    /// <summary>
    /// Alarm 领域对象的 AutoMapper 配置文件
    /// </summary>
    public class AlarmMappingProfile : Profile
    {
        public AlarmMappingProfile()
        {
            // 配置 Alarm -> AlarmDTO 的映射
            CreateMap<Alarm, AlarmDTO>()
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => (AlarmSeverity)src.Level))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (AlarmStatus)src.Status))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.TestId.HasValue ? src.TestId.ToString() : null))
                .ForMember(dest => dest.DeviceId, opt => opt.MapFrom(src => src.DeviceId))
                .ForMember(dest => dest.ProtocolId, opt => opt.MapFrom(src => src.ProtocolId))
                .ForMember(dest => dest.ProtocolItemIndex, opt => opt.MapFrom(src => src.ProtocolItemIndex));
            // 配置 CreateAlarmDto -> Alarm 的映射
            CreateMap<CreateAlarmDto, Alarm>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.ProcessedBy, opt => opt.Ignore())
                .ForMember(dest => dest.ProcessedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Timestamp, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.LastUpdated, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => (int)src.Level))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (int)AlarmStatus.Active))
                .ForMember(dest => dest.TestId, opt => opt.MapFrom(src => src.TestId))
                .ForMember(dest => dest.DeviceId, opt => opt.Ignore())
                .ForMember(dest => dest.ProtocolId, opt => opt.Ignore())
                .ForMember(dest => dest.ProtocolItemIndex, opt => opt.Ignore());
        }
    }
} 