﻿namespace EnvizonController.Presentation.Geometry;

public static class GeometryKeys
{
    public const string SidebarGeometry = "Geometry.Sidebar";
    public const string DeviceListGeometry = "Geometry.DeviceList";
    public const string AddDeviceGeometry = "Geometry.AddDevice";
    public const string DeviceManagerGeometry = "Geometry.DeviceManager";
    public const string WarnGeometry = "Geometry.WarnGeometry";
    public const string DisconnectGeometry = "Geometry.DisconnectGeometry";
    public const string ImportGeometry = "Geometry.ImportGeometry";
    public const string SearchProtocolGeometry = "Geometry.SearchProtocolGeometry";
    public const string AlarmRecordGeometry = "Geometry.AlarmRecordGeometry";
    public const string DataGeometry = "Geometry.DataGeometry";
    public const string ParamSettingGeometry = "Geometry.ParamSettingGeometry";
    public const string DashboardGeometry = "Geometry.DashboardGeometry";
    public const string StopGeometry = "Geometry.StopGeometry";
    public const string RunGeometry = "Geometry.RunGeometry";
    public const string PauseGeometry = "Geometry.PauseGeometry";
    public const string ProgramGeometry = "Geometry.ProgramGeometry";
    public const string TempGeometry = "Geometry.TempGeometry";
    public const string HumGeometry = "Geometry.HumGeometry";
    public const string SettingGeometry = "Geometry.SettingGeometry";
    public const string DeleteGeometry = "Geometry.DeleteGeometry";
    public const string LogGeometry = "Geometry.LogGeometry";
    public const string UserGeometry = "Geometry.UserGeometry";
    public const string RefreshGeometry = "Geometry.RefreshGeometry";
    public const string ComputerGeometry = "Geometry.ComputerGeometry";
    public const string ListViewGeometry = "Geometry.ListViewGeometry";
    public const string GroupViewGeometry = "Geometry.GroupViewGeometry";
    public const string DataGridViewGeometry = "Geometry.DataGridViewGeometry";
}