﻿using EnvizonController.Domain.Repositories;

namespace EnvizonController.Application.Interfaces;

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 报警仓储
    /// </summary>
    IAlarmRepository Alarms { get; }

    /// <summary>
    /// 测试项仓储
    /// </summary>
    ITestItemRepository TestItems { get; }

    /// <summary>
    /// 测试数据点仓储
    /// </summary>
    IDataPointRepository TestDataPoints { get; }

    /// <summary>
    /// 设备仓储
    /// </summary>
    IDeviceRepository Devices { get; }

    /// <summary>
    /// 协议仓储
    /// </summary>
    IProtocolRepository Protocols { get; }

    /// <summary>
    /// 程式表仓储
    /// </summary>
    IProgramRepository Programs { get; }

    /// <summary>
    /// 链接表仓储
    /// </summary>
    IProgramLinkRepository ProgramLinks { get; }
    IProgramLinkStepRepository ProgramLinkSteps { get; }

    /// <summary>
    /// 程式步骤仓储
    /// </summary>
    IProgramStepRepository ProgramSteps { get; }

    /// <summary>
    /// 保存所有更改
    /// </summary>
    Task<int> SaveChangesAsync();

    /// <summary>
    /// 开始事务
    /// </summary>
    Task BeginTransactionAsync();

    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitTransactionAsync();

    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackTransactionAsync();
}