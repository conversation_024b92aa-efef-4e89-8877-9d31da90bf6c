using EnvizonController.Modbus.Abstractions.Interfaces;

namespace EnvizonController.Application.Devices
{
    /// <summary>
    /// Modbus设备服务接口
    /// </summary>
    public interface IModbusDeviceService
    {
        IModbusClient ModbusClient { get; }
        /// <summary>
        /// 获取设备状态流
        /// </summary>
        IObservable<DeviceModel> DeviceStateStream { get; }

        /// <summary>
        /// 获取设备是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功连接</returns>
        Task<bool> ConnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 断开与设备的连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功断开连接</returns>
        Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task RefreshDeviceStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取设备参数
        /// </summary>
        /// <param name="parameterAddress">参数地址</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>参数值</returns>
        Task<ushort> ReadParameterAsync(ushort parameterAddress, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写入设备参数
        /// </summary>
        /// <param name="parameterAddress">参数地址</param>
        /// <param name="value">参数值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        Task<bool> WriteParameterAsync(ushort parameterAddress, ushort value, CancellationToken cancellationToken = default);

        /// <summary>
        /// 读取多个设备参数
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="count">参数数量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>参数值数组</returns>
        Task<ushort[]> ReadParametersAsync(ushort startAddress, ushort count, CancellationToken cancellationToken = default);

        /// <summary>
        /// 写入多个设备参数
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">参数值数组</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否成功写入</returns>
        Task<bool> WriteParametersAsync(ushort startAddress, ushort[] values, CancellationToken cancellationToken = default);
    }
}
