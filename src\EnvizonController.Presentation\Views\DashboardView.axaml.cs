using Avalonia.Controls;
using Avalonia.Interactivity;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Dashboard;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Views;

public partial class DashboardView : UserControl
{
    private Grid? _controlPanel;
    private Border? _deviceListPanel;
    private TextBlock? _expandCollapseIcon;
    private TextBlock? _deviceListExpandCollapseIcon;

    public DashboardView()
    {
        InitializeComponent();
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        _controlPanel = this.FindControl<Grid>("ControlPanel");
        _deviceListPanel = this.FindControl<Border>("DeviceListPanel");
        _expandCollapseIcon = this.FindControl<TextBlock>("ExpandCollapseIcon");
        _deviceListExpandCollapseIcon = this.FindControl<TextBlock>("DeviceListExpandCollapseIcon");
    }

    public void ToggleControlPanel(object sender, RoutedEventArgs e)
    {
        if (_controlPanel == null || _expandCollapseIcon == null) return;

        // 获取ViewModel
        if (DataContext is DashboardViewModel viewModel)
        {
            // 切换控制面板状态
            viewModel.IsControlPanelExpanded = !viewModel.IsControlPanelExpanded;

            // 更新UI
            UpdateControlPanelVisibility(viewModel.IsControlPanelExpanded);
        }
    }

    private void UpdateControlPanelVisibility(bool isExpanded)
    {
        if (_controlPanel == null || _expandCollapseIcon == null) return;

        if (isExpanded)
        {
            // 展开面板
            _controlPanel.Opacity = 1;
            _controlPanel.IsVisible = true;
            _expandCollapseIcon.Text = "\uf078"; // 向下箭头
        }
        else
        {
            // 收缩面板
            _controlPanel.Opacity = 0;
            _controlPanel.IsVisible = false;
            _expandCollapseIcon.Text = "\uf077"; // 向上箭头
        }
    }

    /// <summary>
    /// 切换设备列表面板
    /// </summary>
    public void ToggleDeviceListPanel(object sender, RoutedEventArgs e)
    {
        if (_deviceListPanel == null || _deviceListExpandCollapseIcon == null) return;

        // 获取ViewModel
        if (DataContext is DashboardViewModel viewModel)
        {
            // 切换设备列表面板状态
            viewModel.IsDeviceListPanelExpanded = !viewModel.IsDeviceListPanelExpanded;

            // 更新UI
            UpdateDeviceListPanelVisibility(viewModel.IsDeviceListPanelExpanded);
        }
    }

    /// <summary>
    /// 更新设备列表面板可见性
    /// </summary>
    private void UpdateDeviceListPanelVisibility(bool isExpanded)
    {
        if (_deviceListPanel == null || _deviceListExpandCollapseIcon == null) return;

        if (isExpanded)
        {
            // 展开设备列表面板
            _deviceListPanel.Opacity = 1;
            _deviceListPanel.IsVisible = true;
            _deviceListExpandCollapseIcon.Text = "\uf053"; // 向左箭头
        }
        else
        {
            // 收缩设备列表面板
            _deviceListPanel.Opacity = 0;
            _deviceListPanel.IsVisible = false;
            _deviceListExpandCollapseIcon.Text = "\uf054"; // 向右箭头
        }
    }

    /// <summary>
    /// 选择设备
    /// </summary>
    /// <remarks>
    /// 处理设备选中事件，确保列表中只有一个设备被选中
    /// 如果点击已选中的设备，则保持选中状态不变
    /// </remarks>
    private void SelectDevice(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.DataContext is DeviceListItemViewModel device && DataContext is DashboardViewModel viewModel)
        {
            // 委托给ViewModel处理，确保状态一致性
            viewModel.SelectDeviceCommand.Execute(device);
        }
    }
}