using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 读取线圈状态请求
    /// </summary>
    public class ReadCoilsRequest : ModbusRequest
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; set; }

        /// <summary>
        /// 线圈数量
        /// </summary>
        public ushort CoilCount { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="slaveAddress">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="coilCount">线圈数量</param>
        public ReadCoilsRequest(byte slaveAddress, ushort startAddress, ushort coilCount)
        {
            SlaveAddress = slaveAddress;
            FunctionCode = ModbusFunction.ReadCoils;
            StartAddress = startAddress;
            CoilCount = coilCount;
        }

        /// <summary>
        /// 获取请求的数据部分
        /// </summary>
        /// <returns>请求数据字节数组</returns>
        public override byte[] GetData()
        {
            var data = new byte[4];
            data[0] = (byte)(StartAddress >> 8);    // 高字节
            data[1] = (byte)(StartAddress & 0xFF);  // 低字节
            data[2] = (byte)(CoilCount >> 8);       // 高字节
            data[3] = (byte)(CoilCount & 0xFF);     // 低字节
            return data;
        }
    }
}
