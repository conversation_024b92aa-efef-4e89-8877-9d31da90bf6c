<!--  EnvizonController.Presentation/Views/MainView.axaml  -->
<UserControl
    x:Class="EnvizonController.Presentation.Views.MainView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dialogHostAvalonia="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
    xmlns:enums="clr-namespace:EnvizonController.Shared.Enums;assembly=EnvizonController.Shared"
    xmlns:geometry="clr-namespace:EnvizonController.Presentation.Geometry"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:presentation="clr-namespace:EnvizonController.Presentation"
    xmlns:uikit="https://github.com/avaloniaui/avaloniauikit"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels;assembly=EnvizonController.Presentation"
    d:DesignHeight="600"
    d:DesignWidth="800"
    x:DataType="vm:MainViewModel"
    Background="#050510"
    mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <Border
            Grid.Row="0"
            Height="60"
            Padding="21,0"
            Background="Transparent"
            Classes="cyber-border"
            PointerPressed="TitleBar_PointerPressed">
            <!--  顶部导航栏  -->
            <Grid x:Name="TitleBar" ColumnDefinitions="Auto,*,Auto,Auto">
                <StackPanel
                    Grid.Column="0"
                    Margin="0,9"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Image Source="../Assets/logo.png" Stretch="Uniform" />
                    <TextBlock
                        VerticalAlignment="Center"
                        Classes="h1 glow-primary font-icon"
                        IsVisible="False"
                        Text="&#xf76b;" />
                    <TextBlock
                        Margin="12,0,0,0"
                        VerticalAlignment="Center"
                        Classes="h1 font-cyber glow-primary"
                        Text="Envizon Controller" />
                </StackPanel>

                <StackPanel
                    Grid.Column="1"
                    Margin="30,0,0,0"
                    HorizontalAlignment="Left"
                    Orientation="Horizontal">
                    <!--  Top Navigation Bar using ItemsControl  -->
                    <ItemsControl ItemsSource="{Binding NavigationItems}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal" Spacing="5" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>

                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="vm:NavigationItemViewModel">
                                <Button
                                    Classes="nav-button"
                                    Classes.selected="{Binding IsSelected}"
                                    Command="{Binding $parent[ItemsControl].((vm:MainViewModel)DataContext).TryNavigateToCommand}"
                                    CommandParameter="{Binding}"
                                    Content="{Binding HeaderText}">

                                    <!--  Bind the 'selected' class based on comparison  -->
                                    <!--  Note: $parent[ItemsControl].DataContext refers to MainWindowViewModel  -->
                                    <!--<Button.Classes.selected>
                                    <MultiBinding Converter="{x:Static local:EqualityConverter.Instance}">
                                        <Binding Path="." />
                                    -->
                                    <!--  The current NavigationItemViewModel  -->
                                    <!--
                                        <Binding Path="$parent[ItemsControl].DataContext.SelectedNavigationItem" />
                                    -->
                                    <!--  The selected one  -->
                                    <!--
                                    </MultiBinding>
                                </Button.Classes.selected>-->
                                </Button>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    <!--  <Button  -->
                    <!--  Background="Transparent"  -->
                    <!--  Content="仪表盘"  -->
                    <!--  Margin="5,0" />  -->
                    <!--  <Button  -->
                    <!--  Background="Transparent"  -->
                    <!--  Content="测试方案"  -->
                    <!--  Margin="5,0" />  -->
                    <!--  <Button  -->
                    <!--  Background="Transparent"  -->
                    <!--  Content="历史数据"  -->
                    <!--  Margin="5,0" />  -->
                    <!--  <Button  -->
                    <!--  Background="Transparent"  -->
                    <!--  Content="系统设置"  -->
                    <!--  Margin="5,0" />  -->
                </StackPanel>

                <StackPanel
                    Grid.Column="2"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <TextBlock
                        VerticalAlignment="Center"
                        Classes="font-cyber primary"
                        Text="STATUS: " />
                    <TextBlock
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Classes="font-cyber gray"
                        Classes.Connecting="{Binding ConnectionStatus, Converter={StaticResource EnumEqualsConverter}, ConverterParameter={x:Static enums:ConnectionStatus.Connecting}}"
                        Classes.Error="{Binding ConnectionStatus, Converter={StaticResource EnumEqualsConverter}, ConverterParameter={x:Static enums:ConnectionStatus.Error}}"
                        Classes.Online="{Binding ConnectionStatus, Converter={StaticResource EnumEqualsConverter}, ConverterParameter={x:Static enums:ConnectionStatus.Connected}}">
                        <TextBlock.Styles>
                            <Style Selector="TextBlock">
                                <Setter Property="Text" Value="OFFLINE" />
                                <Setter Property="Foreground" Value="Red" />
                            </Style>
                            <Style Selector="TextBlock.Online">
                                <Setter Property="Text" Value="ONLINE" />
                                <Setter Property="Foreground" Value="Lime" />
                            </Style>
                            <Style Selector="TextBlock.Connecting">
                                <Setter Property="Text" Value="CONNECTING" />
                            </Style>
                            <Style Selector="TextBlock.Error">
                                <Setter Property="Text" Value="ERROR" />
                                <Setter Property="Foreground" Value="Red" />
                            </Style>
                        </TextBlock.Styles>
                    </TextBlock>

                    <Border
                        Width="30"
                        Height="30"
                        Background="#0DF0FF"
                        CornerRadius="16.5">
                        <Path
                            Margin="7"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="{StaticResource {x:Static geometry:GeometryKeys.UserGeometry}}"
                            Fill="{DynamicResource SurfaceBrush}"
                            Stretch="Uniform" />
                    </Border>
                </StackPanel>

                <!--  窗口控制按钮  -->
                <StackPanel
                    Name="WindowButtonPanel"
                    Grid.Column="3"
                    Margin="10,0,0,0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="MinimizeButton"
                        Margin="3,0"
                        Padding="9"
                        Background="Transparent"
                        BorderBrush="#56EFFE"
                        BorderThickness="2"
                        Click="MinimizeButton_Click"
                        CornerRadius="5">
                        <Button.Effect>
                            <DropShadowEffect
                                BlurRadius="10"
                                OffsetX="0.5"
                                OffsetY="0.5"
                                Color="#56EFFE" />
                        </Button.Effect>
                        <TextBlock
                            FontFamily="Segoe MDL2 Assets"
                            FontSize="14"
                            Foreground="#56EFFE"
                            Text="&#xE921;" />
                    </Button>
                    <Button
                        x:Name="MaximizeButton"
                        Margin="6,0"
                        Padding="9"
                        Background="Transparent"
                        BorderBrush="#56EFFE"
                        BorderThickness="2"
                        Click="MaximizeButton_Click"
                        CornerRadius="5">
                        <Button.Effect>
                            <DropShadowEffect
                                BlurRadius="10"
                                OffsetX="0.5"
                                OffsetY="0.5"
                                Color="#56EFFE" />
                        </Button.Effect>
                        <TextBlock
                            x:Name="MaximizeIcon"
                            FontFamily="Segoe MDL2 Assets"
                            FontSize="14"
                            Foreground="#56EFFE"
                            Text="&#xE922;" />
                    </Button>
                    <Button
                        x:Name="CloseButton"
                        Margin="3,0"
                        Padding="9"
                        Background="Transparent"
                        BorderBrush="#FF0000"
                        BorderThickness="2"
                        Click="CloseButton_Click"
                        CornerRadius="5">
                        <Button.Effect>
                            <DropShadowEffect
                                BlurRadius="10"
                                OffsetX="0.5"
                                OffsetY="0.5"
                                Color="#FF0000" />
                        </Button.Effect>
                        <TextBlock
                            FontFamily="Segoe MDL2 Assets"
                            FontSize="14"
                            Foreground="#FF0000"
                            Text="&#xE8BB;" />
                    </Button>
                </StackPanel>

            </Grid>
        </Border>
        <TransitioningContentControl Grid.Row="1" Content="{Binding Content}" />

        <!--<ContentControl Grid.Row="1" Content="{Binding Content}">
            <ContentControl.ContentTemplate>
                <TransitioningContentControl CacheViews="True" Content="{Binding ContentViewModel}" />
        -->
        <!--<presentation:ViewLocator />-->
        <!--
            </ContentControl.ContentTemplate>
        </ContentControl>-->
    </Grid>
</UserControl>