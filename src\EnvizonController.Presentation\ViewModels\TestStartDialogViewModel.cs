using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HanumanInstitute.MvvmDialogs;
using EnvizonController.Presentation.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.ApiClient.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace EnvizonController.Presentation.ViewModels
{
    /// <summary>
    /// 测试启动对话框视图模型
    /// </summary>
    public partial class TestStartDialogViewModel : ViewModelBase, IModalDialogViewModel, ICloseable
    {
        private readonly IProgramApiService _programApiService;
        private readonly IProgramLinkApiService _programLinkApiService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public TestStartDialogViewModel(IProgramApiService programApiService, IProgramLinkApiService programLinkApiService)
        {
            _programApiService = programApiService;
            _programLinkApiService = programLinkApiService;

            // 初始化配置对象
            TestConfiguration = new TestConfiguration();
            InitializeStartupModes();
            InitializeTestModes();

            // 加载程式和链接列表
            _ = LoadProgramsAndLinksAsync();
        }

        #region 属性

        /// <summary>
        /// 当前步骤（0=基本信息，1=模式配置）
        /// </summary>
        [ObservableProperty]
        private int _currentStep = 0;

        partial void OnCurrentStepChanged(int value)
        {
            OnPropertyChanged(nameof(IsBasicInfoStepVisible));
            OnPropertyChanged(nameof(IsConfigStepVisible));
            NextStepCommand.NotifyCanExecuteChanged();
            PreviousStepCommand.NotifyCanExecuteChanged();
            StartTestCommand.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// 测试配置
        /// </summary>
        [ObservableProperty]
        private TestConfiguration _testConfiguration;

        /// <summary>
        /// 启动模式选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<StartupModeItem> _startupModes = new();

        /// <summary>
        /// 测试模式选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TestModeItem> _testModes = new();

        /// <summary>
        /// 程式列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ProgramItem> _programs = new();

        /// <summary>
        /// 链接列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ProgramLinkItem> _programLinks = new();

        /// <summary>
        /// 是否正在加载
        /// </summary>
        [ObservableProperty]
        private bool _isLoading = false;

        partial void OnIsLoadingChanged(bool value)
        {
            NextStepCommand.NotifyCanExecuteChanged();
            PreviousStepCommand.NotifyCanExecuteChanged();
            StartTestCommand.NotifyCanExecuteChanged();
            CancelCommand.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool? DialogResult { get; private set; }

        /// <summary>
        /// 测试名称（用于向后兼容）
        /// </summary>
        public string TestName => TestConfiguration?.TestName ?? string.Empty;

        /// <summary>
        /// 测试备注（用于向后兼容）
        /// </summary>
        public string? TestRemark => TestConfiguration?.TestRemark;

        /// <summary>
        /// 是否显示基本信息步骤
        /// </summary>
        public bool IsBasicInfoStepVisible => CurrentStep == 0;

        /// <summary>
        /// 是否显示配置步骤
        /// </summary>
        public bool IsConfigStepVisible => CurrentStep == 1;

        #endregion

        #region 辅助类

        public class StartupModeItem
        {
            public StartupMode Mode { get; set; }
            public string DisplayName { get; set; } = string.Empty;
        }

        public class TestModeItem
        {
            public TestMode Mode { get; set; }
            public string DisplayName { get; set; } = string.Empty;
        }

        public class ProgramItem
        {
            public long Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }

        public class ProgramLinkItem
        {
            public long Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化启动模式选项
        /// </summary>
        private void InitializeStartupModes()
        {
            StartupModes.Clear();
            StartupModes.Add(new StartupModeItem { Mode = StartupMode.ColdStart, DisplayName = "冷启动" });
            StartupModes.Add(new StartupModeItem { Mode = StartupMode.ResetStart, DisplayName = "复位启动" });
            StartupModes.Add(new StartupModeItem { Mode = StartupMode.HotStart, DisplayName = "热启动" });
        }

        /// <summary>
        /// 初始化测试模式选项
        /// </summary>
        private void InitializeTestModes()
        {
            TestModes.Clear();
            TestModes.Add(new TestModeItem { Mode = TestMode.FixedValue, DisplayName = "定值模式" });
            TestModes.Add(new TestModeItem { Mode = TestMode.Timer, DisplayName = "定时模式" });
            TestModes.Add(new TestModeItem { Mode = TestMode.Program, DisplayName = "程式模式" });
            TestModes.Add(new TestModeItem { Mode = TestMode.Link, DisplayName = "链接模式" });
        }

        /// <summary>
        /// 加载程式和链接列表
        /// </summary>
        private async Task LoadProgramsAndLinksAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                // 加载程式列表
                var programsResult = await _programApiService.GetProgramsAsync(1, 100);
                if (programsResult.IsSuccess && programsResult.Data?.Items != null)
                {
                    Programs.Clear();
                    foreach (var program in programsResult.Data.Items)
                    {
                        Programs.Add(new ProgramItem { Id = program.Id, Name = program.Name });
                    }
                }

                // 加载程式链接列表
                var linksResult = await _programLinkApiService.GetProgramLinksAsync(1, 100);
                if (linksResult.IsSuccess && linksResult.Data?.Items != null)
                {
                    ProgramLinks.Clear();
                    foreach (var link in linksResult.Data.Items)
                    {
                        ProgramLinks.Add(new ProgramLinkItem { Id = link.Id, Name = link.Name });
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"加载数据失败：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 下一步命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanNextStep))]
        private void NextStep()
        {
            if (CurrentStep == 0)
            {
                // 验证基本信息
                if (!ValidateBasicInfo())
                    return;

                // 初始化配置对象
                InitializeConfigurationForMode();
                CurrentStep = 1;
            }
        }

        /// <summary>
        /// 上一步命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanPreviousStep))]
        private void PreviousStep()
        {
            if (CurrentStep > 0)
            {
                CurrentStep--;
            }
        }

        /// <summary>
        /// 开始测试命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanStartTest))]
        private void StartTest()
        {
            if (!ValidateTestConfiguration())
                return;

            DialogResult = true;
            OnPropertyChanged(nameof(DialogResult));
            RequestClose?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 取消命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanCancel))]
        private void Cancel()
        {
            DialogResult = false;
            OnPropertyChanged(nameof(DialogResult));
            RequestClose?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证基本信息
        /// </summary>
        private bool ValidateBasicInfo()
        {
            ErrorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(TestConfiguration.TestName))
            {
                ErrorMessage = "测试名称不能为空";
                return false;
            }

            if (TestConfiguration.TestName.Length < 2 || TestConfiguration.TestName.Length > 50)
            {
                ErrorMessage = "测试名称长度必须为2-50个字符";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证测试配置
        /// </summary>
        private bool ValidateTestConfiguration()
        {
            ErrorMessage = string.Empty;

            // 验证基本信息
            if (!ValidateBasicInfo())
                return false;

            // 根据测试模式验证配置
            return TestConfiguration.TestMode switch
            {
                TestMode.FixedValue => ValidateFixedValueConfig(),
                TestMode.Timer => ValidateTimerConfig(),
                TestMode.Program => ValidateProgramConfig(),
                TestMode.Link => ValidateLinkConfig(),
                _ => false
            };
        }

        /// <summary>
        /// 验证定值模式配置
        /// </summary>
        private bool ValidateFixedValueConfig()
        {
            var config = TestConfiguration.FixedValueConfig;

            if (config.Temperature < -100 || config.Temperature > 200)
            {
                ErrorMessage = "温度设定值必须在-100到200之间";
                return false;
            }

            if (config.Humidity < 0 || config.Humidity > 100)
            {
                ErrorMessage = "湿度设定值必须在0到100之间";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证定时模式配置
        /// </summary>
        private bool ValidateTimerConfig()
        {
            var config = TestConfiguration.TimerConfig;

            // 验证温度和湿度
            if (config.Temperature < -100 || config.Temperature > 200)
            {
                ErrorMessage = "温度设定值必须在-100到200之间";
                return false;
            }

            if (config.Humidity < 0 || config.Humidity > 100)
            {
                ErrorMessage = "湿度设定值必须在0到100之间";
                return false;
            }

            // 验证定时设置
            if (config.Days == 0 && config.Hours == 0 && config.Minutes == 0)
            {
                ErrorMessage = "定时设置不能全为0，请至少设置一个时间值";
                return false;
            }

            if (config.Days < 0 || config.Days > 365)
            {
                ErrorMessage = "天数必须在0到365之间";
                return false;
            }

            if (config.Hours < 0 || config.Hours > 23)
            {
                ErrorMessage = "小时数必须在0到23之间";
                return false;
            }

            if (config.Minutes < 0 || config.Minutes > 59)
            {
                ErrorMessage = "分钟数必须在0到59之间";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证程式模式配置
        /// </summary>
        private bool ValidateProgramConfig()
        {
            var config = TestConfiguration.ProgramConfig;

            if (!config.SelectedProgramId.HasValue || config.SelectedProgramId <= 0)
            {
                ErrorMessage = "请选择一个程式";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证链接模式配置
        /// </summary>
        private bool ValidateLinkConfig()
        {
            var config = TestConfiguration.LinkConfig;

            if (!config.SelectedLinkId.HasValue || config.SelectedLinkId <= 0)
            {
                ErrorMessage = "请选择一个链接";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 初始化当前模式的配置
        /// </summary>
        private void InitializeConfigurationForMode()
        {
            // 根据当前选择的模式初始化默认值
            switch (TestConfiguration.TestMode)
            {
                case TestMode.FixedValue:
                    // 定值模式已有默认值
                    break;
                case TestMode.Timer:
                    // 定时模式已有默认值
                    break;
                case TestMode.Program:
                    // 程式模式：确保有默认选择
                    if (Programs.Any() && !TestConfiguration.ProgramConfig.SelectedProgramId.HasValue)
                    {
                        var firstProgram = Programs.First();
                        TestConfiguration.ProgramConfig.SelectedProgramId = firstProgram.Id;
                        TestConfiguration.ProgramConfig.SelectedProgramName = firstProgram.Name;
                    }
                    break;
                case TestMode.Link:
                    // 链接模式：确保有默认选择
                    if (ProgramLinks.Any() && !TestConfiguration.LinkConfig.SelectedLinkId.HasValue)
                    {
                        var firstLink = ProgramLinks.First();
                        TestConfiguration.LinkConfig.SelectedLinkId = firstLink.Id;
                        TestConfiguration.LinkConfig.SelectedLinkName = firstLink.Name;
                    }
                    break;
            }
        }

        #endregion

        #region 命令CanExecute方法

        /// <summary>
        /// 下一步命令是否可执行
        /// </summary>
        private bool CanNextStep() => CurrentStep == 0 && !IsLoading;

        /// <summary>
        /// 上一步命令是否可执行
        /// </summary>
        private bool CanPreviousStep() => CurrentStep > 0 && !IsLoading;

        /// <summary>
        /// 开始测试命令是否可执行
        /// </summary>
        private bool CanStartTest() => CurrentStep == 1 && !IsLoading;

        /// <summary>
        /// 取消命令是否可执行
        /// </summary>
        private bool CanCancel() => !IsLoading;

        #endregion

        public event EventHandler? RequestClose;
    }
}