using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 程式步骤仓储接口
    /// </summary>
    public interface IProgramStepRepository : IRepository<ProgramStep, long>
    {
        /// <summary>
        /// 根据程式ID获取所有程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>程式步骤集合</returns>
        Task<IEnumerable<ProgramStep>> GetByProgramIdAsync(long programId);

        /// <summary>
        /// 根据程式ID和步骤索引获取程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <param name="index">步骤索引</param>
        /// <returns>程式步骤，如果未找到则返回null</returns>
        Task<ProgramStep?> GetByProgramIdAndIndexAsync(long programId, int index);

        /// <summary>
        /// 根据程式ID删除所有程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>操作任务</returns>
        Task DeleteByProgramIdAsync(long programId);
    }
} 