<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramLinkStepsView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="using:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="600"
    x:DataType="vm:ProgramLinkViewModel"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:ProgramDtoToProgramItemViewModelConverter x:Key="ProgramDtoToProgramItemViewModelConverter" />
    </UserControl.Resources>

    <Grid Margin="15" RowDefinitions="Auto,*,Auto,Auto">
        <!--  顶部标题和刷新按钮  -->
        <Grid
            Grid.Row="0"
            Margin="0,0,0,15"
            ColumnDefinitions="*,Auto">
            <TextBlock
                Grid.Column="0"
                Classes="h3 glow-primary font-cyber"
                Text="程式链接项列表" />

            <Button
                Grid.Column="1"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#0DF0FF"
                BorderThickness="1"
                Command="{Binding RefreshProgramLinksCommand}"
                CornerRadius="4">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon primary"
                        Text="&#xf021;" />
                    <TextBlock Classes="primary font-cyber" Text="刷新步骤" />
                </StackPanel>
            </Button>
        </Grid>

        <!--  步骤列表  -->
        <DataGrid
            Grid.Row="1"
            Margin="0,0,0,10"
            AutoGenerateColumns="False"
            Background="Transparent"
            BorderBrush="#2A2942"
            BorderThickness="1"
            CanUserReorderColumns="False"
            CanUserResizeColumns="True"
            CanUserSortColumns="False"
            GridLinesVisibility="All"
            ItemsSource="{Binding ProgramLinkStepItems}"
            SelectedItem="{Binding SelectedLinkStepItem}"
            SelectionMode="Single">
            <DataGrid.Styles>
                <Style Selector="DataGridRow:selected">
                    <Setter Property="Background" Value="#2A2942" />
                    <Setter Property="BorderBrush" Value="#0DF0FF" />
                    <Setter Property="BorderThickness" Value="1" />
                </Style>
                <Style Selector="DataGridCell">
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="Padding" Value="10,8" />
                </Style>
                <Style Selector="DataGridColumnHeader">
                    <Setter Property="Background" Value="#14131C" />
                    <Setter Property="Foreground" Value="#FFFFFF" />
                    <Setter Property="Padding" Value="10,8" />
                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                    <Setter Property="BorderBrush" Value="#2A2942" />
                </Style>
            </DataGrid.Styles>
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding ExecutionOrder}"
                    Header="NO"
                    IsReadOnly="True" />

                <DataGridTemplateColumn Width="*" Header="程式">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Program.Name}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <ComboBox
                                VerticalAlignment="Center"
                                DisplayMemberBinding="{Binding Name}"
                                ItemsSource="{Binding $parent[UserControl].((vm:ProgramLinkViewModel)DataContext).AvailablePrograms}"
                                SelectedValue="{Binding Program, Delay=500}"
                                SelectedValueBinding="{Binding Program, Delay=500}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="180" Header="操作">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Panel>
                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                    <!--  删除按钮  -->
                                    <Button
                                        Margin="2,0"
                                        Padding="5"
                                        Background="Transparent"
                                        BorderBrush="#FF5252"
                                        BorderThickness="1"
                                        Command="{Binding $parent[UserControl].((vm:ProgramLinkViewModel)DataContext).ConfirmDeleteLinkStepCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4"
                                        ToolTip.Tip="删除">
                                        <TextBlock
                                            Classes="font-icon"
                                            Foreground="#FF5252"
                                            Text="&#xf1f8;" />
                                    </Button>
                                </StackPanel>


                            </Panel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>


                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!--  操作按钮  -->
        <Grid Grid.Row="2" ColumnDefinitions="*,Auto,Auto,Auto">
            <Border
                Padding="10"
                Background="#0F0E1D"
                BorderBrush="#0DF0FF"
                BorderThickness="1"
                CornerRadius="4">
                <Grid ColumnDefinitions="*,Auto">
                    <ComboBox
                        Grid.Column="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        ItemsSource="{Binding AvailablePrograms}"
                        SelectedItem="{Binding SelectedProgram}">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Name}" />
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>

                    <Button
                        Grid.Column="1"
                        Margin="10,0,0,0"
                        Padding="12,8"
                        VerticalAlignment="Center"
                        Background="Transparent"
                        BorderBrush="#0DF0FF"
                        BorderThickness="1"
                        Command="{Binding AddProgramToLinkCommand}"
                        CornerRadius="4">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="0,0,5,0"
                                Classes="font-icon primary"
                                Text="&#xf067;" />
                            <TextBlock Classes="primary font-cyber" Text="添加程式" />
                        </StackPanel>
                    </Button>
                </Grid>
            </Border>

            <Button
                Grid.Column="1"
                Margin="5,0"
                Padding="12,6"
                VerticalAlignment="Center"
                Background="Transparent"
                BorderBrush="#4CAF50"
                BorderThickness="1"
                Command="{Binding SaveAllStepsCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#4CAF50"
                        Text="&#xf0c7;" />
                    <TextBlock Foreground="#4CAF50" Text="保存所有更改" />
                </StackPanel>
            </Button>

            <Button
                Grid.Column="2"
                Margin="5,0"
                Padding="12,6"
                VerticalAlignment="Center"
                Background="Transparent"
                BorderBrush="#FFC107"
                BorderThickness="1"
                Command="{Binding MoveLinkItemUpCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#FFC107"
                        Text="&#xf062;" />
                    <TextBlock Foreground="#FFC107" Text="上移" />
                </StackPanel>
            </Button>
            <Button
                Grid.Column="3"
                Margin="5,0"
                Padding="12,6"
                VerticalAlignment="Center"
                Background="Transparent"
                BorderBrush="#FF9800"
                BorderThickness="1"
                Command="{Binding MoveLinkItemDownCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#FF9800"
                        Text="&#xf063;" />
                    <TextBlock Foreground="#FF9800" Text="下移" />
                </StackPanel>
            </Button>
        </Grid>


    </Grid>
</UserControl> 