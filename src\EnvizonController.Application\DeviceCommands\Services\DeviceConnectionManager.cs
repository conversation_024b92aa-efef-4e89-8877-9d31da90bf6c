using EnvizonController.Application.Devices;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Models;
using EnvizonController.Application.Interfaces.Cache;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace EnvizonController.Application.DeviceCommands.Services
{
    /// <summary>
    /// 设备连接管理器实现
    /// 负责管理设备连接的生命周期、连接池和连接状态监控
    /// </summary>
    public class DeviceConnectionManager : IDeviceConnectionManager
    {
        private readonly IModbusDeviceServiceCache _deviceServiceCache;
        private readonly IDeviceRepository _deviceRepository;
        private readonly ILogger<DeviceConnectionManager> _logger;
        private readonly ConcurrentDictionary<long, ConnectionInfo> _connectionInfoCache;
        private readonly SemaphoreSlim _connectionSemaphore;
        private readonly Timer _cleanupTimer;
        private readonly object _lockObject = new object();

        // 连接配置常量
        private const int DefaultConnectionTimeout = 5000;
        private const int DefaultMaxRetryAttempts = 3;
        private const int DefaultRetryDelayMs = 1000;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceServiceCache">设备服务缓存</param>
        /// <param name="deviceRepository">设备仓储</param>
        /// <param name="logger">日志记录器</param>
        public DeviceConnectionManager(
            IModbusDeviceServiceCache deviceServiceCache,
            IDeviceRepository deviceRepository,
            ILogger<DeviceConnectionManager> logger)
        {
            _deviceServiceCache = deviceServiceCache ?? throw new ArgumentNullException(nameof(deviceServiceCache));
            _deviceRepository = deviceRepository ?? throw new ArgumentNullException(nameof(deviceRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _connectionInfoCache = new ConcurrentDictionary<long, ConnectionInfo>();
            _connectionSemaphore = new SemaphoreSlim(1, 1);
            
            // 设置定时清理任务，每5分钟执行一次
            _cleanupTimer = new Timer(async _ => await CleanupIdleConnectionsAsync(TimeSpan.FromMinutes(10)), 
                null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// 获取设备的Modbus服务
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>Modbus设备服务</returns>
        public async Task<IModbusDeviceService> GetDeviceServiceAsync(long deviceId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("获取设备服务，设备ID: {DeviceId}", deviceId);

                // 1. 从仓储获取设备信息
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null)
                {
                    throw new ArgumentException($"设备不存在，设备ID: {deviceId}", nameof(deviceId));
                }

                // 2. 从缓存获取或创建设备服务
                var deviceService = _deviceServiceCache.GetOrCreate(device);
                
                // 3. 更新连接信息
                UpdateConnectionInfo(deviceId, deviceService.IsConnected, "服务获取成功");

                _logger.LogDebug("设备服务获取成功，设备ID: {DeviceId}, 连接状态: {IsConnected}", 
                    deviceId, deviceService.IsConnected);

                return deviceService;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备服务失败，设备ID: {DeviceId}", deviceId);
                UpdateConnectionInfo(deviceId, false, $"获取服务失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 确保设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> EnsureConnectionAsync(long deviceId, CancellationToken cancellationToken = default)
        {
            await _connectionSemaphore.WaitAsync(cancellationToken);
            try
            {
                _logger.LogInformation("确保设备连接，设备ID: {DeviceId}", deviceId);

                var deviceService = await GetDeviceServiceAsync(deviceId, cancellationToken);
                
                // 如果已连接，直接返回成功
                if (deviceService.IsConnected)
                {
                    _logger.LogDebug("设备已连接，设备ID: {DeviceId}", deviceId);
                    UpdateConnectionInfo(deviceId, true, "已连接");
                    return true;
                }

                // 尝试连接设备
                for (int attempt = 1; attempt <= DefaultMaxRetryAttempts; attempt++)
                {
                    try
                    {
                        _logger.LogDebug("尝试连接设备，设备ID: {DeviceId}, 尝试次数: {Attempt}/{MaxAttempts}", 
                            deviceId, attempt, DefaultMaxRetryAttempts);

                        UpdateConnectionInfo(deviceId, false, $"正在连接 (尝试 {attempt}/{DefaultMaxRetryAttempts})");

                        bool connected = await deviceService.ConnectAsync(cancellationToken);
                        
                        if (connected)
                        {
                            _logger.LogInformation("设备连接成功，设备ID: {DeviceId}, 尝试次数: {Attempt}", 
                                deviceId, attempt);
                            UpdateConnectionInfo(deviceId, true, "连接成功");
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "设备连接失败，设备ID: {DeviceId}, 尝试次数: {Attempt}", 
                            deviceId, attempt);
                        
                        if (attempt == DefaultMaxRetryAttempts)
                        {
                            UpdateConnectionInfo(deviceId, false, $"连接失败: {ex.Message}");
                        }
                    }

                    // 如果不是最后一次尝试，等待一段时间再重试
                    if (attempt < DefaultMaxRetryAttempts)
                    {
                        await Task.Delay(DefaultRetryDelayMs * attempt, cancellationToken);
                    }
                }

                _logger.LogError("设备连接失败，已达到最大重试次数，设备ID: {DeviceId}", deviceId);
                UpdateConnectionInfo(deviceId, false, "连接失败，已达到最大重试次数");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确保设备连接时发生异常，设备ID: {DeviceId}", deviceId);
                UpdateConnectionInfo(deviceId, false, $"连接异常: {ex.Message}");
                return false;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否断开成功</returns>
        public async Task<bool> DisconnectDeviceAsync(long deviceId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("断开设备连接，设备ID: {DeviceId}", deviceId);

                var deviceService = await GetDeviceServiceAsync(deviceId, cancellationToken);
                
                if (!deviceService.IsConnected)
                {
                    _logger.LogDebug("设备已断开连接，设备ID: {DeviceId}", deviceId);
                    UpdateConnectionInfo(deviceId, false, "已断开连接");
                    return true;
                }

                bool disconnected = await deviceService.DisconnectAsync(cancellationToken);
                
                if (disconnected)
                {
                    _logger.LogInformation("设备断开连接成功，设备ID: {DeviceId}", deviceId);
                    UpdateConnectionInfo(deviceId, false, "断开连接成功");
                }
                else
                {
                    _logger.LogWarning("设备断开连接失败，设备ID: {DeviceId}", deviceId);
                    UpdateConnectionInfo(deviceId, deviceService.IsConnected, "断开连接失败");
                }

                return disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开设备连接时发生异常，设备ID: {DeviceId}", deviceId);
                UpdateConnectionInfo(deviceId, false, $"断开连接异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取连接池状态
        /// </summary>
        /// <returns>连接池状态</returns>
        public Task<ConnectionPoolStatus> GetConnectionPoolStatusAsync()
        {
            try
            {
                var status = new ConnectionPoolStatus();
                
                lock (_lockObject)
                {
                    status.DeviceConnections = new Dictionary<long, ConnectionInfo>(_connectionInfoCache);
                    status.TotalConnections = status.DeviceConnections.Count;
                    status.ActiveConnections = status.DeviceConnections.Values.Count(c => c.IsConnected);
                    status.IdleConnections = status.TotalConnections - status.ActiveConnections;
                }

                _logger.LogDebug("获取连接池状态，总连接数: {Total}, 活跃连接数: {Active}, 空闲连接数: {Idle}", 
                    status.TotalConnections, status.ActiveConnections, status.IdleConnections);

                return Task.FromResult(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接池状态时发生异常");
                throw;
            }
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        /// <param name="idleTimeout">空闲超时时间</param>
        /// <returns>清理的连接数</returns>
        public async Task<int> CleanupIdleConnectionsAsync(TimeSpan idleTimeout)
        {
            int cleanedCount = 0;
            var cutoffTime = DateTime.Now - idleTimeout;

            try
            {
                _logger.LogDebug("开始清理空闲连接，超时时间: {IdleTimeout}", idleTimeout);

                var connectionsToCleanup = new List<long>();

                lock (_lockObject)
                {
                    foreach (var kvp in _connectionInfoCache)
                    {
                        var connectionInfo = kvp.Value;
                        if (connectionInfo.LastUsed < cutoffTime && !connectionInfo.IsConnected)
                        {
                            connectionsToCleanup.Add(kvp.Key);
                        }
                    }
                }

                foreach (var deviceId in connectionsToCleanup)
                {
                    try
                    {
                        // 从缓存中移除连接信息
                        if (_connectionInfoCache.TryRemove(deviceId, out var removedConnection))
                        {
                            _logger.LogDebug("清理空闲连接，设备ID: {DeviceId}, 最后使用时间: {LastUsed}", 
                                deviceId, removedConnection.LastUsed);
                            
                            // 从设备服务缓存中移除
                            var device = await _deviceRepository.GetByIdAsync(deviceId);
                            if (device != null)
                            {
                                _deviceServiceCache.Remove(device);
                            }
                            
                            cleanedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "清理设备连接时发生异常，设备ID: {DeviceId}", deviceId);
                    }
                }

                _logger.LogInformation("空闲连接清理完成，清理数量: {CleanedCount}", cleanedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理空闲连接时发生异常");
            }

            return cleanedCount;
        }

        /// <summary>
        /// 检查设备是否在线
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>是否在线</returns>
        public async Task<bool> IsDeviceOnlineAsync(long deviceId)
        {
            try
            {
                _logger.LogDebug("检查设备是否在线，设备ID: {DeviceId}", deviceId);

                var deviceService = await GetDeviceServiceAsync(deviceId);
                bool isOnline = deviceService.IsConnected;

                _logger.LogDebug("设备在线状态检查完成，设备ID: {DeviceId}, 在线状态: {IsOnline}", 
                    deviceId, isOnline);

                return isOnline;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查设备在线状态时发生异常，设备ID: {DeviceId}", deviceId);
                return false;
            }
        }

        /// <summary>
        /// 获取设备连接信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接信息</returns>
        public Task<ConnectionInfo?> GetConnectionInfoAsync(long deviceId)
        {
            try
            {
                _connectionInfoCache.TryGetValue(deviceId, out var connectionInfo);
                
                _logger.LogDebug("获取设备连接信息，设备ID: {DeviceId}, 连接信息存在: {HasInfo}", 
                    deviceId, connectionInfo != null);

                return Task.FromResult(connectionInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备连接信息时发生异常，设备ID: {DeviceId}", deviceId);
                return Task.FromResult<ConnectionInfo?>(null);
            }
        }

        /// <summary>
        /// 更新连接信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="isConnected">是否已连接</param>
        /// <param name="status">状态描述</param>
        private void UpdateConnectionInfo(long deviceId, bool isConnected, string status)
        {
            lock (_lockObject)
            {
                _connectionInfoCache.AddOrUpdate(deviceId,
                    // 添加新连接信息
                    new ConnectionInfo
                    {
                        DeviceId = deviceId,
                        IsConnected = isConnected,
                        LastUsed = DateTime.Now,
                        UseCount = 1,
                        Status = status
                    },
                    // 更新现有连接信息
                    (key, existing) =>
                    {
                        existing.IsConnected = isConnected;
                        existing.LastUsed = DateTime.Now;
                        existing.UseCount++;
                        existing.Status = status;
                        return existing;
                    });
            }

            _logger.LogDebug("更新连接信息，设备ID: {DeviceId}, 连接状态: {IsConnected}, 状态: {Status}", 
                deviceId, isConnected, status);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _logger.LogInformation("正在释放设备连接管理器资源");

                _cleanupTimer?.Dispose();
                _connectionSemaphore?.Dispose();
                
                // 清理所有连接信息
                _connectionInfoCache.Clear();

                _logger.LogInformation("设备连接管理器资源释放完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放设备连接管理器资源时发生异常");
            }
        }
    }
} 