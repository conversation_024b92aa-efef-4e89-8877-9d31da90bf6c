﻿using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Shared.DTOs;

/// <summary>
/// 设备DTO - 用于API响应
/// </summary>
public class DeviceDto
{
    public long Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public byte SlaveId { get; set; }
    public long ProtocolId { get; set; }
    public string ConnectionType { get; set; } = string.Empty;
    public string TransportType { get; set; } = string.Empty;

    // 串口连接参数
    public string? PortName { get; set; }
    public int? BaudRate { get; set; }
    public int? DataBits { get; set; }
    public int? Parity { get; set; }
    public int? StopBits { get; set; }

    // 网络连接参数
    public string? HostAddress { get; set; }
    public int? Port { get; set; }

    // 连接超时参数
    public int ConnectionTimeout { get; set; }
    public int ReadTimeout { get; set; }
    public int WriteTimeout { get; set; }

    // 状态信息
    public DeviceStatus Status { get; set; }
    public ConnectionStatus ConnectionStatus { get; set; }
    /// <summary>
    /// 设备运行状态
    /// </summary>
    public DeviceOperatingStatus OperatingStatus { get; set; } = DeviceOperatingStatus.Unknown;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdatedAt { get; set; }
    public DateTime? LastConnectedAt { get; set; }

    // 其他配置
    public string Remarks { get; set; } = string.Empty;
    public bool AutoStart { get; set; }
    public int CollectionIntervalMs { get; set; }

    public bool IsSelected { get; set; }
}

/// <summary>
/// 创建设备DTO - 用于API请求
/// </summary>
public class CreateDeviceDto
{
    public string Name { get; set; } = string.Empty;
    public byte SlaveId { get; set; } = 1;
    public long ProtocolId { get; set; }
    public string ConnectionType { get; set; } = string.Empty;
    public string TransportType { get; set; } = string.Empty;

    // 串口连接参数
    public string? PortName { get; set; }
    public int? BaudRate { get; set; }
    public int? DataBits { get; set; }
    public int? Parity { get; set; }
    public int? StopBits { get; set; }

    // 网络连接参数
    public string? HostAddress { get; set; }
    public int? Port { get; set; }

    // 连接超时参数
    public int ConnectionTimeout { get; set; } = 5000;
    public int ReadTimeout { get; set; } = 1000;
    public int WriteTimeout { get; set; } = 1000;

    // 其他配置
    public string Remarks { get; set; } = string.Empty;
    public bool AutoStart { get; set; } = false;
    public int CollectionIntervalMs { get; set; } = 1000;
}

/// <summary>
/// 更新设备DTO - 用于API请求
/// </summary>
public class UpdateDeviceDto
{
    public string Name { get; set; } = string.Empty;
    public byte SlaveId { get; set; }
    public long ProtocolId { get; set; }
    public string ConnectionType { get; set; } = string.Empty;
    public string TransportType { get; set; } = string.Empty;

    // 串口连接参数
    public string? PortName { get; set; }
    public int? BaudRate { get; set; }
    public int? DataBits { get; set; }
    public int? Parity { get; set; }
    public int? StopBits { get; set; }

    // 网络连接参数
    public string? HostAddress { get; set; }
    public int? Port { get; set; }

    // 连接超时参数
    public int ConnectionTimeout { get; set; }
    public int ReadTimeout { get; set; }
    public int WriteTimeout { get; set; }

    // 状态信息
    public DeviceStatus Status { get; set; }

    // 其他配置
    public string Remarks { get; set; } = string.Empty;
    public bool AutoStart { get; set; }
    public int CollectionIntervalMs { get; set; }
}