﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 对话框请求消息
/// </summary>
public class DialogRequestMessage : ValueChangedMessage<object>
{
    /// <summary>
    /// 对话框视图模型
    /// </summary>
    public object DialogViewModel => Value;

    /// <summary>
    /// 创建对话框请求消息
    /// </summary>
    /// <param name="dialogViewModel">对话框视图模型</param>
    public DialogRequestMessage(object dialogViewModel) : base(dialogViewModel)
    {
    }
}
