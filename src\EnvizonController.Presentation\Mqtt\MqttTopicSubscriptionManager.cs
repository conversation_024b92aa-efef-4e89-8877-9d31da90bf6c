using EnvizonController.Mqtt.Client.Handlers;
using EnvizonController.Mqtt.Client.Services;
using EnvizonController.Shared.Enums;
using MQTTnet.Protocol;
using Serilog;

namespace EnvizonController.Presentation.Mqtt;

/// <summary>
///     MQTT主题订阅管理器
/// </summary>
public class MqttTopicSubscriptionManager
{
    private readonly IMqttConnectionManager _connectionManager;
    private readonly IMessageHandlerRegistry _handlerRegistry;
    private readonly ILogger _logger;
    private readonly IMqttClientService _mqttClient;
    private readonly Dictionary<string, MqttQualityOfServiceLevel> _subscriptions = new();

    /// <summary>
    ///     初始化 <see cref="MqttTopicSubscriptionManager" /> 类的新实例
    /// </summary>
    /// <param name="mqttClient">MQTT客户端服务</param>
    /// <param name="handlerRegistry">消息处理器注册表</param>
    /// <param name="connectionManager">MQTT连接管理器</param>
    /// <param name="logger">日志记录器</param>
    public MqttTopicSubscriptionManager(
        IMqttClientService mqttClient,
        IMessageHandlerRegistry handlerRegistry,
        IMqttConnectionManager connectionManager,
        ILogger logger)
    {
        _mqttClient = mqttClient ?? throw new ArgumentNullException(nameof(mqttClient));
        _handlerRegistry = handlerRegistry ?? throw new ArgumentNullException(nameof(handlerRegistry));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _logger = logger.ForContext<MqttTopicSubscriptionManager>() ?? throw new ArgumentNullException(nameof(logger));

        // 订阅连接状态变更事件，在重新连接后恢复订阅
        _connectionManager.ConnectionStatusChanged += OnConnectionStatusChanged;
    }

    /// <summary>
    ///     订阅MQTT主题并注册消息处理器
    /// </summary>
    /// <param name="topic">要订阅的主题</param>
    /// <param name="handler">消息处理器</param>
    /// <param name="qos">服务质量级别</param>
    /// <returns>表示异步操作的任务</returns>
    public async Task SubscribeAsync(string topic, IMessageHandler handler,
        MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce)
    {
        // 注册消息处理器
        _handlerRegistry.RegisterHandler(topic, handler);

        // 如果当前已连接，订阅主题
        if (_connectionManager.Status == ConnectionStatus.Connected)
            try
            {
                await _mqttClient.SubscribeAsync(topic, qos);
                _logger.Information($"已订阅主题: {topic}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"订阅主题失败: {topic}");
                throw;
            }

        // 记录订阅，以便在重新连接时恢复
        _subscriptions[topic] = qos;
    }

    /// <summary>
    ///     取消订阅MQTT主题并解除消息处理器注册
    /// </summary>
    /// <param name="topic">要取消订阅的主题</param>
    /// <param name="handler">消息处理器</param>
    /// <returns>表示异步操作的任务</returns>
    public async Task UnsubscribeAsync(string topic, IMessageHandler handler)
    {
        // 解除消息处理器注册
        _handlerRegistry.UnregisterHandler(topic, handler);

        // 检查是否还有其他处理器订阅此主题
        var remainingHandlers = _handlerRegistry.GetHandlers(topic);
        if (!remainingHandlers.Any())
        {
            // 如果没有其他处理器，取消订阅主题
            if (_connectionManager.Status == ConnectionStatus.Connected)
                try
                {
                    await _mqttClient.UnsubscribeAsync(topic);
                    _logger.Information($"已取消订阅主题: {topic}");
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"取消订阅主题失败: {topic}");
                    // 不抛出异常，因为这不应该阻止处理器解除注册
                }

            // 从订阅记录中移除
            _subscriptions.Remove(topic);
        }
    }

    /// <summary>
    ///     获取所有活动订阅
    /// </summary>
    /// <returns>主题和服务质量级别的字典</returns>
    public IReadOnlyDictionary<string, MqttQualityOfServiceLevel> GetActiveSubscriptions()
    {
        return _subscriptions;
    }

    /// <summary>
    ///     连接状态变更事件处理程序
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private async void OnConnectionStatusChanged(object? sender, MqttConnectionStatusChangedEventArgs e)
    {
        try
        {
            // 当连接恢复时，重新订阅所有主题
            if (e.NewStatus == ConnectionStatus.Connected) await ResubscribeAllAsync();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "重新订阅主题失败");
        }
    }

    /// <summary>
    ///     重新订阅所有主题
    /// </summary>
    /// <returns>表示异步操作的任务</returns>
    private async Task ResubscribeAllAsync()
    {
        _logger.Information("重新建立连接后恢复所有主题订阅");

        foreach (var subscription in _subscriptions)
            try
            {
                await _mqttClient.SubscribeAsync(subscription.Key, subscription.Value);
                _logger.Information($"已恢复订阅主题: {subscription.Key}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"恢复订阅主题失败: {subscription.Key}");
                // 继续尝试订阅其他主题
            }
    }
}