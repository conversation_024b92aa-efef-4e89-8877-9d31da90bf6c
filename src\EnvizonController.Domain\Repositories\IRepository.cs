﻿using System.Linq.Expressions;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Repositories;

/// <summary>
///     通用仓储接口
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
public interface IRepository<TEntity, TKey> where TEntity : BaseEntity<TKey>
{
    /// <summary>
    ///     根据ID获取实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体</returns>
    Task<TEntity> GetByIdAsync(TKey id);

    /// <summary>
    ///     获取所有实体
    /// </summary>
    /// <returns>实体集合</returns>
    Task<IEnumerable<TEntity>> GetAllAsync();

    /// <summary>
    ///     根据条件查询实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>符合条件的实体集合</returns>
    Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate);

    /// <summary>
    ///     添加实体
    /// </summary>
    /// <param name="entity">实体</param>
    Task AddAsync(TEntity entity);


    /// <summary>
    ///     批量添加实体
    /// </summary>
    /// <param name="entities">实体集合</param>
    Task AddRangeAsync(IEnumerable<TEntity> entities);

    /// <summary>
    ///     更新实体
    /// </summary>
    /// <param name="entity">实体</param>
    void Update(TEntity entity);

    /// <summary>
    ///     更新实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>异步任务</returns>
    Task UpdateAsync(TEntity entity);

    /// <summary>
    ///     删除实体
    /// </summary>
    /// <param name="entity">实体</param>
    void Remove(TEntity entity);

    /// <summary>
    ///     根据ID删除实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>异步任务</returns>
    Task DeleteAsync(TKey id);

    /// <summary>
    ///     批量删除实体
    /// </summary>
    /// <param name="entities">实体集合</param>
    void RemoveRange(IEnumerable<TEntity> entities);

    /// <summary>
    ///     判断是否存在符合条件的实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate);

    /// <summary>
    ///     获取符合条件的实体数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体数量</returns>
    Task<int> CountAsync(Expression<Func<TEntity, bool>>? predicate = null);
}