using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Enums;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Domain.Repositories;

/// <summary>
///     报警数据访问接口
/// </summary>
public interface IAlarmRepository : IRepository<Alarm, long>
{
    /// <summary>
    /// 获取特定测试运行的所有报警
    /// </summary>
    /// <param name="testRunId">测试运行ID</param>
    /// <returns>报警列表</returns>
    Task<IEnumerable<Alarm>> GetAlarmsByTestRunIdAsync(long testRunId);

    /// <summary>
    /// 获取活动状态的报警
    /// </summary>
    /// <returns>活动报警列表</returns>
    Task<IEnumerable<Alarm>> GetActiveAlarmsAsync();

    /// <summary>
    /// 获取特定级别的报警
    /// </summary>
    /// <param name="level">报警级别</param>
    /// <returns>特定级别的报警列表</returns>
    Task<IEnumerable<Alarm>> GetAlarmsByLevelAsync(AlarmSeverity level);

    /// <summary>
    /// 获取分页的报警列表（支持多参数查询）
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="status">报警状态（可选）</param>
    /// <param name="level">报警级别（可选）</param>
    /// <param name="startDate">开始日期（可选）</param>
    /// <param name="endDate">结束日期（可选）</param>
    /// <param name="testRunId">测试运行ID（可选）</param>
    /// <returns>元组(报警列表, 总条数)</returns>
    Task<(IEnumerable<Alarm> Alarms, int TotalCount)> GetPagedAlarmsAsync(
        int page,
        int pageSize,
        AlarmStatus? status = null,
        AlarmSeverity? level = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? testRunId = null,
        long? DeviceId = null);
}