﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Presentation.ViewModels.Dashboard
{
    // UI友好类型 - 代表一条环境数据
    public class EnvironmentalDataItemViewModel : ViewModelBase // (假设你有一个BaseViewModel实现INotifyPropertyChanged)
    {
        private string _name;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value); // 来自BaseViewModel
        }

        private object _value; // 可以是 object，也可以根据实际情况转为 string 或 double
        public object Value
        {
            get => _value;
            set => SetProperty(ref _value, value);
        }

        private string _unit;
        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        private string _description;
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        // 如果Value需要特定格式化显示，可以添加一个FormattedValue属性
        public string FormattedValue
        {
            get
            {
                if (Value is double d)
                {
                    return $"{d:F2} {Unit}"; // 例如，保留两位小数并附带单位
                }
                return $"{Value} {Unit}";
            }
        }
    }
}
