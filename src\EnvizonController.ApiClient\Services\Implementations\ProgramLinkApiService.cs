using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 程式链接API服务实现
    /// </summary>
    public class ProgramLinkApiService : IProgramLinkApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="config">配置</param>
        public ProgramLinkApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 获取所有程式链接
        /// </summary>
        public async Task<Result<PagedResultDto<ProgramLinkDTO>>> GetProgramLinksAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<ProgramLinkDTO>>("api/programlinks", queryParams);
        }

        /// <summary>
        /// 根据ID获取程式链接
        /// </summary>
        public async Task<Result<ProgramLinkDTO>> GetProgramLinkAsync(long id)
        {
            return await _httpClient.GetAsync<ProgramLinkDTO>($"api/programlinks/{id}");
        }

        /// <summary>
        /// 根据名称获取程式链接
        /// </summary>
        public async Task<Result<ProgramLinkDTO>> GetProgramLinkByNameAsync(string name)
        {
            return await _httpClient.GetAsync<ProgramLinkDTO>($"api/programlinks/name/{Uri.EscapeDataString(name)}");
        }

        /// <summary>
        /// 创建程式链接
        /// </summary>
        public async Task<Result<ProgramLinkDTO>> CreateProgramLinkAsync(ProgramLinkDTO programLinkDto)
        {
            return await _httpClient.PostAsync<ProgramLinkDTO, ProgramLinkDTO>("api/programlinks", programLinkDto);
        }

        /// <summary>
        /// 更新程式链接
        /// </summary>
        public async Task<Result<ProgramLinkDTO>> UpdateProgramLinkAsync(ProgramLinkDTO programLinkDto)
        {
            return await _httpClient.PutAsync<ProgramLinkDTO, ProgramLinkDTO>($"api/programlinks/{programLinkDto.Id}", programLinkDto);
        }

        /// <summary>
        /// 删除程式链接
        /// </summary>
        public async Task<Result<bool>> DeleteProgramLinkAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/programlinks/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 获取程式链接中的所有链接项
        /// </summary>
        public async Task<Result<IEnumerable<ProgramLinkStepDTO>>> GetProgramLinkItemsAsync(long programLinkId)
        {
            return await _httpClient.GetAsync<IEnumerable<ProgramLinkStepDTO>>($"api/programlinks/{programLinkId}/items");
        }

        /// <summary>
        /// 添加程式到链接中
        /// </summary>
        public async Task<Result<ProgramLinkStepDTO>> AddProgramToProgramLinkAsync(long programLinkId, long programId, int? order = null)
        {
            var requestData = new
            {
                ProgramId = programId,
                ExecutionOrder = order
            };
            
            return await _httpClient.PostAsync<ProgramLinkStepDTO, object>($"api/programlinks/{programLinkId}/items", requestData);
        }

        /// <summary>
        /// 从程式链接中移除链接项
        /// </summary>
        public async Task<Result<bool>> RemoveLinkItemAsync(long programLinkId, long linkItemId)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/programlinks/{programLinkId}/items/{linkItemId}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        /// <summary>
        /// 调整程式在链接中的执行顺序
        /// </summary>
        public async Task<Result<ProgramLinkStepDTO>> ReorderLinkItemAsync(long programLinkId, long linkItemId, int newOrder)
        {
            var requestData = new { NewOrder = newOrder };
            return await _httpClient.PutAsync<ProgramLinkStepDTO, object>($"api/programlinks/{programLinkId}/items/{linkItemId}/reorder", requestData);
        }

        /// <summary>
        /// 批量新增或更新程式链接项
        /// </summary>
        public async Task<Result<bool>> BulkUpsertProgramLinkStepItemsAsync(long programLinkId, List<ProgramLinkItemUpsertDto> items)
        {
            var result = await _httpClient.PostAsync<object, List<ProgramLinkItemUpsertDto>>($"api/programlinks/{programLinkId}/items/bulk", items);
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }
    }
}