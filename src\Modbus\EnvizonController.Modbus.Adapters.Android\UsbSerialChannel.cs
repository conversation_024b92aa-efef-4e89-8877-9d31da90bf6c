using Android.Hardware.Usb;
using Android.Content;
using EnvizonController.Modbus.Abstractions.Channels;
using Java.Lang;

namespace EnvizonController.Modbus.Adapters.Android
{
    /// <summary>
    /// 基于Android USB的串口通道
    /// </summary>
    public class UsbSerialChannel : ModbusChannelBase
    {
        private readonly Context _context;
        private readonly int _baudRate;
        private readonly int _dataBits;
        private readonly UsbSerialParity _parity;
        private readonly UsbSerialStopBits _stopBits;
        private readonly int _readTimeout;
        private readonly int _writeTimeout;
        private UsbManager? _usbManager;
        private UsbDevice? _usbDevice;
        private UsbDeviceConnection? _connection;
        private UsbInterface? _interface;
        private UsbEndpoint? _readEndpoint;
        private UsbEndpoint? _writeEndpoint;
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private bool _isDisposed;

        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        public override bool IsConnected => _connection != null && _interface != null;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="context">Android上下文</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBits">数据位</param>
        /// <param name="parity">校验位</param>
        /// <param name="stopBits">停止位</param>
        /// <param name="readTimeout">读取超时（毫秒）</param>
        /// <param name="writeTimeout">写入超时（毫秒）</param>
        public UsbSerialChannel(
            Context context,
            int baudRate = 9600,
            int dataBits = 8,
            UsbSerialParity parity = UsbSerialParity.None,
            UsbSerialStopBits stopBits = UsbSerialStopBits.One,
            int readTimeout = 1000,
            int writeTimeout = 1000)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _baudRate = baudRate;
            _dataBits = dataBits;
            _parity = parity;
            _stopBits = stopBits;
            _readTimeout = readTimeout;
            _writeTimeout = writeTimeout;
        }

        /// <summary>
        /// 连接到通道
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (IsConnected)
                    return;

                // 获取USB管理器
                _usbManager = (UsbManager)_context.GetSystemService(Context.UsbService)!;
                if (_usbManager == null)
                    throw new InvalidOperationException("无法获取USB管理器");

                // 获取USB设备列表
                var deviceList = _usbManager.DeviceList;
                if (deviceList.Count == 0)
                    throw new InvalidOperationException("未找到USB设备");

                // 查找第一个USB设备（实际应用中应该有更复杂的设备选择逻辑）
                _usbDevice = deviceList.Values.FirstOrDefault();
                if (_usbDevice == null)
                    throw new InvalidOperationException("未找到USB设备");

                // 请求USB权限
                if (!_usbManager.HasPermission(_usbDevice))
                {
                    // 在实际应用中，应该使用BroadcastReceiver来处理权限请求结果
                    throw new InvalidOperationException("没有USB设备访问权限");
                }

                // 打开USB连接
                _connection = _usbManager.OpenDevice(_usbDevice);
                if (_connection == null)
                    throw new InvalidOperationException("无法打开USB连接");

                // 查找合适的接口和端点
                for (int i = 0; i < _usbDevice.InterfaceCount; i++)
                {
                    var usbInterface = _usbDevice.GetInterface(i);
                    if (usbInterface.InterfaceClass == UsbClass.CdcData)
                    {
                        _interface = usbInterface;
                        break;
                    }
                }

                if (_interface == null)
                    throw new InvalidOperationException("未找到合适的USB接口");

                // 声明接口
                if (!_connection.ClaimInterface(_interface, true))
                    throw new InvalidOperationException("无法声明USB接口");

                // 查找读写端点
                for (int i = 0; i < _interface.EndpointCount; i++)
                {
                    var endpoint = _interface.GetEndpoint(i);
                    if (endpoint.Type == UsbAddressing.XferBulk)
                    {
                        if (endpoint.Direction == UsbAddressing.In)
                            _readEndpoint = endpoint;
                        else if (endpoint.Direction == UsbAddressing.Out)
                            _writeEndpoint = endpoint;
                    }
                }

                if (_readEndpoint == null || _writeEndpoint == null)
                    throw new InvalidOperationException("未找到合适的USB端点");

                // 配置串口参数
                // 注意：这里的实现依赖于具体的USB-Serial芯片，不同的芯片可能有不同的控制命令
                // 这里只是一个示例，实际应用中需要根据具体的芯片实现
                await ConfigureSerialPortAsync();
            }
            catch
            {
                // 如果连接失败，释放资源
                await DisconnectAsync();
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 配置串口参数
        /// </summary>
        /// <returns>异步任务</returns>
        private async Task ConfigureSerialPortAsync()
        {
            // 这里的实现依赖于具体的USB-Serial芯片，不同的芯片可能有不同的控制命令
            // 这里只是一个示例，实际应用中需要根据具体的芯片实现
            
            // 例如，对于FTDI芯片，可能需要发送控制传输来设置波特率、数据位等
            // 对于CDC ACM设备，可能需要使用CDC控制请求
            
            // 由于这里无法提供通用的实现，所以只是一个占位符
            await Task.CompletedTask;
        }

        /// <summary>
        /// 断开通道连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_connection != null && _interface != null)
                {
                    _connection.ReleaseInterface(_interface);
                }

                _connection?.Dispose();
                _connection = null;
                _interface = null;
                _readEndpoint = null;
                _writeEndpoint = null;
                _usbDevice = null;
                _usbManager = null;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public override async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null || data.Length == 0)
                return;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected || _connection == null || _writeEndpoint == null)
                    throw new InvalidOperationException("USB串口未连接");

                // 使用Task.Run将同步操作包装为异步操作
                await Task.Run(() =>
                {
                    // 创建超时取消令牌
                    using var timeoutCts = new CancellationTokenSource(_writeTimeout);
                    using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);

                    try
                    {
                        // 发送数据
                        int result = _connection.BulkTransfer(_writeEndpoint, data, data.Length, _writeTimeout);
                        if (result < 0)
                            throw new System.IO.IOException("发送数据失败");
                    }
                    catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                    {
                        throw new TimeoutException("发送数据超时");
                    }
                }, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收到的数据</returns>
        public override async Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (!IsConnected || _connection == null || _readEndpoint == null)
                    throw new InvalidOperationException("USB串口未连接");

                // 使用Task.Run将同步操作包装为异步操作
                return await Task.Run(() =>
                {
                    // 创建超时取消令牌
                    using var timeoutCts = new CancellationTokenSource(timeout);
                    using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);

                    try
                    {
                        // 创建缓冲区
                        byte[] buffer = new byte[_readEndpoint.MaxPacketSize];

                        // 接收数据
                        int bytesRead = _connection.BulkTransfer(_readEndpoint, buffer, buffer.Length, timeout);
                        if (bytesRead < 0)
                            throw new System.IO.IOException("接收数据失败");

                        // 如果读取的字节数小于缓冲区大小，则调整缓冲区大小
                        if (bytesRead < buffer.Length)
                        {
                            byte[] result = new byte[bytesRead];
                            Array.Copy(buffer, result, bytesRead);
                            return result;
                        }

                        return buffer;
                    }
                    catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                    {
                        throw new TimeoutException("接收数据超时");
                    }
                }, cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        /// <returns>异步任务</returns>
        public override async Task ClearReceiveBufferAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                if (!IsConnected || _connection == null || _readEndpoint == null)
                    return;

                // 尝试读取并丢弃所有可用数据
                // 注意：这种方法可能不是最有效的，取决于具体的USB-Serial芯片
                byte[] buffer = new byte[_readEndpoint.MaxPacketSize];
                int bytesRead;
                do
                {
                    bytesRead = _connection.BulkTransfer(_readEndpoint, buffer, buffer.Length, 10);
                } while (bytesRead > 0);
            }
            catch
            {
                // 忽略清空缓冲区时的异常
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 释放托管资源
                _semaphore.Dispose();
                if (_connection != null && _interface != null)
                {
                    _connection.ReleaseInterface(_interface);
                }
                _connection?.Dispose();
                _connection = null;
                _interface = null;
                _readEndpoint = null;
                _writeEndpoint = null;
                _usbDevice = null;
                _usbManager = null;
            }

            _isDisposed = true;
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// USB串口校验位
    /// </summary>
    public enum UsbSerialParity
    {
        /// <summary>
        /// 无校验
        /// </summary>
        None,

        /// <summary>
        /// 奇校验
        /// </summary>
        Odd,

        /// <summary>
        /// 偶校验
        /// </summary>
        Even,

        /// <summary>
        /// 标记校验
        /// </summary>
        Mark,

        /// <summary>
        /// 空格校验
        /// </summary>
        Space
    }

    /// <summary>
    /// USB串口停止位
    /// </summary>
    public enum UsbSerialStopBits
    {
        /// <summary>
        /// 1位停止位
        /// </summary>
        One,

        /// <summary>
        /// 1.5位停止位
        /// </summary>
        OnePointFive,

        /// <summary>
        /// 2位停止位
        /// </summary>
        Two
    }
}
