using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Frames
{
    /// <summary>
    /// Modbus帧构建器工厂
    /// </summary>
    public static class ModbusFrameBuilderFactory
    {
        /// <summary>
        /// 创建帧构建器
        /// </summary>
        /// <param name="transportType">传输类型</param>
        /// <returns>帧构建器</returns>
        public static IModbusFrameBuilder CreateFrameBuilder(ModbusTransportType transportType)
        {
            return transportType switch
            {
                ModbusTransportType.Rtu => new ModbusRtuFrameBuilder(),
                ModbusTransportType.Ascii => new ModbusAsciiFrameBuilder(),
                ModbusTransportType.Tcp => new ModbusTcpFrameBuilder(),
                _ => throw new ArgumentException($"不支持的传输类型: {transportType}", nameof(transportType))
            };
        }
    }
}
