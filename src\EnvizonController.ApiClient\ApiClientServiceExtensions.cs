using System;
using EnvizonController.ApiClient.Factories;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Http.Implementations;
using EnvizonController.ApiClient.Services;
using EnvizonController.ApiClient.Services.Implementations;
using Microsoft.Extensions.DependencyInjection;

namespace EnvizonController.ApiClient
{
    /// <summary>
    /// API客户端服务注册扩展
    /// </summary>
    public static class ApiClientServiceExtensions
    {
        /// <summary>
        /// 注册API客户端服务
        /// </summary>
        public static IServiceCollection AddApiClientServices(this IServiceCollection services)
        {
            // 注册HttpClient
            services.AddHttpClient();
            services.AddTransient<IHttpClient, HttpClientWrapper>();

            // 注册API服务
            services.AddTransient<IDeviceApiService, DeviceApiService>();
            services.AddTransient<IAlarmApiService, AlarmApiService>();
            services.AddTransient<IDataPointApiService, DataPointApiService>();
            services.AddTransient<ITestItemApiService, TestItemApiService>();
            services.AddTransient<IProgramApiService, ProgramApiService>();
            services.AddTransient<IProgramLinkApiService, ProgramLinkApiService>();

            // 注册API客户端工厂
            services.AddTransient<IApiClientFactory, ApiClientFactory>();

            return services;
        }
    }
}