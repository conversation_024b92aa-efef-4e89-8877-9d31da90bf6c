using System;
using System.Linq;

namespace EnvizonController.ApiClient.Caching
{
    /// <summary>
    /// 缓存装饰器基类
    /// </summary>
    public abstract class CachedApiServiceBase
    {
        /// <summary>
        /// 缓存接口
        /// </summary>
        protected readonly IApiCache _cache;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="cache">缓存接口实现</param>
        protected CachedApiServiceBase(IApiCache cache)
        {
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        }

        /// <summary>
        /// 构建缓存键
        /// </summary>
        /// <param name="service">服务名称</param>
        /// <param name="method">方法名称</param>
        /// <param name="args">方法参数</param>
        /// <returns>格式化的缓存键</returns>
        protected string BuildCacheKey(string service, string method, params object[] args)
        {
            var argsString = args != null && args.Length > 0
                ? string.Join("_", args.Select(a => a?.ToString() ?? "null"))
                : string.Empty;

            return $"{service}_{method}_{argsString}";
        }
    }
}