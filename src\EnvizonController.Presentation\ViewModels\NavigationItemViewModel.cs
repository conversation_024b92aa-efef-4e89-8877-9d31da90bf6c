﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Presentation.Messages;

namespace EnvizonController.Presentation.ViewModels;

public partial class NavigationItemViewModel : ViewModelBase
{
    [ObservableProperty]
    private string _headerText; // 导航项显示的文本

    [ObservableProperty]
    private string _targetViewKey; // 应该使用 NavigationKeys 中的常量

    [ObservableProperty] 
    private bool _isSelected;

    [ObservableProperty]
    private string _featureName; // 功能名称

    [ObservableProperty]
    private string _estimatedDate; // 预计完成日期

    /// <summary>
    /// 创建一个新的导航项
    /// </summary>
    /// <param name="headerText">导航项显示的文本</param>
    /// <param name="targetViewKey">导航目标键，建议使用 NavigationKeys 中定义的常量</param>
    /// <param name="featureName">功能名称</param>
    /// <param name="estimatedDate">预计完成日期</param>
    public NavigationItemViewModel(string headerText, string targetViewKey, string featureName = "", string estimatedDate = "")
    {
        _headerText = headerText;
        TargetViewKey = targetViewKey;
        _featureName = featureName;
        _estimatedDate = estimatedDate;
    }
}