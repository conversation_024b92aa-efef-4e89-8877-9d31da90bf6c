using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Aggregates;

/// <summary>
///     测试项实体
/// </summary>
public class TestRun : BaseEntity<long>
{

    /// <summary>
    ///     测试步骤数量
    /// </summary>
    public int StepCount { get; private set; }

    /// <summary>
    ///     总测试时间（秒）
    /// </summary>
    public int EstimatedDurationSeconds { get; private set; }

    public int ActualDurationSeconds { get; set; }

    /// <summary>
    ///     测试项名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    ///     测试项描述
    /// </summary>
    public string? Description { get; set; } 

    /// <summary>
    ///     测试开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    ///     测试状态(Running)
    /// </summary>
    public string Status { get; set; } = null!;

    /// <summary>
    ///     设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     关联的设备（非映射字段）
    /// </summary>
    [NotMapped]
    public Device? Device { get; set; }

    /// <summary>
    ///     数据采集点集合
    /// </summary>
    [NotMapped]
    public List<TestDataPoint> DataCollections { get; set; } = new();

    public string ExecutionType { get; set; } = string.Empty;

    // 配置快照（JSON）
    public string ExecutionConfigJson { get; set; } = string.Empty;

    // 业务方法
    public void CreateFromProgram(Program program)
    {
        ExecutionType = "Program";
        var snapshot = new ProgramSnapshot
        {
            Type = "Program",
            OriginalId = program.Id,
            Name = program.Name,
            CycleCount = program.CycleCount,
            CycleStart = program.CycleStart,
            CycleEnd = program.CycleEnd,
            Steps = program.Steps.Select(s => new StepSnapshot
            {
                Index = s.Index,
                Temperature = s.Temperature,
                Humidity = s.Humidity,
                DurationSeconds = s.Duration,
                IsLinear = s.IsLinear
            }).ToList(),
            SnapshotMetadata = new SnapshotMetadata
            {
                OriginalCreatedAt = program.CreatedAt,
                OriginalUpdatedAt = program.UpdatedAt,
                SnapshotCreatedAt = DateTime.Now
            }
        };

        ExecutionConfigJson = JsonSerializer.Serialize(snapshot);
        EstimatedDurationSeconds = CalculateEstimatedDuration(snapshot);
    }

    public void CreateFromProgramLink(ProgramLink programLink)
    {
        ExecutionType = "ProgramLink";
        var snapshot = new ProgramLinkSnapshot
        {
            Type = "ProgramLink",
            OriginalId = programLink.Id,
            Name = programLink.Name,
            CycleCount = programLink.CycleCount,
            Programs = programLink.GetOrderedItems().Select(item => new ProgramLinkItemSnapshot
            {
                ExecutionOrder = item.ExecutionOrder,
                ProgramSnapshot = CreateProgramSnapshot(item.Program!)
            }).ToList(),
            SnapshotMetadata = new SnapshotMetadata
            {
                OriginalCreatedAt = programLink.CreatedAt,
                OriginalUpdatedAt = programLink.UpdatedAt,
                SnapshotCreatedAt = DateTime.Now
            }
        };

        ExecutionConfigJson = JsonSerializer.Serialize(snapshot);
        EstimatedDurationSeconds = CalculateEstimatedDuration(snapshot);
    }

    /// <summary>
    /// 创建程式快照
    /// </summary>
    /// <param name="program">程式</param>
    /// <returns>程式快照</returns>
    private ProgramSnapshot CreateProgramSnapshot(Program program)
    {
        return new ProgramSnapshot
        {
            Type = "Program",
            OriginalId = program.Id,
            Name = program.Name,
            CycleCount = program.CycleCount,
            CycleStart = program.CycleStart,
            CycleEnd = program.CycleEnd,
            Steps = program.Steps.Select(s => new StepSnapshot
            {
                Index = s.Index,
                Temperature = s.Temperature,
                Humidity = s.Humidity,
                DurationSeconds = s.Duration,
                IsLinear = s.IsLinear
            }).ToList(),
            SnapshotMetadata = new SnapshotMetadata
            {
                OriginalCreatedAt = program.CreatedAt,
                OriginalUpdatedAt = program.UpdatedAt,
                SnapshotCreatedAt = DateTime.Now
            }
        };
    }

    /// <summary>
    /// 计算程式快照的预计时间
    /// </summary>
    /// <param name="snapshot">程式快照</param>
    /// <returns>预计时间（秒）</returns>
    private int CalculateEstimatedDuration(ProgramSnapshot snapshot)
    {
        var totalDuration = 0;
        var orderedSteps = snapshot.Steps.OrderBy(s => s.Index).ToList();

        if (!orderedSteps.Any())
            return 0;

        // 如果没有循环配置或循环配置无效，执行所有步骤一次
        if (snapshot.CycleCount <= 0 || snapshot.CycleStart <= 0 || snapshot.CycleEnd <= 0 ||
            snapshot.CycleStart > snapshot.CycleEnd || snapshot.CycleStart > orderedSteps.Count ||
            snapshot.CycleEnd > orderedSteps.Count)
        {
            totalDuration = orderedSteps.Sum(s => s.DurationSeconds);
        }
        else
        {
            // 计算循环前的步骤时间
            var preCycleSteps = orderedSteps.Where(s => s.Index < snapshot.CycleStart);
            totalDuration += preCycleSteps.Sum(s => s.DurationSeconds);

            // 计算循环步骤时间
            var cycleSteps = orderedSteps.Where(s => s.Index >= snapshot.CycleStart && s.Index <= snapshot.CycleEnd);
            var cycleStepsDuration = cycleSteps.Sum(s => s.DurationSeconds);
            totalDuration += cycleStepsDuration * snapshot.CycleCount;

            // 计算循环后的步骤时间
            var postCycleSteps = orderedSteps.Where(s => s.Index > snapshot.CycleEnd);
            totalDuration += postCycleSteps.Sum(s => s.DurationSeconds);
        }

        return totalDuration;
    }

    /// <summary>
    /// 计算程式链接快照的预计时间
    /// </summary>
    /// <param name="snapshot">程式链接快照</param>
    /// <returns>预计时间（秒）</returns>
    private int CalculateEstimatedDuration(ProgramLinkSnapshot snapshot)
    {
        var totalDuration = 0;

        // 计算单次链接执行的时间
        foreach (var programItem in snapshot.Programs.OrderBy(p => p.ExecutionOrder))
        {
            totalDuration += CalculateEstimatedDuration(programItem.ProgramSnapshot);
        }

        // 乘以循环次数
        return totalDuration * Math.Max(1, snapshot.CycleCount);
    }

    /// <summary>
    /// 获取执行配置快照
    /// </summary>
    /// <returns>执行配置快照对象</returns>
    public object? GetExecutionSnapshot()
    {
        if (string.IsNullOrEmpty(ExecutionConfigJson))
            return null;

        try
        {
            return ExecutionType switch
            {
                "Program" => JsonSerializer.Deserialize<ProgramSnapshot>(ExecutionConfigJson),
                "ProgramLink" => JsonSerializer.Deserialize<ProgramLinkSnapshot>(ExecutionConfigJson),
                _ => null
            };
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// 获取程式快照
    /// </summary>
    /// <returns>程式快照，如果不是程式类型或反序列化失败则返回null</returns>
    public ProgramSnapshot? GetProgramSnapshot()
    {
        if (ExecutionType != "Program" || string.IsNullOrEmpty(ExecutionConfigJson))
            return null;

        try
        {
            return JsonSerializer.Deserialize<ProgramSnapshot>(ExecutionConfigJson);
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// 获取程式链接快照
    /// </summary>
    /// <returns>程式链接快照，如果不是程式链接类型或反序列化失败则返回null</returns>
    public ProgramLinkSnapshot? GetProgramLinkSnapshot()
    {
        if (ExecutionType != "ProgramLink" || string.IsNullOrEmpty(ExecutionConfigJson))
            return null;

        try
        {
            return JsonSerializer.Deserialize<ProgramLinkSnapshot>(ExecutionConfigJson);
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// 获取预计的测试步骤总数
    /// </summary>
    /// <returns>预计的测试步骤总数</returns>
    public int GetEstimatedStepCount()
    {
        var snapshot = GetExecutionSnapshot();
        
        return snapshot switch
        {
            ProgramSnapshot programSnapshot => CalculateStepCount(programSnapshot),
            ProgramLinkSnapshot programLinkSnapshot => CalculateLinkStepCount(programLinkSnapshot),
            _ => 0
        };
    }

    /// <summary>
    /// 计算程式快照的步骤总数
    /// </summary>
    /// <param name="snapshot">程式快照</param>
    /// <returns>步骤总数</returns>
    private int CalculateStepCount(ProgramSnapshot snapshot)
    {
        var orderedSteps = snapshot.Steps.OrderBy(s => s.Index).ToList();

        if (!orderedSteps.Any())
            return 0;

        // 如果没有循环配置或循环配置无效，执行所有步骤一次
        if (snapshot.CycleCount <= 0 || snapshot.CycleStart <= 0 || snapshot.CycleEnd <= 0 ||
            snapshot.CycleStart > snapshot.CycleEnd || snapshot.CycleStart > orderedSteps.Count ||
            snapshot.CycleEnd > orderedSteps.Count)
        {
            return orderedSteps.Count;
        }

        // 计算循环前的步骤数
        var preCycleStepsCount = orderedSteps.Count(s => s.Index < snapshot.CycleStart);

        // 计算循环步骤数
        var cycleStepsCount = orderedSteps.Count(s => s.Index >= snapshot.CycleStart && s.Index <= snapshot.CycleEnd);
        var totalCycleStepsCount = cycleStepsCount * snapshot.CycleCount;

        // 计算循环后的步骤数
        var postCycleStepsCount = orderedSteps.Count(s => s.Index > snapshot.CycleEnd);

        return preCycleStepsCount + totalCycleStepsCount + postCycleStepsCount;
    }

    /// <summary>
    /// 计算程式链接快照的步骤总数
    /// </summary>
    /// <param name="snapshot">程式链接快照</param>
    /// <returns>步骤总数</returns>
    private int CalculateLinkStepCount(ProgramLinkSnapshot snapshot)
    {
        var totalSteps = 0;

        // 计算单次链接执行的步骤数
        foreach (var programItem in snapshot.Programs.OrderBy(p => p.ExecutionOrder))
        {
            totalSteps += CalculateStepCount(programItem.ProgramSnapshot);
        }

        // 乘以循环次数
        return totalSteps * Math.Max(1, snapshot.CycleCount);
    }

    /// <summary>
    /// 验证执行配置是否有效
    /// </summary>
    /// <returns>是否有效</returns>
    public bool ValidateExecutionConfig()
    {
        var snapshot = GetExecutionSnapshot();
        
        return snapshot switch
        {
            ProgramSnapshot programSnapshot => ValidateProgramSnapshot(programSnapshot),
            ProgramLinkSnapshot programLinkSnapshot => ValidateProgramLinkSnapshot(programLinkSnapshot),
            _ => false
        };
    }

    /// <summary>
    /// 验证程式快照是否有效
    /// </summary>
    /// <param name="snapshot">程式快照</param>
    /// <returns>是否有效</returns>
    private bool ValidateProgramSnapshot(ProgramSnapshot snapshot)
    {
        if (snapshot == null || string.IsNullOrEmpty(snapshot.Name))
            return false;

        if (!snapshot.Steps.Any())
            return false;

        // 验证步骤索引是否连续
        var orderedSteps = snapshot.Steps.OrderBy(s => s.Index).ToList();
        for (int i = 0; i < orderedSteps.Count; i++)
        {
            if (orderedSteps[i].Index != i + 1)
                return false;
        }

        // 验证循环配置
        if (snapshot.CycleCount > 0 && (snapshot.CycleStart <= 0 || snapshot.CycleEnd <= 0 ||
            snapshot.CycleStart > snapshot.CycleEnd || snapshot.CycleStart > orderedSteps.Count ||
            snapshot.CycleEnd > orderedSteps.Count))
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 验证程式链接快照是否有效
    /// </summary>
    /// <param name="snapshot">程式链接快照</param>
    /// <returns>是否有效</returns>
    private bool ValidateProgramLinkSnapshot(ProgramLinkSnapshot snapshot)
    {
        if (snapshot == null || string.IsNullOrEmpty(snapshot.Name))
            return false;

        if (!snapshot.Programs.Any())
            return false;

        // 验证每个程式快照
        foreach (var programItem in snapshot.Programs)
        {
            if (!ValidateProgramSnapshot(programItem.ProgramSnapshot))
                return false;
        }

        // 验证执行顺序是否连续
        var orderedPrograms = snapshot.Programs.OrderBy(p => p.ExecutionOrder).ToList();
        for (int i = 0; i < orderedPrograms.Count; i++)
        {
            if (orderedPrograms[i].ExecutionOrder != i + 1)
                return false;
        }

        return true;
    }

    /// <summary>
    ///     创建新的测试项
    /// </summary>
    /// <param name="name">测试项名称</param>
    /// <param name="description">测试项描述</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="status"></param>
    /// <returns>创建的测试项</returns>
    public static TestRun Create(string name, long deviceId, string status = "Created", string? description = null)
    {
        return new TestRun
        {
            Name = name,
            Description = description,
            Status = status,
            StartTime = DateTime.Now,
            DeviceId = deviceId,
            StepCount = 0,
            EstimatedDurationSeconds = 0
        };
    }

    /// <summary>
    ///     启动测试
    /// </summary>
    /// <returns>是否成功启动</returns>
    public bool Start()
    {
        // 只有在Created或Stopped状态下才能启动测试
        if (Status != "Created" && Status != "Stopped") return false;

        // 更新状态
        Status = "Running";
        StartTime = DateTime.Now;

        return true;
    }

    /// <summary>
    ///     停止测试
    /// </summary>
    /// <returns>是否成功停止</returns>
    public bool Stop()
    {
        // 只有在Running状态下才能停止测试
        if (Status != "Running") return false;

        // 更新状态
        Status = "Stopped";

        return true;
    }

    /// <summary>
    ///     添加数据采集点
    /// </summary>
    /// <param name="testDataPoint">采集的数据</param>
    /// <returns>创建的数据点，如果测试不在运行中则返回null</returns>
    public TestDataPoint? AddDataCollection(TestDataPoint testDataPoint)
    {
        // 只有在测试运行中时才添加数据点
        if (Status != "Running") return null;

        DataCollections.Add(testDataPoint);
        return testDataPoint;
    }

    /// <summary>
    ///     完成测试
    /// </summary>
    /// <returns>是否成功完成</returns>
    public bool Complete()
    {
        // 只有在Running或Stopped状态下才能完成测试
        if (Status != "Running" && Status != "Stopped") return false;

        // 更新状态
        Status = "Completed";

        return true;
    }
}


/// <summary>
/// 快照元数据
/// </summary>
public class SnapshotMetadata
{
    /// <summary>
    /// 原始对象创建时间
    /// </summary>
    public DateTime OriginalCreatedAt { get; set; }

    /// <summary>
    /// 原始对象更新时间
    /// </summary>
    public DateTime OriginalUpdatedAt { get; set; }

    /// <summary>
    /// 快照创建时间
    /// </summary>
    public DateTime SnapshotCreatedAt { get; set; }
}

/// <summary>
/// 基础快照类
/// </summary>
public abstract class BaseSnapshot
{
    /// <summary>
    /// 快照类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 原始对象ID
    /// </summary>
    public long OriginalId { get; set; }

    /// <summary>
    /// 快照元数据
    /// </summary>
    public SnapshotMetadata SnapshotMetadata { get; set; } = new();
}

/// <summary>
/// 程式快照
/// </summary>
public class ProgramSnapshot : BaseSnapshot
{
    /// <summary>
    /// 程式名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    public int CycleCount { get; set; }

    /// <summary>
    /// 循环开始
    /// </summary>
    public int CycleStart { get; set; }

    /// <summary>
    /// 循环结束
    /// </summary>
    public int CycleEnd { get; set; }

    /// <summary>
    /// 步骤快照集合
    /// </summary>
    public List<StepSnapshot> Steps { get; set; } = new();
}

/// <summary>
/// 程式链接快照
/// </summary>
public class ProgramLinkSnapshot : BaseSnapshot
{
    /// <summary>
    /// 程式链接名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    public int CycleCount { get; set; } = 1;

    /// <summary>
    /// 程式链接项快照集合
    /// </summary>
    public List<ProgramLinkItemSnapshot> Programs { get; set; } = new();
}

/// <summary>
/// 程式链接项快照
/// </summary>
public class ProgramLinkItemSnapshot
{
    /// <summary>
    /// 执行顺序
    /// </summary>
    public int ExecutionOrder { get; set; }

    /// <summary>
    /// 程式快照
    /// </summary>
    public ProgramSnapshot ProgramSnapshot { get; set; } = new();
}

/// <summary>
/// 步骤快照
/// </summary>
public class StepSnapshot
{
    /// <summary>
    /// 步骤索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// 温度（℃）
    /// </summary>
    public double Temperature { get; set; }

    /// <summary>
    /// 湿度（%）
    /// </summary>
    public double Humidity { get; set; }

    /// <summary>
    /// 持续时间（秒）
    /// </summary>
    public int DurationSeconds { get; set; }

    /// <summary>
    /// 是否线性变化
    /// </summary>
    public bool IsLinear { get; set; }
}