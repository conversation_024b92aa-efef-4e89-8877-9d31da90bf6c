namespace EnvizonController.Presentation.Initialization
{
    /// <summary>
    /// 为初始化进度变化事件提供数据
    /// </summary>
    public class InitializationProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 获取当前初始化进度信息
        /// </summary>
        public InitializationProgress Progress { get; }

        /// <summary>
        /// 初始化 <see cref="InitializationProgressEventArgs"/> 类的新实例
        /// </summary>
        /// <param name="progress">初始化进度信息</param>
        public InitializationProgressEventArgs(InitializationProgress progress)
        {
            Progress = progress ?? throw new ArgumentNullException(nameof(progress));
        }
    }
}