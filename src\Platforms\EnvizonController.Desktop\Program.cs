﻿using System;
using Avalonia;
using HotAvalonia;

namespace EnvizonController.Desktop
{
    internal sealed class Program
    {
        // Initialization code. Don't use any Avalonia, third-party APIs or any
        // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
        // yet and stuff might break.
        [STAThread]
        public static void Main(string[] args) => BuildAvaloniaApp()
            .StartWithClassicDesktopLifetime(args);

        // Avalonia configuration, don't remove; also used by visual designer.
        public static AppBuilder BuildAvaloniaApp()
            => AppBuilder.Configure<App>().UseHotReload() // <--- 添加或确保存在这一行调用
                .UsePlatformDetect()
                .WithInterFont()
                .LogToTrace();
    }
}
