<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.DeviceLifetimeControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="150"
    d:DesignWidth="300"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <TextBlock
                Margin="0,0,6,0"
                VerticalAlignment="Center"
                Classes="gray font-icon small"
                Text="&#xf017;" />
            <TextBlock
                Margin="0"
                VerticalAlignment="Center"
                Classes="gray bottom small"
                Text="设备使用寿命时长" />
        </StackPanel>

        <ScrollViewer Grid.Row="1" Margin="0,6,0,0">
            <ItemsControl ItemsSource="{Binding CurrentDevice.DeviceLifetimeItems}">
                <ItemsControl.Styles>
                    <Style Selector="ProgressBar.high">
                        <Setter Property="Foreground" Value="#4CAF50" />
                    </Style>
                    <Style Selector="ProgressBar.medium">
                        <Setter Property="Foreground" Value="#FFC107" />
                    </Style>
                    <Style Selector="ProgressBar.low">
                        <Setter Property="Foreground" Value="#F44336" />
                    </Style>
                </ItemsControl.Styles>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="0,3" RowDefinitions="Auto,Auto">
                            <Grid Grid.Row="0" ColumnDefinitions="*,Auto">
                                <TextBlock
                                    Grid.Column="0"
                                    Classes="small white"
                                    Text="{Binding Name}" />
                                <TextBlock
                                    Grid.Column="1"
                                    HorizontalAlignment="Right"
                                    Classes="small"
                                    Text="{Binding RemainingText}" />
                            </Grid>
                            <Grid Grid.Row="1" ColumnDefinitions="*,Auto">
                                <ProgressBar
                                    Grid.Column="0"
                                    Margin="0,3,8,0"
                                    Classes.high="{Binding PercentRemaining, Converter={StaticResource GreaterThanConverter}, ConverterParameter=60}"
                                    Classes.low="{Binding PercentRemaining, Converter={StaticResource LessThanConverter}, ConverterParameter=30}"
                                    Classes.medium="{Binding PercentRemaining, Converter={StaticResource BetweenConverter}, ConverterParameter=30-60}"
                                    Maximum="100"
                                    Value="{Binding PercentRemaining}" />
                                <TextBlock
                                    Grid.Column="1"
                                    Classes="small"
                                    Text="{Binding PercentText}" />
                            </Grid>
                        </Grid>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Vertical" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
