﻿using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 如果值为 null 或空字符串，则返回 true，否则返回 false
    /// </summary>
    public class NullOrEmptyToTrueConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null)
                return true;
            if (value is string str)
                return string.IsNullOrEmpty(str);
            return false;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}