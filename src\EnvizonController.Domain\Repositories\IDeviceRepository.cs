using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 设备仓储接口
    /// </summary>
    public interface IDeviceRepository : IRepository<Device, long>
    {
        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <returns>设备</returns>
        Task<Device?> GetByNameAsync(string name);

        /// <summary>
        /// 根据协议ID获取设备列表
        /// </summary>
        /// <param name="protocolId">协议ID</param>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetByProtocolIdAsync(long protocolId);

        /// <summary>
        /// 根据连接类型获取设备列表
        /// </summary>
        /// <param name="connectionType">连接类型</param>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetByConnectionTypeAsync(string connectionType);

        /// <summary>
        /// 获取所有串口设备
        /// </summary>
        /// <returns>串口设备列表</returns>
        Task<IEnumerable<Device>> GetSerialDevicesAsync();

        /// <summary>
        /// 获取所有网络设备
        /// </summary>
        /// <returns>网络设备列表</returns>
        Task<IEnumerable<Device>> GetNetworkDevicesAsync();
    }
}
