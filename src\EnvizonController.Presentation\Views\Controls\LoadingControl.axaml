<UserControl
    x:Class="EnvizonController.Presentation.LoadingControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    
    <!-- 添加蒙版背景 -->
    <Grid>
        <!-- 半透明蒙版背景 -->
        <Rectangle Fill="#CC050510" />
        
        <!-- 主要内容 -->
        <Border 
            Width="400" 
            Height="400" 
            Background="#662A2A2A"
            BorderBrush="#56EFFE"
            BorderThickness="2"
            CornerRadius="8">
            
            <Grid>
                <!-- 背景二进制码效果 -->
                <TextBlock 
                    Text="010101110010110101010110001011001010101010101001010101010011010101010101001010101010100101010101010110101010101"
                    TextWrapping="Wrap"
                    Foreground="#33FFFFFF"
                    FontFamily="Consolas"
                    FontSize="10"
                    Opacity="0.3"/>
                
                <!-- 主要内容 -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <!-- 标题 -->
                    <TextBlock 
                        Text="ENVIZON CONTROLLER" 
                        Foreground="#56EFFE" 
                        FontSize="24" 
                        FontWeight="Bold"
                        HorizontalAlignment="Center"
                        Margin="0,0,0,20" />
                    
                    <!-- Logo -->
                    <Image
                        Height="100"
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        Source="/Assets/logo.png" />
                    
                    <!-- 系统提示 -->
                    <TextBlock 
                        Text="正在加载" 
                        Foreground="#0DF0FF" 
                        FontSize="18" 
                        HorizontalAlignment="Center"
                        Margin="0,0,0,10" />
                    
                    <TextBlock 
                        Text="请稍候..." 
                        Foreground="#FFFFFFFF" 
                        FontSize="14" 
                        HorizontalAlignment="Center"
                        Margin="0,0,0,20" />
                    
                    <!-- 进度条 -->
                    <ProgressBar
                        Width="300"
                        Height="6"
                        Margin="0,10,0,20"
                        Background="#33FFFFFF"
                        Foreground="#0DF0FF"
                        IsIndeterminate="True" />
                    
                    <!-- 状态信息 -->
                    <Grid Width="300">
                        <TextBlock 
                            Text="加载中" 
                            Foreground="#56EFFE" 
                            FontSize="12" 
                            HorizontalAlignment="Left" />
                        
                        <TextBlock 
                            Text="处理数据" 
                            Foreground="#FFFFFF" 
                            FontSize="12" 
                            HorizontalAlignment="Center" />
                        
                        <TextBlock 
                            Text="v1.0.0" 
                            Foreground="#56EFFE" 
                            FontSize="12" 
                            HorizontalAlignment="Right" />
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
