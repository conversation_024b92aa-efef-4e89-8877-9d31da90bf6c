<Window
    x:Class="EnvizonController.Presentation.Views.SplashScreenWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="using:EnvizonController.Presentation.Views"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    Title="Envizon Controller"
    Width="600"
    Height="400"
    WindowStartupLocation="CenterScreen"
    CanResize="False"
    ExtendClientAreaToDecorationsHint="True"
    SystemDecorations="None"
    TransparencyLevelHint="AcrylicBlur"
    Background="Transparent"
    mc:Ignorable="d">

    <Design.DataContext>
        <vm:SplashScreenViewModel />
    </Design.DataContext>

    <Panel>
        <ExperimentalAcrylicBorder IsHitTestVisible="False">
            <ExperimentalAcrylicBorder.Material>
                <ExperimentalAcrylicMaterial
                    BackgroundSource="Digger"
                    TintColor="Black"
                    TintOpacity="0.6"
                    MaterialOpacity="0.8" />
            </ExperimentalAcrylicBorder.Material>
        </ExperimentalAcrylicBorder>

        <views:SplashScreen Margin="0" />
    </Panel>
</Window> 