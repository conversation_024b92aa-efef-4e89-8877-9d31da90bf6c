using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using AutoMapper;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Server.Controllers
{
    /// <summary>
    /// 程式链接控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ProgramLinksController : ControllerBase
    {
        private readonly IProgramLinkAppService _programLinkService;
        private readonly IProgramAppService _programService;
        private readonly ILogger<ProgramLinksController> _logger;
        private readonly IMapper _mapper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="programLinkService">程式链接应用服务</param>
        /// <param name="programService">程式应用服务</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="mapper">对象映射器</param>
        public ProgramLinksController(
            IProgramLinkAppService programLinkService,
            IProgramAppService programService,
            ILogger<ProgramLinksController> logger,
            IMapper mapper)
        {
            _programLinkService = programLinkService;
            _programService = programService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// 获取所有程式链接
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>程式链接列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<ProgramLinkDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<ProgramLinkDTO>>> GetProgramLinks(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var programLinks = await _programLinkService.GetAllProgramLinksAsync();
                var programLinksList = programLinks.ToList();

                var pagedProgramLinks = programLinksList
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return Ok(new PagedResultDto<ProgramLinkDTO>
                {
                    Items = pagedProgramLinks,
                    TotalCount = programLinksList.Count,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式链接列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式链接列表时发生错误");
            }
        }

        /// <summary>
        /// 根据ID获取程式链接
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>程式链接详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(ProgramLinkDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ProgramLinkDTO>> GetProgramLink(long id)
        {
            try
            {
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(id);
                if (programLink == null)
                {
                    return NotFound($"ID为{id}的程式链接不存在");
                }

                return Ok(programLink);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式链接 {ProgramLinkId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式链接详情时发生错误");
            }
        }

        /// <summary>
        /// 按名称获取程式链接
        /// </summary>
        /// <param name="name">程式链接名称</param>
        /// <returns>程式链接详情</returns>
        [HttpGet("name/{name}")]
        [ProducesResponseType(typeof(ProgramLinkDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ProgramLinkDTO>> GetProgramLinkByName(string name)
        {
            try
            {
                var programLink = await _programLinkService.GetProgramLinkByNameAsync(name);
                if (programLink == null)
                {
                    return NotFound($"名称为{name}的程式链接不存在");
                }

                return Ok(programLink);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按名称获取程式链接 {ProgramLinkName} 时出错", name);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式链接详情时发生错误");
            }
        }

        /// <summary>
        /// 创建程式链接
        /// </summary>
        /// <param name="programLinkDto">程式链接信息</param>
        /// <returns>创建的程式链接</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ProgramLinkDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramLinkDTO>> CreateProgramLink(ProgramLinkDTO programLinkDto)
        {
            try
            {
                var createdProgramLink = await _programLinkService.CreateProgramLinkAsync(programLinkDto);
                return CreatedAtAction(nameof(GetProgramLink), new { id = createdProgramLink.Id }, createdProgramLink);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "创建程式链接参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建程式链接时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "创建程式链接时发生错误");
            }
        }

        /// <summary>
        /// 更新程式链接
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <param name="programLinkDto">更新的程式链接信息</param>
        /// <returns>更新后的程式链接</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(ProgramLinkDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramLinkDTO>> UpdateProgramLink(long id, ProgramLinkDTO programLinkDto)
        {
            try
            {
                if (id != programLinkDto.Id)
                {
                    return BadRequest("路径ID与请求体ID不匹配");
                }

                var updatedProgramLink = await _programLinkService.UpdateProgramLinkAsync(programLinkDto);
                return Ok(updatedProgramLink);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "更新程式链接参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新程式链接 {ProgramLinkId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "更新程式链接时发生错误");
            }
        }

        /// <summary>
        /// 删除程式链接
        /// </summary>
        /// <param name="id">程式链接ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteProgramLink(long id)
        {
            try
            {
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(id);
                if (programLink == null)
                {
                    return NotFound($"ID为{id}的程式链接不存在");
                }

                bool result = await _programLinkService.DeleteProgramLinkAsync(id);
                if (result)
                {
                    return NoContent();
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "删除程式链接失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除程式链接 {ProgramLinkId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "删除程式链接时发生错误");
            }
        }

        /// <summary>
        /// 获取程式链接中的所有链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <returns>链接项列表</returns>
        [HttpGet("{programLinkId}/items")]
        [ProducesResponseType(typeof(IEnumerable<ProgramLinkStepDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<ProgramLinkStepDTO>>> GetProgramLinkItems(long programLinkId)
        {
            try
            {
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(programLinkId);
                if (programLink == null)
                {
                    return NotFound($"ID为{programLinkId}的程式链接不存在");
                }

                var items = await _programLinkService.GetProgramLinkItemsAsync(programLinkId);
                return Ok(items);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式链接 {ProgramLinkId} 的链接项时出错", programLinkId);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式链接项时发生错误");
            }
        }

        /// <summary>
        /// 添加程式到链接中
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="programId">程式ID</param>
        /// <param name="order">执行顺序（可选）</param>
        /// <returns>添加的链接项</returns>
        [HttpPost("{programLinkId}/items")]
        [ProducesResponseType(typeof(ProgramLinkStepDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramLinkStepDTO>> AddProgramToProgramLink(
            long programLinkId, 
            [FromQuery] long programId, 
            [FromQuery] int? order = null)
        {
            try
            {
                // 检查程式链接是否存在
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(programLinkId);
                if (programLink == null)
                {
                    return NotFound($"ID为{programLinkId}的程式链接不存在");
                }

                // 检查程式是否存在
                var program = await _programService.GetProgramByIdAsync(programId);
                if (program == null)
                {
                    return NotFound($"ID为{programId}的程式不存在");
                }

                var createdItem = await _programLinkService.AddProgramToProgramLinkAsync(programLinkId, programId, order);
                return CreatedAtAction(nameof(GetProgramLinkItems), new { programLinkId = createdItem.ProgramLinkId }, createdItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "添加程式到链接中参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向程式链接 {ProgramLinkId} 添加程式 {ProgramId} 时出错", programLinkId, programId);
                return StatusCode(StatusCodes.Status500InternalServerError, "添加程式到链接中时发生错误");
            }
        }

        /// <summary>
        /// 从程式链接中移除链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="linkItemId">链接项ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{programLinkId}/items/{linkItemId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> RemoveLinkItem(long programLinkId, long linkItemId)
        {
            try
            {
                // 检查程式链接是否存在
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(programLinkId);
                if (programLink == null)
                {
                    return NotFound($"ID为{programLinkId}的程式链接不存在");
                }

                bool result = await _programLinkService.RemoveLinkItemAsync(programLinkId, linkItemId);
                if (result)
                {
                    return NoContent();
                }
                else
                {
                    return NotFound($"ID为{linkItemId}的程式链接项不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从程式链接 {ProgramLinkId} 移除链接项 {LinkItemId} 时出错", programLinkId, linkItemId);
                return StatusCode(StatusCodes.Status500InternalServerError, "从程式链接中移除链接项时发生错误");
            }
        }

        /// <summary>
        /// 调整程式在链接中的执行顺序
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="linkItemId">链接项ID</param>
        /// <param name="newOrder">新的执行顺序</param>
        /// <returns>更新后的链接项</returns>
        [HttpPut("{programLinkId}/items/{linkItemId}/reorder")]
        [ProducesResponseType(typeof(ProgramLinkStepDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramLinkStepDTO>> ReorderLinkItem(
            long programLinkId, 
            long linkItemId, 
            [FromQuery] int newOrder)
        {
            try
            {
                // 检查程式链接是否存在
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(programLinkId);
                if (programLink == null)
                {
                    return NotFound($"ID为{programLinkId}的程式链接不存在");
                }

                if (newOrder < 1)
                {
                    return BadRequest("执行顺序必须大于0");
                }

                var updatedItem = await _programLinkService.ReorderLinkItemAsync(programLinkId, linkItemId, newOrder);
                return Ok(updatedItem);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "调整程式链接项顺序参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调整程式链接 {ProgramLinkId} 的链接项 {LinkItemId} 顺序时出错", programLinkId, linkItemId);
                return StatusCode(StatusCodes.Status500InternalServerError, "调整程式链接项执行顺序时发生错误");
            }
        }

        /// <summary>
        /// 批量新增或更新程式链接项
        /// </summary>
        /// <param name="programLinkId">程式链接ID</param>
        /// <param name="items">要新增或更新的链接项集合</param>
        /// <returns>操作结果</returns>
        [HttpPost("{programLinkId}/items/bulk")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> BulkUpsertProgramLinkStepItems(
            long programLinkId,
            [FromBody] List<ProgramLinkItemUpsertDto> items)
        {
            try
            {
                // 验证参数
                if (items == null || !items.Any())
                {
                    return BadRequest("链接项列表不能为空");
                }

                // 检查程式链接是否存在
                var programLink = await _programLinkService.GetProgramLinkByIdAsync(programLinkId);
                if (programLink == null)
                {
                    return NotFound($"ID为{programLinkId}的程式链接不存在");
                }

                // 调用应用服务进行批量更新
                var result = await _programLinkService.BulkUpsertProgramLinkStepItemsAsync(programLinkId, items);
                
                if (result)
                {
                    return Ok(new { success = true, message = "批量更新程式链接项成功" });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "批量更新程式链接项失败");
                }
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "批量更新程式链接项参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新程式链接 {ProgramLinkId} 的链接项时出错", programLinkId);
                return StatusCode(StatusCodes.Status500InternalServerError, "批量更新程式链接项时发生错误");
            }
        }
    }
}