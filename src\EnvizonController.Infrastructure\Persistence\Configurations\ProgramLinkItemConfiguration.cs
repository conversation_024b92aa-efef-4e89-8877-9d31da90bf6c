using EnvizonController.Domain.Aggregates;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EnvizonController.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 程式链接项实体配置
    /// </summary>
    public class ProgramLinkItemConfiguration : IEntityTypeConfiguration<ProgramLinkStep>
    {
        public void Configure(EntityTypeBuilder<ProgramLinkStep> builder)
        {
            // 表名
            builder.ToTable("ProgramLinkItems");

            // 主键
            builder.HasKey(e => e.Id);

            // 配置自增长主键
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            // 必填属性
            builder.Property(e => e.ProgramLinkId)
                .IsRequired();

            builder.Property(e => e.ProgramId)
                .IsRequired();

            builder.Property(e => e.ExecutionOrder)
                .IsRequired();

            builder.Property(e => e.CreatedAt)
                .IsRequired();

            builder.Property(e => e.UpdatedAt)
                .IsRequired();

            // 索引
            builder.HasIndex(e => new { e.ProgramLinkId, e.ExecutionOrder })
                .IsUnique();

            builder.HasIndex(e => new { e.ProgramLinkId, e.ProgramId });

            // 关系配置
            // ProgramLinkItem -> ProgramLink 关系在ProgramLink配置中已定义

            // ProgramLinkItem -> Program 关系
            builder.HasOne(e => e.Program)
                .WithMany()
                .HasForeignKey(e => e.ProgramId)
                .OnDelete(DeleteBehavior.Restrict); // 使用Restrict防止误删除Program
        }
    }
}