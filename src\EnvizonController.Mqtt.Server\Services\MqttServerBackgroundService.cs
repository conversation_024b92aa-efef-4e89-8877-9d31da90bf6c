using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Mqtt.Server.Services
{
    public class MqttServerBackgroundService : BackgroundService
    {
        private readonly IMqttServerService _mqttServerService;
        private readonly ILogger<MqttServerBackgroundService> _logger;

        public MqttServerBackgroundService(IMqttServerService mqttServerService, ILogger<MqttServerBackgroundService> logger)
        {
            _mqttServerService = mqttServerService;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                await _mqttServerService.StartAsync(stoppingToken);
                
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MQTT服务器后台服务发生错误");
            }
            finally
            {
                await _mqttServerService.StopAsync(stoppingToken);
            }
        }
    }
} 