using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Initializers;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 数据点API服务实现
    /// </summary>
    public class DataPointApiService : IDataPointApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public DataPointApiService(IHttpClient httpClient)
        {
            _httpClient = httpClient;
            _config = AppConfigurationProvider.ConfigurationAsyncLoaded;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        public async Task<Result<PagedResultDto<DataPointDto>>> GetTestRunDataPointsAsync(long testId, int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<DataPointDto>>($"api/datapoints/by-test/{testId}", queryParams);
        }

        public async Task<Result<PagedResultDto<DataPointDto>>> GetDeviceDataPointsAsync(long deviceId, DateTime? startTime, DateTime? endTime, int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            if (startTime.HasValue)
            {
                queryParams["startTime"] = startTime.Value.ToString("o");
            }

            if (endTime.HasValue)
            {
                queryParams["endTime"] = endTime.Value.ToString("o");
            }

            return await _httpClient.GetAsync<PagedResultDto<DataPointDto>>($"api/datapoints/by-device/{deviceId}", queryParams);
        }

        public async Task<Result<DataPointDto>> GetDataPointAsync(long id)
        {
            return await _httpClient.GetAsync<DataPointDto>($"api/datapoints/{id}");
        }
    }
}