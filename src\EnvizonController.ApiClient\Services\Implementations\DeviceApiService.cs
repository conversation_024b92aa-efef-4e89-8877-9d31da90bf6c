using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Results;
using EnvizonController.Configuration.Models;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 设备API服务实现
    /// </summary>
    public class DeviceApiService : IDeviceApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public DeviceApiService(IHttpClient httpClient, DefaultConfig config)
        {
            _httpClient = httpClient;
            _config = config;
        }

        /// <summary>
        /// API服务基础URL
        /// </summary>
        public string BaseUrl => _config.ApiClientSettings.BaseUrl;

        /// <summary>
        /// 初始化API服务
        /// </summary>
        public Task InitializeAsync()
        {
            // 默认实现不需要特殊初始化
            return Task.CompletedTask;
        }

        /// <summary>
        /// 检查API服务状态
        /// </summary>
        public async Task<Result<bool>> CheckHealthAsync()
        {
            var result = await _httpClient.GetAsync<object>("api/health");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }

        public async Task<Result<PagedResultDto<DeviceDto>>> GetDevicesAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<DeviceDto>>($"api/devices", queryParams);
        }

        public async Task<Result<DeviceDto>> GetDeviceAsync(long id)
        {
            return await _httpClient.GetAsync<DeviceDto>($"api/devices/{id}");
        }

        public async Task<Result<DeviceDto>> CreateDeviceAsync(DeviceDto device)
        {
            return await _httpClient.PostAsync<DeviceDto, DeviceDto>($"api/devices", device);
        }

        public async Task<Result<DeviceDto>> UpdateDeviceAsync(long id, DeviceDto device)
        {
            return await _httpClient.PutAsync<DeviceDto, DeviceDto>($"api/devices/{id}", device);
        }

        public async Task<Result<bool>> DeleteDeviceAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/devices/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }
    }
}