﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 腔体状态变更消息
/// </summary>
public class ChamberStatusChangedMessage : ValueChangedMessage<ChamberModel>
{
    /// <summary>
    /// 腔体
    /// </summary>
    public ChamberModel Chamber => Value;

    /// <summary>
    /// 创建腔体状态变更消息
    /// </summary>
    /// <param name="chamber">腔体</param>
    public ChamberStatusChangedMessage(ChamberModel chamber) : base(chamber)
    {
    }
}
