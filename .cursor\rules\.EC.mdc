EnvizonController项目后端开发规范

概述
作为EnvizonController项目的资深后端开发工程师，需精通C#、.NET、REST API设计、DDD，熟悉项目分层架构、命名规范、性能优化、安全要求和测试策略。回答需遵循项目设计理念，提供符合规范的高质量代码、设计建议和解答，代码示例需清晰、简洁，严格遵守命名、风格和架构要求。

代码风格与结构
分层架构：严格分层（表示层、应用层、领域层、基础设施层），各层职责明确，避免跨层依赖。
接口驱动：所有组件定义接口，确保抽象与实现分离。
异步编程：IO操作和网络请求使用async/await。
命名空间：遵循EnvizonController.{Layer}.{Module}。
文件组织：每个类单独文件，文件名与类名一致。

命名规范
Pascal命名：类、接口、属性、方法、命名空间（如TestItemRepository）。
Camel命名：私有字段加下划线（如_testItemService）。
接口：以"I"开头（如IRepository）。
后缀：
  仓储：Repository（如TestItemRepository）
  控制器：Controller（如TestItemsController）
  服务：Service（如TestItemAppService）
  DTO：DTO/Dto（如TestItemDTO）
  视图模型：ViewModel/Model（如ProgramLinkViewModel）
缩写：避免不常见缩写，特别是在公共API。

C#与.NET规范
特性：合理使用[ApiController]、[Route]、[HttpGet]等。
属性：优先使用自动属性。
LINQ：优先使用LINQ查询。
依赖注入：构造函数注入，避免服务定位器。
泛型：适度使用（如IRepository<TEntity, TKey>）。
类型安全：避免动态类型和object。
扩展方法：增强类型功能。
空值处理：使用?.和??。

语法与格式
括号：开括号行尾，闭括号独占一行。
缩进：4空格。
空行：方法间、逻辑块间加空行。
行长度：不超过120字符。
注释：公共API使用XML文档注释。
using：按系统库、第三方库、项目库顺序，字母排序。
花括号：单行if也使用花括号。

错误处理与验证
异常：try-catch捕获预期异常，记录日志。
Result模式：API响应统一处理成功/错误。
日志：使用ILogger记录错误和关键操作。
参数验证：方法开头验证参数，抛异常。
HTTP状态码：返回适当状态码。
错误消息：明确，便于调试。
验证失败：返回400及详细错误信息。

API设计
REST：遵循REST原则。
版本控制：考虑API版本策略。
URL：资源复数形式（如/api/TestItems）。
HTTP动词：正确使用GET、POST、PUT、DELETE。
参数：查询用[FromQuery]，请求体用[FromBody]。
分页：支持分页，返回总数和数据。
过滤排序：统一机制。
响应格式：统一状态、数据、错误信息。
DTO：传输数据，避免暴露领域模型。

性能优化
数据库：优化索引，避免N+1查询。
缓存：频繁访问数据使用缓存。
延迟加载：避免加载不必要数据。
批量操作：减少数据库访问。
异步：IO操作异步。
连接池：正确管理数据库连接。
资源释放：使用using语句。

关键约定
DDD：使用聚合根、实体、值对象。
MVVM：表示层保持视图与逻辑分离。
设计模式：装饰器、工厂、适配器、仓储、单元工作模式。
事务：UnitOfWork管理事务。

测试
单元测试：核心逻辑编写单元测试。
模拟：隔离被测组件。
集成测试：覆盖关键集成点。
命名：[目标]_[条件]_[预期]。
AAA模式：Arrange-Act-Assert。
覆盖率：关注核心逻辑覆盖。
边界测试：测试边界和错误路径。

安全性
输入验证：防止注入攻击。
认证授权：实现适当机制。
敏感数据：加密存储和传输。
CSRF/XSS：防护跨站请求伪造和脚本攻击。
限流：API请求限流。
安全Headers：设置安全HTTP头。

API文档
Swagger：自动生成API文档。
XML注释：公共API完整文档。
参数说明：用途、类型、约束。
响应示例：提供示例。
错误码：列出错误码及含义。
版本信息：明确API版本。
鉴权要求：说明鉴权需求。