﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 导航消息
/// </summary>
public class NavigateMessage : ValueChangedMessage<string>
{
    /// <summary>
    /// 导航键
    /// </summary>
    public string NavigationKey => Value;

    /// <summary>
    /// 创建导航消息
    /// </summary>
    /// <param name="navigationKey">导航键</param>
    public NavigateMessage(string navigationKey) : base(navigationKey)
    {
    }

    /// <summary>
    /// 创建导航到仪表盘的消息
    /// </summary>
    /// <returns>导航消息</returns>
    public static NavigateMessage Dashboard() => new(NavigationKeys.Dashboard);

    /// <summary>
    /// 创建导航到测试方案的消息
    /// </summary>
    /// <returns>导航消息</returns>
    public static NavigateMessage TestPlan() => new(NavigationKeys.TestPlan);

    /// <summary>
    /// 创建导航到报警记录的消息
    /// </summary>
    /// <returns>导航消息</returns>
    public static NavigateMessage AlarmRecord() => new(NavigationKeys.AlarmRecord);

    /// <summary>
    /// 创建导航到历史数据的消息
    /// </summary>
    /// <returns>导航消息</returns>
    public static NavigateMessage HistoricalData() => new(NavigationKeys.HistoricalData);

    /// <summary>
    /// 创建导航到系统设置的消息
    /// </summary>
    /// <returns>导航消息</returns>
    public static NavigateMessage SystemSettings() => new(NavigationKeys.SystemSettings);
}
