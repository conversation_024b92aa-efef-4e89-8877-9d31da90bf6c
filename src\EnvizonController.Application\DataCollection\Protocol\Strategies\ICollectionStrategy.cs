﻿using EnvizonController.Application.Devices;
using EnvizonController.Domain.Aggregates;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EnvizonController.Application.DataCollection.Protocol;
using static EnvizonController.Application.DataCollection.Protocol.ProtocolDataCollector;

namespace EnvizonController.Application.DataCollection.Strategies
{
    /// <summary>
    /// 数据采集策略接口，定义了所有采集策略需要实现的方法。
    /// </summary>
    public interface ICollectionStrategy
    {
        /// <summary>
        /// 判断当前策略是否可以处理指定的协议项。
        /// </summary>
        /// <param name="item">要检查的协议项。</param>
        /// <returns>如果该策略可以处理该项则返回 true，否则返回 false。</returns>
        bool CanHandle(ProtocolItem item);

        /// <summary>
        /// 使用指定设备异步收集协议项的数据。
        /// </summary>
        /// <param name="deviceCollectionDetails">设备采集详细信息。</param>
        /// <param name="item">需要收集数据的协议项。</param>
        /// <param name="device">用于采集数据的设备。</param>
        /// <param name="modbusDeviceService">Modbus 设备服务，用于与设备通信。</param>
        /// <returns>包含采集到的数据的字典。例如：{ "value": 123, "unit": "mA" }。</returns>
        Task<Dictionary<string, object>> CollectAsync(
            DeviceCollectionDetails deviceCollectionDetails,
            ProtocolItem item,
            Device device,
            IModbusDeviceService modbusDeviceService);
    }
}