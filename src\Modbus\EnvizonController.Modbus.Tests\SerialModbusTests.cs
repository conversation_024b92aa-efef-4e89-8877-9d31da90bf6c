using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Client;
using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;
using EnvizonController.Modbus.Tests.Mocks;
using Xunit;

namespace EnvizonController.Modbus.Tests
{
    /// <summary>
    /// 串口Modbus通信测试
    /// </summary>
    public class SerialModbusTests
    {
        #region 协议测试

        [Fact]
        public void ReadCoilsRequest_GetFrame_ReturnsCorrectFrame()
        {
            // Arrange
            byte slaveAddress = 1;
            ushort startAddress = 0;
            ushort coilCount = 8;
            var request = new ReadCoilsRequest(slaveAddress, startAddress, coilCount);

            // Act
            byte[] frame = request.GetFrame();

            // Assert
            Assert.Equal(6, frame.Length);
            Assert.Equal(slaveAddress, frame[0]);
            Assert.Equal((byte)ModbusFunction.ReadCoils, frame[1]);
            Assert.Equal(0x00, frame[2]); // 起始地址高字节
            Assert.Equal(0x00, frame[3]); // 起始地址低字节
            Assert.Equal(0x00, frame[4]); // 线圈数量高字节
            Assert.Equal(0x08, frame[5]); // 线圈数量低字节
        }

        [Fact]
        public void ReadCoilsResponse_ParseResponse_ParsesCoilValuesCorrectly()
        {
            // Arrange
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadCoils;
            byte byteCount = 1;
            byte[] coilValues = new byte[] { 0x05 }; // 0000 0101 - 第1和第3位为1

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(coilValues, 0, responseData, 3, byteCount);

            var response = new ReadCoilsResponse();

            // Act
            response.ParseResponse(responseData);

            // Assert
            Assert.Equal(8, response.CoilValues.Length);
            Assert.True(response.CoilValues[0]);
            Assert.False(response.CoilValues[1]);
            Assert.True(response.CoilValues[2]);
            Assert.False(response.CoilValues[3]);
            Assert.False(response.CoilValues[4]);
            Assert.False(response.CoilValues[5]);
            Assert.False(response.CoilValues[6]);
            Assert.False(response.CoilValues[7]);
        }

        [Fact]
        public void ReadCoilsResponse_ParseResponse_WithExceptionResponse_SetsIsExceptionToTrue()
        {
            // Arrange
            byte slaveAddress = 1;
            byte exceptionFunctionCode = (byte)((byte)ModbusFunction.ReadCoils | 0x80); // 设置最高位为1表示异常
            byte exceptionCode = (byte)ModbusExceptionCode.IllegalDataAddress;

            byte[] responseData = new byte[] { slaveAddress, exceptionFunctionCode, exceptionCode };

            var response = new ReadCoilsResponse();

            // Act
            response.ParseResponse(responseData);

            // Assert
            Assert.True(response.IsException);
            Assert.Equal(ModbusFunction.ReadCoils, response.FunctionCode);
            Assert.Equal(ModbusExceptionCode.IllegalDataAddress, response.ExceptionCode);
        }

        #endregion

        #region 帧构建测试

        [Fact]
        public void ModbusRtuFrameBuilder_BuildRequestFrame_AddsCorrectCrc()
        {
            // Arrange
            var frameBuilder = new ModbusRtuFrameBuilder();
            var request = new ReadCoilsRequest(1, 0, 8);

            // Act
            byte[] frame = frameBuilder.BuildRequestFrame(request);

            // Assert
            Assert.Equal(8, frame.Length);

            // 验证CRC
            byte[] expectedCrc = ModbusCrc.CalculateCrc(frame.Take(6).ToArray());
            Assert.Equal(expectedCrc[0], frame[6]);
            Assert.Equal(expectedCrc[1], frame[7]);
        }

        [Fact]
        public void ModbusRtuFrameBuilder_ParseResponseFrame_ValidatesAndParsesCrcCorrectly()
        {
            // Arrange
            var frameBuilder = new ModbusRtuFrameBuilder();
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadCoils;
            byte byteCount = 1;
            byte[] coilValues = new byte[] { 0x05 }; // 0000 0101 - 第1和第3位为1

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(coilValues, 0, responseData, 3, byteCount);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            var response = new ReadCoilsResponse();

            // Act
            bool result = frameBuilder.ParseResponseFrame(responseFrame, response);

            // Assert
            Assert.True(result);
            Assert.Equal(8, response.CoilValues.Length);
            Assert.True(response.CoilValues[0]);
            Assert.False(response.CoilValues[1]);
            Assert.True(response.CoilValues[2]);
            Assert.False(response.CoilValues[3]);
            Assert.False(response.CoilValues[4]);
            Assert.False(response.CoilValues[5]);
            Assert.False(response.CoilValues[6]);
            Assert.False(response.CoilValues[7]);
        }

        [Fact]
        public void ModbusRtuFrameBuilder_ParseResponseFrame_WithInvalidCrc_ReturnsFalse()
        {
            // Arrange
            var frameBuilder = new ModbusRtuFrameBuilder();
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadCoils;
            byte byteCount = 1;
            byte[] coilValues = new byte[] { 0x05 };

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(coilValues, 0, responseData, 3, byteCount);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            // 修改CRC使其无效
            responseFrame[responseFrame.Length - 1] = 0x00;

            var response = new ReadCoilsResponse();

            // Act
            bool result = frameBuilder.ParseResponseFrame(responseFrame, response);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 通道测试

        [Fact]
        public async Task ModbusClient_ReadCoilsAsync_SendsCorrectRequestAndParsesResponse()
        {
            // Arrange
            var channel = new TestModbusChannel();
            var client = new ModbusClient(channel, ModbusTransportType.Rtu);
            await client.ConnectAsync();

            // 模拟响应
            byte slaveAddress = 1;
            byte functionCode = (byte)ModbusFunction.ReadCoils;
            byte byteCount = 1;
            byte[] coilValues = new byte[] { 0x05 }; // 0000 0101 - 第1和第3位为1

            byte[] responseData = new byte[3 + byteCount];
            responseData[0] = slaveAddress;
            responseData[1] = functionCode;
            responseData[2] = byteCount;
            Array.Copy(coilValues, 0, responseData, 3, byteCount);

            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);

            // 设置响应
            channel.EnqueueResponse(responseFrame);

            // Act
            bool[] result = await client.ReadCoilsAsync(slaveAddress, 0, 8);

            // Assert
            Assert.Equal(8, result.Length);
            Assert.True(result[0]);
            Assert.False(result[1]);
            Assert.True(result[2]);
            Assert.False(result[3]);
            Assert.False(result[4]);
            Assert.False(result[5]);
            Assert.False(result[6]);
            Assert.False(result[7]);

            // 验证发送的请求
            Assert.Single(channel.SentData);
            byte[] requestFrame = channel.SentData[0];
            Assert.Equal(slaveAddress, requestFrame[0]);
            Assert.Equal(functionCode, requestFrame[1]);
            Assert.Equal(0x00, requestFrame[2]); // 起始地址高字节
            Assert.Equal(0x00, requestFrame[3]); // 起始地址低字节
            Assert.Equal(0x00, requestFrame[4]); // 线圈数量高字节
            Assert.Equal(0x08, requestFrame[5]); // 线圈数量低字节
        }

        [Fact]
        public async Task ModbusClient_ReadCoilsAsync_WithExceptionResponse_ThrowsInvalidOperationException()
        {
            // Arrange
            var channel = new TestModbusChannel();
            var client = new ModbusClient(channel, ModbusTransportType.Rtu);
            await client.ConnectAsync();

            // 模拟异常响应
            byte slaveAddress = 1;
            byte exceptionFunctionCode = (byte)((byte)ModbusFunction.ReadCoils | 0x80); // 设置最高位为1表示异常
            byte exceptionCode = (byte)ModbusExceptionCode.IllegalDataAddress;

            byte[] responseData = new byte[] { slaveAddress, exceptionFunctionCode, exceptionCode };
            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);

            // 设置响应
            channel.EnqueueResponse(responseFrame);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => client.ReadCoilsAsync(slaveAddress, 0, 8));

            Assert.Contains("Modbus异常", exception.Message);
        }

        [Fact]
        public async Task ModbusClient_ReadCoilsAsync_WithNoResponse_ThrowsTimeoutException()
        {
            // Arrange
            var channel = new TestModbusChannel();
            var client = new ModbusClient(channel, ModbusTransportType.Rtu);
            await client.ConnectAsync();

            // 不设置响应，模拟通信超时

            // Act & Assert
            var exception = await Assert.ThrowsAsync<TimeoutException>(
                () => client.ReadCoilsAsync(1, 0, 8));

            Assert.Contains("接收超时", exception.Message);
        }

        #endregion
    }
}
