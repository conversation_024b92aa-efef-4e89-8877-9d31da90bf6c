namespace EnvizonController.Modbus.Abstractions.Interfaces
{
    /// <summary>
    /// Modbus通信通道接口
    /// </summary>
    public interface IModbusChannel : IDisposable
    {
        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接到通道
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task ConnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 断开通道连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task SendAsync(byte[] data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收到的数据</returns>
        Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default);

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        /// <returns>异步任务</returns>
        Task ClearReceiveBufferAsync();
    }
}
