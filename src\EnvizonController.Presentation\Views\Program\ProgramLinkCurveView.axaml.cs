using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using AvaloniaLineSeriesChart.Data;
using AvaloniaLineSeriesChart.Data.Enums;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Shared.DTOs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Avalonia.Threading;

namespace EnvizonController.Presentation.Views.Program
{
    /// <summary>
    /// 程式链接曲线视图控件，用于展示多个程式的温湿度曲线
    /// </summary>
    public partial class ProgramLinkCurveView : UserControl
    {
        // 图例数据集合
        private ObservableCollection<LegendItem> _legendItems = new ObservableCollection<LegendItem>();

        // 颜色方案，用于区分不同程式的曲线
        private readonly Color[] _programColors = new Color[]
        {
            Colors.LawnGreen,                // 亮绿色
            Colors.Gold,                     // 金色
            Colors.DeepPink,                 // 深粉色
            Colors.Orange,                   // 橙色
            Colors.Aqua,                     // 水蓝色
            Colors.YellowGreen,              // 黄绿色
        };

        public ProgramLinkCurveView()
        {
            InitializeComponent();
            InitializeChart();

            // 数据上下文变更事件处理
            DataContextChanged += OnDataContextChanged;

            // 图例控件绑定数据
            var legendItems = this.FindControl<ItemsControl>("LegendItems");
            if (legendItems != null)
            {
                legendItems.ItemsSource = _legendItems;
            }
        }

        private void OnDataContextChanged(object sender, EventArgs e)
        {
            if (DataContext is ProgramLinkViewModel vm)
            {
                // 监听SelectedProgramLink变化
                vm.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(ProgramLinkViewModel.SelectedProgramLink))
                    {
                        UpdateChart();
                    }
                };

                // 监听ProgramLinkStepItems集合变化
                vm.ProgramLinkStepItems.CollectionChanged += (s, args) =>
                {
                    UpdateChart();
                };

                // 初始加载
                UpdateChart();
            }
        }

        private void InitializeChart()
        {
            var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
            if (lineChart == null) return;

            // 设置图表主题
            lineChart.SetTheme(new ChartTheme
            {
                BackgroundColor = Colors.Transparent,
                TextColor = Colors.White,
                AxisColor = Color.FromRgb(0x66, 0x66, 0x66),
                GridLineColor = Color.FromRgb(0x44, 0x44, 0x44),
                DefaultSeriesColors = _programColors,
                TooltipBackgroundColor = Color.FromRgb(50, 50, 50)
            });

            // 设置X轴为时间轴
            lineChart.SetXAxisType(XAxisType.DateTime);
            lineChart.SetXTickCount(6);
            lineChart.SetXAxisLabelFormatter(s =>
            {
                // 格式化X轴标签为时间，显示格式为"日 时:分:秒"
                return DateTime.Now.AddSeconds(Convert.ToDouble(s)).ToString("ddd HH:mm:ss");
            });
        }

        private void UpdateChart()
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                // 清除图表数据和图例
                lineChart.ClearSeries();
                _legendItems.Clear();

                // 检查数据上下文和选中的程式链接
                if (DataContext is not ProgramLinkViewModel vm || vm.SelectedProgramLink == null || !vm.ProgramLinkStepItems.Any())
                {
                    lineChart.Refresh(); // 即使没有数据，也刷新图表以清空内容
                    return;
                }

                // 按执行顺序获取排序后的程式链接项
                var orderedLinkItems = vm.ProgramLinkStepItems.OrderBy(i => i.ExecutionOrder).ToList();
                if (!orderedLinkItems.Any())
                {
                    lineChart.Refresh();
                    return;
                }

                // 创建共用的温度和湿度数据系列
                var temperatureSeries = new LineSeriesData
                {
                    Name = "温度",
                    YAxisPosition = YAxisPosition.Left,
                    Color =  Color.FromRgb(0xe6, 0x2f, 0xf3),
                };
                temperatureSeries.SetXDataType<double>();
                lineChart.AddSeries(temperatureSeries);

                var humiditySeries = new LineSeriesData
                {
                    Name = "湿度",
                    YAxisPosition = YAxisPosition.Right,
                    Color = Color.FromRgb(0x0d, 0xf0, 0xff)
                };
                humiditySeries.SetXDataType<double>();
                lineChart.AddSeries(humiditySeries);

                // 添加到图例
                _legendItems.Add(new LegendItem 
                { 
                    Name = "温度", 
                    Color = new SolidColorBrush( Color.FromRgb(0xe6, 0x2f, 0xf3)) 
                });
                _legendItems.Add(new LegendItem 
                { 
                    Name = "湿度", 
                    Color = new SolidColorBrush(Color.FromRgb(0x0d, 0xf0, 0xff))
                });

                // 初始时间点
                double currentTime = 0;

                // 记录当前温湿度值
                double currentTemp = 0;
                double currentHumidity = 0;
                bool isFirstPoint = true;

                // 获取程序链接的循环次数
                int programLinkCycleCount = vm.SelectedProgramLink.CycleCount > 0 ? vm.SelectedProgramLink.CycleCount : 1;

                // 循环整个程序链接
                for (int linkCycle = 0; linkCycle < programLinkCycleCount; linkCycle++)
                {
                    // 如果不是第一次循环，添加程序链接循环标记
                    if (linkCycle > 0)
                    {
                        //var linkCycleMarker = new LineSeriesData
                        //{
                        //    Name = $"Program Link Cycle {linkCycle}",
                        //    YAxisPosition = YAxisPosition.Left,
                        //    ShowYAxis = false,
                        //    Color = Color.FromArgb(150, 255, 100, 100)
                        //};
                        //linkCycleMarker.SetXDataType<double>();
                        //linkCycleMarker.Points.Add(new Point(currentTime, 0));
                        //linkCycleMarker.Points.Add(new Point(currentTime, 100)); // 垂直线
                        //lineChart.AddSeries(linkCycleMarker);
                    }

                    // 处理每个程式链接项
                    foreach (var linkItem in orderedLinkItems)
                    {
                        var program = linkItem.Program;
                        if (program == null) continue;

                        // 处理程式的所有步骤
                        if (program.Steps != null && program.Steps.Any())
                        {
                            // 构建有效的步骤列表，考虑循环配置
                            List<ProgramStepDTO> effectiveSteps = new List<ProgramStepDTO>();
                            var originalSteps = program.Steps.ToList();
                            
                            // 验证循环参数
                            bool hasValidCycleConfig = program.CycleCount > 0 &&
                                                     program.CycleEnd > 0 &&
                                                     program.CycleStart > 0 &&
                                                     program.CycleStart <= program.CycleEnd &&
                                                     program.CycleStart <= originalSteps.Count &&
                                                     program.CycleEnd <= originalSteps.Count;

                            for (int i = 0; i < originalSteps.Count; i++)
                            {
                                effectiveSteps.Add(originalSteps[i]);
                                // 如果当前步骤是循环块的结束，并且循环配置有效
                                if (hasValidCycleConfig && (i + 1) == program.CycleEnd)
                                {
                                    for (int c = 0; c < program.CycleCount; c++)
                                    {
                                        for (int j = program.CycleStart - 1; j < program.CycleEnd; j++)
                                        {
                                            effectiveSteps.Add(originalSteps[j]);
                                        }
                                    }
                                }
                            }

                            if (!effectiveSteps.Any()) continue;

                            // 如果是第一个程式，添加初始点
                            if (isFirstPoint)
                            {
                                currentTemp = effectiveSteps.First().Temperature;
                                currentHumidity = effectiveSteps.First().Humidity;
                                temperatureSeries.Points.Add(new Point(currentTime, currentTemp));
                                humiditySeries.Points.Add(new Point(currentTime, currentHumidity));
                                isFirstPoint = false;
                            }
                            else
                            {
                                // 程式转换点 - 添加垂直标记线
                                //var marker = new LineSeriesData
                                //{
                                //    Name = $"Program Transition",
                                //    YAxisPosition = YAxisPosition.Left,
                                //    ShowYAxis = false,
                                //    Color = Color.FromArgb(100, 255, 255, 255)
                                //};
                                //marker.SetXDataType<double>();
                                //marker.Points.Add(new Point(currentTime, 0));
                                //marker.Points.Add(new Point(currentTime, 100)); // 垂直线
                                //lineChart.AddSeries(marker);
                            }

                            // 遍历处理程式的每个步骤
                            foreach (var step in effectiveSteps)
                            {
                                if (step.IsLinear)
                                {
                                    // 线性变化
                                    currentTime += step.Duration;
                                    currentTemp = step.Temperature;
                                    currentHumidity = step.Humidity;
                                    temperatureSeries.Points.Add(new Point(currentTime, currentTemp));
                                    humiditySeries.Points.Add(new Point(currentTime, currentHumidity));
                                }
                                else
                                {
                                    // 非线性变化：瞬间跳变后保持
                                    if (currentTemp != step.Temperature)
                                    {
                                        temperatureSeries.Points.Add(new Point(currentTime, currentTemp));
                                        currentTemp = step.Temperature;
                                        temperatureSeries.Points.Add(new Point(currentTime, currentTemp));
                                    }

                                    if (currentHumidity != step.Humidity)
                                    {
                                        humiditySeries.Points.Add(new Point(currentTime, currentHumidity));
                                        currentHumidity = step.Humidity;
                                        humiditySeries.Points.Add(new Point(currentTime, currentHumidity));
                                    }

                                    // 持续保持当前值
                                    currentTime += step.Duration;
                                    temperatureSeries.Points.Add(new Point(currentTime, currentTemp));
                                    humiditySeries.Points.Add(new Point(currentTime, currentHumidity));
                                }
                            }
                        }
                    }
                }

                // 图表刷新
                lineChart.Refresh();
            });
        }
    }

    // 图例项
    public class LegendItem
    {
        public string Name { get; set; }
        public IBrush Color { get; set; }
    }
}