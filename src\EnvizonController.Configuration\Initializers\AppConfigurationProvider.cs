﻿using EnvizonController.Configuration.Models;
using EnvizonController.Configuration.Persistence;

namespace EnvizonController.Configuration.Initializers;

public static class AppConfigurationProvider
{
    // 定义配置文件的路径
    // 可以考虑使其可配置或动态确定
    private static readonly string ConfigFilePath =
        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");

    // DefaultConfig 的 ConfigurationManager 单例实例
    private static readonly Lazy<IConfigurationManager<DefaultConfig>> ManagerInstance =
        new(() => new ConfigurationManager<DefaultConfig>());

    // Lazy<T> 确保配置的线程安全单次初始化
    private static readonly Lazy<DefaultConfig> ConfigInstance = new(LoadConfiguration);

    // 可选：显式异步初始化（状态管理更复杂）
    private static DefaultConfig? _asyncLoadedConfig;
    private static readonly Lock InitLock = new();
    private static Task? _initializationTask;

    // 公共访问器，用于获取单例 ConfigurationManager
    public static IConfigurationManager<DefaultConfig> Manager => ManagerInstance.Value;

    // 公共访问器，用于获取单例加载的 DefaultConfig 实例
    public static DefaultConfig Configuration => ConfigInstance.Value;

    // 异步加载配置的访问器（确保已调用 InitializeAsync）
    public static DefaultConfig ConfigurationAsyncLoaded
    {
        get
        {
            if (_asyncLoadedConfig == null)
                // 选项 1：阻塞直到加载完成（可能导致死锁）
                // InitializeAsync().GetAwaiter().GetResult();
                // 选项 2：如果未准备好则抛出异常
                throw new InvalidOperationException(
                    "配置尚未异步初始化。请先调用 InitializeAsync() 方法。");
            // 选项 3：返回默认值（可能隐藏错误）
            // return new DefaultConfig();
            return _asyncLoadedConfig;
        }
    }

    // 初始化逻辑，仅由 Lazy<T> 调用一次
    private static DefaultConfig LoadConfiguration()
    {
        Console.WriteLine($"静态初始化器：从 {ConfigFilePath} 加载配置...");
        // 在静态初始化器中使用同步加载方法以简化操作。
        // 异步加载在此处更复杂（例如，使用 .Result/.GetAwaiter().GetResult()
        // 在某些同步上下文中（如 UI 或 ASP.NET Classic）可能导致死锁）。
        // 如果严格需要异步，请考虑显式的异步初始化方法。
        var config = Manager.Load(ConfigFilePath);

        // 如果文件不存在，Load 方法将返回一个默认实例。
        // 我们应该将此默认实例保存回文件。
        if (!File.Exists(ConfigFilePath))
        {
            Console.WriteLine(
                $"静态初始化器：未找到配置文件。将默认配置保存到 {ConfigFilePath}...");
            try
            {
                Manager.Save(config, ConfigFilePath); // 使用同步保存方法
            }
            catch (Exception ex)
            {
                Console.WriteLine($"静态初始化器：保存默认配置失败 - {ex.Message}");
                // 决定如何处理此问题 - 应用程序可能无法在没有保存配置的情况下运行？
            }
        }

        return config;
    }

    public static async Task InitializeAsync()
    {
        if (_asyncLoadedConfig != null) return; // 已加载

        Task initTask;
        lock (InitLock)
        {
            if (_initializationTask == null)
            {
                Console.WriteLine($"静态初始化器（异步）：开始从 {ConfigFilePath} 加载配置...");
                _initializationTask = Task.Run(async () =>
                {
                    var config = await Manager.LoadAsync(ConfigFilePath);
                    if (!File.Exists(ConfigFilePath))
                    {
                        Console.WriteLine($"静态初始化器（异步）：将默认配置保存到 {ConfigFilePath}...");
                        await Manager.SaveAsync(config, ConfigFilePath);
                    }

                    _asyncLoadedConfig = config; // 存储加载的配置
                });
            }

            initTask = _initializationTask;
        }

        await initTask; // 等待初始化任务完成
    }
}


// --- Your DefaultConfig and DefaultConfig classes remain the same ---
// public class DefaultConfig : DefaultConfig { ... }
// public abstract class DefaultConfig { ... }

// --- Your ConfigurationManager<T> class remains the same ---
// public class ConfigurationManager<T> : IConfigurationManager<T> where T : DefaultConfig, new() { ... }
// public interface IConfigurationManager<T> where T : DefaultConfig, new() { ... }