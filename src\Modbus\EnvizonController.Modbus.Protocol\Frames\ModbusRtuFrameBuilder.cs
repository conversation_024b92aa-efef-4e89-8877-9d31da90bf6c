using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;

namespace EnvizonController.Modbus.Protocol.Frames
{
    /// <summary>
    /// Modbus RTU帧构建器
    /// </summary>
    public class ModbusRtuFrameBuilder : IModbusFrameBuilder
    {
        /// <summary>
        /// 构建请求帧
        /// </summary>
        /// <param name="request">Modbus请求</param>
        /// <returns>完整的请求帧字节数组</returns>
        public byte[] BuildRequestFrame(ModbusRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            // 获取请求的基本帧（地址+功能码+数据）
            byte[] frame = request.GetFrame();
            
            // 添加CRC校验
            return ModbusCrc.AppendCrc(frame);
        }

        /// <summary>
        /// 解析响应帧
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <param name="response">Modbus响应对象</param>
        /// <returns>是否成功解析</returns>
        public bool ParseResponseFrame(byte[] responseFrame, ModbusResponse response)
        {
            if (responseFrame == null || responseFrame.Length < 4) // 至少需要地址(1)+功能码(1)+CRC(2)
                return false;

            if (response == null)
                throw new ArgumentNullException(nameof(response));

            // 验证CRC
            if (!ValidateResponseFrame(responseFrame))
                return false;

            try
            {
                // 解析响应（不包括CRC）
                byte[] frameWithoutCrc = new byte[responseFrame.Length - 2];
                Array.Copy(responseFrame, frameWithoutCrc, responseFrame.Length - 2);
                
                response.ParseResponse(frameWithoutCrc);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证响应帧的有效性
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <returns>响应帧是否有效</returns>
        public bool ValidateResponseFrame(byte[] responseFrame)
        {
            if (responseFrame == null || responseFrame.Length < 4)
                return false;

            // 验证CRC
            return ModbusCrc.ValidateCrc(responseFrame);
        }
    }
}
