<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.EnvironmentMonitorControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:EnvizonController.Presentation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helpers="clr-namespace:EnvizonController.Presentation.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <UserControl.Resources>
        <converters:ItemCountToColumnCountConverter x:Key="ItemCountToColumnCountConverter" />
        <converters:ItemCountToFontSizeConverter
            x:Key="SingleItemFontSizeConverter"
            DefaultFontSize="45"
            SingleItemFontSize="60" />
    </UserControl.Resources>
    <Panel>

        <Border Classes="cyber-border bigCard" />
        <Grid Classes="bigCard-in" RowDefinitions="Auto,*">
            <StackPanel Grid.Row="0" Orientation="Horizontal">
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="font-icon primary h3"
                    Text="&#xf2c9;" />
                <TextBlock
                    Margin="12,0,0,0"
                    VerticalAlignment="Center"
                    Classes="primary h3"
                    FontSize="21"
                    Text="环境监测" />
            </StackPanel>
            <StackPanel
                Grid.Row="0"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Orientation="Horizontal">

                <Button
                    Margin="5,0"
                    helpers:CommonHelper.CommonBrush="#FF003C"
                    BorderThickness="0"
                    Classes="glow2 Red"
                    IsVisible="False">

                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            Classes="font-icon"
                            FontSize="14"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="&#xf04d;" />
                        <TextBlock
                            Margin="5,0,0,0"
                            Foreground="{Binding $parent[Button].Foreground}"
                            Text="停止中" />
                    </StackPanel>
                </Button>
                <TextBlock
                    Margin="0,0,6,0"
                    VerticalAlignment="Center"
                    Classes="gray small"
                    Text="实时更新" />
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="gray font-icon rotate"
                    Text="&#xe4bb;" />
            </StackPanel>
            <Grid Grid.Row="1" Margin="0,12,0,0">
                <Grid x:Name="Grid" Grid.Row="0">
                    <ItemsControl ItemsSource="{Binding CurrentDevice.EnvironmentalDataGroupList}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="{Binding $parent[ItemsControl].ItemCount, Converter={StaticResource ItemCountToColumnCountConverter}}" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Panel Margin="4.5">
                                    <Border Classes="cyber-border bigCard" CornerRadius="12" />
                                    <Grid Classes="bigCard-in" RowDefinitions="Auto,*,Auto">
                                        <!--  Title and Icon  -->
                                        <Grid Grid.Row="0" ColumnDefinitions="*, Auto">
                                            <TextBlock
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Classes="primary"
                                                FontWeight="SemiBold"
                                                Text="{Binding ValueName}" />
                                            <TextBlock
                                                Grid.Column="1"
                                                HorizontalAlignment="Right"
                                                Classes="font-icon primary"
                                                FontSize="21"
                                                Foreground="{Binding IconColor, Converter={StaticResource HexColorToSolidColorBrushConverter}}"
                                                Text="{Binding IconName}" />
                                        </Grid>

                                        <!--  Value Display  -->
                                        <StackPanel
                                            Grid.Row="1"
                                            Margin="0,10,0,10"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Orientation="Vertical">
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Classes="font-cyber glow-white"
                                                FontSize="{Binding $parent[ItemsControl].ItemCount, Converter={StaticResource SingleItemFontSizeConverter}}"
                                                FontWeight="Bold"
                                                Text="{Binding Value, StringFormat={}#.00}" />
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Classes="font-value primary"
                                                FontSize="18"
                                                FontWeight="Bold"
                                                Text="{Binding Unit}" />
                                        </StackPanel>

                                        <!--  Additional Info (e.g., SetPoint, Output)  -->
                                        <UniformGrid
                                            Grid.Row="2"
                                            VerticalAlignment="Bottom"
                                            Columns="2"
                                            Rows="1">
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock
                                                    Classes="font-value gray2"
                                                    FontSize="15"
                                                    Text="设定值: " />
                                                <TextBlock
                                                    Margin="3,0,0,0"
                                                    Classes="font-value primary"
                                                    FontSize="15"
                                                    FontWeight="Bold"
                                                    Text="{Binding SetValue}" />
                                            </StackPanel>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock
                                                    Classes="font-value gray2"
                                                    FontSize="15"
                                                    Text="输出: " />
                                                <TextBlock
                                                    Margin="3,0,0,0"
                                                    Classes="font-value primary"
                                                    FontSize="15"
                                                    FontWeight="Bold"
                                                    Text="{Binding Output}" />
                                            </StackPanel>
                                        </UniformGrid>
                                    </Grid>
                                </Panel>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </Grid>
            </Grid>
        </Grid>
    </Panel>
</UserControl>