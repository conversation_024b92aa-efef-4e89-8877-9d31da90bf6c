﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Context\**" />
    <Compile Remove="Models\**" />
    <Compile Remove="ValueObjects\**" />
    <EmbeddedResource Remove="Context\**" />
    <EmbeddedResource Remove="Models\**" />
    <EmbeddedResource Remove="ValueObjects\**" />
    <None Remove="Context\**" />
    <None Remove="Models\**" />
    <None Remove="ValueObjects\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Shared\EnvizonController.Shared.csproj" />
  </ItemGroup>

</Project>
