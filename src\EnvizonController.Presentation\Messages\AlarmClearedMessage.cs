﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 报警清除消息
/// </summary>
public class AlarmClearedMessage : ValueChangedMessage<long>
{
    /// <summary>
    /// 报警ID
    /// </summary>
    public long AlarmId => Value;

    /// <summary>
    /// 创建报警清除消息
    /// </summary>
    /// <param name="alarmId">报警ID</param>
    public AlarmClearedMessage(long alarmId) : base(alarmId)
    {
    }
}
