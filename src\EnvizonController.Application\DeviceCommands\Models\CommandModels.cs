using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.DeviceCommands.Models
{
    /// <summary>
    /// 指令类型枚举
    /// </summary>
    public enum CommandType
    {
        /// <summary>
        /// 读取操作
        /// </summary>
        Read,
        
        /// <summary>
        /// 写入操作
        /// </summary>
        Write,
        
        /// <summary>
        /// 自定义操作
        /// </summary>
        Custom
    }

    /// <summary>
    /// 指令请求
    /// </summary>
    public class CommandRequest
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public string CommandName { get; set; } = string.Empty;
        
        /// <summary>
        /// 协议项名称
        /// </summary>
        public string? ProtocolItemName { get; set; }
        
        /// <summary>
        /// 指令参数
        /// </summary>
        public object? Parameters { get; set; }
        
        /// <summary>
        /// 指令类型
        /// </summary>
        public CommandType Type { get; set; }
    }

    /// <summary>
    /// 指令信息
    /// </summary>
    public class CommandInfo
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;
        
        /// <summary>
        /// 指令描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// 指令类型
        /// </summary>
        public CommandType Type { get; set; }
        
        /// <summary>
        /// 别名列表
        /// </summary>
        public List<string> Aliases { get; set; } = new();
        
        /// <summary>
        /// 指令参数列表
        /// </summary>
        public List<CommandParameter> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 指令参数
    /// </summary>
    public class CommandParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 数据类型
        /// </summary>
        public Type DataType { get; set; } = typeof(object);
        
        /// <summary>
        /// 是否必需参数
        /// </summary>
        public bool IsRequired { get; set; }
        
        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }
        
        /// <summary>
        /// 参数描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 自定义指令
    /// </summary>
    public class CustomCommand
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 功能码
        /// </summary>
        public byte FunctionCode { get; set; }
        
        /// <summary>
        /// 地址
        /// </summary>
        public ushort Address { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public ushort Count { get; set; }
        
        /// <summary>
        /// 数据
        /// </summary>
        public byte[]? Data { get; set; }
    }

    /// <summary>
    /// 指令执行结果
    /// </summary>
    public class CommandResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }
        
        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 执行耗时
        /// </summary>
        public TimeSpan ExecutionDuration { get; set; }
        
        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 泛型指令执行结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class CommandResult<T> : CommandResult
    {
        /// <summary>
        /// 结果数据
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// 批量指令执行结果
    /// </summary>
    public class BatchCommandResult
    {
        /// <summary>
        /// 各个指令的执行结果
        /// </summary>
        public List<CommandResult> Results { get; set; } = new();
        
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }
        
        /// <summary>
        /// 总执行时间
        /// </summary>
        public TimeSpan TotalExecutionTime { get; set; }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }
        
        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new();
    }

    /// <summary>
    /// 连接池状态
    /// </summary>
    public class ConnectionPoolStatus
    {
        /// <summary>
        /// 总连接数
        /// </summary>
        public int TotalConnections { get; set; }
        
        /// <summary>
        /// 活跃连接数
        /// </summary>
        public int ActiveConnections { get; set; }
        
        /// <summary>
        /// 空闲连接数
        /// </summary>
        public int IdleConnections { get; set; }
        
        /// <summary>
        /// 设备连接信息
        /// </summary>
        public Dictionary<long, ConnectionInfo> DeviceConnections { get; set; } = new();
    }

    /// <summary>
    /// 连接信息
    /// </summary>
    public class ConnectionInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public long DeviceId { get; set; }
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime LastUsed { get; set; }
        
        /// <summary>
        /// 使用次数
        /// </summary>
        public int UseCount { get; set; }
        
        /// <summary>
        /// 状态描述
        /// </summary>
        public string Status { get; set; } = string.Empty;
    }
} 