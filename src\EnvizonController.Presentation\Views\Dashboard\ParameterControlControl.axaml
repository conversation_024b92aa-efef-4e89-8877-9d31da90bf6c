<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.ParameterControlControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="200"
    d:DesignWidth="300"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Border Classes="cyber-noGlow-border card">
        <Grid RowDefinitions="Auto,*">
            <StackPanel Grid.Row="0" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,6,16"
                    VerticalAlignment="Center"
                    Classes="h4 primary font-icon"
                    Text="&#xf080;" />
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="h4 primary bottom"
                    Text="参数监控" />
            </StackPanel>

            <UniformGrid
                Grid.Row="1"
                Columns="2"
                Rows="2">
                <Border Classes="cyber-border card">
                    <Grid RowDefinitions="Auto,*,Auto">
                        <TextBlock
                            HorizontalAlignment="Center"
                            Classes="gray small"
                            Text="{Binding CurrentDevice.ExhaustTemperature.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Margin="0,3"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Classes="white font-cyber h3"
                            Text="{Binding CurrentDevice.ExhaustTemperature.Value}" />
                        <TextBlock
                            Grid.Row="2"
                            HorizontalAlignment="Center"
                            Classes="primary font-value"
                            Text="{Binding CurrentDevice.ExhaustTemperature.Unit}" />
                    </Grid>
                </Border>
                <Border Classes="cyber-border card">
                    <Grid RowDefinitions="Auto,*,Auto">
                        <TextBlock
                            HorizontalAlignment="Center"
                            Classes="gray small"
                            Text="{Binding CurrentDevice.ReturnTemperature.Name}" />
                        <TextBlock
                            Name="回气温度"
                            Grid.Row="1"
                            Margin="0,3"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Classes="white font-cyber  h3"
                            Text="{Binding CurrentDevice.ReturnTemperature.Value}" />
                        <TextBlock
                            Grid.Row="2"
                            HorizontalAlignment="Center"
                            Classes="primary font-value"
                            Text="{Binding CurrentDevice.ReturnTemperature.Unit}" />
                    </Grid>
                </Border>


                <Border Classes="cyber-border card">
                    <Grid RowDefinitions="Auto,*,Auto">
                        <TextBlock
                            HorizontalAlignment="Center"
                            Classes="gray small"
                            Text="{Binding CurrentDevice.ReturnPressure.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Margin="0,3"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Classes="white font-cyber  h3"
                            Text="{Binding CurrentDevice.ReturnPressure.Value}" />
                        <TextBlock
                            Grid.Row="2"
                            HorizontalAlignment="Center"
                            Classes="primary font-value"
                            Text="{Binding CurrentDevice.ReturnPressure.Unit}" />
                    </Grid>
                </Border>
                <Border Classes="cyber-border card">
                    <Grid RowDefinitions="Auto,*,Auto">
                        <TextBlock
                            HorizontalAlignment="Center"
                            Classes="gray small"
                            Text="{Binding CurrentDevice.ExhaustPressure.Name}" />
                        <TextBlock
                            Grid.Row="1"
                            Margin="0,3"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Classes="white font-cyber  h3"
                            Text="{Binding CurrentDevice.ExhaustPressure.Value}" />
                        <TextBlock
                            Grid.Row="2"
                            HorizontalAlignment="Center"
                            Classes="primary  font-value"
                            Text="{Binding CurrentDevice.ExhaustPressure.Unit}" />
                    </Grid>
                </Border>
            </UniformGrid>
        </Grid>
    </Border>
</UserControl>
