using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EnvizonController.Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 程式步骤仓储实现
    /// </summary>
    public class ProgramStepRepository : Repository<ProgramStep, long>, IProgramStepRepository
    {
        public ProgramStepRepository(AppDbContext dbContext) : base(dbContext)
        {
        }

        /// <summary>
        /// 根据程式ID获取所有程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>程式步骤集合</returns>
        public async Task<IEnumerable<ProgramStep>> GetByProgramIdAsync(long programId)
        {
            return await _dbSet.Where(ps => ps.ProgramId == programId)
                                 .OrderBy(ps => ps.Index)
                                 .ToListAsync();
        }

        /// <summary>
        /// 根据程式ID和步骤索引获取程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <param name="index">步骤索引</param>
        /// <returns>程式步骤，如果未找到则返回null</returns>
        public async Task<ProgramStep?> GetByProgramIdAndIndexAsync(long programId, int index)
        {
            return await _dbSet.FirstOrDefaultAsync(ps => ps.ProgramId == programId && ps.Index == index);
        }

        /// <summary>
        /// 根据程式ID删除所有程式步骤
        /// </summary>
        /// <param name="programId">程式ID</param>
        /// <returns>操作任务</returns>
        public async Task DeleteByProgramIdAsync(long programId)
        {
            var stepsToDelete = await _dbSet.Where(ps => ps.ProgramId == programId).ToListAsync();
            if (stepsToDelete.Any())
            {
                _dbSet.RemoveRange(stepsToDelete);
                // SaveChangesAsync 将由 UnitOfWork 处理
            }
        }
    }
} 