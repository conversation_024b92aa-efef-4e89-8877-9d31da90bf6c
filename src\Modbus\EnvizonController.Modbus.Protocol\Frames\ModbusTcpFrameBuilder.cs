using EnvizonController.Modbus.Protocol.Models;

namespace EnvizonController.Modbus.Protocol.Frames
{
    /// <summary>
    /// Modbus TCP帧构建器
    /// </summary>
    public class ModbusTcpFrameBuilder : IModbusFrameBuilder
    {
        private ushort _transactionId = 0;

        /// <summary>
        /// 构建请求帧
        /// </summary>
        /// <param name="request">Modbus请求</param>
        /// <returns>完整的请求帧字节数组</returns>
        public byte[] BuildRequestFrame(ModbusRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            // 获取请求的基本帧（地址+功能码+数据）
            byte[] frame = request.GetFrame();

            // 计算长度（不包括MBAP头）
            ushort length = (ushort)(frame.Length);

            // 创建MBAP头（7字节）
            byte[] mbapHeader = new byte[7];

            // 事务标识符（2字节）
            mbapHeader[0] = (byte)(_transactionId >> 8);
            mbapHeader[1] = (byte)(_transactionId & 0xFF);

            // 递增事务标识符
            _transactionId++;

            // 协议标识符（2字节，总是0）
            mbapHeader[2] = 0;
            mbapHeader[3] = 0;

            // 长度（2字节）
            mbapHeader[4] = (byte)(length >> 8);
            mbapHeader[5] = (byte)(length & 0xFF);

            // 单元标识符（1字节，通常与从站地址相同）
            mbapHeader[6] = request.SlaveAddress;

            // 组合MBAP头和PDU（不包括从站地址，因为它已经在MBAP头中）
            byte[] tcpFrame = new byte[mbapHeader.Length + frame.Length - 1];
            Array.Copy(mbapHeader, 0, tcpFrame, 0, mbapHeader.Length);
            Array.Copy(frame, 1, tcpFrame, mbapHeader.Length, frame.Length - 1);

            return tcpFrame;
        }

        /// <summary>
        /// 解析响应帧
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <param name="response">Modbus响应对象</param>
        /// <returns>是否成功解析</returns>
        public bool ParseResponseFrame(byte[] responseFrame, ModbusResponse response)
        {
            if (responseFrame == null || responseFrame.Length < 9) // 至少需要MBAP头(7)+功能码(1)+数据(至少1)
                return false;

            if (response == null)
                throw new ArgumentNullException(nameof(response));

            // 验证帧格式
            if (!ValidateResponseFrame(responseFrame))
                return false;

            try
            {
                // 从MBAP头获取单元标识符（从站地址）
                byte unitId = responseFrame[6];

                // 创建包含从站地址的PDU
                byte[] pdu = new byte[responseFrame.Length - 6];
                pdu[0] = unitId; // 从站地址
                Array.Copy(responseFrame, 7, pdu, 1, responseFrame.Length - 7);

                response.ParseResponse(pdu);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证响应帧的有效性
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <returns>响应帧是否有效</returns>
        public bool ValidateResponseFrame(byte[] responseFrame)
        {
            if (responseFrame == null || responseFrame.Length < 9)
                return false;

            // 验证协议标识符（应为0x0000）
            if (responseFrame[2] != 0x00 || responseFrame[3] != 0x00)
                return false;

            // 获取长度字段
            ushort length = (ushort)((responseFrame[4] << 8) | responseFrame[5]);

            // 验证长度字段与实际长度是否匹配
            return length == responseFrame.Length - 6;
        }
    }
}
