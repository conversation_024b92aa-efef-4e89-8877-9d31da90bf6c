using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Services;
using AutoMapper;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestItemsController : ControllerBase
    {
        private readonly ITestItemAppService _testItemService;
        private readonly ILogger<TestItemsController> _logger;
        private readonly IMapper _mapper;

        public TestItemsController(
            ITestItemAppService testItemService,
            ILogger<TestItemsController> logger,
            IMapper mapper)
        {
            _testItemService = testItemService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// 获取所有测试项
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>测试项列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<TestRunDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<TestRunDTO>>> GetTestItems(
            [FromQuery] TestItemQueryParams queryParams)
        {
            try
            {
                var pagedResult = await _testItemService.GetTestItemsAsync(queryParams);
                return Ok(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试项列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取测试项列表时发生错误");
            }
        }

        /// <summary>
        /// 根据ID获取测试项
        /// </summary>
        /// <param name="id">测试项ID</param>
        /// <returns>测试项详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(TestRunDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TestRunDTO>> GetTestItem(long id)
        {
            try
            {
                var testItem = await _testItemService.GetTestRunByIdAsync(id);
                if (testItem == null)
                {
                    return NotFound($"ID为{id}的测试项不存在");
                }

                return Ok(testItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试项 {TestItemId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取测试项详情时发生错误");
            }
        }

        /// <summary>
        /// 创建测试项
        /// </summary>
        /// <param name="testRunDTO">测试项信息</param>
        /// <returns>创建的测试项</returns>
        [HttpPost]
        [ProducesResponseType(typeof(TestRunDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TestRunDTO>> CreateTestItem(TestRunDTO testRunDTO)
        {
            try
            {
                // 直接调用应用服务创建测试项
                var createdTestItem = await _testItemService.CreateTestItemAsync(testRunDTO);
                
                return CreatedAtAction(nameof(GetTestItem), new { id = createdTestItem.Id }, createdTestItem);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "创建测试项时参数错误: {Message}", ex.Message);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建测试项时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "创建测试项时发生错误");
            }
        }

        /// <summary>
        /// 更新测试项
        /// </summary>
        /// <param name="id">测试项ID</param>
        /// <param name="testRunDTO">更新的测试项信息</param>
        /// <returns>更新后的测试项</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(TestRunDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TestRunDTO>> UpdateTestItem(long id, TestRunDTO testRunDTO)
        {
            try
            {
                if (id != testRunDTO.Id)
                {
                    return BadRequest("请求路径中的ID与请求体中的ID不匹配");
                }

                // 检查测试项是否存在
                var existingTestItem = await _testItemService.GetTestRunByIdAsync(id);
                if (existingTestItem == null)
                {
                    return NotFound($"ID为{id}的测试项不存在");
                }

                var updatedTestItem = await _testItemService.UpdateTestItemAsync(testRunDTO);
                return Ok(updatedTestItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新测试项 {TestItemId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "更新测试项时发生错误");
            }
        }

        /// <summary>
        /// 删除测试项
        /// </summary>
        /// <param name="id">测试项ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTestItem(long id)
        {
            try
            {
                var success = await _testItemService.DeleteTestItemAsync(id);
                if (!success)
                {
                    return NotFound($"ID为{id}的测试项不存在");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除测试项 {TestItemId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "删除测试项时发生错误");
            }
        }

        /// <summary>
        /// 启动测试
        /// </summary>
        /// <param name="id">测试项ID</param>
        /// <returns>启动后的测试项</returns>
        [HttpPost("{id}/start")]
        [ProducesResponseType(typeof(TestRunDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TestRunDTO>> StartTest(long id)
        {
            try
            {
                var testItem = await _testItemService.StartTestAsync(id);
                if (testItem == null)
                {
                    return BadRequest($"ID为{id}的测试项无法启动，可能已经在运行或已完成");
                }

                return Ok(testItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动测试 {TestItemId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "启动测试时发生错误");
            }
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        /// <param name="id">测试项ID</param>
        /// <returns>停止后的测试项</returns>
        [HttpPost("{id}/stop")]
        [ProducesResponseType(typeof(TestRunDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TestRunDTO>> StopTest(long id)
        {
            try
            {
                var testItem = await _testItemService.StopTestAsync(id);
                if (testItem == null)
                {
                    return BadRequest($"ID为{id}的测试项无法停止，可能未在运行");
                }

                return Ok(testItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止测试 {TestItemId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "停止测试时发生错误");
            }
        }
    }
}