# DataChartView 重构说明

## 概述

本次重构主要解决了图例项透明度与系列可见性状态不同步的问题，并重新设计了代码架构，使其更加易于维护和扩展。

## 主要改进

### 1. 统一的可见性管理器 (SeriesVisibilityManager)

创建了一个专门的管理器类来统一处理图例项和筛选项的可见性状态同步：

```csharp
public class SeriesVisibilityManager
{
    // 统一管理图例项和筛选项的关联关系
    private readonly Dictionary<string, (LegendItem legend, SeriesFilterItem filter)> _itemLinks;
    
    // 建立关联关系
    public void LinkItems(LegendItem legendItem, SeriesFilterItem filterItem);
    
    // 更新系列可见性（自动同步图例和筛选项）
    public void UpdateSeriesVisibility(string seriesName, bool isVisible, LineChartControl lineChart);
    
    // 更新Y轴可见性
    public void UpdateYAxisVisibility(string seriesName, bool isVisible, LineChartControl lineChart);
    
    // 切换图例项可见性
    public void ToggleLegendItemVisibility(string seriesName, LineChartControl lineChart);
    
    // 批量操作
    public void ShowAllSeries(LineChartControl lineChart);
    public void HideAllSeries(LineChartControl lineChart);
}
```

### 2. 增强的数据模型

#### LegendItem 增强版
```csharp
public class LegendItem : INotifyPropertyChanged
{
    public bool IsVisible { get; set; }
    
    // 新增：自动计算的透明度属性
    public double Opacity => IsVisible ? 1.0 : 0.4;
    
    // 当IsVisible改变时，自动通知Opacity属性更新
    set
    {
        if (_isVisible != value)
        {
            _isVisible = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(Opacity)); // 关键：同时通知透明度更新
        }
    }
}
```

#### SeriesFilterItem 增强版
```csharp
public class SeriesFilterItem : INotifyPropertyChanged
{
    public bool IsSeriesVisible { get; set; }
    public bool IsYAxisVisible { get; set; }
    
    // 新增：自动计算的透明度属性
    public double Opacity => IsSeriesVisible ? 1.0 : 0.4;
    
    // 当IsSeriesVisible改变时，自动通知Opacity属性更新
    set
    {
        if (_isSeriesVisible != value)
        {
            _isSeriesVisible = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(Opacity)); // 关键：同时通知透明度更新
        }
    }
}
```

### 3. XAML 绑定优化

#### 图例项透明度绑定
```xml
<Border Opacity="{Binding Opacity}" PointerPressed="OnLegendItemClick">
    <!-- 图例内容 -->
</Border>
```

#### 筛选项透明度绑定
```xml
<Border Opacity="{Binding Opacity}">
    <!-- 筛选项内容 -->
</Border>
```

## 架构优势

### 1. 单一职责原则
- `SeriesVisibilityManager`: 专门负责可见性状态管理
- `LegendItem`: 专门负责图例数据
- `SeriesFilterItem`: 专门负责筛选数据
- `DataChartView`: 专门负责UI交互

### 2. 状态同步自动化
- 任何一处的可见性改变都会自动同步到其他相关组件
- 透明度自动根据可见性状态计算，无需手动设置

### 3. 易于扩展
- 新增可见性相关功能只需在 `SeriesVisibilityManager` 中添加方法
- 数据模型支持轻松添加新的计算属性

### 4. 代码复用
- 所有可见性操作都通过管理器统一处理，避免重复代码
- 事件处理方法大幅简化

## 使用示例

### 初始化
```csharp
public DataChartView()
{
    InitializeComponent();
    _visibilityManager = new SeriesVisibilityManager(_legendItems, _filterItems);
    // ... 其他初始化
}
```

### 创建数据系列时建立关联
```csharp
var legendItem = new LegendItem { Name = protocol, Color = brush, IsVisible = true };
var filterItem = new SeriesFilterItem { SeriesName = protocol, IsSeriesVisible = true };

_legendItems.Add(legendItem);
_filterItems.Add(filterItem);

// 建立关联关系
_visibilityManager.LinkItems(legendItem, filterItem);
```

### 处理用户交互
```csharp
// 筛选项可见性改变
private void OnSeriesVisibilityChanged(object? sender, RoutedEventArgs e)
{
    if (sender is CheckBox checkBox && checkBox.DataContext is SeriesFilterItem filterItem)
    {
        _visibilityManager.UpdateSeriesVisibility(filterItem.SeriesName, checkBox.IsChecked == true, lineChart);
    }
}

// 图例项点击
private void OnLegendItemClick(object? sender, RoutedEventArgs e)
{
    if (sender is Border border && border.DataContext is LegendItem legendItem)
    {
        _visibilityManager.ToggleLegendItemVisibility(legendItem.Name, lineChart);
    }
}
```

## 关键特性

### 1. 自动透明度管理
- 图例项和筛选项的透明度会根据可见性状态自动调整
- 可见时透明度为 1.0，不可见时为 0.4
- 无需手动设置 `border.Opacity`

### 2. 双向同步
- 从筛选面板改变系列可见性 → 自动更新图例透明度
- 点击图例项切换可见性 → 自动更新筛选项状态
- 批量显示/隐藏 → 所有相关项目同步更新

### 3. 性能优化
- 使用字典快速查找关联项目
- 避免重复的UI更新操作
- 统一的刷新机制

## 维护指南

### 添加新的可见性功能
1. 在 `SeriesVisibilityManager` 中添加新方法
2. 在数据模型中添加相应属性（如需要）
3. 在XAML中添加绑定（如需要）
4. 在事件处理中调用管理器方法

### 修改透明度逻辑
只需修改 `LegendItem.Opacity` 和 `SeriesFilterItem.Opacity` 属性的计算逻辑即可。

### 扩展数据模型
在 `LegendItem` 或 `SeriesFilterItem` 中添加新属性，并在属性变更时调用 `OnPropertyChanged()`。

## 总结

本次重构通过引入专门的管理器类和增强的数据模型，解决了透明度同步问题，同时大幅提升了代码的可维护性和可扩展性。新架构遵循SOLID原则，使得未来的功能扩展变得更加容易。 