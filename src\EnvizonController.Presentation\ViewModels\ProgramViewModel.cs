using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using EnvizonController.ApiClient.Services;
using EnvizonController.Presentation.Extensions;
using EnvizonController.Presentation.Messages;
using EnvizonController.Presentation.ViewModels.Program;
using HanumanInstitute.MvvmDialogs;
using HanumanInstitute.MvvmDialogs.FrameworkDialogs;

namespace EnvizonController.Presentation.ViewModels;

/// <summary>
///     程式表管理视图模型
/// </summary>
public class ProgramViewModel : ViewModelBase
{
    private readonly IDialogService _dialogService;
    private readonly IProgramApiService _programApiService;
    private readonly IMessenger _messenger;

    public ProgramViewModel(IProgramApiService programApiService, IDialogService dialogService, IMessenger messenger)
    {
        _programApiService = programApiService;
        _dialogService = dialogService;
        _messenger = messenger;

        // 初始化命令
        SwitchToCurveViewCommand = new RelayCommand(() => { ShowCurveView = true; });
        SwitchToStepsViewCommand = new RelayCommand(() => { ShowStepsView = true; });

        AddProgramCommand = new AsyncRelayCommand(AddProgramAsync);
        EditProgramCommand = new AsyncRelayCommand<ProgramItemViewModel>(EditProgram);

        DeleteProgramCommand = new AsyncRelayCommand<ProgramItemViewModel>(DeleteProgramAsync);
        RefreshProgramsCommand = new AsyncRelayCommand(LoadProgramsAsync);

        // 初始化刷新程式步骤命令
        RefreshProgramStepsCommand = new AsyncRelayCommand(RefreshProgramStepsAsync, CanManageProgramSteps);

        AddProgramStepCommand = new RelayCommand(AddProgramStep, CanManageProgramSteps);
        EditProgramStepCommand = new RelayCommand(EditProgramStep, CanEditProgramStep);
        DeleteProgramStepCommand = new RelayCommand(DeleteProgramStep, CanDeleteProgramStep);
        MoveProgramStepUpCommand = new RelayCommand(MoveProgramStepUp, CanMoveProgramStepUp);
        MoveProgramStepDownCommand = new RelayCommand(MoveProgramStepDown, CanMoveProgramStepDown);

        // 添加行内编辑和删除确认命令
        InlineEditStepCommand = new RelayCommand<ProgramStepItemViewModel>(InlineEditStep);
        SaveStepEditCommand = new RelayCommand<ProgramStepItemViewModel>(SaveStepEdit);
        CancelStepEditCommand = new RelayCommand<ProgramStepItemViewModel>(CancelStepEdit);
        SaveAllStepsCommand = new AsyncRelayCommand(SaveAllStepsAsync);
        ConfirmDeleteStepCommand = new AsyncRelayCommand<ProgramStepItemViewModel>(ConfirmDeleteStep);
        ConfirmDeleteCommand = new RelayCommand(ConfirmDelete);
        CancelDeleteCommand = new RelayCommand(CancelDelete);

        // 错误消息展开切换命令
        ToggleErrorExpandCommand = new RelayCommand(ToggleErrorExpand);

        // 清除错误消息命令
        ClearErrorCommand = new RelayCommand(ClearError);

        // 初始加载数据
        _ = LoadProgramsAsync();
    }

    #region 属性

    // 程式表列表
    public ObservableCollection<ProgramItemViewModel> Programs { get; } = new();

    // 程式步骤列表
    public ObservableCollection<ProgramStepItemViewModel> ProgramSteps { get; } = new();

    // 当前选中的程式表
    private ProgramItemViewModel? _selectedProgram;

    public ProgramItemViewModel? SelectedProgram
    {
        get => _selectedProgram;
        set
        {
            if (SetProperty(ref _selectedProgram, value) && value != null) _ = LoadProgramStepsAsync(value.Id);

            AddProgramStepCommand.NotifyCanExecuteChanged();
            RefreshProgramStepsCommand.NotifyCanExecuteChanged();
        }
    }

    // 当前选中的程式步骤
    private ProgramStepItemViewModel? _selectedProgramStep;

    public ProgramStepItemViewModel? SelectedProgramStep
    {
        get => _selectedProgramStep;
        set
        {
            SetProperty(ref _selectedProgramStep, value);
            MoveProgramStepUpCommand.NotifyCanExecuteChanged();
            MoveProgramStepDownCommand.NotifyCanExecuteChanged();
        }
    }

    // 当前正在编辑的步骤
    private ProgramStepItemViewModel _editingProgramStep;

    public ProgramStepItemViewModel EditingProgramStep
    {
        get => _editingProgramStep;
        set => SetProperty(ref _editingProgramStep, value);
    }

    // 当前编辑步骤的备份（用于取消编辑时恢复）
    private ProgramStepItemViewModel? _editingProgramStepBackup;

    // 要删除的步骤
    private ProgramStepItemViewModel? _stepToDelete;

    // 删除确认弹窗可见性
    private bool _isShowingDeleteConfirm;

    public bool IsShowingDeleteConfirm
    {
        get => _isShowingDeleteConfirm;
        set => SetProperty(ref _isShowingDeleteConfirm, value);
    }

    // 显示视图类型
    private bool _showCurveView = true;

    public bool ShowCurveView
    {
        get => _showCurveView;
        set
        {
            if (SetProperty(ref _showCurveView, value) && value) ShowStepsView = false;
        }
    }

    private bool _showStepsView;

    public bool ShowStepsView
    {
        get => _showStepsView;
        set
        {
            if (SetProperty(ref _showStepsView, value) && value) ShowCurveView = false;
        }
    }

    // 加载状态
    private bool _isLoading;

    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    // 错误信息
    private string _errorMessage;

    public string ErrorMessage
    {
        get => _errorMessage;
        set => SetProperty(ref _errorMessage, value);
    }

    // 错误信息是否展开
    private bool _isErrorExpanded;

    public bool IsErrorExpanded
    {
        get => _isErrorExpanded;
        set => SetProperty(ref _isErrorExpanded, value);
    }

    #endregion

    #region 命令

    // 程式表命令
    public IRelayCommand SwitchToCurveViewCommand { get; }
    public IRelayCommand SwitchToStepsViewCommand { get; }
    public IRelayCommand AddProgramCommand { get; }
    public IAsyncRelayCommand<ProgramItemViewModel> EditProgramCommand { get; }
    public IAsyncRelayCommand<ProgramItemViewModel> DeleteProgramCommand { get; }
    public IRelayCommand RefreshProgramsCommand { get; }

    // 刷新程式步骤命令
    public IRelayCommand RefreshProgramStepsCommand { get; }

    // 程式步骤命令
    public IRelayCommand AddProgramStepCommand { get; }
    public IRelayCommand EditProgramStepCommand { get; }
    public IRelayCommand DeleteProgramStepCommand { get; }
    public IRelayCommand MoveProgramStepUpCommand { get; }
    public IRelayCommand MoveProgramStepDownCommand { get; }

    // 行内编辑命令
    public IRelayCommand<ProgramStepItemViewModel> InlineEditStepCommand { get; }
    public IRelayCommand<ProgramStepItemViewModel> SaveStepEditCommand { get; }
    public IRelayCommand<ProgramStepItemViewModel> CancelStepEditCommand { get; }

    // 保存所有步骤命令
    public IAsyncRelayCommand SaveAllStepsCommand { get; }

    // 删除确认命令
    public IAsyncRelayCommand<ProgramStepItemViewModel> ConfirmDeleteStepCommand { get; }
    public IRelayCommand ConfirmDeleteCommand { get; }
    public IRelayCommand CancelDeleteCommand { get; }

    // 错误消息展开切换命令
    public IRelayCommand ToggleErrorExpandCommand { get; }

    // 清除错误消息命令
    public IRelayCommand ClearErrorCommand { get; }

    #endregion

    #region 程式表方法

    public async Task LoadProgramsAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            var result = await _programApiService.GetProgramsAsync();

            if (result.IsSuccess)
            {
                Programs.Clear();
                foreach (var programDto in result.Data.Items) Programs.Add(ProgramItemViewModel.FromDto(programDto));

                // 如果有数据且没有选中项，则选中第一项
                if (Programs.Any() && SelectedProgram == null) SelectedProgram = Programs.First();
            }
            else
            {
                ErrorMessage = $"加载程式表失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"加载程式表异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadProgramStepsAsync(long programId)
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;


            var result = await _programApiService.GetProgramStepsAsync(programId);

            if (result.IsSuccess)
            {
                ProgramSteps.Clear();
                foreach (var stepDto in result.Data) ProgramSteps.Add(ProgramStepItemViewModel.FromDto(stepDto));

                // 排序
                var sortedSteps = ProgramSteps.OrderBy(s => s.Index).ToList();
                ProgramSteps.Clear();
                foreach (var step in sortedSteps) ProgramSteps.Add(step);
            }
            else
            {
                ErrorMessage = $"加载程式步骤失败: {result.ErrorMessage}";
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"加载程式步骤异常: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task AddProgramAsync()
    {
        var programItemViewModel = await _dialogService.AddProgramAsync(this);

        // 在实际应用中，这里应该调用对话框进行编辑然后保存到数据库
        // 这里简化为直接添加到集合
        if (programItemViewModel != null)
        {
            Programs.Add(programItemViewModel);
            SelectedProgram = programItemViewModel;
            
            // 发送程序添加消息
            _messenger.Send(new ProgramChangedMessage(programItemViewModel, ChangeType.Added));
        }
    }

    private async Task EditProgram(ProgramItemViewModel programItemViewModel)
    {
        var editedItemViewModel = await _dialogService.EditProgramAsync(this, programItemViewModel);

        if (editedItemViewModel != null)
        {
            // 重新加载程序列表以确保所有更改都得到反映
            await LoadProgramsAsync();
            
            // 发送程序更新消息
            _messenger.Send(new ProgramChangedMessage(editedItemViewModel, ChangeType.Updated));
        }
    }

    private async Task DeleteProgramAsync(ProgramItemViewModel programItemViewModel)
    {
        // 显示确认对话框
        var confirmResult = await _dialogService.ShowMessageBoxAsync(
            $"确定要删除程式表 '{programItemViewModel?.Name}' 吗？此操作不可恢复。",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (confirmResult != true) return; // User clicked No or closed the dialog

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // 调用API删除程式表
            var response = await _programApiService.DeleteProgramAsync(programItemViewModel.Id);

            if (response.IsSuccess)
            {
                // 从集合中移除已删除的程式表
                var programToRemove = programItemViewModel;
                Programs.Remove(programToRemove);
                SelectedProgram = Programs.FirstOrDefault();

                // 发送程序删除消息
                _messenger.Send(new ProgramChangedMessage(programToRemove, ChangeType.Deleted));

                // 显示成功消息
                await _dialogService.ShowMessageBoxAsync(
                    $"程式表 '{programToRemove?.Name}' 已成功删除。",
                    "删除成功",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Information);
            }
            else
            {
                ErrorMessage = $"删除程式表失败: {response.ErrorMessage}";
                await _dialogService.ShowMessageBoxAsync(
                    ErrorMessage,
                    "删除失败",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"删除程式表异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                ErrorMessage,
                "错误",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    #endregion

    #region 保存所有步骤

    private async Task SaveAllStepsAsync()
    {
        try
        {
            if (SelectedProgram == null)
            {
                return;
            }

            IsLoading = true;
            ErrorMessage = string.Empty;

            // 获取所有步骤的DTO
            var stepsDto = ProgramSteps.Select(s => s.ToDto()).ToList();

            // 调用API保存
            var result = await _programApiService.BatchUpdateProgramStepsAsync(SelectedProgram.Id, stepsDto);

            if (result.IsSuccess)
            {
                await LoadProgramsAsync();
                await _dialogService.ShowMessageBoxAsync(
                    "程序步骤保存成功！",
                    "保存成功",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Information);
            }
            else
            {
                ErrorMessage = $"保存程式步骤失败: {result.ErrorMessage}";
                await _dialogService.ShowMessageBoxAsync(
                    $"保存程式步骤失败: {result.ErrorMessage}",
                    "保存失败",
                    MessageBoxButton.Ok,
                    MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"保存程式步骤异常: {ex.Message}";
            await _dialogService.ShowMessageBoxAsync(
                $"保存程式步骤异常: {ex.Message}",
                "保存异常",
                MessageBoxButton.Ok,
                MessageBoxImage.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    #endregion

    #region 程式步骤方法

    private async Task RefreshProgramStepsAsync()
    {
        if (SelectedProgram != null)
        {
            await LoadProgramStepsAsync(SelectedProgram.Id);
        }
    }

    private void AddProgramStep()
    {
        if (SelectedProgram != null)
        {
            var maxIndex = ProgramSteps.Count > 0 ? ProgramSteps.Max(s => s.Index) : 0;
            var newStep = new ProgramStepItemViewModel
            {
                ProgramId = SelectedProgram.Id,
                Index = maxIndex + 1,
                Temperature = 25.0, // 默认温度
                Humidity = 50.0, // 默认湿度
                Duration = 3600, // 默认持续时间（1小时）
                IsLinear = false, // 默认非线性
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
            };

            ProgramSteps.Add(newStep);
            SelectedProgramStep = newStep;

            // 更新命令可执行状态
            MoveProgramStepUpCommand.NotifyCanExecuteChanged();
            MoveProgramStepDownCommand.NotifyCanExecuteChanged();
        }
    }

    private bool CanManageProgramSteps()
    {
        return SelectedProgram != null;
    }

    private void EditProgramStep()
    {
        if (SelectedProgramStep != null)
        {
            // 在实际应用中，这里应该调用对话框进行编辑然后更新到数据库
            // 这里简化为直接修改对象
            SelectedProgramStep.Temperature += 1;
            SelectedProgramStep.UpdatedAt = DateTime.Now;
        }
    }

    private bool CanEditProgramStep()
    {
        return SelectedProgramStep != null;
    }

    private async void DeleteProgramStep()
    {
        if (SelectedProgramStep != null)
        {
            // 显示确认对话框
            var confirmResult = await _dialogService.ShowMessageBoxAsync(
                $"确定要删除此程序步骤吗？",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (confirmResult == true)
            {
                // 用户确认后从集合中移除
                ProgramSteps.Remove(SelectedProgramStep);
                SelectedProgramStep = ProgramSteps.FirstOrDefault();

                // 重新编号
                var index = 1;
                foreach (var step in ProgramSteps.OrderBy(s => s.Index)) step.Index = index++;
            }
        }
    }

    private bool CanDeleteProgramStep()
    {
        return SelectedProgramStep != null;
    }

    private void MoveProgramStepUp()
    {
        if (SelectedProgramStep != null && SelectedProgramStep.Index > 0)
        {
            var currentIndex = SelectedProgramStep.Index;
            var stepAbove = ProgramSteps.FirstOrDefault(s => s.Index == currentIndex - 1);

            if (stepAbove != null)
            {
                // 交换索引
                stepAbove.Index = currentIndex;
                SelectedProgramStep.Index = currentIndex - 1;

                // 重新排序显示
                var sortedSteps = ProgramSteps.OrderBy(s => s.Index).ToList();
                ProgramSteps.Clear();
                foreach (var step in sortedSteps) ProgramSteps.Add(step);

                // 确保选中的步骤仍然被选中
                SelectedProgramStep = ProgramSteps.FirstOrDefault(s => s.Index == currentIndex - 1);


                // 更新命令可执行状态
                MoveProgramStepUpCommand.NotifyCanExecuteChanged();
                MoveProgramStepDownCommand.NotifyCanExecuteChanged();
            }
        }
    }

    private bool CanMoveProgramStepUp()
    {
        return SelectedProgramStep is { Index: > 1 };
    }

    private void MoveProgramStepDown()
    {
        if (SelectedProgramStep != null)
        {
            var currentIndex = SelectedProgramStep.Index;
            var maxIndex = ProgramSteps.Count > 0 ? ProgramSteps.Max(s => s.Index) : 0;

            if (currentIndex < maxIndex)
            {
                var stepBelow = ProgramSteps.FirstOrDefault(s => s.Index == currentIndex + 1);

                if (stepBelow != null)
                {
                    // 交换索引
                    stepBelow.Index = currentIndex;
                    SelectedProgramStep.Index = currentIndex + 1;

                    // 重新排序显示
                    var sortedSteps = ProgramSteps.OrderBy(s => s.Index).ToList();
                    ProgramSteps.Clear();
                    foreach (var step in sortedSteps) ProgramSteps.Add(step);

                    // 确保选中的步骤仍然被选中
                    SelectedProgramStep = ProgramSteps.FirstOrDefault(s => s.Index == currentIndex + 1);

                    // 更新命令可执行状态
                    MoveProgramStepUpCommand.NotifyCanExecuteChanged();
                    MoveProgramStepDownCommand.NotifyCanExecuteChanged();
                }
            }
        }
    }

    private bool CanMoveProgramStepDown()
    {
        return SelectedProgramStep != null &&
               ProgramSteps.Count > 0 &&
               SelectedProgramStep.Index < ProgramSteps.Max(s => s.Index);
    }

    #endregion

    #region 行内编辑方法

    private void InlineEditStep(ProgramStepItemViewModel step)
    {
        if (step != null)
        {
            // 如果已经有正在编辑的步骤，先取消编辑
            if (EditingProgramStep != null && EditingProgramStep != step)
            {
                CancelStepEdit(EditingProgramStep);
            }

            // 创建步骤的备份，用于取消编辑时恢复
            _editingProgramStepBackup = new ProgramStepItemViewModel
            {
                Id = step.Id,
                ProgramId = step.ProgramId,
                Index = step.Index,
                Temperature = step.Temperature,
                Humidity = step.Humidity,
                Duration = step.Duration,
                IsLinear = step.IsLinear,
                CreatedAt = step.CreatedAt,
                UpdatedAt = step.UpdatedAt
            };

            // 设置当前编辑的步骤
            EditingProgramStep = step;
            SelectedProgramStep = step;

            // 在实际应用中，这里应该进入行内编辑模式
            // 在XAML中，我们使用DataGrid的CellEditingTemplate来实现这一点
        }
    }

    private void SaveStepEdit(ProgramStepItemViewModel step)
    {
        if (step != null)
        {
            // 在实际应用中，这里应该调用API保存更改
            // 这里简化为直接更新对象
            step.UpdatedAt = DateTime.Now;

            // 清除编辑状态
            EditingProgramStep = null;
            _editingProgramStepBackup = null;
        }
    }

    private void CancelStepEdit(ProgramStepItemViewModel step)
    {
        if (step != null && _editingProgramStepBackup != null)
        {
            // 恢复为备份的值
            step.Temperature = _editingProgramStepBackup.Temperature;
            step.Humidity = _editingProgramStepBackup.Humidity;
            step.Duration = _editingProgramStepBackup.Duration;
            step.IsLinear = _editingProgramStepBackup.IsLinear;

            // 清除编辑状态
            EditingProgramStep = null;
            _editingProgramStepBackup = null;
        }
    }

    #endregion

    #region 删除确认方法

    private async Task ConfirmDeleteStep(ProgramStepItemViewModel deleteStepItemViewModel)
    {
        // 显示确认对话框
        var confirmResult = await _dialogService.ShowMessageBoxAsync(
            $"确定要删除此程序步骤吗？",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);

        if (confirmResult == true)
        {
            // 用户确认后从集合中移除
            ProgramSteps.Remove(deleteStepItemViewModel);

            // 重新编号
            var index = 1;
            foreach (var step in ProgramSteps.OrderBy(s => s.Index)) step.Index = index++;
        }
    }

    private void ConfirmDelete()
    {
        if (_stepToDelete != null)
        {
            // 在实际应用中，这里应该调用API删除步骤
            // 这里简化为直接从集合中移除
            ProgramSteps.Remove(_stepToDelete);

            // 如果删除的是当前选中的步骤，重新选择第一个步骤
            if (SelectedProgramStep == _stepToDelete)
                SelectedProgramStep = ProgramSteps.FirstOrDefault();

            // 重新编号
            var index = 1;
            foreach (var step in ProgramSteps.OrderBy(s => s.Index))
                step.Index = index++;

            // 清除状态
            _stepToDelete = null;
            IsShowingDeleteConfirm = false;
        }
    }

    private void CancelDelete()
    {
        // 取消删除，清除状态
        _stepToDelete = null;
        IsShowingDeleteConfirm = false;
    }

    #endregion

    #region 错误消息展开切换方法

    private void ToggleErrorExpand()
    {
        IsErrorExpanded = !IsErrorExpanded;
    }

    #endregion

    #region 清除错误消息方法

    private void ClearError()
    {
        ErrorMessage = string.Empty;
    }

    #endregion
}  