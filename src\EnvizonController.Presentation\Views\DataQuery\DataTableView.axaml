<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQuery.DataTableView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls1="clr-namespace:EnvizonController.Presentation.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dq="clr-namespace:EnvizonController.Presentation.ViewModels.DataQuery"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:EnvizonController.Shared.DTOs;assembly=EnvizonController.Shared"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="600"
    d:DesignWidth="800"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">

    <Grid RowDefinitions="*,Auto">
        <!--  数据表格  -->
        <DataGrid
            x:Name="dataGrid"
            Grid.Row="0"
            Margin="10"
            AutoGenerateColumns="False"
            Background="Transparent"
            CanUserResizeColumns="True"
            CanUserSortColumns="True"
            GridLinesVisibility="All"
            IsReadOnly="True"
            ItemsSource="{Binding DataPoints}">

            <DataGrid.Styles>
                <Style Selector="DataGridColumnHeader">
                    <Setter Property="Background" Value="#1A2A3A" />
                    <Setter Property="Foreground" Value="#0DF0FF" />
                    <Setter Property="FontWeight" Value="Bold" />
                    <Setter Property="HorizontalContentAlignment" Value="Center" />
                </Style>
                <Style Selector="DataGridCell">
                    <Setter Property="Foreground" Value="White" />
                    <Setter Property="HorizontalContentAlignment" Value="Left" />
                </Style>
            </DataGrid.Styles>
        </DataGrid>

        <!--  分页控件  -->
        <controls1:PaginationControl
            Grid.Row="1"
            Margin="0,0,0,20"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom"
            MaxVisiblePages="3"
            PageChangedCommand="{Binding PageChangedCommand}"
            PaginationInfo="{Binding PaginationInfo}"
            ShowFirstLastButtons="False"
            ShowNumericButtons="False"
            ShowCurrentPageInfo="True" />
    </Grid>
</UserControl>