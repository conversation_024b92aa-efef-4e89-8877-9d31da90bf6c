using EnvizonController.Modbus.Abstractions.Clients;
using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Models;

namespace EnvizonController.Modbus.Client
{
    /// <summary>
    /// Modbus客户端实现
    /// </summary>
    public class ModbusClient : ModbusClientBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="channel">通信通道</param>
        /// <param name="transportType">传输类型</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="retryDelayMilliseconds">重试延迟（毫秒）</param>
        /// <param name="responseTimeout">响应超时（毫秒）</param>
        public ModbusClient(
            IModbusChannel channel,
            ModbusTransportType transportType,
            int retryCount = 3,
            int retryDelayMilliseconds = 100,
            int responseTimeout = 1000)
            : base(channel, transportType, retryCount, retryDelayMilliseconds, responseTimeout)
        {
        }
    }
}
