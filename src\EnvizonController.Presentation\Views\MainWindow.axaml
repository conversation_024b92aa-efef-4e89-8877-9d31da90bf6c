<Window
    x:Class="EnvizonController.Presentation.Views.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="clr-namespace:EnvizonController.Presentation.Views"
    Title="EnvizonController"
    Width="1280"
    Height="800"
    d:DesignHeight="800"
    d:DesignWidth="1280"
    Background="#050510"
    CanResize="True"
    ExtendClientAreaChromeHints="NoChrome"
    ExtendClientAreaTitleBarHeightHint="-1"
    ExtendClientAreaToDecorationsHint="True"
    Icon="/Assets/avalonia-logo.ico"
    SystemDecorations="BorderOnly"
    TransparencyLevelHint="Transparent"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <views:MainView />
</Window>
