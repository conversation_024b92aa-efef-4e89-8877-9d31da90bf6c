using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using Serilog;
using System;
using EnvizonController.Application.Devices;

namespace EnvizonController.Infrastructure.Services.Devices
{
    /// <summary>
    /// Modbus设备服务工厂
    /// </summary>
    public class ModbusDeviceServiceFactory
    {
        private readonly ILogger _logger;
        private readonly IModbusChannelFactory _channelFactory;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="channelFactory">Modbus通道工厂</param>
        /// <param name="serviceProvider">服务提供者</param>
        public ModbusDeviceServiceFactory(
            ILogger logger, 
            IModbusChannelFactory channelFactory,
            IServiceProvider serviceProvider)
        {
            _logger = logger ?? Log.ForContext<ModbusDeviceServiceFactory>();
            _channelFactory = channelFactory;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 创建Modbus设备服务
        /// </summary>
        /// <param name="device">设备配置</param>
        /// <returns>Modbus设备服务</returns>
        public IModbusDeviceService CreateDeviceService(Device device)
        {
            var deviceLogger = _logger.ForContext<ModbusDeviceService>();
            return new ModbusDeviceService(deviceLogger, device, _serviceProvider, _channelFactory);
        }

        /// <summary>
        /// 创建串口Modbus设备服务
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="portName">端口名称</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>Modbus设备服务</returns>
        public IModbusDeviceService CreateSerialDeviceService(
            long deviceId,
            string deviceName,
            string portName,
            int baudRate = 9600,
            byte slaveAddress = 1)
        {
            // 创建设备实体
            var device = Device.Create(
                name: deviceName,
                slaveId: slaveAddress,
                protocolId: 0,
                connectionType: "Serial",
                transportType: "Rtu");
            
            // 设置其他属性
            device.Id = deviceId;
            device.PortName = portName;
            device.BaudRate = baudRate;
            device.DataBits = 8;
            device.Parity = 0; // None
            device.StopBits = 1; // One
            device.ReadTimeout = 1000;
            device.WriteTimeout = 1000;
            
            return CreateDeviceService(device);
        }

        /// <summary>
        /// 创建TCP Modbus设备服务
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="hostAddress">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="slaveAddress">从站地址</param>
        /// <returns>Modbus设备服务</returns>
        public IModbusDeviceService CreateTcpDeviceService(
            long deviceId,
            string deviceName,
            string hostAddress,
            int port = 502,
            byte slaveAddress = 1)
        {
            // 创建设备实体
            var device = Device.Create(
                name: deviceName,
                slaveId: slaveAddress,
                protocolId: 0,
                connectionType: "Network",
                transportType: "Tcp");
            
            // 设置其他属性
            device.Id = deviceId;
            device.HostAddress = hostAddress;
            device.Port = port;
            device.ConnectionTimeout = 5000;
            
            return CreateDeviceService(device);
        }
    }
}
