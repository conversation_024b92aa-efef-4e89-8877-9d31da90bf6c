﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Shared.DTOs
{
    public partial class ChamberModel : ObservableObject
    {
        [ObservableProperty]
        private int _chamberId;

        [ObservableProperty]
        private string _chamberName;

        [ObservableProperty]
        private ChamberStatus _status;

        [ObservableProperty]
        private bool _isPoweredOn;

        [ObservableProperty]
        private AlarmLevel _alarmLevel;

        [ObservableProperty]
        private string _alarmMessage;

        [ObservableProperty]
        private double _temp;
        [ObservableProperty]
        private double _hum;

        // 传感器数据字典 - 可扩展性设计
        [ObservableProperty]
        private ObservableDictionary<string, double> _sensorData = new();

        public ChamberModel(int chamberId, string chamberName)
        {
            _chamberId = chamberId;
            _chamberName = chamberName;
            _status = ChamberStatus.NotInitialized;
            _isPoweredOn = false;
            _alarmLevel = AlarmLevel.None;

            // 初始化常见传感器数据
            SensorData.Add("Temperature", 0);
            SensorData.Add("Humidity", 0);
        }
    }
}
