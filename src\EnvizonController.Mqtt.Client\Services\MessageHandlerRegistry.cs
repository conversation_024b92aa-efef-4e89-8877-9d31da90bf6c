using EnvizonController.Mqtt.Client.Handlers;
using Serilog;

namespace EnvizonController.Mqtt.Client.Services
{
    public class MessageHandlerRegistry : IMessageHandlerRegistry
    {
        private readonly Dictionary<string, List<IMessageHandler>> _handlers = new();
        private readonly ILogger _logger;

        public MessageHandlerRegistry(ILogger logger)
        {
            _logger = logger;
        }

        public void RegisterHandler(string topicPattern, IMessageHandler handler)
        {
            if (!_handlers.ContainsKey(topicPattern))
            {
                _handlers[topicPattern] = new List<IMessageHandler>();
            }
            
            if (!_handlers[topicPattern].Contains(handler))
            {
                _handlers[topicPattern].Add(handler);
                _logger.Debug($"已注册消息处理器: Topic={topicPattern}, Hand<PERSON>={handler.GetType().Name}");
            }
        }

        public void UnregisterHandler(string topicPattern, IMessageHandler handler)
        {
            if (_handlers.Contains<PERSON><PERSON>(topicPattern))
            {
                _handlers[topicPattern].Remove(handler);
                _logger.Debug($"已注销消息处理器: Topic={topicPattern}, Handler={handler.GetType().Name}");
                
                if (_handlers[topicPattern].Count == 0)
                {
                    _handlers.Remove(topicPattern);
                }
            }
        }

        public IEnumerable<IMessageHandler> GetHandlers(string topic)
        {
            var result = new List<IMessageHandler>();
            
            foreach (var entry in _handlers)
            {
                if (TopicMatchesPattern(topic, entry.Key))
                {
                    result.AddRange(entry.Value);
                }
            }
            
            return result;
        }

        private bool TopicMatchesPattern(string topic, string pattern)
        {
            // 实现MQTT主题匹配逻辑
            // 例如: "sensors/+/temperature" 匹配 "sensors/living-room/temperature"
            if (topic == pattern)
            {
                return true;
            }

            string[] topicLevels = topic.Split('/');
            string[] patternLevels = pattern.Split('/');

            // '#'通配符允许匹配零个或多个级别
            if (patternLevels[patternLevels.Length - 1] == "#")
            {
                // 检查前面的级别是否匹配
                for (int i = 0; i < patternLevels.Length - 1; i++)
                {
                    if (i >= topicLevels.Length)
                    {
                        return false;
                    }

                    if (patternLevels[i] != "+" && patternLevels[i] != topicLevels[i])
                    {
                        return false;
                    }
                }
                return true;
            }

            // 如果级别数不同且没有'#'通配符，则不匹配
            if (topicLevels.Length != patternLevels.Length)
            {
                return false;
            }

            // 检查每个级别
            for (int i = 0; i < topicLevels.Length; i++)
            {
                // '+'通配符匹配当前级别中的任何内容
                if (patternLevels[i] != "+" && patternLevels[i] != topicLevels[i])
                {
                    return false;
                }
            }

            return true;
        }
    }
} 