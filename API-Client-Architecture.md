# EnvizonController客户端API访问接口架构设计

## 1. 需求分析与目标

基于EnvizonController项目的结构，设计一个符合设计模式原则的客户端API访问接口架构，使EnvizonController.Presentation能够访问EnvizonController.Server.Controllers提供的API。

### 设计目标

1. 符合SOLID原则，特别是单一职责原则和依赖倒置原则
2. 利用适当的设计模式（如Repository模式、工厂模式、适配器模式等）
3. 支持依赖注入
4. 处理异步操作和错误处理
5. 提供清晰的接口抽象，使客户端代码与具体实现解耦

## 2. 总体架构设计

采用分层架构设计，将客户端API访问接口分为以下几层：

```mermaid
graph TD
    A[Presentation Layer] --> B[API Client Abstractions]
    B --> C[API Client Implementations]
    C --> D[HTTP Client/Communication]
    
    E[错误处理] --> B
    E --> C
    
    F[模型映射] --> B
    F --> C
    
    G[配置管理] --> C
```

### 2.1 关键组件和层次

1. **API接口抽象层（API Client Abstractions）**：
   - 定义所有API操作的接口
   - 不包含任何实现细节
   - 按业务功能划分接口

2. **API实现层（API Client Implementations）**：
   - 实现API接口抽象层定义的接口
   - 处理HTTP请求、序列化和反序列化
   - 实现Result模式的错误处理

3. **HTTP通信层（HTTP Client/Communication）**：
   - 封装底层HTTP通信细节
   - 提供HTTP请求的创建、发送和处理

4. **错误处理组件**：
   - 实现Result模式返回结果
   - 统一处理API错误和异常

5. **配置管理组件**：
   - 集成现有的EnvizonController.Configuration组件
   - 提供API客户端特定的配置

## 3. 详细设计

### 3.1 核心接口设计

#### 3.1.1 Http客户端接口

```csharp
namespace EnvizonController.ApiClient.Http
{
    /// <summary>
    /// HTTP客户端接口
    /// </summary>
    public interface IHttpClient
    {
        /// <summary>
        /// 发送GET请求
        /// </summary>
        Task<Result<T>> GetAsync<T>(string url, IDictionary<string, string> queryParams = null);

        /// <summary>
        /// 发送POST请求
        /// </summary>
        Task<Result<T>> PostAsync<T, TContent>(string url, TContent content);

        /// <summary>
        /// 发送PUT请求
        /// </summary>
        Task<Result<T>> PutAsync<T, TContent>(string url, TContent content);

        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        Task<Result<T>> DeleteAsync<T>(string url);
    }
}
```

#### 3.1.2 Result模式接口

```csharp
namespace EnvizonController.ApiClient.Results
{
    /// <summary>
    /// API结果接口
    /// </summary>
    public interface IResult
    {
        bool IsSuccess { get; }
        string ErrorMessage { get; }
        int StatusCode { get; }
    }

    /// <summary>
    /// 泛型API结果接口
    /// </summary>
    public interface IResult<T> : IResult
    {
        T Data { get; }
    }

    /// <summary>
    /// API结果实现
    /// </summary>
    public class Result<T> : IResult<T>
    {
        public bool IsSuccess { get; private set; }
        public string ErrorMessage { get; private set; }
        public int StatusCode { get; private set; }
        public T Data { get; private set; }

        private Result(bool isSuccess, T data, string errorMessage, int statusCode)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            StatusCode = statusCode;
        }

        public static Result<T> Success(T data, int statusCode = 200)
        {
            return new Result<T>(true, data, null, statusCode);
        }

        public static Result<T> Failure(string errorMessage, int statusCode)
        {
            return new Result<T>(false, default, errorMessage, statusCode);
        }
    }
}
```

#### 3.1.3 API客户端配置

利用现有的EnvizonController.Configuration组件，扩展DefaultConfig类：

```csharp
namespace EnvizonController.Configuration.Models
{
    /// <summary>
    /// API客户端配置
    /// </summary>
    public class ApiClientSettings : ObservableObject
    {
        /// <summary>
        /// API服务器基础URL
        /// </summary>
        [ObservableProperty]
        private string _baseUrl = "http://localhost:5000";

        /// <summary>
        /// HTTP请求超时时间（毫秒）
        /// </summary>
        [ObservableProperty]
        private int _timeoutMs = 30000;

        /// <summary>
        /// 重试次数
        /// </summary>
        [ObservableProperty]
        private int _retryCount = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        [ObservableProperty]
        private int _retryDelayMs = 1000;
    }
}

// 在DefaultConfig中添加API客户端设置
public class DefaultConfig
{
    public MqttSettings MqttSettings { get; set; } = new MqttSettings();
    public ApiClientSettings ApiClientSettings { get; set; } = new ApiClientSettings();
    // 其他现有配置...
}
```

### 3.2 API服务接口设计

每个控制器对应一个API服务接口：

#### 3.2.1 设备API服务接口

```csharp
namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 设备API服务接口
    /// </summary>
    public interface IDeviceApiService
    {
        /// <summary>
        /// 获取所有设备
        /// </summary>
        Task<Result<PagedResultDto<DeviceDTO>>> GetDevicesAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        Task<Result<DeviceDTO>> GetDeviceAsync(long id);

        /// <summary>
        /// 创建设备
        /// </summary>
        Task<Result<DeviceDTO>> CreateDeviceAsync(DeviceDTO device);

        /// <summary>
        /// 更新设备
        /// </summary>
        Task<Result<DeviceDTO>> UpdateDeviceAsync(long id, DeviceDTO device);

        /// <summary>
        /// 删除设备
        /// </summary>
        Task<Result<bool>> DeleteDeviceAsync(long id);
    }
}
```

#### 3.2.2 报警API服务接口

```csharp
namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 报警API服务接口
    /// </summary>
    public interface IAlarmApiService
    {
        /// <summary>
        /// 获取报警列表
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取活动报警
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetActiveAlarmsAsync(int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取指定级别的报警
        /// </summary>
        Task<Result<PagedResultDto<AlarmDTO>>> GetAlarmsByLevelAsync(AlarmSeverity level, int page = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取报警
        /// </summary>
        Task<Result<AlarmDTO>> GetAlarmAsync(string id);

        /// <summary>
        /// 创建报警
        /// </summary>
        Task<Result<AlarmDTO>> CreateAlarmAsync(AlarmDTO alarm);

        /// <summary>
        /// 处理报警
        /// </summary>
        Task<Result<AlarmDTO>> ProcessAlarmAsync(long id, string processedBy);
    }
}
```

#### 3.2.3 数据点API服务接口

```csharp
namespace EnvizonController.ApiClient.Services
{
    /// <summary>
    /// 数据点API服务接口
    /// </summary>
    public interface IDataPointApiService
    {
        /// <summary>
        /// 获取测试运行的数据点
        /// </summary>
        Task<Result<PagedResultDto<DataPointDTO>>> GetTestRunDataPointsAsync(long testId, int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取设备的数据点
        /// </summary>
        Task<Result<PagedResultDto<DataPointDTO>>> GetDeviceDataPointsAsync(long deviceId, DateTime? startTime, DateTime? endTime, int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取数据点详情
        /// </summary>
        Task<Result<DataPointDTO>> GetDataPointAsync(long id);
    }
}
```

### 3.3 API服务实现设计

每个API服务接口对应一个实现类：

#### 3.3.1 设备API服务实现

```csharp
namespace EnvizonController.ApiClient.Services.Implementations
{
    /// <summary>
    /// 设备API服务实现
    /// </summary>
    public class DeviceApiService : IDeviceApiService
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;

        public DeviceApiService(IHttpClient httpClient, DefaultConfig config)
        {
            _httpClient = httpClient;
            _config = config;
        }

        public async Task<Result<PagedResultDto<DeviceDTO>>> GetDevicesAsync(int page = 1, int pageSize = 20)
        {
            var queryParams = new Dictionary<string, string>
            {
                ["page"] = page.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            return await _httpClient.GetAsync<PagedResultDto<DeviceDTO>>($"api/devices", queryParams);
        }

        public async Task<Result<DeviceDTO>> GetDeviceAsync(long id)
        {
            return await _httpClient.GetAsync<DeviceDTO>($"api/devices/{id}");
        }

        public async Task<Result<DeviceDTO>> CreateDeviceAsync(DeviceDTO device)
        {
            return await _httpClient.PostAsync<DeviceDTO, DeviceDTO>($"api/devices", device);
        }

        public async Task<Result<DeviceDTO>> UpdateDeviceAsync(long id, DeviceDTO device)
        {
            return await _httpClient.PutAsync<DeviceDTO, DeviceDTO>($"api/devices/{id}", device);
        }

        public async Task<Result<bool>> DeleteDeviceAsync(long id)
        {
            var result = await _httpClient.DeleteAsync<object>($"api/devices/{id}");
            return Result<bool>.Success(result.IsSuccess, result.StatusCode);
        }
    }
}
```

#### 3.3.2 其他API服务实现

报警和数据点API服务的实现类似于设备API服务，遵循相同的模式。

### 3.4 HTTP客户端实现

```csharp
namespace EnvizonController.ApiClient.Http.Implementations
{
    /// <summary>
    /// HTTP客户端实现
    /// </summary>
    public class HttpClientWrapper : IHttpClient
    {
        private readonly System.Net.Http.HttpClient _httpClient;
        private readonly DefaultConfig _config;
        private readonly ILogger<HttpClientWrapper> _logger;

        public HttpClientWrapper(System.Net.Http.HttpClient httpClient, DefaultConfig config, ILogger<HttpClientWrapper> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;

            // 配置HttpClient
            _httpClient.BaseAddress = new Uri(_config.ApiClientSettings.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromMilliseconds(_config.ApiClientSettings.TimeoutMs);
        }

        public async Task<Result<T>> GetAsync<T>(string url, IDictionary<string, string> queryParams = null)
        {
            try
            {
                var requestUrl = url;
                if (queryParams != null && queryParams.Count > 0)
                {
                    var queryString = string.Join("&", queryParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
                    requestUrl = $"{url}?{queryString}";
                }

                var response = await _httpClient.GetAsync(requestUrl);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GET请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> PostAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PostAsync(url, jsonContent);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"POST请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> PutAsync<T, TContent>(string url, TContent content)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(content),
                    Encoding.UTF8,
                    "application/json");

                var response = await _httpClient.PutAsync(url, jsonContent);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"PUT请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        public async Task<Result<T>> DeleteAsync<T>(string url)
        {
            try
            {
                var response = await _httpClient.DeleteAsync(url);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DELETE请求出错: {url}");
                return Result<T>.Failure($"请求失败: {ex.Message}", 500);
            }
        }

        private async Task<Result<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var data = JsonSerializer.Deserialize<T>(content, options);
                    return Result<T>.Success(data, (int)response.StatusCode);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "响应解析错误");
                    return Result<T>.Failure($"响应解析错误: {ex.Message}", (int)response.StatusCode);
                }
            }
            else
            {
                _logger.LogWarning($"HTTP错误: {(int)response.StatusCode} {response.ReasonPhrase}");
                return Result<T>.Failure($"HTTP错误: {response.ReasonPhrase}", (int)response.StatusCode);
            }
        }
    }
}
```

### 3.5 API客户端工厂

使用工厂模式创建API服务实例：

```csharp
namespace EnvizonController.ApiClient.Factories
{
    /// <summary>
    /// API客户端工厂接口
    /// </summary>
    public interface IApiClientFactory
    {
        IDeviceApiService CreateDeviceApiService();
        IAlarmApiService CreateAlarmApiService();
        IDataPointApiService CreateDataPointApiService();
    }

    /// <summary>
    /// API客户端工厂实现
    /// </summary>
    public class ApiClientFactory : IApiClientFactory
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;
        private readonly ILogger<ApiClientFactory> _logger;

        public ApiClientFactory(IHttpClient httpClient, DefaultConfig config, ILogger<ApiClientFactory> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;
        }

        public IDeviceApiService CreateDeviceApiService()
        {
            return new DeviceApiService(_httpClient, _config);
        }

        public IAlarmApiService CreateAlarmApiService()
        {
            return new AlarmApiService(_httpClient, _config);
        }

        public IDataPointApiService CreateDataPointApiService()
        {
            return new DataPointApiService(_httpClient, _config);
        }
    }
}
```

### 3.6 依赖注入设置

在应用程序启动时注册所有服务：

```csharp
namespace EnvizonController.ApiClient
{
    /// <summary>
    /// API客户端服务注册扩展
    /// </summary>
    public static class ApiClientServiceExtensions
    {
        /// <summary>
        /// 注册API客户端服务
        /// </summary>
        public static IServiceCollection AddApiClientServices(this IServiceCollection services)
        {
            // 注册HttpClient
            services.AddHttpClient();
            services.AddTransient<IHttpClient, HttpClientWrapper>();

            // 注册API服务
            services.AddTransient<IDeviceApiService, DeviceApiService>();
            services.AddTransient<IAlarmApiService, AlarmApiService>();
            services.AddTransient<IDataPointApiService, DataPointApiService>();

            // 注册API客户端工厂
            services.AddTransient<IApiClientFactory, ApiClientFactory>();

            return services;
        }
    }
}
```

## 4. 设计模式应用

### 4.1 使用的设计模式

1. **Repository模式**：API服务接口和实现类遵循Repository模式，隐藏数据访问细节，提供面向领域的接口。

2. **工厂模式**：使用ApiClientFactory创建API服务实例，集中管理实例创建逻辑。

3. **适配器模式**：HttpClientWrapper作为适配器，将System.Net.Http.HttpClient适配为IHttpClient接口。

4. **策略模式**：在Result模式中，可以根据不同的错误情况返回不同的结果策略。

5. **依赖注入模式**：所有组件都设计为可注入的服务，遵循依赖倒置原则。

### 4.2 设计原则遵循

1. **单一职责原则（SRP）**：每个类和接口都有明确的单一职责。

2. **开放封闭原则（OCP）**：架构设计为可扩展的，例如可以轻松添加新的API服务。

3. **里氏替换原则（LSP）**：接口实现都遵循接口契约，保证可替换性。

4. **接口隔离原则（ISP）**：接口设计为小而精确，只包含必要的方法。

5. **依赖倒置原则（DIP）**：高层模块依赖于抽象，而不是具体实现。

## 5. 缓存扩展设计（预留）

虽然当前不需要实现缓存，但我们可以在架构中预留缓存扩展点：

```csharp
namespace EnvizonController.ApiClient.Caching
{
    /// <summary>
    /// API缓存接口
    /// </summary>
    public interface IApiCache
    {
        T Get<T>(string key) where T : class;
        void Set<T>(string key, T value, TimeSpan? expiry = null) where T : class;
        void Remove(string key);
        bool Contains(string key);
    }

    /// <summary>
    /// 缓存装饰器基类
    /// </summary>
    public abstract class CachedApiServiceBase
    {
        protected readonly IApiCache _cache;

        protected CachedApiServiceBase(IApiCache cache)
        {
            _cache = cache;
        }

        protected string BuildCacheKey(string service, string method, params object[] args)
        {
            var argsString = args != null && args.Length > 0
                ? string.Join("_", args.Select(a => a?.ToString() ?? "null"))
                : string.Empty;

            return $"{service}_{method}_{argsString}";
        }
    }
}
```

## 6. 使用示例

```csharp
// 在ViewModel中使用API服务
public class DeviceViewModel : ViewModelBase
{
    private readonly IDeviceApiService _deviceApiService;
    private ObservableCollection<DeviceDTO> _devices;

    public ObservableCollection<DeviceDTO> Devices
    {
        get => _devices;
        set => SetProperty(ref _devices, value);
    }

    public DeviceViewModel(IDeviceApiService deviceApiService)
    {
        _deviceApiService = deviceApiService;
        Devices = new ObservableCollection<DeviceDTO>();
    }

    public async Task LoadDevicesAsync()
    {
        var result = await _deviceApiService.GetDevicesAsync();
        
        if (result.IsSuccess)
        {
            Devices.Clear();
            foreach (var device in result.Data.Items)
            {
                Devices.Add(device);
            }
        }
        else
        {
            // 处理错误
            Debug.WriteLine($"加载设备失败: {result.ErrorMessage}");
        }
    }
}
```

## 7. 总结与建议

### 7.1 设计优势

1. **解耦性**：客户端代码与API实现完全解耦，便于维护和测试。

2. **可扩展性**：架构设计便于添加新的API服务和功能。

3. **统一错误处理**：使用Result模式统一处理API错误，降低错误处理的复杂性。

4. **依赖注入支持**：所有组件都设计为可注入的服务，便于测试和替换。

5. **缓存扩展性**：预留了缓存扩展点，便于后续添加缓存功能。

### 7.2 实施建议

1. **分阶段实施**：先实现核心组件（HTTP客户端和基本API服务），然后逐步添加其他功能。

2. **单元测试**：为所有组件编写单元测试，确保功能正确性。

3. **文档完善**：为所有接口和类编写详细的XML文档注释。

4. **日志记录**：在关键点添加日志记录，便于排查问题。

5. **性能监控**：添加性能监控点，便于优化API调用性能。

通过这个架构设计，EnvizonController.Presentation将能够以结构化、可维护和可扩展的方式访问EnvizonController.Server.Controllers提供的API。