<UserControl x:Class="EnvizonController.Modbus.SerialTester.Views.LogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EnvizonController.Modbus.SerialTester.Views"
             xmlns:vm="clr-namespace:EnvizonController.Modbus.SerialTester.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="600"
             d:DataContext="{d:DesignInstance Type=vm:LogViewModel}">
    <Grid>
        <TabControl>
            <TabItem Header="日志列表">
                <ListView ItemsSource="{Binding Logs}" Margin="5">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="时间" Width="180">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Timestamp, StringFormat=yyyy-MM-dd HH:mm:ss.fff}" Foreground="{Binding Color}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="级别" Width="80">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Level}" Foreground="{Binding Color}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Header="消息" Width="300">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Message}" Foreground="{Binding Color}" TextWrapping="Wrap" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>
            </TabItem>
            <TabItem Header="原始日志">
                <TextBox Text="{Binding LogText, Mode=OneWay}" 
                         IsReadOnly="True"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Auto"
                         FontFamily="Consolas"
                         Margin="5"/>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
