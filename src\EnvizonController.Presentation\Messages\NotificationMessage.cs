﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 通知消息
/// </summary>
public class NotificationMessage : ValueChangedMessage<(string Title, string Message, NotificationType Type)>
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title => Value.Title;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Message => Value.Message;

    /// <summary>
    /// 通知类型
    /// </summary>
    public NotificationType Type => Value.Type;

    /// <summary>
    /// 创建通知消息
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <param name="type">通知类型</param>
    public NotificationMessage(string title, string message, NotificationType type = NotificationType.Information) 
        : base((title, message, type))
    {
    }

    /// <summary>
    /// 创建信息通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>通知消息</returns>
    public static NotificationMessage Information(string title, string message)
        => new(title, message, NotificationType.Information);

    /// <summary>
    /// 创建成功通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>通知消息</returns>
    public static NotificationMessage Success(string title, string message)
        => new(title, message, NotificationType.Success);

    /// <summary>
    /// 创建警告通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>通知消息</returns>
    public static NotificationMessage Warning(string title, string message)
        => new(title, message, NotificationType.Warning);

    /// <summary>
    /// 创建错误通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <returns>通知消息</returns>
    public static NotificationMessage Error(string title, string message)
        => new(title, message, NotificationType.Error);
}

/// <summary>
/// 通知类型
/// </summary>
public enum NotificationType
{
    /// <summary>
    /// 信息
    /// </summary>
    Information = 0,

    /// <summary>
    /// 成功
    /// </summary>
    Success = 1,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 2,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 3
}
