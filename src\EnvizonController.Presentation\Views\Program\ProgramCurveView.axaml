<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramCurveView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AvaloniaLineSeriesChart.Controls;assembly=AvaloniaLineSeriesChart"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="600"
    x:DataType="vm:ProgramViewModel"
    mc:Ignorable="d">

    <Grid Margin="15" RowDefinitions="Auto,*,Auto">
        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,15"
            Classes="h3 glow-primary font-cyber"
            Text="程式曲线视图" />

        <Border
            Grid.Row="1"
            Background="#0F0E1D"
            Classes="cyber-noGlow-border">
            <Grid>
                <!--  图表区域 - 未来可以添加实际图表控件  -->
                <controls:LineChartControl
                    Name="LineChart"
                    VerticalAlignment="Stretch" />
            </Grid>
        </Border>
    </Grid>
</UserControl> 