using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    public class EnumEqualsConverter : IValueConverter
    {
        public static readonly EnumEqualsConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;
            
            if (value is Enum enumValue)
            {
                string paramStr = parameter.ToString();
                string enumStr = enumValue.ToString();
                
                return enumStr.Equals(paramStr, StringComparison.OrdinalIgnoreCase);
            }
            
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 