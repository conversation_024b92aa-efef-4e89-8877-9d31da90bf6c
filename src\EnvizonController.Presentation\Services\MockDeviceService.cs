using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Services
{
    /// <summary>
    /// 模拟设备服务实现
    /// 注意：这是一个临时实现，实际项目中应替换为真正的API调用
    /// </summary>
    public class MockDeviceService : IDeviceService
    {
        private readonly List<DeviceDto> _devices;

        public MockDeviceService()
        {
            // 初始化模拟数据
            _devices = new List<DeviceDto>
            {
                new DeviceDto
                {
                    Id = 1,
                    Name = "温控设备-1",
                    SlaveId = 1,
                    ProtocolId = 1,
                    ConnectionType = "Serial",
                    TransportType = "RTU",
                    PortName = "COM1",
                    BaudRate = 9600,
                    Status = DeviceStatus.Normal,
                    ConnectionStatus = ConnectionStatus.Connected,
                    CreatedAt = DateTime.Now.AddDays(-10),
                    LastUpdatedAt = DateTime.Now.AddHours(-2),
                    LastConnectedAt = DateTime.Now.AddMinutes(-10),
                    Remarks = "主要用于温度控制",
                    AutoStart = true,
                    CollectionIntervalMs = 1000
                },
                new DeviceDto
                {
                    Id = 2,
                    Name = "湿度控制器-1",
                    SlaveId = 2,
                    ProtocolId = 1,
                    ConnectionType = "TCP",
                    TransportType = "TCP",
                    HostAddress = "*************",
                    Port = 502,
                    Status = DeviceStatus.Warning,
                    ConnectionStatus = ConnectionStatus.Disconnected,
                    CreatedAt = DateTime.Now.AddDays(-5),
                    LastUpdatedAt = DateTime.Now.AddHours(-5),
                    LastConnectedAt = DateTime.Now.AddHours(-5),
                    Remarks = "主要用于湿度控制",
                    AutoStart = false,
                    CollectionIntervalMs = 2000
                },
                new DeviceDto
                {
                    Id = 3,
                    Name = "压力监测器-1",
                    SlaveId = 3,
                    ProtocolId = 2,
                    ConnectionType = "Serial",
                    TransportType = "ASCII",
                    PortName = "COM2",
                    BaudRate = 19200,
                    Status = DeviceStatus.Normal,
                    ConnectionStatus = ConnectionStatus.Connected,
                    CreatedAt = DateTime.Now.AddDays(-3),
                    LastUpdatedAt = DateTime.Now.AddHours(-1),
                    LastConnectedAt = DateTime.Now.AddMinutes(-2),
                    Remarks = "主要用于压力监测",
                    AutoStart = true,
                    CollectionIntervalMs = 500
                }
            };
        }

        public Task<ServiceResult<PagedResultDto<DeviceDto>>> GetDevicesAsync(int page = 1, int pageSize = 20)
        {
            try
            {
                var items = _devices
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var result = new PagedResultDto<DeviceDto>
                {
                    Items = items,
                    TotalCount = _devices.Count,
                    Page = page,
                    PageSize = pageSize
                };

                return Task.FromResult(ServiceResult<PagedResultDto<DeviceDto>>.Success(result));
            }
            catch (Exception ex)
            {
                return Task.FromResult(ServiceResult<PagedResultDto<DeviceDto>>.Failure(ex.Message, 500));
            }
        }

        public Task<ServiceResult<DeviceDto>> GetDeviceAsync(long id)
        {
            try
            {
                var device = _devices.FirstOrDefault(d => d.Id == id);

                if (device == null)
                {
                    return Task.FromResult(ServiceResult<DeviceDto>.Failure($"设备ID {id} 不存在", 404));
                }

                return Task.FromResult(ServiceResult<DeviceDto>.Success(device));
            }
            catch (Exception ex)
            {
                return Task.FromResult(ServiceResult<DeviceDto>.Failure(ex.Message, 500));
            }
        }

        public Task<ServiceResult<DeviceDto>> CreateDeviceAsync(DeviceDto device)
        {
            try
            {
                // 为新设备分配ID
                device.Id = _devices.Count > 0 ? _devices.Max(d => d.Id) + 1 : 1;
                device.CreatedAt = DateTime.Now;

                _devices.Add(device);

                return Task.FromResult(ServiceResult<DeviceDto>.Success(device, 201));
            }
            catch (Exception ex)
            {
                return Task.FromResult(ServiceResult<DeviceDto>.Failure(ex.Message, 500));
            }
        }

        public Task<ServiceResult<DeviceDto>> UpdateDeviceAsync(long id, DeviceDto device)
        {
            try
            {
                var existingDevice = _devices.FirstOrDefault(d => d.Id == id);

                if (existingDevice == null)
                {
                    return Task.FromResult(ServiceResult<DeviceDto>.Failure($"设备ID {id} 不存在", 404));
                }

                // 更新设备属性
                existingDevice.Name = device.Name;
                existingDevice.SlaveId = device.SlaveId;
                existingDevice.ProtocolId = device.ProtocolId;
                existingDevice.ConnectionType = device.ConnectionType;
                existingDevice.TransportType = device.TransportType;
                existingDevice.PortName = device.PortName;
                existingDevice.BaudRate = device.BaudRate;
                existingDevice.DataBits = device.DataBits;
                existingDevice.Parity = device.Parity;
                existingDevice.StopBits = device.StopBits;
                existingDevice.HostAddress = device.HostAddress;
                existingDevice.Port = device.Port;
                existingDevice.ConnectionTimeout = device.ConnectionTimeout;
                existingDevice.ReadTimeout = device.ReadTimeout;
                existingDevice.WriteTimeout = device.WriteTimeout;
                existingDevice.Status = device.Status;
                existingDevice.Remarks = device.Remarks;
                existingDevice.AutoStart = device.AutoStart;
                existingDevice.CollectionIntervalMs = device.CollectionIntervalMs;
                existingDevice.LastUpdatedAt = DateTime.Now;

                return Task.FromResult(ServiceResult<DeviceDto>.Success(existingDevice));
            }
            catch (Exception ex)
            {
                return Task.FromResult(ServiceResult<DeviceDto>.Failure(ex.Message, 500));
            }
        }

        public Task<ServiceResult<bool>> DeleteDeviceAsync(long id)
        {
            try
            {
                var device = _devices.FirstOrDefault(d => d.Id == id);

                if (device == null)
                {
                    return Task.FromResult(ServiceResult<bool>.Failure($"设备ID {id} 不存在", 404));
                }

                _devices.Remove(device);

                return Task.FromResult(ServiceResult<bool>.Success(true, 204));
            }
            catch (Exception ex)
            {
                return Task.FromResult(ServiceResult<bool>.Failure(ex.Message, 500));
            }
        }
    }
}