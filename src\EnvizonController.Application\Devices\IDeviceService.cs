using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Application.Devices
{
    /// <summary>
    /// 设备服务接口
    /// </summary>
    public interface IDeviceService
    {
        /// <summary>
        /// 获取所有设备
        /// </summary>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetAllDevicesAsync();

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>设备</returns>
        Task<Device?> GetDeviceByIdAsync(long id);

        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <returns>设备</returns>
        Task<Device?> GetDeviceByNameAsync(string name);

        /// <summary>
        /// 根据协议ID获取设备列表
        /// </summary>
        /// <param name="protocolId">协议ID</param>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetDevicesByProtocolIdAsync(long protocolId);

        /// <summary>
        /// 根据连接类型获取设备列表
        /// </summary>
        /// <param name="connectionType">连接类型</param>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetDevicesByConnectionTypeAsync(string connectionType);

        /// <summary>
        /// 获取所有串口设备
        /// </summary>
        /// <returns>串口设备列表</returns>
        Task<IEnumerable<Device>> GetSerialDevicesAsync();

        /// <summary>
        /// 获取所有网络设备
        /// </summary>
        /// <returns>网络设备列表</returns>
        Task<IEnumerable<Device>> GetNetworkDevicesAsync();

        /// <summary>
        /// 添加设备
        /// </summary>
        /// <param name="device">设备</param>
        /// <returns>添加的设备</returns>
        Task<Device> AddDeviceAsync(Device device);

        /// <summary>
        /// 更新设备
        /// </summary>
        /// <param name="device">设备</param>
        /// <returns>更新的设备</returns>
        Task<Device> UpdateDeviceAsync(Device device);

        /// <summary>
        /// 删除设备
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <returns>是否成功删除</returns>
        Task<bool> DeleteDeviceAsync(long id);

    }
}
