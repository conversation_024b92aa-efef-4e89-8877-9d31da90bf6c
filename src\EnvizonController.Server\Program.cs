using EnvizonController.Application;
using EnvizonController.Application.DataCollection;
using EnvizonController.Application.Extensions;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using EnvizonController.Application.Services;
using EnvizonController.Domain;
using EnvizonController.Infrastructure;
using EnvizonController.Infrastructure.Persistence;
using EnvizonController.Mqtt.Server.DependencyInjection;
using Microsoft.OpenApi.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 1. 添加配置源
builder.Configuration.AddJsonFile("appsettings.json", true, true);

// 2. 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();
builder.Host.UseSerilog();

// 3. 注册服务
var services = builder.Services;
var configuration = builder.Configuration;

// 添加配置和日志服务
services.AddSingleton<IConfiguration>(configuration);
services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));
services.AddSingleton(Log.Logger);

// 添加API相关服务
services.AddControllers();
services.AddEndpointsApiExplorer();
services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "EnvizonController API",
        Version = "v1",
        Description = "EnvizonController系统的API接口"
    });

    // 添加JWT认证支持
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// 添加CORS策略
services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

// 添加应用层服务
services.AddApplicationServices();
services.AddInfrastructureServices(configuration);
services.AddDomainServices();

// 手动注册 ProtocolAppService
services.AddScoped<IProtocolAppService, ProtocolAppService>();

// 添加 MQTT 服务
services.AddMqttServer();
//services.AddMqttClient(options =>
//{
//    // 根据服务器需要调整 MQTT 客户端配置
//    options.WithClientId("EnvizonServerMqttClient") // 使用不同的客户端 ID
//        .WithTcpServer("localhost", 1883) // 或者从配置读取地址
//        .WithCleanSession();
//});

// 添加 Quartz 设备数据采集服务
services.AddQuartzDeviceCollection();

// 将 QuartzDeviceCollectionBackgroundService 注册为托管服务（替换原来的 DataCollectionBackgroundService）
services.AddHostedService<QuartzDeviceCollectionBackgroundService>();

var app = builder.Build();

// 4. 初始化默认协议和设备 (在应用构建之后)
using (var scope = app.Services.CreateScope())
{
    // 确保数据库和表已创建
    var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    dbContext.Database.EnsureCreated();
    // 或者使用迁移 
    // dbContext.Database.Migrate();

    // 初始化默认协议
    var protocolAppService = scope.ServiceProvider.GetRequiredService<IProtocolAppService>();
    protocolAppService.InitializeDefaultProtocols();

    var programAppService = scope.ServiceProvider.GetRequiredService<IProgramAppService>();
    programAppService.InitializeDefaultProgramsAsync().Wait();

    // 初始化默认设备
    var deviceAppService = scope.ServiceProvider.GetRequiredService<IDeviceAppService>();
    deviceAppService.InitializeDefaultDeviceAsync().Wait();

    // 初始化默认测试项
    var testItemAppService = scope.ServiceProvider.GetRequiredService<ITestItemAppService>();
    testItemAppService.InitializeDefaultTestItemsAsync().Wait();
}

// 配置 HTTP 请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "EnvizonController API v1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI在根路径访问
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// 注释掉认证暂时不使用
// app.UseAuthentication();
// app.UseAuthorization();

// 配置默认欢迎页和API路由
app.MapGet("/", () => "Welcome to EnvizonController API Server!");
app.MapControllers();

app.Run();