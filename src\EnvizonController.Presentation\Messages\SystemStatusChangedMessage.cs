﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 系统状态变更消息
/// </summary>
public class SystemStatusChangedMessage : ValueChangedMessage<SystemStatus>
{
    /// <summary>
    /// 系统状态
    /// </summary>
    public SystemStatus Status => Value;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// 创建系统状态变更消息
    /// </summary>
    /// <param name="status">系统状态</param>
    /// <param name="message">状态消息</param>
    public SystemStatusChangedMessage(SystemStatus status, string message = "") : base(status)
    {
        Message = message;
    }
}

/// <summary>
/// 系统状态
/// </summary>
public enum SystemStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 0,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 1,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 2,

    /// <summary>
    /// 致命错误
    /// </summary>
    Fatal = 3,

    /// <summary>
    /// 维护模式
    /// </summary>
    Maintenance = 4,

    /// <summary>
    /// 初始化中
    /// </summary>
    Initializing = 5,

    /// <summary>
    /// 关闭中
    /// </summary>
    ShuttingDown = 6
}
