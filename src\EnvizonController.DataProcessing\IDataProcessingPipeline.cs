using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing
{
    /// <summary>
    /// 数据处理管道接口
    /// </summary>
    public interface IDataProcessingPipeline
    {
        /// <summary>
        /// 获取管道名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 获取管道描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 获取处理器列表
        /// </summary>
        IReadOnlyList<IDataProcessor> Processors { get; }
        
        /// <summary>
        /// 添加处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        IDataProcessingPipeline AddProcessor(IDataProcessor processor);
        
        /// <summary>
        /// 移除处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        IDataProcessingPipeline RemoveProcessor(IDataProcessor processor);
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context);
        
        /// <summary>
        /// 清空管道
        /// </summary>
        void Clear();
    }
}
