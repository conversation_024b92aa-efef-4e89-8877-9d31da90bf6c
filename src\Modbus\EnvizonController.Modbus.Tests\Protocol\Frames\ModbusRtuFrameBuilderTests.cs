using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Frames
{
    public class ModbusRtuFrameBuilderTests
    {
        private readonly ModbusRtuFrameBuilder _frameBuilder;
        
        public ModbusRtuFrameBuilderTests()
        {
            _frameBuilder = new ModbusRtuFrameBuilder();
        }
        
        [Fact]
        public void BuildRequestFrame_WithReadHoldingRegistersRequest_ReturnsCorrectFrame()
        {
            // Arrange
            var request = new ReadHoldingRegistersRequest(1,0,10);
            
            // Act
            byte[] frame = _frameBuilder.BuildRequestFrame(request);
            
            // Assert
            Assert.Equal(8, frame.Length);
            Assert.Equal(0x01, frame[0]); // 从站地址
            Assert.Equal((byte)ModbusFunction.ReadHoldingRegisters, frame[1]); // 功能码
            Assert.Equal(0x00, frame[2]); // 起始地址高字节
            Assert.Equal(0x00, frame[3]); // 起始地址低字节
            Assert.Equal(0x00, frame[4]); // 寄存器数量高字节
            Assert.Equal(0x0A, frame[5]); // 寄存器数量低字节
            
            // 验证CRC
            byte[] expectedCrc = ModbusCrc.CalculateCrc(frame.Take(6).ToArray());
            Assert.Equal(expectedCrc[0], frame[6]);
            Assert.Equal(expectedCrc[1], frame[7]);
        }
        
        [Fact]
        public void ParseResponseFrame_WithValidReadHoldingRegistersResponse_ReturnsTrue()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14, 
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            
            var response = new ReadHoldingRegistersResponse();
            
            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);
            
            // Assert
            Assert.True(result);
            Assert.Equal(1, response.SlaveAddress);
            Assert.Equal(ModbusFunction.ReadHoldingRegisters, response.FunctionCode);
            Assert.Equal(10, response.RegisterValues.Length);
            
            for (int i = 0; i < 10; i++)
            {
                Assert.Equal((ushort)(i + 1), response.RegisterValues[i]);
            }
        }
        
        [Fact]
        public void ParseResponseFrame_WithInvalidCrc_ReturnsFalse()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14, 
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            // 修改CRC使其无效
            responseFrame[responseFrame.Length - 1] = 0x00;
            
            var response = new ReadHoldingRegistersResponse();
            
            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);
            
            // Assert
            Assert.False(result);
        }
        
        [Fact]
        public void ValidateResponseFrame_WithValidFrame_ReturnsTrue()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14, 
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            
            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);
            
            // Assert
            Assert.True(result);
        }
        
        [Fact]
        public void ValidateResponseFrame_WithInvalidFrame_ReturnsFalse()
        {
            // Arrange
            byte[] responseData = new byte[] { 0x01, 0x03, 0x14, 
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A };
            byte[] responseFrame = ModbusCrc.AppendCrc(responseData);
            // 修改CRC使其无效
            responseFrame[responseFrame.Length - 1] = 0x00;
            
            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);
            
            // Assert
            Assert.False(result);
        }
    }
}
