using System;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Processors.Implementation
{
    /// <summary>
    /// JSON转换器
    /// </summary>
    public class JsonTransformer : BaseDataProcessor, IDataTransformer
    {
        /// <summary>
        /// 转换函数
        /// </summary>
        public Func<object, object> TransformFunction { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="description">处理器描述</param>
        /// <param name="transformFunction">转换函数</param>
        public JsonTransformer(string name, string description, Func<object, object> transformFunction)
            : base(name, description)
        {
            TransformFunction = transformFunction ?? throw new ArgumentNullException(nameof(transformFunction));
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public override Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            try
            {
                // 应用转换函数
                context.ProcessedData = TransformFunction(context.RawData);
                return Task.FromResult(context);
            }
            catch (Exception ex)
            {
                context.Status = ProcessingStatus.Failed;
                context.AddError($"转换器 {Name} 处理时出错: {ex.Message}");
                return Task.FromResult(context);
            }
        }
    }
}
