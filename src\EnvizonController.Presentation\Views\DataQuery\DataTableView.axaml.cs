using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Data;
using Avalonia.Markup.Xaml;
using EnvizonController.Presentation.ViewModels;

namespace EnvizonController.Presentation.Views.DataQuery
{
    public partial class DataTableView : UserControl
    {
        private DataGrid? _dataGrid;
        private readonly Dictionary<int, DataGridColumn> _dynamicColumns = new();
        private bool _columnsInitialized = false;
        private readonly Dictionary<int, string> _protocolDisplayNames = new();

        public DataTableView()
        {
            InitializeComponent();

            AttachedToVisualTree += DataTableView_AttachedToVisualTree;
            DetachedFromVisualTree += DataTableView_DetachedFromVisualTree;
        }

        private void DataTableView_AttachedToVisualTree(object? sender, VisualTreeAttachmentEventArgs e)
        {
            if (DataContext is DataQueryViewModel viewModel)
            {
                // 监听DataPoints集合变化
                if (viewModel.DataPoints is INotifyCollectionChanged observableCollection)
                {
                    observableCollection.CollectionChanged += DataPoints_CollectionChanged;
                }

                // 监听SelectedTestItem变化
                viewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 初始化列
                InitializeColumns();
            }
        }

        private void DataTableView_DetachedFromVisualTree(object? sender, VisualTreeAttachmentEventArgs e)
        {
            if (DataContext is DataQueryViewModel viewModel)
            {
                // 取消事件订阅
                if (viewModel.DataPoints is INotifyCollectionChanged observableCollection)
                {
                    observableCollection.CollectionChanged -= DataPoints_CollectionChanged;
                }

                viewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(DataQueryViewModel.SelectedTestItem) ||
                e.PropertyName == nameof(DataQueryViewModel.DataPoints))
            {
                // 当选中的测试项或数据点集合变化时，重新初始化列
                _columnsInitialized = false;
                InitializeColumns();
            }
        }

        private void DataPoints_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // 当数据点集合发生变化时，确保列已经初始化
            if (!_columnsInitialized && DataContext is DataQueryViewModel viewModel && viewModel.DataPoints.Count > 0)
            {
                InitializeColumns();
            }
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
            _dataGrid = this.FindControl<DataGrid>("dataGrid");
        }

        private void InitializeColumns()
        {
            if (_dataGrid == null || DataContext is not DataQueryViewModel viewModel)
                return;

            // 确保有数据可用于创建列
            if (viewModel.DataPoints.Count == 0)
                return;

            // 如果列已经初始化，则不需要重新创建
            if (_columnsInitialized)
                return;

            _dataGrid.Columns.Clear();
            _dynamicColumns.Clear();

            // 添加ID列
            _dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "ID",
                Binding = new Binding("Id"),
                Width = new DataGridLength(80)
            });

            // 添加时间戳列
            _dataGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "时间",
                Binding = new Binding("Timestamp") { StringFormat = "yyyy-MM-dd HH:mm:ss" },
                Width = new DataGridLength(150)
            });

            // 获取第一个数据点，用于确定需要创建哪些动态列
            var firstDataPoint = viewModel.DataPoints.FirstOrDefault();
            if (firstDataPoint != null && firstDataPoint.Values.Count > 0)
            {
                // 尝试获取协议信息
                TryLoadProtocolDisplayNames();

                // 按ProtocolIndex排序，确保列顺序一致
                var sortedValues = firstDataPoint.Values.OrderBy(v => v.ProtocolItemIndex).ToList();

                // 为每个ProtocolIndex创建一个列
                foreach (var valueData in sortedValues)
                {
                    var protocolIndex = valueData.ProtocolItemIndex;

                    // 获取列标题，优先使用协议项的显示名称，如果没有则使用默认名称
                    string columnHeader = _protocolDisplayNames.TryGetValue(protocolIndex, out string? displayName) && !string.IsNullOrEmpty(displayName)
                        ? displayName
                        : $"参数 {protocolIndex}";

                    // 创建列
                    var column = new DataGridTextColumn
                    {
                        Header = columnHeader,
                        // 使用自定义绑定路径访问Values集合中特定ProtocolIndex的值
                        Binding = new Binding($"Values[{GetValueIndex(protocolIndex)}].Value"),
                        Width = new DataGridLength(1, DataGridLengthUnitType.Star)
                    };

                    _dataGrid.Columns.Add(column);
                    _dynamicColumns[protocolIndex] = column;
                }
            }

            _columnsInitialized = true;
        }

        // 尝试加载协议显示名称
        private void TryLoadProtocolDisplayNames()
        {
            _protocolDisplayNames.Clear();

            try
            {
                // 在实际项目中，这里应该通过依赖注入获取协议服务
                // 由于我们没有直接访问协议服务的方式，这里使用一些预定义的协议名称
                // 这只是一个示例，实际应用中应该通过服务获取真实的协议信息

                // 添加一些常见的协议项名称作为示例
                _protocolDisplayNames[1] = "环境温度";
                _protocolDisplayNames[2] = "设定温度";
                _protocolDisplayNames[3] = "湿度";
                _protocolDisplayNames[4] = "压力";
                _protocolDisplayNames[5] = "流量";
                _protocolDisplayNames[6] = "电压";
                _protocolDisplayNames[7] = "电流";
                _protocolDisplayNames[8] = "功率";
                _protocolDisplayNames[9] = "转速";
                _protocolDisplayNames[10] = "频率";
            }
            catch (Exception ex)
            {
                // 忽略异常，使用默认列名
                System.Diagnostics.Debug.WriteLine($"获取协议信息时出错: {ex.Message}");
            }
        }

        // 辅助方法：根据ProtocolIndex获取在Values集合中的索引
        private int GetValueIndex(int protocolIndex)
        {
            if (DataContext is not DataQueryViewModel viewModel || viewModel.DataPoints.Count == 0)
                return -1;

            var firstDataPoint = viewModel.DataPoints.First();
            for (int i = 0; i < firstDataPoint.Values.Count; i++)
            {
                if (firstDataPoint.Values[i].ProtocolItemIndex == protocolIndex)
                    return i;
            }

            return -1;
        }
    }
}