using AutoMapper;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Application.Services;

/// <summary>
///     程式链接应用服务实现
/// </summary>
public class ProgramLinkAppService : IProgramLinkAppService
{
    private readonly ILogger<ProgramLinkAppService> _logger;
    private readonly IMapper _mapper;
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    ///     构造函数
    /// </summary>
    public ProgramLinkAppService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<ProgramLinkAppService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProgramLinkDTO>> GetAllProgramLinksAsync()
    {
        var programLinks = await _unitOfWork.ProgramLinks.GetAllAsync();
        return _mapper.Map<IEnumerable<ProgramLinkDTO>>(programLinks);
    }

    /// <inheritdoc />
    public async Task<ProgramLinkDTO> GetProgramLinkByIdAsync(long id)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdAsync(id);
        return _mapper.Map<ProgramLinkDTO>(programLink);
    }

    /// <inheritdoc />
    public async Task<ProgramLinkDTO> GetProgramLinkByNameAsync(string name)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByNameAsync(name);
        return _mapper.Map<ProgramLinkDTO>(programLink);
    }

    /// <inheritdoc />
    public async Task<ProgramLinkDTO> CreateProgramLinkAsync(ProgramLinkDTO programLinkDto)
    {
        // 参数校验
        if (string.IsNullOrWhiteSpace(programLinkDto.Name))
        {
            _logger.LogWarning("创建程式链接时名称为空");
            throw new ArgumentException("程式链接名称不能为空", nameof(programLinkDto.Name));
        }

        // 检查名称唯一性
        var exist = await _unitOfWork.ProgramLinks.GetByNameAsync(programLinkDto.Name);
        if (exist != null)
        {
            _logger.LogWarning("创建程式链接时名称已存在: {Name}", programLinkDto.Name);
            throw new InvalidOperationException($"程式链接名称已存在: {programLinkDto.Name}");
        }

        // 映射并初始化
        var now = DateTime.Now;
        var programLink = _mapper.Map<ProgramLink>(programLinkDto);
        programLink.CreatedAt = now;
        programLink.UpdatedAt = now;

        // 处理子项（如果有）
        if (programLink.Items != null && programLink.Items.Count > 0)
            foreach (var item in programLink.Items)
            {
                item.CreatedAt = now;
                item.UpdatedAt = now;
            }

        await _unitOfWork.ProgramLinks.AddAsync(programLink);
        await _unitOfWork.SaveChangesAsync();

        // 通过ID重新查询，确保返回的是数据库中的最新数据（含自增ID等）
        var created = await _unitOfWork.ProgramLinks.GetByIdAsync(programLink.Id);
        return _mapper.Map<ProgramLinkDTO>(created);
    }


    /// <inheritdoc />
    public async Task<ProgramLinkDTO> UpdateProgramLinkAsync(ProgramLinkDTO programLinkDto)
    {
        var existingProgramLink = await _unitOfWork.ProgramLinks.GetByIdAsync(programLinkDto.Id);
        if (existingProgramLink == null)
        {
            _logger.LogWarning("尝试更新不存在的程式链接 ID: {ProgramLinkId}", programLinkDto.Id);
            return null;
        }

        _mapper.Map(programLinkDto, existingProgramLink);
        await _unitOfWork.ProgramLinks.UpdateAsync(existingProgramLink);
        await _unitOfWork.SaveChangesAsync();
        return _mapper.Map<ProgramLinkDTO>(existingProgramLink);
    }

    /// <inheritdoc />
    public async Task<bool> DeleteProgramLinkAsync(long id)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdAsync(id);
        if (programLink == null)
        {
            _logger.LogWarning("尝试删除不存在的程式链接 ID: {ProgramLinkId}", id);
            return false;
        }

        await _unitOfWork.ProgramLinks.DeleteAsync(programLink.Id);
        await _unitOfWork.SaveChangesAsync();
        return true;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProgramLinkStepDTO>> GetProgramLinkItemsAsync(long programLinkId)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdWithItemsAsync(programLinkId);
        if (programLink == null) return new List<ProgramLinkStepDTO>();

        return _mapper.Map<IEnumerable<ProgramLinkStepDTO>>(programLink.GetOrderedItems());
    }

    /// <inheritdoc />
    public async Task<ProgramLinkStepDTO> AddProgramToProgramLinkAsync(long programLinkId, long programId,
        int? order = null)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdWithItemsAsync(programLinkId);
        if (programLink == null)
        {
            _logger.LogWarning("尝试向不存在的程式链接添加程式 链接ID: {ProgramLinkId}", programLinkId);
            return null;
        }

        var program = await _unitOfWork.Programs.GetByIdAsync(programId);
        if (program == null)
        {
            _logger.LogWarning("尝试添加不存在的程式 程式ID: {ProgramId}", programId);
            return null;
        }

        var linkItem = programLink.AddProgram(program, order);
        await _unitOfWork.ProgramLinks.UpdateAsync(programLink);
        await _unitOfWork.SaveChangesAsync();
        return _mapper.Map<ProgramLinkStepDTO>(linkItem);
    }

    /// <inheritdoc />
    public async Task<bool> RemoveLinkItemAsync(long programLinkId, long linkItemId)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdWithItemsAsync(programLinkId);
        if (programLink == null)
        {
            _logger.LogWarning("尝试从不存在的程式链接移除链接项 链接ID: {ProgramLinkId}", programLinkId);
            return false;
        }

        var linkItem = programLink.Items.FirstOrDefault(i => i.Id == linkItemId);
        if (linkItem == null)
        {
            _logger.LogWarning("尝试移除不存在的链接项 链接项ID: {LinkItemId}", linkItemId);
            return false;
        }

        var result = programLink.RemoveLinkItem(linkItem);
        if (result)
        {
            await _unitOfWork.ProgramLinks.UpdateAsync(programLink);
            await _unitOfWork.SaveChangesAsync();
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<ProgramLinkStepDTO> ReorderLinkItemAsync(long programLinkId, long linkItemId, int newOrder)
    {
        var programLink = await _unitOfWork.ProgramLinks.GetByIdWithItemsAsync(programLinkId);
        if (programLink == null)
        {
            _logger.LogWarning("尝试在不存在的程式链接中调整顺序 链接ID: {ProgramLinkId}", programLinkId);
            return null;
        }

        var linkItem = programLink.Items.FirstOrDefault(i => i.Id == linkItemId);
        if (linkItem == null)
        {
            _logger.LogWarning("尝试调整不存在的链接项顺序 链接项ID: {LinkItemId}", linkItemId);
            return null;
        }

        // 保存原始顺序
        var oldOrder = linkItem.ExecutionOrder;

        // 如果顺序没有变化，直接返回
        if (oldOrder == newOrder) return _mapper.Map<ProgramLinkStepDTO>(linkItem);

        // 调整其他项目的顺序
        if (newOrder < oldOrder)
            // 向上移动：将中间项目向下移动
            foreach (var item in programLink.Items.Where(i =>
                         i.ExecutionOrder >= newOrder && i.ExecutionOrder < oldOrder))
                item.ExecutionOrder++;
        else
            // 向下移动：将中间项目向上移动
            foreach (var item in programLink.Items.Where(i =>
                         i.ExecutionOrder > oldOrder && i.ExecutionOrder <= newOrder))
                item.ExecutionOrder--;

        // 设置新顺序
        linkItem.ExecutionOrder = newOrder;
        programLink.UpdatedAt = DateTime.Now;

        await _unitOfWork.ProgramLinks.UpdateAsync(programLink);
        await _unitOfWork.SaveChangesAsync();
        return _mapper.Map<ProgramLinkStepDTO>(linkItem);
    }

    /// <inheritdoc />
    public async Task<bool> BulkUpsertProgramLinkStepItemsAsync(long programLinkId, List<ProgramLinkItemUpsertDto> items)
    {
        await _unitOfWork.BeginTransactionAsync();
        try
        {
            var programLink = await _unitOfWork.ProgramLinks.GetByIdWithItemsAsync(programLinkId);
            if (programLink == null)
            {
                _logger.LogWarning("尝试批量更新不存在的程式链接中的链接项 链接ID: {ProgramLinkId}", programLinkId);
                await _unitOfWork.RollbackTransactionAsync();
                return false;
            }
            // 获取所有涉及的程式ID
            var programIds = items.Select(i => i.ProgramId).Distinct().ToList();
            var programs = await _unitOfWork.Programs.GetProgramsByIdsAsync(programIds);

            // 验证所有程式都存在
            foreach (var item in items)
            {
                if (!programs.Any(p => p.Id == item.ProgramId))
                {
                    _logger.LogWarning("尝试添加不存在的程式到链接 程式ID: {ProgramId}", item.ProgramId);
                    await _unitOfWork.RollbackTransactionAsync();
                    return false;
                }
            }

            programLink.ClearItems();
            await _unitOfWork.ProgramLinkSteps.DeleteByProgramLinkIdAsync(programLinkId);

            // 3. 添加新项目
            foreach (var newItem in items)
            {
                var program = programs.First(p => p.Id == newItem.ProgramId);
                var linkItem = new ProgramLinkStep
                {
                    ProgramLinkId = programLinkId,
                    ProgramId = newItem.ProgramId,
                    ExecutionOrder = newItem.ExecutionOrder,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                };
                programLink.Items.Add(linkItem);
            }

            // 更新程式链接的更新时间
            programLink.UpdatedAt = DateTime.Now;

            // 保存更改
            await _unitOfWork.ProgramLinkSteps.AddRangeAsync(programLink.Items);
            await _unitOfWork.SaveChangesAsync();

            await _unitOfWork.CommitTransactionAsync();
            return true;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "批量更新程式链接项时出错 链接ID: {ProgramLinkId}", programLinkId);
            return false;
        }
    }

}