<UserControl
    x:Class="EnvizonController.Presentation.Views.SplashScreen"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="400"
    d:DesignWidth="600"
    x:DataType="vm:SplashScreenViewModel"
    Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
    mc:Ignorable="d">

    <Design.DataContext>
        <vm:SplashScreenViewModel />
    </Design.DataContext>

    <Grid MaxWidth="500" RowDefinitions="Auto,*,Auto,Auto,Auto">
        <!--  Logo  -->
        <Image
            Grid.Row="0"
            Height="100"
            Margin="0,30,0,20"
            HorizontalAlignment="Center"
            Source="/Assets/logo.png" />

        <!--  Status Message  -->
        <TextBlock
            Grid.Row="1"
            Margin="0,0,0,10"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontSize="16"
            Text="{Binding StatusMessage}" />

        <!--  Progress Bar  -->
        <ProgressBar
            Grid.Row="2"
            Height="20"
            Margin="20,0,20,20"
            IsEnabled="{Binding IsInitializing}"
            Maximum="100"
            Minimum="0"
            Value="{Binding Progress}" />

        <!--  Error Message  -->
        <TextBlock
            Grid.Row="3"
            Margin="20,0,20,10"
            HorizontalAlignment="Center"
            Foreground="Red"
            IsVisible="{Binding HasError}"
            Text="{Binding ErrorMessage}"
            TextWrapping="Wrap" />

        <!--  Optional: Retry Button or Close Button on Error  -->
        <!--
        <Button Grid.Row="4"
                Content="Retry"
                IsVisible="{Binding HasError}"
                HorizontalAlignment="Center"
                Command="{Binding RetryCommand}"
                Margin="0,0,0,20"/>
        -->
    </Grid>
</UserControl>