using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvizonController.DataPush;
using Microsoft.Extensions.Logging;

namespace EnvizonController.DataProcessing.Implementation
{
    /// <summary>
    /// 数据处理服务实现
    /// </summary>
    public class DataProcessingService : IDataProcessingService
    {
        private readonly Dictionary<string, IDataProcessingPipeline> _pipelines = new();
        private readonly IDataPushService _pushService;
        private readonly ILogger<DataProcessingService> _logger;
        
        /// <summary>
        /// 获取所有注册的管道
        /// </summary>
        public IReadOnlyDictionary<string, IDataProcessingPipeline> Pipelines => _pipelines;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pushService">数据推送服务</param>
        /// <param name="logger">日志记录器</param>
        public DataProcessingService(IDataPushService pushService, ILogger<DataProcessingService> logger)
        {
            _pushService = pushService ?? throw new ArgumentNullException(nameof(pushService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 注册管道
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <param name="pipeline">处理管道</param>
        public void RegisterPipeline(string name, IDataProcessingPipeline pipeline)
        {
            if (string.IsNullOrEmpty(name))
            {
                throw new ArgumentException("管道名称不能为空", nameof(name));
            }
            
            if (pipeline == null)
            {
                throw new ArgumentNullException(nameof(pipeline));
            }
            
            _pipelines[name] = pipeline;
            _logger.LogInformation("已注册管道: {Name}", name);
        }
        
        /// <summary>
        /// 注销管道
        /// </summary>
        /// <param name="name">管道名称</param>
        public void UnregisterPipeline(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                throw new ArgumentException("管道名称不能为空", nameof(name));
            }
            
            if (_pipelines.ContainsKey(name))
            {
                _pipelines.Remove(name);
                _logger.LogInformation("已注销管道: {Name}", name);
            }
        }
        
        /// <summary>
        /// 获取管道
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <returns>处理管道</returns>
        public IDataProcessingPipeline GetPipeline(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                throw new ArgumentException("管道名称不能为空", nameof(name));
            }
            
            if (_pipelines.TryGetValue(name, out var pipeline))
            {
                return pipeline;
            }
            
            return null;
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="rawData">原始数据</param>
        /// <param name="metadata">元数据</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessDataAsync(string pipelineName, object rawData, Dictionary<string, object> metadata = null)
        {
            if (string.IsNullOrEmpty(pipelineName))
            {
                throw new ArgumentException("管道名称不能为空", nameof(pipelineName));
            }
            
            if (rawData == null)
            {
                throw new ArgumentNullException(nameof(rawData));
            }
            
            if (!_pipelines.TryGetValue(pipelineName, out var pipeline))
            {
                throw new InvalidOperationException($"未找到名为 {pipelineName} 的管道");
            }
            
            // 创建处理上下文
            var context = new DataProcessingContext(rawData, metadata: metadata);
            
            try
            {
                // 处理数据
                context = (DataProcessingContext)await pipeline.ProcessAsync(context);
                _logger.LogInformation("管道 {PipelineName} 处理数据完成，状态: {Status}", pipelineName, context.Status);
                
                return context;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "管道 {PipelineName} 处理数据时出错", pipelineName);
                context.Status = ProcessingStatus.Failed;
                context.AddError($"处理数据时出错: {ex.Message}");
                return context;
            }
        }
        
        /// <summary>
        /// 处理数据并推送
        /// </summary>
        /// <param name="pipelineName">管道名称</param>
        /// <param name="rawData">原始数据</param>
        /// <param name="topic">推送主题</param>
        /// <param name="messageType">消息类型</param>
        /// <param name="metadata">元数据</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessAndPushAsync(string pipelineName, object rawData, string topic, MessageType messageType, Dictionary<string, object> metadata = null)
        {
            // 处理数据
            var result = await ProcessDataAsync(pipelineName, rawData, metadata);
            
            // 检查处理结果
            if (result.Status == ProcessingStatus.Succeeded)
            {
                try
                {
                    // 推送处理后的数据
                    await _pushService.PushAsync(topic, result.ProcessedData, messageType);
                    _logger.LogInformation("已推送处理后的数据到主题 {Topic}", topic);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "推送数据到主题 {Topic} 时出错", topic);
                    result.Status = ProcessingStatus.Failed;
                    result.AddError($"推送数据时出错: {ex.Message}");
                }
            }
            
            return result;
        }
    }
}
