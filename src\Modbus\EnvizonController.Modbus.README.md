# EnvizonController Modbus 通信框架

这是一个高度模块化、跨平台的 Modbus 通信框架，专为 EnvizonController 项目设计。该框架支持所有主流 Modbus 变体（RTU、ASCII、TCP），并提供统一的 API 接口，隐藏底层实现细节。

## 架构设计

框架采用严格的分层设计模式与依赖注入原则，完全分离平台特定代码与协议逻辑，确保在 Avalonia 跨平台应用中无缝运行（尤其是 Android 和桌面环境）。

### 层级结构

1. **协议核心层 (EnvizonController.Modbus.Protocol)**
   - 实现纯 Modbus 协议逻辑（无平台依赖）
   - 包含：帧构建、报文解析、功能码处理、CRC/LRC 校验算法
   - 设计为可测试、可替换的纯逻辑组件

2. **通信抽象层 (EnvizonController.Modbus.Abstractions)**
   - 定义统一的通信通道接口（IModbusChannel）
   - 处理通信生命周期、超时和重试逻辑
   - 提供不同连接方式的抽象基类

3. **平台适配层**
   - **桌面适配器 (EnvizonController.Modbus.Adapters.Desktop)**：基于 System.IO.Ports.SerialPort 的串口实现
   - **Android 适配器 (EnvizonController.Modbus.Adapters.Android)**：基于 Android USB API 的串口实现
   - **网络适配器 (EnvizonController.Modbus.Adapters.Network)**：跨平台的 TCP/IP 实现

4. **应用服务层 (EnvizonController.Modbus.Client)**
   - 提供高级 ModbusClient API
   - 支持同步/异步操作模式
   - 集成数据转换与缓存机制
   - 实现设备配置与状态管理

## 使用示例

### 创建 Modbus RTU 客户端（桌面环境）

```csharp
// 创建串口通道
var channel = new SerialPortChannel(
    portName: "COM1",
    baudRate: 9600,
    dataBits: 8,
    parity: System.IO.Ports.Parity.None,
    stopBits: System.IO.Ports.StopBits.One);

// 创建 Modbus RTU 客户端
var client = ModbusClientFactory.CreateRtuClient(channel);

// 连接到设备
await client.ConnectAsync();

try
{
    // 读取保持寄存器
    ushort[] values = await client.ReadHoldingRegistersAsync(
        slaveAddress: 1,
        startAddress: 0,
        registerCount: 10);

    // 写入多个寄存器
    bool success = await client.WriteMultipleRegistersAsync(
        slaveAddress: 1,
        startAddress: 0,
        values: new ushort[] { 1, 2, 3, 4, 5 });
}
finally
{
    // 断开连接
    await client.DisconnectAsync();
}
```

### 创建 Modbus TCP 客户端

```csharp
// 创建 TCP 通道
var channel = new TcpClientChannel(
    hostAddress: "*************",
    port: 502,
    connectionTimeout: 5000);

// 创建 Modbus TCP 客户端
var client = ModbusClientFactory.CreateTcpClient(channel);

// 连接到设备
await client.ConnectAsync();

try
{
    // 读取输入寄存器
    ushort[] values = await client.ReadInputRegistersAsync(
        slaveAddress: 1,
        startAddress: 0,
        registerCount: 10);
}
finally
{
    // 断开连接
    await client.DisconnectAsync();
}
```

### 创建 Modbus RTU 客户端（Android 环境）

```csharp
// 创建 USB 串口通道
var channel = new UsbSerialChannel(
    context: Android.App.Application.Context,
    baudRate: 9600,
    dataBits: 8,
    parity: UsbSerialParity.None,
    stopBits: UsbSerialStopBits.One);

// 创建 Modbus RTU 客户端
var client = ModbusClientFactory.CreateRtuClient(channel);

// 连接到设备
await client.ConnectAsync();

try
{
    // 读取线圈状态
    bool[] values = await client.ReadCoilsAsync(
        slaveAddress: 1,
        startAddress: 0,
        coilCount: 10);
}
finally
{
    // 断开连接
    await client.DisconnectAsync();
}
```

## 扩展性设计

框架设计考虑了以下扩展性需求：

1. **自定义功能码实现**：可以通过继承 ModbusRequest 和 ModbusResponse 类来实现自定义功能码。

2. **寄存器映射与类型转换**：可以在应用层实现寄存器到具体数据类型的映射和转换。

3. **诊断与日志记录**：框架中的各个组件都设计为可观察和可诊断的。

4. **设备发现与连接管理**：可以在应用层实现设备发现和连接管理机制。

## 性能与可靠性

框架实现了以下性能和可靠性特性：

1. **通信效率优化**：支持批量读写操作，减少通信开销。

2. **健壮的异常处理与恢复机制**：所有通信操作都有适当的异常处理和恢复策略。

3. **可配置的超时与重试策略**：可以根据具体环境和需求配置超时和重试参数。

4. **线程安全实现**：所有通信操作都是线程安全的，可以在多线程环境中使用。

## 许可证

本框架采用 MIT 许可证。
