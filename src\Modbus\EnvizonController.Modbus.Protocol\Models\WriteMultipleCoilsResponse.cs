using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写多个线圈响应
    /// </summary>
    public class WriteMultipleCoilsResponse : ModbusResponse
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; private set; }

        /// <summary>
        /// 线圈数量
        /// </summary>
        public ushort CoilCount { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public WriteMultipleCoilsResponse()
        {
            FunctionCode = ModbusFunction.WriteMultipleCoils;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 6)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            StartAddress = (ushort)((frame[2] << 8) | frame[3]);
            CoilCount = (ushort)((frame[4] << 8) | frame[5]);
        }
    }
}
