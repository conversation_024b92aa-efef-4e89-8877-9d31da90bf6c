using EnvizonController.Modbus.Abstractions.Interfaces;
using Serilog;
using System.IO.Ports;
using EnvizonController.Modbus.Abstractions.Enums;
using System.Reflection;

namespace EnvizonController.Infrastructure.Services.Devices
{
    /// <summary>
    /// Modbus通道工厂实现
    /// </summary>
    public class ModbusChannelFactory : IModbusChannelFactory
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="serviceProvider">服务提供者</param>
        public ModbusChannelFactory(ILogger logger, IServiceProvider serviceProvider)
        {
            _logger = logger ?? Log.ForContext<ModbusChannelFactory>();
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 创建串口通道
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <param name="baudRate">波特率</param>
        /// <param name="dataBits">数据位</param>
        /// <param name="parity">校验位</param>
        /// <param name="stopBits">停止位</param>
        /// <param name="readTimeout">读取超时</param>
        /// <param name="writeTimeout">写入超时</param>
        /// <returns>Modbus通道</returns>
        public IModbusChannel CreateSerialChannel(
            string portName, 
            int baudRate, 
            int dataBits, 
            Parity parity, 
            StopBits stopBits, 
            int readTimeout, 
            int writeTimeout)
        {
            _logger.Debug("正在创建串口通道: {PortName}, {BaudRate}波特", portName, baudRate);
            
            try
            {
                // 加载SerialPortChannel类型
                var assembly = Assembly.Load("EnvizonController.Modbus.Adapters.Desktop");
                Type? serialChannelType = assembly.GetType("EnvizonController.Modbus.Adapters.Desktop.SerialPortChannel");
                
                if (serialChannelType == null)
                {
                    _logger.Error("找不到SerialPortChannel类型");
                    throw new InvalidOperationException("找不到SerialPortChannel类型");
                }
                
                // 创建实例
                var channel = (IModbusChannel)Activator.CreateInstance(
                    serialChannelType, 
                    portName,
                    baudRate,
                    dataBits,
                    parity,
                    stopBits,
                    readTimeout,
                    writeTimeout)!;
                
                _logger.Information("串口通道创建成功: {PortName}", portName);
                return channel;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.Error(ex, "创建串口通道失败: {PortName}", portName);
                throw new InvalidOperationException($"创建串口通道失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建网络通道
        /// </summary>
        /// <param name="hostAddress">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="connectionTimeout">连接超时</param>
        /// <returns>Modbus通道</returns>
        public IModbusChannel CreateNetworkChannel(
            string hostAddress, 
            int port, 
            int connectionTimeout)
        {
            _logger.Debug("正在创建网络通道: {HostAddress}:{Port}", hostAddress, port);
            
            try
            {
                // 加载TcpClientChannel类型
                Type? tcpChannelType = Type.GetType("EnvizonController.Modbus.Adapters.Network.TcpClientChannel, EnvizonController.Modbus.Adapters.Network");
                
                if (tcpChannelType == null)
                {
                    _logger.Error("找不到TcpClientChannel类型");
                    throw new InvalidOperationException("找不到TcpClientChannel类型");
                }
                
                // 创建实例
                var channel = (IModbusChannel)Activator.CreateInstance(
                    tcpChannelType, 
                    hostAddress,
                    port,
                    connectionTimeout)!;
                
                _logger.Information("网络通道创建成功: {HostAddress}:{Port}", hostAddress, port);
                return channel;
            }
            catch (Exception ex) when (ex is not InvalidOperationException)
            {
                _logger.Error(ex, "创建网络通道失败: {HostAddress}:{Port}", hostAddress, port);
                throw new InvalidOperationException($"创建网络通道失败: {ex.Message}", ex);
            }
        }
    }
} 