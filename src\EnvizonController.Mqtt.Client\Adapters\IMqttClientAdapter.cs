using MQTTnet.Protocol;

namespace EnvizonController.Mqtt.Client.Adapters
{
    public interface IMqttClientAdapter
    {
        Task ConnectAsync();
        Task DisconnectAsync();
        Task PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce);
        Task SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtMostOnce);
        Task UnsubscribeAsync(string topic);
        event Func<string, string, Task> MessageReceived;
    }
} 