<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramListView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="350"
    x:DataType="vm:ProgramViewModel"
    mc:Ignorable="d">

    <Border Classes="cyber-border">
        <Grid RowDefinitions="Auto,*,Auto">

            <TextBlock
                Grid.Row="0"
                Margin="15,10"
                Classes="h3 primary font-cyber"
                Text="程式表列表" />
            <ListBox
                Grid.Row="1"
                Margin="10"
                Padding="0"
                Background="Transparent"
                ItemsSource="{Binding Programs}"
                SelectedItem="{Binding SelectedProgram}">
                <ListBox.Styles>
                    <Style Selector="ListBoxItem">
                        <Setter Property="Padding" Value="10,8" />
                        <Setter Property="Margin" Value="0,2" />
                        <Setter Property="Background" Value="#14131C" />
                        <Setter Property="CornerRadius" Value="4" />
                    </Style>
                    <Style Selector="ListBoxItem:selected">
                        <Setter Property="Background" Value="#252335" />
                        <Setter Property="BorderBrush" Value="#0DF0FF" />
                        <Setter Property="BorderThickness" Value="1" />
                    </Style>
                </ListBox.Styles>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Grid ColumnDefinitions="*,Auto">
                            <StackPanel Grid.Column="0">
                                <TextBlock Classes="h4 primary font-cyber" Text="{Binding Name}" />
                                <StackPanel Orientation="Horizontal">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Classes="small" Text="循环: " />
                                        <TextBlock Classes="small" Text="{Binding CycleCount}" />
                                    </StackPanel>
                                    <StackPanel Margin="12,0,0,0" Orientation="Horizontal">
                                        <TextBlock Classes="small" Text="步骤数: " />
                                        <TextBlock Classes="small" Text="{Binding StepCount}" />
                                    </StackPanel>
                                </StackPanel>
                                <TextBlock
                                    Classes="gray"
                                    FontSize="12"
                                    Text="{Binding CreatedAt, StringFormat='创建于: {0:yyyy-MM-dd HH:mm}'}" />
                            </StackPanel>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button
                                    MinWidth="45"
                                    MinHeight="45"
                                    Margin="2,0"
                                    Padding="6,5"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="#0DF0FF"
                                    BorderThickness="0"
                                    Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).EditProgramCommand}"
                                    CommandParameter="{Binding}"
                                    CornerRadius="4">
                                    <TextBlock
                                        Classes="font-icon primary"
                                        FontSize="18"
                                        Text="&#xf044;" />
                                </Button>
                                <Button
                                    MinWidth="45"
                                    MinHeight="45"
                                    Margin="2,0"
                                    Padding="6,5"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    Background="Transparent"
                                    BorderBrush="#FF4556"
                                    BorderThickness="0"
                                    Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).DeleteProgramCommand}"
                                    CommandParameter="{Binding}"
                                    CornerRadius="4">
                                    <TextBlock
                                        Classes="font-icon"
                                        FontSize="18"
                                        Foreground="#FF4556"
                                        Text="&#xf1f8;" />
                                </Button>
                            </StackPanel>
                        </Grid>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
            <Button
                Grid.Row="2"
                Margin="15,10"
                HorizontalAlignment="Stretch"
                Classes="glow2"
                Command="{Binding AddProgramCommand}"
                CornerRadius="4">
                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        VerticalAlignment="Center"
                        Classes="font-icon"
                        Text="&#xf067;" />
                    <TextBlock Text="新建程式" />
                </StackPanel>
            </Button>
        </Grid>
    </Border>
</UserControl> 