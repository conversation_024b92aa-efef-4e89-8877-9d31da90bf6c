using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// 写多个寄存器响应
    /// </summary>
    public class WriteMultipleRegistersResponse : ModbusResponse
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public ushort StartAddress { get; private set; }

        /// <summary>
        /// 寄存器数量
        /// </summary>
        public ushort RegisterCount { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public WriteMultipleRegistersResponse()
        {
            FunctionCode = ModbusFunction.WriteMultipleRegisters;
        }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public override void ParseResponse(byte[] frame)
        {
            base.ParseResponse(frame);

            if (IsException)
                return;

            if (frame.Length < 6)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            StartAddress = (ushort)((frame[2] << 8) | frame[3]);
            RegisterCount = (ushort)((frame[4] << 8) | frame[5]);
        }
    }
}
