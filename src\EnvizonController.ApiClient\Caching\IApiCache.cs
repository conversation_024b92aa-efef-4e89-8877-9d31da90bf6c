using System;

namespace EnvizonController.ApiClient.Caching
{
    /// <summary>
    /// API缓存接口
    /// </summary>
    public interface IApiCache
    {
        /// <summary>
        /// 获取缓存项
        /// </summary>
        /// <typeparam name="T">缓存对象类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存对象，如果不存在则返回null</returns>
        T Get<T>(string key) where T : class;

        /// <summary>
        /// 设置缓存项
        /// </summary>
        /// <typeparam name="T">缓存对象类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存对象</param>
        /// <param name="expiry">过期时间，null表示不过期</param>
        void Set<T>(string key, T value, TimeSpan? expiry = null) where T : class;

        /// <summary>
        /// 移除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        void Remove(string key);

        /// <summary>
        /// 检查缓存项是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        bool Contains(string key);
    }
}