using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using System.Collections.Generic;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Application.Interfaces
{
    /// <summary>
    /// 数据采集应用服务接口
    /// </summary>
    public interface IDataCollectionAppService
    {
        /// <summary>
        ///     保存采集的数据点
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="dataJson">采集的数据（JSON格式）</param>
        /// <returns>保存的数据点，如果测试不在运行中则返回null</returns>
        Task<TestDataPoint?> SaveDataPointAsync(long testRunId, TestDataPoint testDataPoint);

        /// <summary>
        ///     根据设备ID保存采集的数据点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataJson">采集的数据（JSON格式）</param>
        /// <returns>保存的数据点，如果没有找到运行中的测试项则返回null</returns>
        Task<TestDataPoint?> SaveDataPointByDeviceIdAsync(long deviceId, TestDataPoint testDataPoint);

        /// <summary>
        ///     创建数据采集报警
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="message">报警消息</param>
        /// <returns>创建的报警，如果测试不在运行中则返回null</returns>
        Task<Alarm?> CreateCollectionAlarmAsync(long testRunId, string message);

        /// <summary>
        ///     根据设备ID创建数据采集报警
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="message">报警消息</param>
        /// <returns>创建的报警，如果没有找到运行中的测试项则返回null</returns>
        Task<Alarm?> CreateCollectionAlarmByDeviceIdAsync(long deviceId, string message);
        
        /// <summary>
        /// 获取测试运行的数据点列表
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>数据点列表</returns>
        Task<IEnumerable<TestDataPoint>> GetTestRunDataPointsAsync(long testRunId, int page = 1, int pageSize = 20);
        
        /// <summary>
        /// 获取测试运行的数据点列表（分页）
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的数据点列表</returns>
        Task<PagedResultDto<DataPointDto>> GetTestRunDataPointsPagedAsync(long testRunId, int page = 1, int pageSize = 20);
        
        /// <summary>
        /// 获取设备的数据点列表
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>数据点列表</returns>
        Task<IEnumerable<TestDataPoint>> GetDeviceDataPointsAsync(long deviceId, DateTime? startTime = null, DateTime? endTime = null, int page = 1, int pageSize = 20);
        
        /// <summary>
        /// 获取报警列表
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>报警列表</returns>
        Task<IEnumerable<Alarm>> GetAlarmsAsync(long? testRunId = null, int page = 1, int pageSize = 20);
        
        /// <summary>
        /// 获取数据点详情
        /// </summary>
        /// <param name="dataPointId">数据点ID</param>
        /// <returns>数据点详情</returns>
        Task<TestDataPoint?> GetDataPointDetailAsync(long dataPointId);
        
        /// <summary>
        ///     保存报警数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="alarms">报警数据列表</param>
        /// <returns>保存操作的结果</returns>
        Task<bool> SaveAlarmsAsync(long deviceId, List<AlarmDTO> alarms);
    }
}