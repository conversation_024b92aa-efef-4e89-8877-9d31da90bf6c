using EnvizonController.Application.DeviceCommands.Models;

namespace EnvizonController.Application.DeviceCommands.Interfaces
{
    /// <summary>
    /// 设备指令服务接口（主服务接口）
    /// </summary>
    public interface IDeviceCommandService
    {
        /// <summary>
        /// 基于指令名称智能发送
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="commandName">指令名称</param>
        /// <param name="parameters">指令参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>指令执行结果</returns>
        Task<CommandResult> SendCommandAsync(string deviceId, string commandName, 
            object? parameters = null, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 基于协议项名称发送
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="deviceId">设备ID</param>
        /// <param name="protocolItemName">协议项名称</param>
        /// <param name="value">写入值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>指令执行结果</returns>
        Task<CommandResult<T>> SendByProtocolItemAsync<T>(string deviceId, string protocolItemName, 
            T? value = default, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 预定义设备控制指令 - 暂停设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>指令执行结果</returns>
        Task<CommandResult> PauseDeviceAsync(string deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 预定义设备控制指令 - 恢复设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>指令执行结果</returns>
        Task<CommandResult> ResumeDeviceAsync(string deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 预定义设备控制指令 - 停止设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>指令执行结果</returns>
        Task<CommandResult> StopDeviceAsync(string deviceId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 批量指令发送
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="commands">指令列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>批量指令执行结果</returns>
        Task<BatchCommandResult> SendBatchCommandsAsync(string deviceId, 
            IEnumerable<CommandRequest> commands, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 读取设备数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="deviceId">设备ID</param>
        /// <param name="protocolItemName">协议项名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取结果</returns>
        Task<CommandResult<T>> ReadDeviceDataAsync<T>(string deviceId, string protocolItemName, 
            CancellationToken cancellationToken = default);
    }
} 