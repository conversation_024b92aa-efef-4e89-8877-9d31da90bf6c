using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 测试数据点仓储接口
    /// </summary>
    public interface IDataPointRepository : IRepository<TestDataPoint, long>
    {
        /// <summary>
        /// 获取特定测试运行的所有数据点
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <returns>数据点列表</returns>
        Task<IEnumerable<TestDataPoint>> GetByTestRunIdAsync(long testRunId);

        /// <summary>
        /// 获取特定测试运行的数据点分页列表
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页的数据点列表</returns>
        Task<PagedResultDto<TestDataPoint>> GetPagedByTestRunIdAsync(long testRunId, int page = 1, int pageSize = 20);
    }
}
