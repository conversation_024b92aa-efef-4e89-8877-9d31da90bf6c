﻿using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Configuration.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Configuration.Models
{
    public class LogSettings : ObservableObject
    {
        private LogLevel _logLevel = LogLevel.Information;
        private int _retentionDays = 30;
        private string _logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

        public LogLevel LogLevel
        {
            get => _logLevel;
            set => SetProperty(ref _logLevel, value);
        }

        public int RetentionDays
        {
            get => _retentionDays;
            set => SetProperty(ref _retentionDays, value);
        }

        public string LogPath
        {
            get => _logPath;
            set => SetProperty(ref _logPath, value);
        }
    }
}
