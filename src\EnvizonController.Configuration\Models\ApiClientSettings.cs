using CommunityToolkit.Mvvm.ComponentModel;

namespace EnvizonController.Configuration.Models
{
    /// <summary>
    /// API客户端配置
    /// </summary>
    public partial class ApiClientSettings : ObservableObject
    {
        /// <summary>
        /// API服务器基础URL
        /// </summary>
        [ObservableProperty]
        private string _baseUrl = "https://localhost:7110";

        /// <summary>
        /// HTTP请求超时时间（毫秒）
        /// </summary>
        [ObservableProperty]
        private int _timeoutMs = 30000;

        /// <summary>
        /// 重试次数
        /// </summary>
        [ObservableProperty]
        private int _retryCount = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        [ObservableProperty]
        private int _retryDelayMs = 1000;
    }
}