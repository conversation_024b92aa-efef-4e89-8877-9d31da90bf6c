using AutoMapper;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Domain.Services;
using EnvizonController.Shared.DTOs;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Application.Services;

/// <summary>
///     程式表应用服务实现
/// </summary>
public class ProgramAppService : IProgramAppService
{
    private readonly ILogger<ProgramAppService> _logger;
    private readonly IMapper _mapper;
    private readonly IProgramDomainService _programDomainService;
    private readonly IProgramRepository _programRepository;
    private readonly IProgramStepRepository _programStepRepository;
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="programRepository">程式表仓储</param>
    /// <param name="unitOfWork">工作单元</param>
    /// <param name="mapper">对象映射器</param>
    /// <param name="programDomainService">程式表领域服务</param>
    /// <param name="programStepRepository">程式步骤仓储</param>
    /// <param name="logger">日志记录器</param>
    public ProgramAppService(
        IProgramRepository programRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IProgramDomainService programDomainService,
        IProgramStepRepository programStepRepository,
        ILogger<ProgramAppService> logger)
    {
        _programRepository = programRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _programDomainService = programDomainService;
        _programStepRepository = programStepRepository;
        _logger = logger;
    }

    /// <summary>
    ///     获取所有程式表
    /// </summary>
    public async Task<IEnumerable<ProgramDTO>> GetAllProgramsAsync()
    {
        var programs = await _programRepository.GetAllWithStepsAsync();
        return _mapper.Map<IEnumerable<ProgramDTO>>(programs);
    }

    /// <summary>
    ///     根据ID获取程式表
    /// </summary>
    public async Task<ProgramDTO?> GetProgramByIdAsync(long id)
    {
        var program = await _programRepository.GetByIdWithStepsAsync(id);
        return program != null ? _mapper.Map<ProgramDTO>(program) : null;
    }

    /// <summary>
    ///     根据名称获取程式表
    /// </summary>
    public async Task<ProgramDTO?> GetProgramByNameAsync(string name)
    {
        var program = await _programRepository.GetByNameAsync(name);
        return program != null ? _mapper.Map<ProgramDTO>(program) : null;
    }

    /// <summary>
    ///     创建程式表
    /// </summary>
    public async Task<ProgramDTO> CreateProgramAsync(ProgramDTO programDto)
    {
        // 验证程式名称
        var isNameValid = await _programDomainService.ValidateProgramNameAsync(programDto.Name);
        if (!isNameValid) throw new ArgumentException($"程式名称 {programDto.Name} 无效或已存在");

        // 验证循环参数
        var isCycleValid =
            await _programDomainService.ValidateProgramCycleAsync(programDto.CycleStart, programDto.CycleEnd);
        if (!isCycleValid) throw new ArgumentException("程式循环参数无效");

        // 映射并创建程式表
        var program = _mapper.Map<Program>(programDto);
        program.CreatedAt = DateTime.Now;
        program.UpdatedAt = DateTime.Now;

        // 处理步骤
        if (programDto.Steps != null && programDto.Steps.Any())
        {
            program.Steps = new List<ProgramStep>();

            foreach (var stepDto in programDto.Steps)
            {
                var step = _mapper.Map<ProgramStep>(stepDto);
                step.ProgramId = program.Id;
                step.CreatedAt = DateTime.Now;
                step.UpdatedAt = DateTime.Now;

                // 验证步骤参数
                var isStepValid = await _programDomainService.ValidateProgramStepAsync(step);
                if (!isStepValid) throw new ArgumentException($"程式步骤参数无效，步骤索引: {step.Index}");

                program.AddStep(step);
            }

            // 验证步骤顺序
            var isOrderValid = _programDomainService.ValidateProgramStepsOrder(program.Steps);
            if (!isOrderValid) throw new ArgumentException("程式步骤顺序无效，请确保索引连续且不重复");
        }

        // 保存到数据库
        await _programRepository.AddAsync(program);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("创建程式表 {ProgramName} 成功，ID: {ProgramId}", program.Name, program.Id);

        return _mapper.Map<ProgramDTO>(program);
    }

    /// <summary>
    ///     更新程式表
    /// </summary>
    public async Task<ProgramDTO> UpdateProgramAsync(ProgramDTO programDto)
    {
        // 检查程式表是否存在
        var existingProgram = await _programRepository.GetByIdWithStepsAsync(programDto.Id);
        if (existingProgram == null) throw new KeyNotFoundException($"程式表 ID: {programDto.Id} 不存在");

        // 如果名称发生变化，验证新名称
        if (existingProgram.Name != programDto.Name)
        {
            var isNameValid = await _programDomainService.ValidateProgramNameAsync(programDto.Name);
            if (!isNameValid) throw new ArgumentException($"程式名称 {programDto.Name} 无效或已存在");
        }

        // 验证循环参数
        var isCycleValid =
            await _programDomainService.ValidateProgramCycleAsync(programDto.CycleStart, programDto.CycleEnd);
        if (!isCycleValid) throw new ArgumentException("程式循环参数无效");

        // 更新基本信息
        existingProgram.Name = programDto.Name;
        existingProgram.CycleCount = programDto.CycleCount;
        existingProgram.CycleStart = programDto.CycleStart;
        existingProgram.CycleEnd = programDto.CycleEnd;
        existingProgram.UpdatedAt = DateTime.Now;

        // 保存程式表
        _programRepository.Update(existingProgram);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("更新程式表 {ProgramName} 成功，ID: {ProgramId}", existingProgram.Name, existingProgram.Id);

        return _mapper.Map<ProgramDTO>(existingProgram);
    }

    /// <summary>
    ///     删除程式表
    /// </summary>
    public async Task<bool> DeleteProgramAsync(long id)
    {
        try
        {
            await _programRepository.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("删除程式表成功，ID: {ProgramId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除程式表失败，ID: {ProgramId}", id);
            return false;
        }
    }

    /// <summary>
    ///     获取程式表的所有步骤
    /// </summary>
    public async Task<IEnumerable<ProgramStepDTO>> GetProgramStepsAsync(long programId)
    {
        var program = await _programRepository.GetByIdWithStepsAsync(programId);
        if (program == null) throw new KeyNotFoundException($"程式表 ID: {programId} 不存在");

        var orderedSteps = _programDomainService.OrderStepsByIndex(program.Steps);
        return _mapper.Map<IEnumerable<ProgramStepDTO>>(orderedSteps);
    }

    /// <summary>
    ///     创建程式步骤
    /// </summary>
    public async Task<ProgramStepDTO> CreateProgramStepAsync(ProgramStepDTO stepDto)
    {
        // 检查程式表是否存在
        var program = await _programRepository.GetByIdWithStepsAsync(stepDto.ProgramId);
        if (program == null) throw new KeyNotFoundException($"程式表 ID: {stepDto.ProgramId} 不存在");

        // 映射步骤
        var step = _mapper.Map<ProgramStep>(stepDto);
        step.CreatedAt = DateTime.Now;
        step.UpdatedAt = DateTime.Now;

        // 验证步骤参数
        var isStepValid = await _programDomainService.ValidateProgramStepAsync(step);
        if (!isStepValid) throw new ArgumentException($"程式步骤参数无效，步骤索引: {step.Index}");

        // 添加步骤
        program.AddStep(step);

        // 验证步骤顺序
        var isOrderValid = _programDomainService.ValidateProgramStepsOrder(program.Steps);
        if (!isOrderValid) throw new ArgumentException("程式步骤顺序无效，请确保索引连续且不重复");

        // 更新程式表
        program.UpdatedAt = DateTime.Now;
        _programRepository.Update(program);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("为程式表 {ProgramId} 创建步骤成功，步骤索引: {StepIndex}", program.Id, step.Index);
        return _mapper.Map<ProgramStepDTO>(step);
    }

    /// <summary>
    ///     更新程式步骤
    /// </summary>
    public async Task<ProgramStepDTO> UpdateProgramStepAsync(ProgramStepDTO stepDto)
    {
        // 检查程式表是否存在
        var program = await _programRepository.GetByIdWithStepsAsync(stepDto.ProgramId);
        if (program == null) throw new KeyNotFoundException($"程式表 ID: {stepDto.ProgramId} 不存在");

        // 检查步骤是否存在
        var existingStep = program.FindStepById(stepDto.Id);
        if (existingStep == null) throw new KeyNotFoundException($"程式步骤 ID: {stepDto.Id} 不存在");

        // 更新步骤信息
        existingStep.Humidity = stepDto.Humidity;
        existingStep.Temperature = stepDto.Temperature;
        existingStep.IsLinear = stepDto.IsLinear;
        existingStep.Duration = stepDto.Duration;
        existingStep.UpdatedAt = DateTime.Now;

        // 如果索引发生变化，需要重新验证步骤顺序
        if (existingStep.Index != stepDto.Index)
        {
            existingStep.Index = stepDto.Index;

            // 验证步骤顺序
            var isOrderValid = _programDomainService.ValidateProgramStepsOrder(program.Steps);
            if (!isOrderValid) throw new ArgumentException("程式步骤顺序无效，请确保索引连续且不重复");
        }

        // 验证步骤参数
        var isStepValid = await _programDomainService.ValidateProgramStepAsync(existingStep);
        if (!isStepValid) throw new ArgumentException($"程式步骤参数无效，步骤索引: {existingStep.Index}");

        // 更新程式表
        program.UpdatedAt = DateTime.Now;
        _programRepository.Update(program);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("更新程式步骤成功，步骤ID: {StepId}", existingStep.Id);
        return _mapper.Map<ProgramStepDTO>(existingStep);
    }

    /// <summary>
    ///     删除程式步骤
    /// </summary>
    public async Task<bool> DeleteProgramStepAsync(long id)
    {
        // 在所有程式表中查找并删除指定ID的步骤
        var programs = await _programRepository.GetAllWithStepsAsync();

        foreach (var program in programs)
        {
            var step = program.FindStepById(id);
            if (step != null)
            {
                // 移除步骤
                var removed = program.RemoveStep(step);
                if (removed)
                {
                    // 更新程式表
                    program.UpdatedAt = DateTime.Now;
                    _programRepository.Update(program);
                    await _unitOfWork.SaveChangesAsync();

                    _logger.LogInformation("删除程式步骤成功，步骤ID: {StepId}", id);
                    return true;
                }
            }
        }

        _logger.LogWarning("删除程式步骤失败，步骤ID: {StepId} 不存在", id);
        return false;
    }

    /// <summary>
    ///     批量更新程式步骤
    /// </summary>
    public async Task<IEnumerable<ProgramStepDTO>> BatchUpdateProgramStepsAsync(long programId, IEnumerable<ProgramStepDTO> stepDtos)
    {
        try
        {
            // 开始事务
            await _unitOfWork.BeginTransactionAsync();

            // 检查程式表是否存在
            var program = await _programRepository.GetByIdWithStepsAsync(programId);
            if (program == null)
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw new KeyNotFoundException($"程式表 ID: {programId} 不存在");
            }

            // 显式删除旧的步骤
            if (program.Steps != null && program.Steps.Any())
            {
                await _unitOfWork.ProgramSteps.DeleteByProgramIdAsync(programId);
            }
            program.ClearSteps();
            // 添加新的步骤
            var newMappedSteps = new List<ProgramStep>();
            foreach (var stepDto in stepDtos)
            {
                var step = _mapper.Map<ProgramStep>(stepDto);
                step.ProgramId = programId;
                stepDto.Id = 0;

                step.CreatedAt = DateTime.Now;
                step.UpdatedAt = DateTime.Now;

                newMappedSteps.Add(step);
            }

            // 验证步骤顺序
            var isOrderValid = _programDomainService.ValidateProgramStepsOrder(newMappedSteps);
            if (!isOrderValid)
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw new ArgumentException("程式步骤顺序无效，请确保索引连续且不重复");
            }

            // 验证所有步骤参数
            foreach (var step in newMappedSteps)
            {
                var isStepValid = await _programDomainService.ValidateProgramStepAsync(step);
                if (!isStepValid)
                {
                    await _unitOfWork.RollbackTransactionAsync();
                    throw new ArgumentException($"程式步骤参数无效，步骤索引: {step.Index}");
                }
            }

            // 将验证后的新步骤添加到 program 实体
            foreach (var step in newMappedSteps)
            {
                program.AddStep(step);
            }

            // 更新程式表
            program.UpdatedAt = DateTime.Now;
            _unitOfWork.ProgramSteps.AddRangeAsync(program.Steps);

            // 提交事务
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("批量更新程式表 {ProgramId} 的步骤成功，共 {StepCount} 个步骤", programId, program.Steps.Count);
            return _mapper.Map<IEnumerable<ProgramStepDTO>>(program.Steps);
        }
        catch (Exception ex)
        {
            // 发生异常时回滚事务
            //await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "批量更新程式表 {ProgramId} 的步骤失败", programId);
            throw;
        }
    }
    /// <summary>
    /// 初始化默认 Program 数据
    /// </summary>
    public async Task InitializeDefaultProgramsAsync()
    {
        _logger.LogInformation("开始初始化默认 Program 数据...");

        var existingProgram = await _programRepository.GetByNameAsync("Default Program");
        if (existingProgram == null)
        {
            _logger.LogInformation("未找到 'Default Program'，正在创建...");

            // 使用CreateProgramAsync方法创建默认程序
            var defaultProgramDto = new ProgramDTO
            {
                Name = "Default Program",
                CycleCount = 1,
                CycleStart = 1,
                CycleEnd = 1,
                Steps = new List<ProgramStepDTO>
                {
                    new ProgramStepDTO
                    {
                        Index = 1,
                        Humidity = 50.0,
                        Temperature = 25.0,
                        IsLinear = false,
                        Duration = 60,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new ProgramStepDTO
                    {
                        Index = 2,
                        Humidity = 55.0,
                        Temperature = 26.0,
                        IsLinear = false,
                        Duration = 120,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new ProgramStepDTO
                    {
                        Index = 3,
                        Humidity = 60.0,
                        Temperature = 27.0,
                        IsLinear = false,
                        Duration = 180,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    },
                    new ProgramStepDTO
                    {
                        Index = 4,
                        Humidity = 50.0,
                        Temperature = 25.0,
                        IsLinear = false,
                        Duration = 60,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    }
                }

            };

            try
            {
                // 使用现有的CreateProgramAsync方法创建程序
                var createdProgram = await CreateProgramAsync(defaultProgramDto);
                _logger.LogInformation("'Default Program' 创建成功，ID: {ProgramId}", createdProgram.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建默认 Program 时发生错误");
                return;
            }
        }
        else
        {
            _logger.LogInformation("'Default Program' 已存在，跳过创建。");
        }

        _logger.LogInformation("默认 Program 数据初始化完成。");
    }
}