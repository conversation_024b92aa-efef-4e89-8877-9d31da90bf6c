using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EnvizonController.DataProcessing.Implementation
{
    /// <summary>
    /// 基础数据处理管道
    /// </summary>
    public class BaseDataProcessingPipeline : IDataProcessingPipeline
    {
        private readonly List<IDataProcessor> _processors = new();
        
        /// <summary>
        /// 获取管道名称
        /// </summary>
        public string Name { get; }
        
        /// <summary>
        /// 获取管道描述
        /// </summary>
        public string Description { get; }
        
        /// <summary>
        /// 获取处理器列表
        /// </summary>
        public IReadOnlyList<IDataProcessor> Processors => _processors.AsReadOnly();
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">管道名称</param>
        /// <param name="description">管道描述</param>
        public BaseDataProcessingPipeline(string name, string description = "")
        {
            Name = name;
            Description = description;
        }
        
        /// <summary>
        /// 添加处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        public IDataProcessingPipeline AddProcessor(IDataProcessor processor)
        {
            _processors.Add(processor);
            return this;
        }
        
        /// <summary>
        /// 移除处理器
        /// </summary>
        /// <param name="processor">数据处理器</param>
        /// <returns>当前管道实例</returns>
        public IDataProcessingPipeline RemoveProcessor(IDataProcessor processor)
        {
            _processors.Remove(processor);
            return this;
        }
        
        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="context">处理上下文</param>
        /// <returns>处理结果</returns>
        public async Task<IDataProcessingContext> ProcessAsync(IDataProcessingContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }
            
            context.Status = ProcessingStatus.Processing;
            
            foreach (var processor in _processors)
            {
                if (processor.CanProcess(context))
                {
                    try
                    {
                        context = await processor.ProcessAsync(context);
                        
                        // 如果处理失败或跳过，则停止管道
                        if (context.Status == ProcessingStatus.Failed || context.Status == ProcessingStatus.Skipped)
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        context.Status = ProcessingStatus.Failed;
                        context.AddError($"处理器 {processor.Name} 处理时出错: {ex.Message}");
                        break;
                    }
                }
            }
            
            // 如果处理完成且状态仍为Processing，则设置为Succeeded
            if (context.Status == ProcessingStatus.Processing)
            {
                context.Status = ProcessingStatus.Succeeded;
            }
            
            return context;
        }
        
        /// <summary>
        /// 清空管道
        /// </summary>
        public void Clear()
        {
            _processors.Clear();
        }
    }
}
