using System;
using System.Threading.Tasks;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接管理器接口，负责管理MQTT连接的生命周期和状态
    /// </summary>
    public interface IMqttConnectionManager : IDisposable
    {
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        ConnectionStatus Status { get; }

        /// <summary>
        /// 当连接状态变化时发生
        /// </summary>
        event EventHandler<MqttConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 异步连接到MQTT服务器
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        Task ConnectAsync();

        /// <summary>
        /// 异步断开与MQTT服务器的连接
        /// </summary>
        /// <returns>表示异步操作的任务</returns>
        Task DisconnectAsync();
    }
}