using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Threading;
using AvaloniaLineSeriesChart.Data;
using AvaloniaLineSeriesChart.Data.Enums;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Shared.DTOs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace EnvizonController.Presentation.Views.DataQuery
{
    public partial class DataChartView : UserControl
    {
        private ObservableCollection<LegendItem> _legendItems = new ObservableCollection<LegendItem>();
        private ObservableCollection<SeriesFilterItem> _filterItems = new ObservableCollection<SeriesFilterItem>();
        private SeriesVisibilityManager _visibilityManager;
        
        // 赛博朋克风格的颜色调色板
        private readonly Color[] _cyberColors = new Color[]
        {
            Color.FromRgb(0xe6, 0x2f, 0xf3), // 紫红色
            Color.FromRgb(0x0d, 0xf0, 0xff), // 青色
            Color.FromRgb(0x00, 0xff, 0x41), // 霓虹绿
            Color.FromRgb(0xff, 0x00, 0x7f), // 霓虹粉
            Color.FromRgb(0xff, 0x9f, 0x00), // 橙色
            Color.FromRgb(0x7f, 0xff, 0x00), // 黄绿色
            Color.FromRgb(0x00, 0x7f, 0xff), // 蓝色
            Color.FromRgb(0xff, 0x00, 0xff), // 品红色
        };

        public DataChartView()
        {
            InitializeComponent();
            _visibilityManager = new SeriesVisibilityManager(_legendItems, _filterItems);
            InitializeChart();
            InitializeLegend();
            InitializeFilter();
            // 订阅 ViewModel 的 PropertyChanged 事件，以便在数据更改时更新图表
            DataContextChanged += OnDataContextChanged;
        }

        private void OnDataContextChanged(object? sender, EventArgs e)
        {
            if (DataContext is DataQueryViewModel vm)
            {
                vm.PropertyChanged += (s, args) =>
                {
                    if (args.PropertyName == nameof(DataQueryViewModel.DataPoints) ||
                        args.PropertyName == nameof(DataQueryViewModel.SelectedTestItem))
                    {
                        UpdateChart();
                    }
                };
                // 初始加载
                UpdateChart();
            }
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void InitializeLegend()
        {
            var legendItems = this.FindControl<ItemsControl>("LegendItems");
            if (legendItems != null)
            {
                legendItems.ItemsSource = _legendItems;
            }
        }

        private void InitializeFilter()
        {
            var filterItems = this.FindControl<ItemsControl>("FilterItems");
            if (filterItems != null)
            {
                filterItems.ItemsSource = _filterItems;
            }
        }

        private void InitializeChart()
        {
            var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
            if (lineChart == null) return;

            lineChart.SetTheme(new ChartTheme
            {
                BackgroundColor = Colors.Transparent,
                TextColor = Colors.White,
                AxisColor = Color.FromRgb(0x66, 0x66, 0x66),
                GridLineColor = Color.FromRgb(0x44, 0x44, 0x44),
                DefaultSeriesColors = _cyberColors,
                TooltipBackgroundColor = Color.FromRgb(50, 50, 50)
            });
            lineChart.SetXAxisType(XAxisType.DateTime); // X轴代表时间戳
            lineChart.SetXTickCount(5);
            lineChart.SetXAxisLabelFormatter(s =>
            {
                // 格式化X轴标签为时间格式
                if (s != null && double.TryParse(s.ToString(), out double timestamp))
                {
                    // 将时间戳转换为DateTime
                    var dateTime = DateTimeOffset.FromUnixTimeMilliseconds((long)timestamp).DateTime.ToLocalTime();
                    return dateTime.ToString("MM-dd HH:mm:ss");
                }
                return s?.ToString() ?? string.Empty;
            });
        }

        private void UpdateChart()
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                // 获取图表控件
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                // 清除旧的曲线数据和图例
                lineChart.ClearSeries();
                _legendItems.Clear();
                _filterItems.Clear();

                // 检查数据上下文和数据点是否有效
                if (DataContext is not DataQueryViewModel vm || vm.DataPoints == null || !vm.DataPoints.Any())
                {
                    lineChart.Refresh(); // 即使没有数据，也刷新图表以清空内容
                    UpdateStatistics(null); // 清空统计信息
                    return;
                }

                // 获取数据点集合
                var dataPoints = vm.DataPoints.ToList();
                
                // 创建数据系列字典，每个协议索引对应一个数据系列
                Dictionary<string, LineSeriesData> seriesDict = new Dictionary<string, LineSeriesData>();
                
                // 收集所有可能的协议索引
                HashSet<string> protocolIndices = new HashSet<string>();
                foreach (var dataPoint in dataPoints)
                {
                    foreach (var value in dataPoint.Values)
                    {
                        protocolIndices.Add(value.ProtocolName);
                    }
                }
                
                // 为每个协议索引创建一个数据系列
                int colorIndex = 0;
                foreach (var protocol in protocolIndices.OrderBy(p => p))
                {
                    var color = _cyberColors[colorIndex % _cyberColors.Length];
                    var series = new LineSeriesData
                    {
                        Name = $"{protocol}",
                        YAxisPosition = YAxisPosition.Left,
                        Color = color
                    };
                    seriesDict[protocol] = series;
                    lineChart.AddSeries(series);
                    series.SetXDataType<double>(); // 设置X轴为时间戳类型
                    
                    // 添加到图例
                    var legendItem = new LegendItem 
                    { 
                        Name = protocol, 
                        Color = new SolidColorBrush(color),
                        IsVisible = true
                    };
                    _legendItems.Add(legendItem);
                    
                    // 添加到筛选项
                    var filterItem = new SeriesFilterItem
                    {
                        SeriesName = protocol,
                        IsSeriesVisible = true,
                        IsYAxisVisible = true,
                        Color = new SolidColorBrush(color)
                    };
                    _filterItems.Add(filterItem);
                    
                    // 建立关联关系
                    _visibilityManager.LinkItems(legendItem, filterItem);
                    
                    colorIndex++;
                }
                
                // 添加数据点到对应的数据系列
                foreach (var dataPoint in dataPoints.OrderBy(dp => dp.Timestamp))
                {
                    // 将时间戳转换为毫秒时间戳，用于X轴
                    double timestamp = new DateTimeOffset(dataPoint.Timestamp).ToUnixTimeMilliseconds();
                    
                    foreach (var valueData in dataPoint.Values)
                    {
                        if (seriesDict.TryGetValue(valueData.ProtocolName, out var series) && 
                            double.TryParse(valueData.Value, out double value))
                        {
                            series.Points.Add(new Point(timestamp, value));
                        }
                    }
                }
                
                // 刷新图表
                lineChart.Refresh();
                
                // 更新统计信息
                UpdateStatistics(dataPoints);
            });
        }

        private void UpdateStatistics(List<DataPointDto>? dataPoints)
        {
            var maxValueText = this.FindControl<TextBlock>("MaxValueText");
            var minValueText = this.FindControl<TextBlock>("MinValueText");
            var avgValueText = this.FindControl<TextBlock>("AvgValueText");

            if (dataPoints == null || !dataPoints.Any())
            {
                if (maxValueText != null) maxValueText.Text = "--";
                if (minValueText != null) minValueText.Text = "--";
                if (avgValueText != null) avgValueText.Text = "--";
                return;
            }

            // 收集所有数值
            var allValues = new List<double>();
            foreach (var dataPoint in dataPoints)
            {
                foreach (var value in dataPoint.Values)
                {
                    if (double.TryParse(value.Value, out double numValue))
                    {
                        allValues.Add(numValue);
                    }
                }
            }

            if (allValues.Any())
            {
                var max = allValues.Max();
                var min = allValues.Min();
                var avg = allValues.Average();

                if (maxValueText != null) maxValueText.Text = max.ToString("F2");
                if (minValueText != null) minValueText.Text = min.ToString("F2");
                if (avgValueText != null) avgValueText.Text = avg.ToString("F2");
            }
            else
            {
                if (maxValueText != null) maxValueText.Text = "--";
                if (minValueText != null) minValueText.Text = "--";
                if (avgValueText != null) avgValueText.Text = "--";
            }
        }

        private void OnSeriesVisibilityChanged(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox && checkBox.DataContext is SeriesFilterItem filterItem)
            {
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                _visibilityManager.UpdateSeriesVisibility(filterItem.SeriesName, checkBox.IsChecked == true, lineChart);
            }
        }

        private void OnYAxisVisibilityChanged(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is CheckBox checkBox && checkBox.DataContext is SeriesFilterItem filterItem)
            {
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                _visibilityManager.UpdateYAxisVisibility(filterItem.SeriesName, checkBox.IsChecked == true, lineChart);
            }
        }

        private void OnShowAllSeries(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
            if (lineChart == null) return;

            _visibilityManager.ShowAllSeries(lineChart);
        }

        private void OnHideAllSeries(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
            if (lineChart == null) return;

            _visibilityManager.HideAllSeries(lineChart);
        }

        private void OnLegendItemClick(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is Border border && border.DataContext is LegendItem legendItem)
            {
                var lineChart = this.FindControl<AvaloniaLineSeriesChart.Controls.LineChartControl>("LineChart");
                if (lineChart == null) return;

                _visibilityManager.ToggleLegendItemVisibility(legendItem.Name, lineChart);
            }
        }
    }

    /// <summary>
    /// 数据系列可见性管理器 - 统一管理图例项和筛选项的可见性状态同步
    /// </summary>
    public class SeriesVisibilityManager
    {
        private readonly ObservableCollection<LegendItem> _legendItems;
        private readonly ObservableCollection<SeriesFilterItem> _filterItems;
        private readonly Dictionary<string, (LegendItem legend, SeriesFilterItem filter)> _itemLinks;

        public SeriesVisibilityManager(ObservableCollection<LegendItem> legendItems, ObservableCollection<SeriesFilterItem> filterItems)
        {
            _legendItems = legendItems;
            _filterItems = filterItems;
            _itemLinks = new Dictionary<string, (LegendItem, SeriesFilterItem)>();
        }

        /// <summary>
        /// 建立图例项和筛选项的关联关系
        /// </summary>
        public void LinkItems(LegendItem legendItem, SeriesFilterItem filterItem)
        {
            _itemLinks[legendItem.Name] = (legendItem, filterItem);
        }

        /// <summary>
        /// 更新系列可见性
        /// </summary>
        public void UpdateSeriesVisibility(string seriesName, bool isVisible, AvaloniaLineSeriesChart.Controls.LineChartControl lineChart)
        {
            if (!_itemLinks.TryGetValue(seriesName, out var items)) return;

            // 更新筛选项状态
            items.filter.IsSeriesVisible = isVisible;
            
            // 更新图例项状态
            items.legend.IsVisible = isVisible;

            // 应用到图表
            if (isVisible)
            {
                lineChart.ShowSeriesByName(seriesName);
                items.filter.IsYAxisVisible = true;
            }
            else
            {
                lineChart.HideSeriesByName(seriesName);
                items.filter.IsYAxisVisible = false;
            }

            lineChart.Refresh();
        }

        /// <summary>
        /// 更新Y轴可见性
        /// </summary>
        public void UpdateYAxisVisibility(string seriesName, bool isVisible, AvaloniaLineSeriesChart.Controls.LineChartControl lineChart)
        {
            if (!_itemLinks.TryGetValue(seriesName, out var items)) return;

            items.filter.IsYAxisVisible = isVisible;

            var series = lineChart.Series.FirstOrDefault(s => s.Name == seriesName);
            if (series != null && series.ShowYAxis != isVisible)
            {
                if (isVisible)
                {
                    lineChart.ShowYAxisByName(seriesName);
                }
                else
                {
                    lineChart.HideYAxisByName(seriesName);
                }
            }
        }

        /// <summary>
        /// 切换图例项可见性
        /// </summary>
        public void ToggleLegendItemVisibility(string seriesName, AvaloniaLineSeriesChart.Controls.LineChartControl lineChart)
        {
            if (!_itemLinks.TryGetValue(seriesName, out var items)) return;

            var newVisibility = !items.legend.IsVisible;
            UpdateSeriesVisibility(seriesName, newVisibility, lineChart);
        }

        /// <summary>
        /// 显示所有系列
        /// </summary>
        public void ShowAllSeries(AvaloniaLineSeriesChart.Controls.LineChartControl lineChart)
        {
            foreach (var kvp in _itemLinks)
            {
                UpdateSeriesVisibility(kvp.Key, true, lineChart);
            }
        }

        /// <summary>
        /// 隐藏所有系列
        /// </summary>
        public void HideAllSeries(AvaloniaLineSeriesChart.Controls.LineChartControl lineChart)
        {
            foreach (var kvp in _itemLinks)
            {
                UpdateSeriesVisibility(kvp.Key, false, lineChart);
            }
        }
    }

    /// <summary>
    /// 图例项 - 增强版，支持透明度属性
    /// </summary>
    public class LegendItem : INotifyPropertyChanged
    {
        private bool _isVisible = true;

        public string Name { get; set; } = string.Empty;
        public IBrush Color { get; set; } = Brushes.White;
        
        public bool IsVisible 
        { 
            get => _isVisible;
            set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Opacity));
                }
            }
        }

        /// <summary>
        /// 透明度属性 - 根据可见性自动计算
        /// </summary>
        public double Opacity => IsVisible ? 1.0 : 0.4;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 筛选项 - 增强版，支持透明度属性
    /// </summary>
    public class SeriesFilterItem : INotifyPropertyChanged
    {
        private bool _isSeriesVisible = true;
        private bool _isYAxisVisible = true;

        public string SeriesName { get; set; } = string.Empty;
        
        public bool IsSeriesVisible 
        { 
            get => _isSeriesVisible;
            set
            {
                if (_isSeriesVisible != value)
                {
                    _isSeriesVisible = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Opacity));
                }
            }
        }
        
        public bool IsYAxisVisible 
        { 
            get => _isYAxisVisible;
            set
            {
                if (_isYAxisVisible != value)
                {
                    _isYAxisVisible = value;
                    OnPropertyChanged();
                }
            }
        }
        
        public IBrush Color { get; set; } = Brushes.White;

        /// <summary>
        /// 透明度属性 - 根据系列可见性自动计算
        /// </summary>
        public double Opacity => IsSeriesVisible ? 1.0 : 0.4;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}