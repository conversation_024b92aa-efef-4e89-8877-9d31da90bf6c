using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Shared.Enums;
using System.Collections.Generic;

namespace EnvizonController.Domain.Services.Implementations;

/// <summary>
///     数据采集服务实现
/// </summary>
public class DataCollectionDomainService : IDataCollectionDomainService
{
    private readonly ITestItemRepository _testItemRepository;

    public DataCollectionDomainService(
        ITestItemRepository testItemRepository)
    {
        _testItemRepository = testItemRepository;
    }

    /// <summary>
    ///     保存采集的数据点
    /// </summary>
    public async Task<TestDataPoint?> SaveDataPointAsync(long testRunId, TestDataPoint testDataPoint)
    {
        // 验证测试运行是否存在
        var testItem = await _testItemRepository.GetByIdAsync(testRunId);

        // 使用TestRun的方法添加数据点，它会检查测试是否在运行中
        var dataPoint = testItem.AddDataCollection(testDataPoint);

        // 如果测试不在运行中，AddDataCollection会返回null
        if (dataPoint == null) return null;
        return dataPoint;
    }

    /// <summary>
    ///     根据设备ID保存采集的数据点
    /// </summary>
    public async Task<TestDataPoint?> SaveDataPointByDeviceIdAsync(long deviceId, TestDataPoint testDataPoint)
    {
        // 获取设备的最新运行中测试项
        var testItem = await _testItemRepository.GetLatestRunningTestItemByDeviceIdAsync(deviceId);
        var v= await _testItemRepository.GetAllAsync();
        // 如果没有找到运行中的测试项，返回null
        if (testItem == null) return null;

        // 使用测试项的方法添加数据点
        var dataPoint = testItem.AddDataCollection(testDataPoint);

        // 如果测试不在运行中，AddDataCollection会返回null
        return dataPoint;
    }

    /// <summary>
    ///     创建数据采集报警
    /// </summary>
    public async Task<Alarm?> CreateCollectionAlarmAsync(long testRunId, string message)
    {
        // 验证测试运行是否存在
        var testItem = await _testItemRepository.GetByIdAsync(testRunId);

        // 使用TestRun的方法添加报警，它会检查测试是否在运行中
        var alarm = Alarm.AddAlarm("DataCollection", message, testRunId);

        // 如果测试不在运行中，AddAlarm会返回null
        if (alarm == null) return null;

        // 设置报警级别
        alarm.Level = (int)AlarmSeverity.Medium;
        return alarm;
    }

    /// <summary>
    ///     根据设备ID创建数据采集报警
    /// </summary>
    public async Task<Alarm?> CreateCollectionAlarmByDeviceIdAsync(long deviceId, string message)
    {
        // 获取设备的最新运行中测试项
        var testItem = await _testItemRepository.GetLatestRunningTestItemByDeviceIdAsync(deviceId);

        // 如果没有找到运行中的测试项，返回null
        if (testItem == null) return null;

        // 使用测试项ID创建报警
        var alarm = Alarm.AddAlarm("DataCollection", message, testItem.Id);

        // 设置报警级别
        if (alarm != null)
        {
            alarm.Level = (int)AlarmSeverity.Medium;
        }

        return alarm;
    }
    
    /// <summary>
    ///     根据设备ID保存报警数据
    /// </summary>
    public async Task<bool> SaveAlarmsByDeviceIdAsync(long deviceId, List<Alarm> alarms)
    {
        if (alarms == null || !alarms.Any())
        {
            return true; // 没有数据需要保存，认为是成功的
        }
        
        try
        {
            // 获取设备的最新运行中测试项
            var testItem = await _testItemRepository.GetLatestRunningTestItemByDeviceIdAsync(deviceId);
            
            // 如果没有找到运行中的测试项，仅记录设备ID
            if (testItem == null)
            {
                // 设置设备ID，但不关联测试ID
                foreach (var alarm in alarms)
                {
                    alarm.DeviceId = deviceId;
                }
            }
            else
            {
                // 设置设备ID和测试ID
                foreach (var alarm in alarms)
                {
                    alarm.DeviceId = deviceId;
                    alarm.TestId = testItem.Id;
                }
            }
            
            // 由于Alarm是实体而不是聚合根，我们不在这里保存
            // 调用者应负责将它们添加到仓储
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
}