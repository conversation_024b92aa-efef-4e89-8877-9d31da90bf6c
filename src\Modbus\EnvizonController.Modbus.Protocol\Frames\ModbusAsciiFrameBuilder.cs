using EnvizonController.Modbus.Protocol.Models;
using EnvizonController.Modbus.Protocol.Utils;
using System.Text;

namespace EnvizonController.Modbus.Protocol.Frames
{
    /// <summary>
    /// Modbus ASCII帧构建器
    /// </summary>
    public class ModbusAsciiFrameBuilder : IModbusFrameBuilder
    {
        private const char StartCharacter = ':';
        private const string EndCharacters = "\r\n";

        /// <summary>
        /// 构建请求帧
        /// </summary>
        /// <param name="request">Modbus请求</param>
        /// <returns>完整的请求帧字节数组</returns>
        public byte[] BuildRequestFrame(ModbusRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            // 获取请求的基本帧（地址+功能码+数据）
            byte[] frame = request.GetFrame();
            
            // 添加LRC校验
            byte[] frameWithLrc = ModbusLrc.AppendLrc(frame);
            
            // 转换为ASCII字符串
            string asciiFrame = ModbusLrc.BytesToAscii(frameWithLrc);
            
            // 添加起始和结束字符
            string completeFrame = StartCharacter + asciiFrame + EndCharacters;
            
            // 转换为字节数组
            return Encoding.ASCII.GetBytes(completeFrame);
        }

        /// <summary>
        /// 解析响应帧
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <param name="response">Modbus响应对象</param>
        /// <returns>是否成功解析</returns>
        public bool ParseResponseFrame(byte[] responseFrame, ModbusResponse response)
        {
            if (responseFrame == null || responseFrame.Length < 9) // 至少需要:(1)+地址(2)+功能码(2)+LRC(2)+CRLF(2)
                return false;

            if (response == null)
                throw new ArgumentNullException(nameof(response));

            // 验证帧格式
            if (!ValidateResponseFrame(responseFrame))
                return false;

            try
            {
                // 转换为ASCII字符串
                string asciiFrame = Encoding.ASCII.GetString(responseFrame);
                
                // 移除起始和结束字符
                string dataAscii = asciiFrame.Substring(1, asciiFrame.Length - 3);
                
                // 转换为字节数组
                byte[] binaryData = ModbusLrc.AsciiToBytes(dataAscii);
                
                // 验证LRC
                if (!ModbusLrc.ValidateLrc(binaryData))
                    return false;
                
                // 解析响应（不包括LRC）
                byte[] frameWithoutLrc = new byte[binaryData.Length - 1];
                Array.Copy(binaryData, frameWithoutLrc, binaryData.Length - 1);
                
                response.ParseResponse(frameWithoutLrc);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证响应帧的有效性
        /// </summary>
        /// <param name="responseFrame">响应帧字节数组</param>
        /// <returns>响应帧是否有效</returns>
        public bool ValidateResponseFrame(byte[] responseFrame)
        {
            if (responseFrame == null || responseFrame.Length < 9)
                return false;

            // 检查起始和结束字符
            string asciiFrame = Encoding.ASCII.GetString(responseFrame);
            
            if (!asciiFrame.StartsWith(StartCharacter.ToString()) || 
                !asciiFrame.EndsWith(EndCharacters))
                return false;
            
            // 检查长度是否为偶数（每个字节用两个ASCII字符表示）
            string dataAscii = asciiFrame.Substring(1, asciiFrame.Length - 3);
            if (dataAscii.Length % 2 != 0)
                return false;
            
            try
            {
                // 转换为字节数组
                byte[] binaryData = ModbusLrc.AsciiToBytes(dataAscii);
                
                // 验证LRC
                return ModbusLrc.ValidateLrc(binaryData);
            }
            catch
            {
                return false;
            }
        }
    }
}
