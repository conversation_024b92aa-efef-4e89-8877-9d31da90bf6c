using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Collections.Concurrent;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将十六进制颜色字符串转换为SolidColorBrush的转换器，支持颜色缓存
    /// </summary>
    public class HexColorToSolidColorBrushConverter : IValueConverter
    {
        // 使用ConcurrentDictionary作为线程安全的缓存容器
        private static readonly ConcurrentDictionary<string, SolidColorBrush> _colorCache = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string hexColor)
                return new SolidColorBrush(Colors.White);  // 默认返回白色

            // 处理空字符串或null
            if (string.IsNullOrWhiteSpace(hexColor))
                return new SolidColorBrush(Colors.White);

            // 从缓存中获取颜色，如果缓存中不存在则创建并添加到缓存
            return _colorCache.GetOrAdd(hexColor, hex =>
            {
                try
                {
                    // 解析颜色并创建SolidColorBrush
                    return new SolidColorBrush(Color.Parse(hex));
                }
                catch
                {
                    // 解析失败时返回白色
                    return new SolidColorBrush(Colors.White);
                }
            });
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 