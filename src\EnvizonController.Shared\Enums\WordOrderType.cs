﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Shared.Enums
{
    /// <summary>
    /// 字序类型（适用于32位及以上数据类型）
    /// </summary>
    public enum WordOrderType
    {
        /// <summary>
        /// 大端序 (ABCD): 高位字在前，高字节在前 (标准Modbus通常是这样理解的，但实际设备五花八门)。
        /// </summary>
        BigEndian,

        /// <summary>
        /// 小端序 (DCBA): 低位字在前，低字节在前。
        /// </summary>
        LittleEndian,

        /// <summary>
        /// 大端字序字节交换 (CDAB): 高位字在前，但字内字节交换 (低字节在前)。这是非常常见的一种非标但广泛使用的顺序。
        /// </summary>
        BigEndianWordSwap,

        /// <summary>
        /// 小端字序字节交换 (BADC): 低位字在前，但字内字节交换 (高字节在前)。
        /// </summary>
        LittleEndianWordSwap
    }
}
