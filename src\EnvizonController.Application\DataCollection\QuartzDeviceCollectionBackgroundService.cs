using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace EnvizonController.Application.DataCollection;

/// <summary>
/// Quartz设备数据采集后台服务
/// </summary>
public class QuartzDeviceCollectionBackgroundService(
    IQuartzDeviceCollectionService quartzDeviceCollectionService,
    ILogger<QuartzDeviceCollectionBackgroundService> logger)
    : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            logger.LogInformation("正在启动Quartz设备数据采集服务...");
            await quartzDeviceCollectionService.StartAsync(stoppingToken);
            logger.LogInformation("Quartz设备数据采集服务已启动");

            // 保持服务运行直到应用程序关闭
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 预期的取消，无需处理
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Quartz设备数据采集服务运行时出错：{Message}", ex.Message);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("正在停止Quartz设备数据采集服务...");
        await quartzDeviceCollectionService.StopAsync(cancellationToken);
        logger.LogInformation("Quartz设备数据采集服务已停止");

        await base.StopAsync(cancellationToken);
    }
} 