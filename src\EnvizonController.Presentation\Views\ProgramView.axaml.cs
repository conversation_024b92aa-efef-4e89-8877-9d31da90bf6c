using Avalonia.Controls;
using Avalonia.Interactivity;
using EnvizonController.Presentation.ViewModels;
using CommunityToolkit.Mvvm.DependencyInjection;

namespace EnvizonController.Presentation.Views
{
    public partial class ProgramView : UserControl
    {
        public ProgramView()
        {
            InitializeComponent();
        
        }
        
        /// <summary>
        /// 处理错误消息点击事件
        /// </summary>
        private void OnErrorMessageTapped(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProgramViewModel viewModel)
            {
                viewModel.ToggleErrorExpandCommand.Execute(null);
            }
        }
    }
}