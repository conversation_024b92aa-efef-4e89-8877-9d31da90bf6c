﻿using System.Collections.ObjectModel;
using System.Text.Json;
using Avalonia.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using EnvizonController.ApiClient.Services;
using EnvizonController.Presentation.Helpers;
using EnvizonController.Shared.DTOs;

namespace EnvizonController.Presentation.ViewModels.Dashboard;

/// <summary>
///     环境数据项，包含键和值
/// </summary>
public class EnvironmentalDataItem
{
    public string Key { get; set; } = string.Empty;
    public object Value { get; set; } = new();
}
/// <summary>
/// 通用数据项视图模型，包含单位、值、名称和Key
/// </summary>
public partial class GenericDataItemViewModel<T> : ObservableObject
{
    [ObservableProperty]
    private string _key = string.Empty;

    [ObservableProperty]
    private string _name = string.Empty;

    [ObservableProperty]
    private T? _value;

    [ObservableProperty]
    private string _unit = string.Empty;

    [ObservableProperty]
    private string _status = string.Empty;

    public GenericDataItemViewModel() { }

    public GenericDataItemViewModel(string key, string name, T? value, string unit)
    {
        _key = key;
        _name = name;
        _value = value;
        _unit = unit;
    }
}
/// <summary>
///     环境数据分组，用于界面展示
/// </summary>
public partial class EnvironmentalDataGroup : ViewModelBase
{
    [ObservableProperty] private string _iconColor = string.Empty;
    [ObservableProperty] private string _iconName = string.Empty;
    [ObservableProperty] private ObservableCollection<EnvironmentalDataItem> _items = new();
    [ObservableProperty] private string _key = string.Empty;
    [ObservableProperty] private string _output = string.Empty; // 如无实际用途可移除
    [ObservableProperty] private string _setValue = string.Empty;
    [ObservableProperty] private string _unit = string.Empty;
    [ObservableProperty] private string _value = string.Empty;
    [ObservableProperty] private string _valueName = string.Empty;
}

/// <summary>
///     设备项视图模型，处理环境数据与测试进度
/// </summary>
public partial class DeviceItemViewModel : ViewModelBase
{
    [ObservableProperty] private GenericDataItemViewModel<float> _exhaustTemperature;
    [ObservableProperty] private GenericDataItemViewModel<float> _returnTemperature;
    [ObservableProperty] private GenericDataItemViewModel<float> _returnPressure;
    [ObservableProperty] private GenericDataItemViewModel<float> _exhaustPressure;

    // 数据项KEY常量
    private const string ExhaustTemperatureKey = "排气温度";
    private const string ReturnTemperatureKey = "回气温度";
    private const string ReturnPressureKey = "回气压力";
    private const string ExhaustPressureKey = "排气压力";

    #region 当前运行参数
    [ObservableProperty] private GenericDataItemViewModel<float> _heatingOutput;
    [ObservableProperty] private GenericDataItemViewModel<float> _coolingOutput;
    [ObservableProperty] private GenericDataItemViewModel<float> _humidificationOutput;
    [ObservableProperty] private GenericDataItemViewModel<float> _dehumidificationOutput;

    // 添加对应的常量键值
    private const string HeatingOutputKey = "加热输出";
    private const string CoolingOutputKey = "制冷输出";
    private const string HumidificationOutputKey = "加湿输出";
    private const string DehumidificationOutputKey = "除湿输出";
    #endregion

    // 新增状态常量
    private const string StatusRunning = "运行中";
    private const string StatusStopped = "停止";

    // Json字典常量Key
    private const string ValueJsonKey = "value";
    private const string UnitJsonKey = "unit";
    private const string NameJsonKey = "name";

    // 环境数据分组Key
    private const string AmbientTemperatureGroupKey = "环境温度";
    private const string AmbientHumidityGroupKey = "环境湿度";

    // 输入Key匹配常量
    private const string AmbientTemperatureInputKeyPart = "环境温度";
    private const string AmbientHumidityInputKeyPart = "环境湿度";
    private const string SetTemperatureInputKeyPart = "设定温度";
    private const string SetHumidityInputKeyPart = "设定湿度";
    private const string TemperatureGenericPart = "温度";
    private const string HumidityGenericPart = "湿度";

    // 设备状态数据Key
    private const string CurrentProgramStepDataKey = "当前程式步骤";
    private const string TimedOperationHoursKey = "定时已运行_小时";
    private const string TimedOperationMinutesKey = "定时已运行_分钟";

    // 图标与颜色常量
    private const string TemperatureIcon = "\uf06d";
    private const string TemperatureColor = "#E62FF3";
    private const string HumidityIcon = "\uf043";
    private const string HumidityColor = "#5ADCFE";
    private const string DefaultIconColor = "#808080";

    [ObservableProperty] private DeviceCollectionDetailsReceivedDTO? _deviceDTO;
    [ObservableProperty] private List<EnvironmentalDataGroup> _environmentalDataGroupList = new();
    [ObservableProperty] private TestProgressViewModel? _testProgressViewModel;
    /// <summary>
    ///     报警信息列表
    /// </summary>
    [ObservableProperty] private ObservableCollection<AlarmItemViewModel> _alarms = new();
    [ObservableProperty] private ObservableCollection<DeviceLifetimeItem> _deviceLifetimeItems = new();
    private readonly SemaphoreSlim _initLock = new(1, 1);
    private readonly CancellationTokenSource _initCancellationSource = new();

    /// <summary>
    ///     初始化设备数据，如果已有正在执行的初始化操作，则新调用将直接返回
    /// </summary>
    public async Task InitAsync(DeviceCollectionDetailsReceivedDTO? detailsReceivedDTO = null)
    {

        if (_initLock.CurrentCount == 0)
        {
            return; // 直接返回，不等待
        }

        try
        {
            await _initLock.WaitAsync();
            DeviceDTO = detailsReceivedDTO;
            currenTestItemDTO = null;
            if (DeviceDTO == null)
            {
                EnvironmentalDataGroupList = new List<EnvironmentalDataGroup>();
                TestProgressViewModel?.Clear();
                return;
            }

            ProcessEnvironmentalData();
            ProcessPressureAndTemperatureData();

            // 使用取消令牌处理异步操作，使其可以被中断
            await ProcessTestProgressViewModel(_initCancellationSource.Token);

            await ProcessAlarms();
            await ProcessDeviceLifetime();
        }
        finally
        {
            _initLock.Release();
        }
    }
    /// <summary>
    /// 处理设备零件寿命数据
    /// </summary>
    private async Task ProcessDeviceLifetime()
    {
        // 清空现有的寿命数据集合，准备填充新数据
        DeviceLifetimeItems.Clear();

        if (DeviceDTO?.DeviceStateData == null)
        {
            // 如果没有设备数据或状态数据为空，则模拟一些寿命数据
            SimulateDeviceLifetimeData();
            return;
        }

        // 设备零件寿命数据前缀常量
        const string compressorLifetimeKey = "压缩机寿命";
        const string filterLifetimeKey = "过滤器寿命";
        const string fanLifetimeKey = "风扇寿命";
        const string pumpLifetimeKey = "水泵寿命";

        // 从设备状态数据中提取寿命数据
        foreach (var dataItem in DeviceDTO.DeviceStateData)
        {
            var key = dataItem.Key;
            var properties = dataItem.Value;

            // 检查是否是寿命相关数据
            if (key.Contains("寿命") || key.Contains("lifetime") || key.Contains("usage"))
            {
                var nameForDisplay = properties.GetStringValueOrDefault(NameJsonKey, key);
                var unit = properties.GetStringValueOrDefault(UnitJsonKey, "小时");

                // 尝试获取当前使用时间和最大寿命时间
                if (properties.TryGetDoubleValue(ValueJsonKey, out var currentUsage) &&
                    properties.TryGetDoubleValue("maxLifetime", out var maxLifetime) && maxLifetime > 0)
                {
                    // 计算剩余寿命百分比
                    var percentRemaining = Math.Max(0, Math.Min(100, (1 - currentUsage / maxLifetime) * 100));

                    // 创建并添加寿命数据项
                    DeviceLifetimeItems.Add(new DeviceLifetimeItem
                    {
                        Name = nameForDisplay,
                        CurrentUsage = currentUsage,
                        MaxLifetime = maxLifetime,
                        PercentRemaining = percentRemaining,
                        Unit = unit
                    });
                }
            }
        }

        // 如果没有找到任何寿命数据，则模拟一些数据
        if (DeviceLifetimeItems.Count == 0)
        {
            await Dispatcher.UIThread.InvokeAsync(() => { SimulateDeviceLifetimeData(); });
           
        }

    }

    /// <summary>
    /// 模拟设备零件寿命数据（用于测试或当实际数据不可用时）
    /// </summary>
    private void SimulateDeviceLifetimeData()
    {
        var random = new Random();

        // 模拟压缩机寿命数据
        DeviceLifetimeItems.Add(new DeviceLifetimeItem
        {
            Name = "压缩机",
            CurrentUsage = Math.Round(random.NextDouble() * 5000, 0),
            MaxLifetime = 10000,
            PercentRemaining = Math.Round(random.NextDouble() * 90 + 10, 1), // 10% - 100%
            Unit = "小时"
        });

        // 模拟过滤器寿命数据
        DeviceLifetimeItems.Add(new DeviceLifetimeItem
        {
            Name = "空气过滤器",
            CurrentUsage = Math.Round(random.NextDouble() * 900, 0),
            MaxLifetime = 1000,
            PercentRemaining = Math.Round(random.NextDouble() * 60 + 10, 1), // 10% - 70%
            Unit = "小时"
        });

        // 模拟风扇寿命数据
        DeviceLifetimeItems.Add(new DeviceLifetimeItem
        {
            Name = "冷却风扇",
            CurrentUsage = Math.Round(random.NextDouble() * 4000, 0),
            MaxLifetime = 8000,
            PercentRemaining = Math.Round(random.NextDouble() * 80 + 15, 1), // 15% - 95%
            Unit = "小时"
        });

        // 模拟控制板寿命数据
        DeviceLifetimeItems.Add(new DeviceLifetimeItem
        {
            Name = "控制电路",
            CurrentUsage = Math.Round(random.NextDouble() * 3000, 0),
            MaxLifetime = 15000,
            PercentRemaining = Math.Round(random.NextDouble() * 50 + 50, 1), // 50% - 100%
            Unit = "小时"
        });
    }

    /// <summary>
    /// 处理设备的报警信息
    /// </summary>
    /// <param name="deviceData">设备采集详情数据</param>
    private async Task ProcessAlarms()
    {
        if (DeviceDTO == null || DeviceDTO.Alarms == null || !DeviceDTO.Alarms.Any())
        {
            return;
        }

        // 根据AlarmDTO列表创建AlarmItemViewModel
        foreach (var alarmDto in DeviceDTO.Alarms)
        {
            // 检查是否已存在相同ID的报警
            var existingAlarm = Alarms.FirstOrDefault(a => a.Id == alarmDto.Id);
            if (existingAlarm != null)
            {
                // 更新已存在的报警
                existingAlarm.Status = alarmDto.Status;
                existingAlarm.ProcessedBy = alarmDto.ProcessedBy;
                existingAlarm.LastUpdated = alarmDto.LastUpdated;
                existingAlarm.ProcessedAt = alarmDto.ProcessedAt;
            }
            else
            {

                // 添加新报警
                await Dispatcher.UIThread.InvokeAsync(() => Alarms.Add(new AlarmItemViewModel(alarmDto)));
            }
        }

        // 保持报警信息不超过50条
        while (Alarms.Count > 50)
        {
            Alarms.RemoveAt(0);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 初始化GenericDataItemViewModel属性
    /// </summary>
    private void InitializeGenericDataItems()
    {
        ExhaustTemperature = new GenericDataItemViewModel<float>(ExhaustTemperatureKey, "排气温度", 0, "°C");
        ReturnTemperature = new GenericDataItemViewModel<float>(ReturnTemperatureKey, "回气温度", 0, "°C");
        ReturnPressure = new GenericDataItemViewModel<float>(ReturnPressureKey, "回气压力", 0, "Bar");
        ExhaustPressure = new GenericDataItemViewModel<float>(ExhaustPressureKey, "排气压力", 0, "Bar");
        //运行参数
        HeatingOutput = new GenericDataItemViewModel<float>(HeatingOutputKey, "加热输出", 0, "%");
        CoolingOutput = new GenericDataItemViewModel<float>(CoolingOutputKey, "制冷输出", 0, "%");
        HumidificationOutput = new GenericDataItemViewModel<float>(HumidificationOutputKey, "加湿输出", 0, "%");
        DehumidificationOutput = new GenericDataItemViewModel<float>(DehumidificationOutputKey, "除湿输出", 0, "%");
    }

    /// <summary>
    /// 更新输出项的状态
    /// </summary>
    private void UpdateOutputItemStatus(GenericDataItemViewModel<float> item)
    {
        if (item.Value != 0)
        {
            item.Status = StatusRunning;
        }
        else
        {
            item.Status = StatusStopped;
        }
    }

    /// <summary>
    /// 处理设备的压力和温度数据
    /// </summary>
    private void ProcessPressureAndTemperatureData()
    {
        // 确保之前已初始化过
        if (ExhaustTemperature == null || ReturnTemperature == null ||
            ReturnPressure == null || ExhaustPressure == null ||
            HeatingOutput == null || CoolingOutput == null ||
            HumidificationOutput == null || DehumidificationOutput == null)
        {
            InitializeGenericDataItems();
        }

        if (DeviceDTO?.EnvironmentalData == null) return;

        var dataItemsToProcess = new List<Tuple<GenericDataItemViewModel<float>, string>>
        {
            Tuple.Create(ExhaustTemperature, ExhaustTemperatureKey),
            Tuple.Create(ReturnTemperature, ReturnTemperatureKey),
            Tuple.Create(ReturnPressure, ReturnPressureKey),
            Tuple.Create(ExhaustPressure, ExhaustPressureKey),
            Tuple.Create(HeatingOutput, HeatingOutputKey),
            Tuple.Create(CoolingOutput, CoolingOutputKey),
            Tuple.Create(HumidificationOutput, HumidificationOutputKey),
            Tuple.Create(DehumidificationOutput, DehumidificationOutputKey)
        };

        foreach (var (item, key) in dataItemsToProcess)
        {
            if (DeviceDTO.EnvironmentalData.TryGetValue(key, out var data))
            {
                ProcessGenericDataItem(item, data);
            }
        }

        // 更新输出项的状态
        UpdateOutputItemStatus(HeatingOutput);
        UpdateOutputItemStatus(CoolingOutput);
        UpdateOutputItemStatus(HumidificationOutput);
        UpdateOutputItemStatus(DehumidificationOutput);

        // 如果没有真实数据，可以模拟一些值
        SimulateDataIfNeeded();
    }

    /// <summary>
    /// 处理单个GenericDataItem的数据
    /// </summary>
    private void ProcessGenericDataItem(GenericDataItemViewModel<float> item,
                                        IReadOnlyDictionary<string, JsonElement> properties)
    {
        if (properties.TryGetDoubleValue(ValueJsonKey, out var value))
        {
            item.Value = (float)Math.Round(value, 2);
        }

        // 更新单位
        var unit = properties.GetStringValueOrDefault(UnitJsonKey);
        if (!string.IsNullOrEmpty(unit))
        {
            item.Unit = unit;
        }

        // 更新名称
        var name = properties.GetStringValueOrDefault(NameJsonKey);
        if (!string.IsNullOrEmpty(name))
        {
            item.Name = name;
        }
    }

    /// <summary>
    /// 在没有实际数据时模拟一些数据值
    /// </summary>
    private void SimulateDataIfNeeded()
    {
        var random = new Random();

        // 仅当没有实际值时模拟数据
        if (ExhaustTemperature.Value == 0) ExhaustTemperature.Value = (float)Math.Round(random.NextDouble() * 50 + 60, 2); // 60-110°C
        if (ReturnTemperature.Value == 0) ReturnTemperature.Value = (float)Math.Round(random.NextDouble() * 30 + 20, 2); // 20-50°C
        if (ReturnPressure.Value == 0) ReturnPressure.Value = (float)Math.Round(random.NextDouble() * 0.5 + 0.1, 2); // 0.1-0.6 MPa
        if (ExhaustPressure.Value == 0) ExhaustPressure.Value = (float)Math.Round(random.NextDouble() * 1.5 + 0.5, 2); // 0.5-2.0 MPa

        // 模拟新增属性的数据
        if (HeatingOutput.Value == 0) HeatingOutput.Value = (float)Math.Round(random.NextDouble() * 100, 2); // 0-100%
        if (CoolingOutput.Value == 0) CoolingOutput.Value = (float)Math.Round(random.NextDouble() * 100, 2); // 0-100%
        if (HumidificationOutput.Value == 0) HumidificationOutput.Value = (float)Math.Round(random.NextDouble() * 100, 2); // 0-100%
        if (DehumidificationOutput.Value == 0) DehumidificationOutput.Value = (float)Math.Round(random.NextDouble() * 100, 2); // 0-100%

        // 更新模拟数据的状态
        UpdateOutputItemStatus(HeatingOutput);
        UpdateOutputItemStatus(CoolingOutput);
        UpdateOutputItemStatus(HumidificationOutput);
        UpdateOutputItemStatus(DehumidificationOutput);
    }

    private TestRunDTO? currenTestItemDTO = null;
    /// <summary>
    ///     处理测试进度视图模型
    /// </summary>
    private async Task ProcessTestProgressViewModel(CancellationToken cancellationToken = default)
    {
        TestProgressViewModel ??= new TestProgressViewModel();

        if (DeviceDTO?.TestId.HasValue == true || currenTestItemDTO == null)
        {
            var testItemApiService = Ioc.Default.GetRequiredService<ITestItemApiService>();

            // 添加取消令牌支持，确保可以取消网络请求
            try
            {
                var result = await testItemApiService.GetTestItemAsync(DeviceDTO.TestId.Value)
                    .WaitAsync(cancellationToken);

                var currentStepIndex = GetCurrentProgramStepIndex(DeviceDTO.DeviceStateData);
                var durationSeconds = GetDurationSecondsFromDeviceStateData(DeviceDTO.DeviceStateData);
                if(currenTestItemDTO == null)
                {
                    currenTestItemDTO = result.Data;
                    TestProgressViewModel.Update(currenTestItemDTO, currentStepIndex, durationSeconds);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，可以记录日志或执行其他操作
                return;
            }
        }
        else
        {
            TestProgressViewModel.Clear();
        }
    }

    /// <summary>
    ///     获取当前程式步骤索引
    /// </summary>
    private int GetCurrentProgramStepIndex(
        IReadOnlyDictionary<string, Dictionary<string, JsonElement>>? deviceStateData)
    {
        if (deviceStateData != null &&
            deviceStateData.TryGetValue(CurrentProgramStepDataKey, out var stepDataProperties))
            return stepDataProperties.GetInt32ValueOrDefault(ValueJsonKey, -1);
        return -1;
    }

    /// <summary>
    ///     获取定时运行总秒数
    /// </summary>
    private int GetDurationSecondsFromDeviceStateData(
        IReadOnlyDictionary<string, Dictionary<string, JsonElement>>? deviceStateData)
    {
        if (deviceStateData == null) return 0;

        var hours = 0;
        if (deviceStateData.TryGetValue(TimedOperationHoursKey, out var hourDict))
            hours = hourDict.GetInt32ValueOrDefault(ValueJsonKey);

        var minutes = 0;
        if (deviceStateData.TryGetValue(TimedOperationMinutesKey, out var minuteDict))
            minutes = minuteDict.GetInt32ValueOrDefault(ValueJsonKey);
        return hours * 3600 + minutes * 60;
    }

    /// <summary>
    ///     处理环境数据分组与填充
    /// </summary>
    private void ProcessEnvironmentalData()
    {
        if (DeviceDTO == null) return;

        var newEnvironmentalDataGroups = new List<EnvironmentalDataGroup>();

        foreach (var environmentalDataPair in DeviceDTO.EnvironmentalData)
        {
            var originalInputKey = environmentalDataPair.Key;
            var properties = environmentalDataPair.Value;

            var group = GetOrCreateGroup(originalInputKey, properties, newEnvironmentalDataGroups);

            // 填充分组的主要属性
            if (originalInputKey.Contains(AmbientTemperatureInputKeyPart))
            {
                if (properties.TryGetDoubleValue(ValueJsonKey, out var tempValue))
                    group.Value = Math.Round(tempValue, 2).ToString();
                group.Unit = properties.GetStringValueOrDefault(UnitJsonKey);
            }
            else if (originalInputKey.Contains(SetTemperatureInputKeyPart))
            {
                var setValue = properties.GetStringValueOrDefault(ValueJsonKey);
                var unit = properties.GetStringValueOrDefault(UnitJsonKey);
                group.SetValue = $"{setValue}{unit}";
            }
            else if (originalInputKey.Contains(AmbientHumidityInputKeyPart))
            {
                if (properties.TryGetDoubleValue(ValueJsonKey, out var humValue))
                    group.Value = Math.Round(humValue, 2).ToString();
                group.Unit = properties.GetStringValueOrDefault(UnitJsonKey);
            }
            else if (originalInputKey.Contains(SetHumidityInputKeyPart))
            {
                var setValue = properties.GetStringValueOrDefault(ValueJsonKey);
                var unit = properties.GetStringValueOrDefault(UnitJsonKey);
                group.SetValue = $"{setValue}{unit}";
            }

            // 添加所有原始属性到Items集合，便于详细展示
            foreach (var itemProperty in properties)
                group.Items.Add(new EnvironmentalDataItem
                {
                    Key = itemProperty.Key,
                    Value = itemProperty.Value.ValueKind switch
                    {
                        JsonValueKind.String => itemProperty.Value.GetString() ?? string.Empty,
                        JsonValueKind.Number => itemProperty.Value.GetRawText(),
                        JsonValueKind.True => "true",
                        JsonValueKind.False => "false",
                        JsonValueKind.Null => "null",
                        _ => itemProperty.Value.ToString()
                    }
                });
        }

        EnvironmentalDataGroupList = newEnvironmentalDataGroups;
    }

    /// <summary>
    ///     获取或创建环境数据分组
    /// </summary>
    private EnvironmentalDataGroup GetOrCreateGroup(
        string originalInputKey,
        IReadOnlyDictionary<string, JsonElement> properties,
        List<EnvironmentalDataGroup> currentGroups)
    {
        string groupKeyToUse;
        string groupDisplayName;
        var iconName = string.Empty;
        var iconColor = DefaultIconColor;

        var isTemperatureRelated = originalInputKey.Contains(TemperatureGenericPart);
        var isHumidityRelated = originalInputKey.Contains(HumidityGenericPart);

        if (isTemperatureRelated)
        {
            groupKeyToUse = AmbientTemperatureGroupKey;
            groupDisplayName = AmbientTemperatureGroupKey;
            iconName = TemperatureIcon;
            iconColor = TemperatureColor;
        }
        else if (isHumidityRelated)
        {
            groupKeyToUse = AmbientHumidityGroupKey;
            groupDisplayName = AmbientHumidityGroupKey;
            iconName = HumidityIcon;
            iconColor = HumidityColor;
        }
        else
        {
            groupKeyToUse = originalInputKey;
            groupDisplayName = properties.GetStringValueOrDefault(NameJsonKey, originalInputKey);
        }

        var existingGroup = currentGroups.FirstOrDefault(g => g.Key == groupKeyToUse);
        if (existingGroup != null) return existingGroup;

        var newGroup = new EnvironmentalDataGroup
        {
            Key = groupKeyToUse,
            ValueName = groupDisplayName,
            IconName = iconName,
            IconColor = iconColor
        };
        currentGroups.Add(newGroup);
        return newGroup;
    }
}