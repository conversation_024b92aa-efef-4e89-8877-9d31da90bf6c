using EnvizonController.Modbus.Protocol.Enums;
using EnvizonController.Modbus.Protocol.Frames;
using EnvizonController.Modbus.Protocol.Models;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Frames
{
    public class ModbusTcpFrameBuilderTests
    {
        private readonly ModbusTcpFrameBuilder _frameBuilder;
        
        public ModbusTcpFrameBuilderTests()
        {
            _frameBuilder = new ModbusTcpFrameBuilder();
        }
        
        [Fact]
        public void BuildRequestFrame_WithReadHoldingRegistersRequest_ReturnsCorrectFrame()
        {
            // Arrange
            var request = new ReadHoldingRegistersRequest(1,0,10);
            
            // Act
            byte[] frame = _frameBuilder.BuildRequestFrame(request);
            
            // Assert
            Assert.Equal(12, frame.Length);
            
            // MBAP头
            // 事务标识符（2字节）- 可能会变化，所以不检查具体值
            Assert.Equal(0x00, frame[2]); // 协议标识符高字节
            Assert.Equal(0x00, frame[3]); // 协议标识符低字节
            Assert.Equal(0x00, frame[4]); // 长度高字节
            Assert.Equal(0x06, frame[5]); // 长度低字节
            Assert.Equal(0x01, frame[6]); // 单元标识符（从站地址）
            
            // PDU
            Assert.Equal((byte)ModbusFunction.ReadHoldingRegisters, frame[7]); // 功能码
            Assert.Equal(0x00, frame[8]); // 起始地址高字节
            Assert.Equal(0x00, frame[9]); // 起始地址低字节
            Assert.Equal(0x00, frame[10]); // 寄存器数量高字节
            Assert.Equal(0x0A, frame[11]); // 寄存器数量低字节
        }
        
        [Fact]
        public void ParseResponseFrame_WithValidReadHoldingRegistersResponse_ReturnsTrue()
        {
            // Arrange
            // MBAP头
            byte[] mbapHeader = new byte[] { 
                0x00, 0x01, // 事务标识符
                0x00, 0x00, // 协议标识符
                0x00, 0x17, // 长度（23字节）
                0x01 // 单元标识符
            };
            
            // PDU（不包括单元标识符）
            byte[] pdu = new byte[] { 
                0x03, // 功能码
                0x14, // 字节数
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A 
            };
            
            // 组合MBAP头和PDU
            byte[] responseFrame = new byte[mbapHeader.Length + pdu.Length];
            Array.Copy(mbapHeader, 0, responseFrame, 0, mbapHeader.Length);
            Array.Copy(pdu, 0, responseFrame, mbapHeader.Length, pdu.Length);
            
            var response = new ReadHoldingRegistersResponse();
            
            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);
            
            // Assert
            Assert.True(result);
            Assert.Equal(1, response.SlaveAddress);
            Assert.Equal(ModbusFunction.ReadHoldingRegisters, response.FunctionCode);
            Assert.Equal(10, response.RegisterValues.Length);
            
            for (int i = 0; i < 10; i++)
            {
                Assert.Equal((ushort)(i + 1), response.RegisterValues[i]);
            }
        }
        
        [Fact]
        public void ParseResponseFrame_WithInvalidProtocolId_ReturnsFalse()
        {
            // Arrange
            // MBAP头（无效的协议标识符）
            byte[] mbapHeader = new byte[] { 
                0x00, 0x01, // 事务标识符
                0x01, 0x00, // 无效的协议标识符（应为0x0000）
                0x00, 0x17, // 长度（23字节）
                0x01 // 单元标识符
            };
            
            // PDU（不包括单元标识符）
            byte[] pdu = new byte[] { 
                0x03, // 功能码
                0x14, // 字节数
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A 
            };
            
            // 组合MBAP头和PDU
            byte[] responseFrame = new byte[mbapHeader.Length + pdu.Length];
            Array.Copy(mbapHeader, 0, responseFrame, 0, mbapHeader.Length);
            Array.Copy(pdu, 0, responseFrame, mbapHeader.Length, pdu.Length);
            
            var response = new ReadHoldingRegistersResponse();
            
            // Act
            bool result = _frameBuilder.ParseResponseFrame(responseFrame, response);
            
            // Assert
            Assert.False(result);
        }
        
        [Fact]
        public void ValidateResponseFrame_WithValidFrame_ReturnsTrue()
        {
            // Arrange
            // MBAP头
            byte[] mbapHeader = new byte[] { 
                0x00, 0x01, // 事务标识符
                0x00, 0x00, // 协议标识符
                0x00, 0x17, // 长度（23字节）
                0x01 // 单元标识符
            };
            
            // PDU（不包括单元标识符）
            byte[] pdu = new byte[] { 
                0x03, // 功能码
                0x14, // 字节数
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A 
            };
            
            // 组合MBAP头和PDU
            byte[] responseFrame = new byte[mbapHeader.Length + pdu.Length];
            Array.Copy(mbapHeader, 0, responseFrame, 0, mbapHeader.Length);
            Array.Copy(pdu, 0, responseFrame, mbapHeader.Length, pdu.Length);
            
            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);
            
            // Assert
            Assert.True(result);
        }
        
        [Fact]
        public void ValidateResponseFrame_WithInvalidFrame_ReturnsFalse()
        {
            // Arrange
            // MBAP头（无效的协议标识符）
            byte[] mbapHeader = new byte[] { 
                0x00, 0x01, // 事务标识符
                0x01, 0x00, // 无效的协议标识符（应为0x0000）
                0x00, 0x17, // 长度（23字节）
                0x01 // 单元标识符
            };
            
            // PDU（不包括单元标识符）
            byte[] pdu = new byte[] { 
                0x03, // 功能码
                0x14, // 字节数
                0x00, 0x01, 0x00, 0x02, 0x00, 0x03, 0x00, 0x04, 0x00, 0x05, 
                0x00, 0x06, 0x00, 0x07, 0x00, 0x08, 0x00, 0x09, 0x00, 0x0A 
            };
            
            // 组合MBAP头和PDU
            byte[] responseFrame = new byte[mbapHeader.Length + pdu.Length];
            Array.Copy(mbapHeader, 0, responseFrame, 0, mbapHeader.Length);
            Array.Copy(pdu, 0, responseFrame, mbapHeader.Length, pdu.Length);
            
            // Act
            bool result = _frameBuilder.ValidateResponseFrame(responseFrame);
            
            // Assert
            Assert.False(result);
        }
    }
}
