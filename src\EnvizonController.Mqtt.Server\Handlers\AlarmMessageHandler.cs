using EnvizonController.Mqtt.Server.Models;
using EnvizonController.Mqtt.Server.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EnvizonController.Mqtt.Server.Handlers
{
    public class AlarmMessageHandler : IMessageHandler
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<AlarmMessageHandler> _logger;

        public AlarmMessageHandler(INotificationService notificationService, ILogger<AlarmMessageHandler> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        public bool CanHandle(string topic)
        {
            return topic.StartsWith("alarms/");
        }

        public void HandleMessage(string topic, string message, string clientId)
        {
            try
            {
                var alarm = JsonSerializer.Deserialize<AlarmNotification>(message);
                if (alarm == null)
                {
                    _logger.LogWarning($"无法解析告警消息: {message}");
                    return;
                }

                _logger.LogInformation($"处理告警消息: {alarm.Message}");
                
                // 发布通知
                _notificationService.Publish(alarm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理告警消息时发生错误");
            }
        }
    }
} 