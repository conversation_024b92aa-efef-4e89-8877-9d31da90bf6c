using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Common;

namespace EnvizonController.Domain.Repositories
{
    /// <summary>
    /// 程式表仓储接口
    /// </summary>
    public interface IProgramRepository : IRepository<Program, long>
    {
        /// <summary>
        /// 根据名称获取程式表
        /// </summary>
        /// <param name="name">程式表名称</param>
        /// <returns>程式表，如果未找到则返回null</returns>
        Task<Program?> GetByNameAsync(string name);

        /// <summary>
        /// 获取所有程式表及其步骤
        /// </summary>
        /// <returns>包含步骤的程式表集合</returns>
        Task<IEnumerable<Program>> GetAllWithStepsAsync();

        /// <summary>
        /// 根据ID获取程式表及其步骤
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>包含步骤的程式表，如果未找到则返回null</returns>
        Task<Program?> GetByIdWithStepsAsync(long id);

        /// <summary>
        /// 根据ID集合获取多个程式
        /// </summary>
        /// <param name="ids">程式ID集合</param>
        /// <returns>程式集合</returns>
        Task<IEnumerable<Program>> GetProgramsByIdsAsync(IEnumerable<long> ids);
    }
}