using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为选中索引/非选中索引状态的背景色
    /// </summary>
    public class BoolToSelectedIndexBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected 
                    ? new SolidColorBrush(Color.Parse("#13293D")) // 选中索引的背景色（深蓝色，比普通选中略深）
                    : new SolidColorBrush(Color.Parse("#0A0A14")); // 非选中索引的背景色（深灰色）
            }

            return new SolidColorBrush(Color.Parse("#0A0A14"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}