using EnvizonController.Domain.Aggregates;

namespace EnvizonController.Domain.Services
{
    /// <summary>
    /// 程式表领域服务接口
    /// 提供与程式表聚合相关的领域逻辑
    /// </summary>
    public interface IProgramDomainService
    {
        /// <summary>
        /// 验证程式名称是否有效
        /// </summary>
        /// <param name="name">程式名称</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        Task<bool> ValidateProgramNameAsync(string name);

        /// <summary>
        /// 验证程式循环参数是否有效
        /// </summary>
        /// <param name="cycleStart">循环开始步骤</param>
        /// <param name="cycleEnd">循环结束步骤</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        Task<bool> ValidateProgramCycleAsync(int cycleStart, int cycleEnd);

        /// <summary>
        /// 验证程式步骤参数是否有效
        /// </summary>
        /// <param name="step">程式步骤</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        Task<bool> ValidateProgramStepAsync(ProgramStep step);

        /// <summary>
        /// 计算步骤总持续时间
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>总持续时间（秒）</returns>
        int CalculateTotalDuration(IEnumerable<ProgramStep> steps);

        /// <summary>
        /// 按索引排序步骤
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>排序后的步骤集合</returns>
        IEnumerable<ProgramStep> OrderStepsByIndex(IEnumerable<ProgramStep> steps);

        /// <summary>
        /// 验证程式步骤顺序的完整性
        /// </summary>
        /// <param name="steps">程式步骤集合</param>
        /// <returns>验证结果，true表示有效，false表示无效</returns>
        bool ValidateProgramStepsOrder(IEnumerable<ProgramStep> steps);
    }
}