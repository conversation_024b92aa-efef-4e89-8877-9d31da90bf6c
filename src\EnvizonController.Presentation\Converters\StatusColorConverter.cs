using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace EnvizonController.Presentation.Converters
{
    public class StatusColorConverter : IValueConverter
    {

        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status.ToLowerInvariant() switch
                {
                    "在线" => new SolidColorBrush(Color.Parse("#4CAF50")),
                    "离线" => new SolidColorBrush(Color.Parse("#FF4556")),
                    _ => new SolidColorBrush(Color.Parse("#9DA3AF"))
                };
            }
            
            return new SolidColorBrush(Color.Parse("#9DA3AF"));
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 