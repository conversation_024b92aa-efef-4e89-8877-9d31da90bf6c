<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQueryView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dataQuery="clr-namespace:EnvizonController.Presentation.Views.DataQuery"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:presentation="clr-namespace:EnvizonController.Presentation"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="800"
    d:DesignWidth="1200"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="FlyoutPresenter.wider">
            <Setter Property="MaxWidth" Value="850" />
        </Style>
        <!--  选项卡样式  -->
        <Style Selector="TabItem">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="Height" Value="40" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="Margin" Value="0" />
            <Setter Property="Padding" Value="20,0" />
            <Setter Property="BorderThickness" Value="1,1,1,0" />
            <Setter Property="BorderBrush" Value="#224F5F" />
        </Style>
        <Style Selector="TabItem:selected">
            <Setter Property="Background" Value="#0B0A1A" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
        </Style>
        <Style Selector="TabControl WrapPanel">
            <Setter Property="Background" Value="Transparent" />
        </Style>
    </UserControl.Styles>
    <Grid Background="{DynamicResource SurfaceBrush}" ColumnDefinitions="320,*">
        <!--  左侧栏 - 测试项选择与筛选区  -->
        <dataQuery:TestItemSelectionView Grid.Column="0" DataContext="{Binding}" />

        <Grid Grid.Column="1" RowDefinitions="Auto,*">
            <!--  顶部标题和搜索区  -->
            <dataQuery:TopToolbarView Grid.Row="0" DataContext="{Binding}" />

            <!--  主内容区 - 使用TabControl  -->
            <TabControl
                Grid.Row="1"
                Margin="12"
                Padding="0"
                SelectedIndex="{Binding SelectedViewIndex}">
                <!--  数据图表视图  -->
                <TabItem Header="数据图表">
                    <Border
                        Margin="0"
                        Padding="0"
                        Classes="cyber-border">
                        <dataQuery:DataChartView DataContext="{Binding}" />
                    </Border>
                </TabItem>

                <!--  数据表格视图  -->
                <TabItem Header="数据表格">
                    <Border
                        Margin="0"
                        Padding="0"
                        Classes="cyber-border">
                        <dataQuery:DataTableView DataContext="{Binding}" />
                    </Border>
                </TabItem>

                <!--  测试步骤视图  -->
                <TabItem Header="测试步骤">
                    <Border
                        Margin="0"
                        Padding="0"
                        Classes="cyber-border">
                        <dataQuery:TestStepsView DataContext="{Binding}" />
                    </Border>
                </TabItem>
            </TabControl>
        </Grid>
        <presentation:LoadingControl
            Grid.Column="0"
            Grid.ColumnSpan="2"
            IsVisible="{Binding IsLoading}" />
    </Grid>
</UserControl> 