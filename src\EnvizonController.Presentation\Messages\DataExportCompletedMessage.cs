﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 数据导出完成消息
/// </summary>
public class DataExportCompletedMessage : ValueChangedMessage<(string FilePath, bool IsSuccess, string ErrorMessage)>
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath => Value.FilePath;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess => Value.IsSuccess;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage => Value.ErrorMessage;

    /// <summary>
    /// 创建数据导出完成消息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="errorMessage">错误消息</param>
    public DataExportCompletedMessage(string filePath, bool isSuccess, string errorMessage = "") 
        : base((filePath, isSuccess, errorMessage))
    {
    }
}
