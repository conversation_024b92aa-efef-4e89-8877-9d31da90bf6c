using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "完成" => new SolidColorBrush(Color.Parse("#0DF0FF")),
                    "运行中" => new SolidColorBrush(Color.Parse("#5FE872")),
                    "失败" => new SolidColorBrush(Color.Parse("#FF4D6F")),
                    "暂停" => new SolidColorBrush(Color.Parse("#FFAA33")),
                    _ => new SolidColorBrush(Color.Parse("#9DA3AF"))
                };
            }
            
            return new SolidColorBrush(Color.Parse("#9DA3AF"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 