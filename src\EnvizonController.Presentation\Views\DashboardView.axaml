<UserControl
    x:Class="EnvizonController.Presentation.Views.DashboardView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helpers="clr-namespace:EnvizonController.Presentation.Helpers"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    xmlns:dashboard="clr-namespace:EnvizonController.Presentation.Views.Dashboard"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="Button">
            <Setter Property="Background" Value="#1A2A3A" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="Padding" Value="10,5" />
        </Style>
        <Style Selector="ProgressBar">
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="Background" Value="#1A2A3A" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Height" Value="6" />
        </Style>
        <Style Selector="TextBlock">
            <Setter Property="Foreground" Value="#FFFFFF" />
        </Style>
        <Style Selector="TextBlock.header">
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,10" />
        </Style>
        <Style Selector="TextBlock.Second">
            <Setter Property="Foreground" Value="#9DA3AF" />
        </Style>
        <Style Selector="TextBlock.Third">
            <Setter Property="Foreground" Value="#6B7280" />
        </Style>
        <Style Selector="TextBlock.icon">
            <Setter Property="FontFamily" Value="{DynamicResource FontAwesomeSolid}" />
            <!--  可以覆盖基础样式属性  -->
        </Style>
        <Style Selector="TextBlock.value">
            <Setter Property="Foreground" Value="#FFFFFF" />
            <Setter Property="FontSize" Value="39" />
            <Setter Property="FontWeight" Value="Bold" />
        </Style>
        <Style Selector="TextBlock.rotate">
            <Setter Property="RenderTransform">
                <RotateTransform Angle="0" />
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="50%,50%" />
            <Style.Animations>
                <Animation
                    FillMode="None"
                    IterationCount="INFINITE"
                    PlaybackDirection="Normal"
                    Duration="0:0:3">
                    <KeyFrame Cue="0">
                        <Setter Property="RotateTransform.Angle" Value="0" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="RotateTransform.Angle" Value="360" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </UserControl.Styles>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  左侧设备列表切换按钮  -->
        <Button
            Name="DeviceListToggleButton"
            Grid.Row="0"
            Margin="8,120,0,0"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            BorderThickness="1"
            Classes="glow"
            Click="ToggleDeviceListPanel"
            IsVisible="{Binding !#DeviceListPanel.IsVisible}"
            ZIndex="2">
            <Grid>
                <TextBlock
                    Name="DeviceListExpandCollapseIcon"
                    HorizontalAlignment="Center"
                    Classes="font-icon"
                    FontSize="14"
                    Foreground="{Binding $parent[Button].Foreground}"
                    Text="&#xf054;" />
            </Grid>
        </Button>

        <!--  左侧设备列表面板  -->
        <Border
            Name="DeviceListPanel"
            Grid.Row="0"
            Grid.RowSpan="2"
            Width="200"
            Margin="5,5,0,5"
            HorizontalAlignment="Left"
            VerticalAlignment="Stretch"
            Background="#1A2A3A"
            BorderBrush="#0DF0FF"
            BorderThickness="0,0,1,0"
            IsVisible="False"
            Opacity="0"
            ZIndex="1">
            <Grid RowDefinitions="Auto,*">
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBlock
                        Grid.Column="0"
                        Margin="10,10,0,5"
                        Classes="header"
                        Text="设备列表" />

                    <Button
                        Grid.Column="1"
                        Margin="0,10,10,5"
                        Padding="5"
                        Command="{Binding RefreshDevicesCommand}"
                        ToolTip.Tip="刷新设备列表">
                        <TextBlock
                            Classes="font-icon"
                            FontFamily="{DynamicResource FontAwesomeSolid}"
                            Text="&#xf2f1;" />
                    </Button>
                </Grid>

                <ScrollViewer Grid.Row="1" Margin="5,0,5,5">
                    <ItemsControl ItemsSource="{Binding ApiDevices}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button
                                    Margin="0,2"
                                    Padding="10,5"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Classes.selected="{Binding IsSelected, FallbackValue=False}"
                                    Click="SelectDevice"
                                    Tag="{Binding}">
                                    <Button.Styles>
                                        <Style Selector="Button">
                                            <Setter Property="Background" Value="#1A2A3A" />
                                            <Setter Property="Foreground" Value="White" />
                                            <Setter Property="BorderBrush" Value="#2A3A4A" />
                                            <Setter Property="BorderThickness" Value="1" />
                                            <Setter Property="Transitions">
                                                <Transitions>
                                                    <BrushTransition Property="Background" Duration="0:0:0.2" />
                                                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                                                    <BrushTransition Property="Foreground" Duration="0:0:0.2" />
                                                </Transitions>
                                            </Setter>
                                        </Style>
                                        <Style Selector="Button:pointerover">
                                            <Setter Property="Background" Value="#2A3A4A" />
                                            <Setter Property="BorderBrush" Value="#0DF0FF" />
                                            <Setter Property="Cursor" Value="Hand" />
                                        </Style>
                                        <Style Selector="Button.selected">
                                            <Setter Property="Background" Value="#2A3A4A" />
                                            <Setter Property="BorderBrush" Value="#0DF0FF" />
                                            <Setter Property="BorderThickness" Value="2" />
                                            <Setter Property="Foreground" Value="#0DF0FF" />
                                            <Setter Property="FontWeight" Value="Bold" />
                                        </Style>
                                    </Button.Styles>
                                    <Grid ColumnDefinitions="Auto,*">
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="0,0,10,0"
                                            Classes="icon"
                                            Foreground="#0DF0FF"
                                            IsVisible="{Binding IsSelected}"
                                            Text="&#xf00c;" />
                                        <TextBlock Grid.Column="1" Text="{Binding Name}" />
                                    </Grid>
                                </Button>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>

        <!--  设备列表展开后的收起按钮  -->
        <Button
            Name="DeviceListCollapseButton"
            Grid.Row="0"
            Margin="210,120,0,0"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            BorderThickness="1"
            Classes="glow"
            Click="ToggleDeviceListPanel"
            IsVisible="{Binding #DeviceListPanel.IsVisible}"
            ZIndex="2">
            <Grid>
                <TextBlock
                    HorizontalAlignment="Center"
                    Classes="font-icon"
                    FontFamily="{DynamicResource FontAwesomeSolid}"
                    FontSize="14"
                    Foreground="{Binding $parent[Button].Foreground}"
                    Text="&#xf053;" />
            </Grid>
        </Button>

        <!--  主要内容区域  -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="0.6*" />
                <ColumnDefinition Width="0.7*" />
            </Grid.ColumnDefinitions>

            <!--  左侧列 - 环境监测  -->
            <Grid Grid.Column="0" RowDefinitions="*,*">
                <!--  环境监测面板  -->
                <dashboard:EnvironmentMonitorControl Grid.Row="0" />

                <!--  环境曲线  -->
                <dashboard:EnvironmentChartControl Grid.Row="1" />
            </Grid>

            <!--  中间列  -->
            <Grid
                Grid.Column="1"
                Margin="10,0"
                RowDefinitions="*,*">
                <!--  测试进度  -->
                <dashboard:TestProgressControl Grid.Row="0" DataContext="{Binding CurrentDevice.TestProgressViewModel}" />

                <!--  系统状态  -->
                <dashboard:ParameterControlControl Grid.Row="1" Margin="0,10,0,0" />
            </Grid>

            <!--  右侧列  -->
            <Grid Grid.Column="2" RowDefinitions="*,Auto,Auto">
                <!--  右侧面板 - 使用Grid嵌套并增加行定义  -->
                <!--
                <Grid Grid.Row="0" RowDefinitions="*,*">
                -->
                <!--  报警信息  -->
                <!--
                    <controls:AlarmInfoControl Grid.Row="0" />

                -->
                <!--  API设备列表  -->
                <!--
                    <controls:ApiDevicesControl Grid.Row="1" Margin="0,10,0,0" />
                </Grid>-->
                <!--  报警信息  -->
                <dashboard:AlarmInfoControl Grid.Row="0" />

                <!--  当前运行参数  -->
                <dashboard:CurrentRunningParamsControl Grid.Row="1" Margin="0,12,0,0" />

                <!--  设备状态  -->
                <dashboard:DeviceLifetimeControl Grid.Row="2" Margin="9,9,9,0" />
            </Grid>
        </Grid>
        <!--  切换按钮  -->
        <Button
            Name="ToggleButton"
            Grid.Row="0"
            Grid.RowSpan="2"
            Margin="0,0,0,14"
            HorizontalAlignment="Center"
            VerticalAlignment="Bottom"
            BorderThickness="1"
            Classes="glow"
            Click="ToggleControlPanel"
            ZIndex="2">
            <Grid>
                <TextBlock
                    Name="ExpandCollapseIcon"
                    HorizontalAlignment="Center"
                    Classes="font-icon"
                    FontSize="14"
                    Foreground="{Binding $parent[Button].Foreground}"
                    Text="&#xf078;" />
            </Grid>
        </Button>

        <!--  底部控制面板  -->
        <Grid
            Name="ControlPanel"
            Grid.Row="0"
            Grid.RowSpan="2"
            Margin="13,5"
            VerticalAlignment="Bottom">
            <dashboard:ControlPanelControl />
        </Grid>
    </Grid>
</UserControl>
