﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
    One for Windows with net9.0-windows TFM, one for MacOS with net9.0-macos and one with net9.0 TFM for Linux.-->
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
  </PropertyGroup>

  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia.Desktop" Version="$(AvaloniaVersion)" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="$(AvaloniaVersion)" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\..\EnvizonController\EnvizonController.csproj" />
  </ItemGroup>

	<PropertyGroup>
		<!-- 可选：如果你想显式禁用 HotAvalonia 的自动启用特性，添加此行。
       如果你依赖 .UseHotReload() 手动启用，建议添加此设置。 -->
		<HotAvaloniaAutoEnable>false</HotAvaloniaAutoEnable>
	</PropertyGroup>

	<ItemGroup>
		<!-- 添加 HotAvalonia NuGet 包引用 -->
		<!-- PrivateAssets="All" 确保此依赖不会传递给引用此项目的其他项目 -->
		<PackageReference Include="HotAvalonia" Version="3.0.0" PrivateAssets="All" />
		<!-- 保留你项目原有的其他 PackageReference -->
	</ItemGroup>
</Project>
