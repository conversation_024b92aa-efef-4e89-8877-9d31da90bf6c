using EnvizonController.Domain.Enums;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.Interfaces
{
    /// <summary>
    /// 报警应用服务接口
    /// 处理报警相关的应用层逻辑，包括DTO转换
    /// </summary>
    public interface IAlarmAppService
    {
        /// <summary>
        /// 创建新的报警
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <param name="name">报警名称</param>
        /// <param name="message">报警消息</param>
        /// <param name="level">报警级别</param>
        /// <returns>创建的报警DTO，如果测试不在运行中则返回null</returns>
        Task<AlarmDTO?> CreateAlarmAsync(long testRunId, string name, string message, AlarmSeverity level = AlarmSeverity.High);

        /// <summary>
        /// 处理报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="processedBy">处理人</param>
        /// <returns>更新后的报警DTO</returns>
        Task<AlarmDTO> ProcessAlarmAsync(long alarmId, string processedBy);

        /// <summary>
        /// 获取特定测试运行的所有报警
        /// </summary>
        /// <param name="testRunId">测试运行ID</param>
        /// <returns>报警DTO列表</returns>
        Task<IEnumerable<AlarmDTO>> GetAlarmsByTestRunAsync(long testRunId);

        /// <summary>
        /// 获取所有活动状态的报警
        /// </summary>
        /// <returns>活动报警DTO列表</returns>
        Task<IEnumerable<AlarmDTO>> GetActiveAlarmsAsync();

        /// <summary>
        /// 获取特定级别的报警
        /// </summary>
        /// <param name="level">报警级别</param>
        /// <returns>特定级别的报警DTO列表</returns>
        Task<IEnumerable<AlarmDTO>> GetAlarmsByLevelAsync(AlarmSeverity level);

        /// <summary>
        /// 获取报警列表（支持多参数查询和分页）
        /// </summary>
        /// <param name="queryParams">查询参数，包括分页、状态、级别、日期范围等</param>
        /// <returns>分页的报警DTO列表</returns>
        Task<PagedResultDto<AlarmDTO>> GetAlarmsAsync(AlarmQueryParams queryParams);
    }
}
