using System;

namespace EnvizonController.Configuration.Enums
{
    /// <summary>
    /// 指定串口通信的停止位的值
    /// </summary>
    public enum StopBits
    {
        /// <summary>
        /// 不使用停止位
        /// </summary>
        None = 0,

        /// <summary>
        /// 使用一个停止位
        /// </summary>
        One = 1,

        /// <summary>
        /// 使用两个停止位
        /// </summary>
        Two = 2,

        /// <summary>
        /// 使用 1.5 个停止位
        /// </summary>
        OnePointFive = 3
    }

    /// <summary>
    /// 指定串口通信的奇偶校验检查协议
    /// </summary>
    public enum Parity
    {
        /// <summary>
        /// 不使用奇偶校验检查
        /// </summary>
        None = 0,

        /// <summary>
        /// 设置奇偶校验位，使位数等于奇数
        /// </summary>
        Odd = 1,

        /// <summary>
        /// 设置奇偶校验位，使位数等于偶数
        /// </summary>
        Even = 2,

        /// <summary>
        /// 将奇偶校验位保留为 1
        /// </summary>
        Mark = 3,

        /// <summary>
        /// 将奇偶校验位保留为 0
        /// </summary>
        Space = 4
    }

    /// <summary>
    /// 指定串口通信的握手协议
    /// </summary>
    public enum Handshake
    {
        /// <summary>
        /// 不使用握手
        /// </summary>
        None = 0,

        /// <summary>
        /// 使用 XON/XOFF 软件握手协议
        /// </summary>
        XOnXOff = 1,

        /// <summary>
        /// 使用请求发送/清除发送 (RTS/CTS) 硬件流控制
        /// </summary>
        RequestToSend = 2,

        /// <summary>
        /// 使用请求发送/清除发送 (RTS/CTS) 硬件流控制和 XON/XOFF 软件控制
        /// </summary>
        RequestToSendXOnXOff = 3
    }
} 