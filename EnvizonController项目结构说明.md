# EnvizonController 项目结构说明文档

## 1. 项目概述

EnvizonController 是一个综合型环境控制与监测系统，设计用于实验室环境测试和工业生产环境监控。系统支持多种通信协议（Modbus、MQTT）、多平台部署（Desktop、Android、iOS、Browser），并提供统一的设备管理、数据采集、测试计划执行和告警管理功能。

### 1.1 系统目标

- 提供全面的环境参数（温度、湿度等）监测与控制
- 支持可编程测试计划的定义和执行
- 实现跨平台部署和访问
- 确保不同环境下数据的可靠采集和传输
- 提供实时监控和历史数据分析

## 2. 系统架构

EnvizonController 采用清晰的分层架构设计，遵循领域驱动设计(DDD)原则和依赖注入模式，确保系统各组件之间的松耦合和高内聚。

### 2.1 架构分层

```
┌─────────────────────────────────────────────────────────────┐
│                      表示层 (Presentation)                    │
│    Avalonia UI框架 + MVVM模式 + 跨平台视图适配                │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                      应用层 (Application)                     │
│    应用服务 + 命令/查询处理 + 事件处理 + 数据转换             │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                      领域层 (Domain)                         │
│    领域模型 + 业务规则 + 领域服务 + 领域事件                 │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层 (Infrastructure)                │
│    数据持久化 + 通信协议实现 + 第三方服务集成                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心架构模式

- **领域驱动设计(DDD)**: 通过领域模型捕获业务规则和流程
- **MVVM模式**: 用于表示层的UI逻辑分离
- **依赖注入**: 实现组件之间的松耦合
- **仓储模式**: 用于数据访问的抽象
- **工厂模式**: 用于创建复杂对象，如Modbus客户端
- **适配器模式**: 用于处理不同平台的特定实现

## 3. 主要组件和职责

### 3.1 通信协议模块

#### 3.1.1 Modbus 通信框架

```
┌─────────────────────────────────────────────────────────────┐
│                 EnvizonController.Modbus.Protocol            │
│                     (协议核心层 - 纯协议逻辑)                │
└───────────────────────────┬─────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                EnvizonController.Modbus.Abstractions         │
│                  (通信抽象层 - 接口和基类)                   │
└─────┬─────────────────────┬──────────────────────┬──────────┘
      ↓                     ↓                      ↓
┌──────────────┐    ┌──────────────┐      ┌───────────────┐
│Desktop适配器 │    │Android适配器 │      │ 网络适配器    │
│  (串口通信)  │    │ (USB串口API) │      │ (TCP/IP通信)  │
└──────┬───────┘    └──────┬───────┘      └───────┬───────┘
       └────────────────┬─────────────────────────┘
                        ↓
           ┌─────────────────────────┐
           │EnvizonController.Modbus.Client│
           │    (应用服务层 - 高级API)     │
           └─────────────────────────┘
```

职责:
- 支持Modbus RTU、ASCII和TCP协议变体
- 提供跨平台的通信实现（Desktop、Android、Network）
- 提供统一的高级API，隐藏底层通信细节
- 处理通信异常、超时和重试逻辑

#### 3.1.2 MQTT 通信模块

```
┌─────────────────────────┐   ┌─────────────────────────┐
│EnvizonController.Mqtt.Client│   │EnvizonController.Mqtt.Server│
│    (MQTT客户端实现)     │   │     (MQTT服务器实现)     │
└─────────────────────────┘   └─────────────────────────┘
```

职责:
- 提供MQTT客户端连接和消息发布/订阅功能
- 实现MQTT服务器用于本地消息中转
- 支持TLS加密通信
- 提供消息处理器注册和分发机制

### 3.2 数据处理模块

#### 3.2.1 数据接收模块

```
┌───────────────────────────────────────────────┐
│         EnvizonController.DataReceiver         │
│                                               │
│  ┌─────────────┐          ┌─────────────┐    │
│  │  MQTT实现   │          │  MVVM实现   │    │
│  └─────────────┘          └─────────────┘    │
└───────────────────────────────────────────────┘
```

职责:
- 统一的数据接收服务接口
- 支持MQTT协议接收数据
- 支持MVVM消息总线接收数据
- 消息处理器注册和分发

#### 3.2.2 数据推送模块

```
┌───────────────────────────────────────────────┐
│          EnvizonController.DataPush            │
│                                               │
│  ┌─────────────┐          ┌─────────────┐    │
│  │  MQTT实现   │          │  MVVM实现   │    │
│  └─────────────┘          └─────────────┘    │
└───────────────────────────────────────────────┘
```

职责:
- 统一的数据推送服务接口
- 支持MQTT协议推送数据
- 支持MVVM消息总线推送数据
- 数据序列化和格式转换

### 3.3 应用服务层

```
┌───────────────────────────────────────────────┐
│         EnvizonController.Application          │
│                                               │
│  ┌─────────────┐  ┌─────────────┐            │
│  │设备管理服务 │  │测试项管理服务│            │
│  └─────────────┘  └─────────────┘            │
│                                               │
│  ┌─────────────┐  ┌─────────────┐            │
│  │数据采集服务 │  │告警管理服务 │            │
│  └─────────────┘  └─────────────┘            │
└───────────────────────────────────────────────┘
```

职责:
- 实现业务流程和用例
- 协调领域对象和服务
- 数据传输对象(DTO)的转换
- 事务管理和一致性保证

### 3.4 表示层

```
┌───────────────────────────────────────────────┐
│        EnvizonController.Presentation          │
│                                               │
│  ┌─────────────┐  ┌─────────────┐            │
│  │ViewModels   │  │Views        │            │
│  └─────────────┘  └─────────────┘            │
│                                               │
│  ┌─────────────┐  ┌─────────────┐            │
│  │Controls     │  │Services     │            │
│  └─────────────┘  └─────────────┘            │
└───────────────────────────────────────────────┘
```

职责:
- 用户界面的实现
- 用户交互处理
- 数据绑定和展示
- 视图状态管理

## 4. 不同平台的支持情况

### 4.1 功能分布

|     功能模块    |  Desktop  |  Android  |   iOS   |  Browser  |
|----------------|-----------|-----------|---------|-----------|
| 设备配置与管理  |     ✓     |    部分    |   部分   |    ✗      |
| 测试计划设计    |     ✓     |    部分    |   部分   |    ✗      |
| 测试执行与控制  |     ✓     |     ✓     |    ✓    |    ✗      |
| 数据监控与查看  |     ✓     |     ✓     |    ✓    |    ✓      |
| 告警管理        |     ✓     |     ✓     |    ✓    |    ✓      |
| 报告生成与导出  |     ✓     |     ✗     |    ✗    |    ✓      |
| 系统管理        |     ✓     |     ✗     |    ✗    |    ✗      |

### 4.2 平台特性

#### 4.2.1 Desktop版本
- 支持直接串口通信（使用System.IO.Ports）
- 提供完整的配置和管理功能
- 支持本地数据库存储
- 可作为独立应用或配合服务器使用

#### 4.2.2 Android版本
- 使用USB串口API进行设备通信
- 专注于移动环境下的监控和简单控制
- 界面针对触屏优化
- 通过MQTT与服务器同步数据

#### 4.2.3 iOS版本
- 主要通过网络通信（无直接串口访问）
- 专注于移动环境下的监控和简单控制
- 界面针对触屏优化
- 通过MQTT与服务器同步数据

#### 4.2.4 Browser版本
- 纯网络通信
- 专注于数据可视化和报表查看
- 通过Web API访问服务器数据
- 支持远程监控但无直接控制功能

## 5. 通信协议实现

### 5.1 Modbus实现

#### 5.1.1 架构设计

Modbus实现采用严格的分层设计:

1. **协议核心层**: 纯协议逻辑，无平台依赖
   - 帧构建与解析
   - 功能码处理
   - CRC/LRC校验算法

2. **通信抽象层**: 定义统一接口
   - IModbusChannel接口
   - ModbusChannelBase抽象基类
   - 通信生命周期管理

3. **平台适配层**: 实现平台特定通信
   - Desktop: SerialPortChannel (System.IO.Ports)
   - Android: UsbSerialChannel (Android USB API)
   - 网络: TcpClientChannel (跨平台)

4. **应用服务层**: 高级API
   - ModbusClient统一API
   - ModbusClientFactory创建工厂
   - 数据类型转换扩展方法

#### 5.1.2 典型使用场景示例

**Desktop环境使用Modbus RTU进行设备通信**:
```csharp
// 创建串口通道
var channel = new SerialPortChannel(
    portName: "COM1",
    baudRate: 9600,
    dataBits: 8,
    parity: System.IO.Ports.Parity.None,
    stopBits: System.IO.Ports.StopBits.One);

// 创建Modbus RTU客户端
var client = ModbusClientFactory.CreateRtuClient(channel);

// 连接设备并读取保持寄存器
await client.ConnectAsync();
ushort[] values = await client.ReadHoldingRegistersAsync(
    slaveAddress: 1,
    startAddress: 0,
    registerCount: 10);
```

**Android环境使用Modbus通信**:
```csharp
// 创建USB串口通道
var channel = new UsbSerialChannel(
    context: Android.App.Application.Context,
    baudRate: 9600,
    dataBits: 8,
    parity: UsbSerialParity.None,
    stopBits: UsbSerialStopBits.One);

// 创建Modbus RTU客户端
var client = ModbusClientFactory.CreateRtuClient(channel);

// 连接设备并读取线圈状态
await client.ConnectAsync();
bool[] coils = await client.ReadCoilsAsync(
    slaveAddress: 1,
    startAddress: 0,
    coilCount: 10);
```

### 5.2 MQTT实现

#### 5.2.1 架构设计

MQTT实现分为客户端和服务器两部分:

1. **MQTT客户端**:
   - MqttClientService核心服务类
   - 消息处理器注册表机制
   - 自动重连和会话恢复机制

2. **MQTT服务器**:
   - MqttServerService服务类
   - 客户端连接管理
   - 主题和权限控制
   - 消息路由与转发

#### 5.2.2 典型使用场景示例

**配置并使用MQTT客户端发布数据**:
```csharp
// 客户端配置
var options = new MqttClientOptions
{
    BrokerUrl = "mqtt.example.com",
    Port = 1883,
    ClientId = $"device-{Guid.NewGuid()}",
    Username = "username",
    Password = "password"
};

// 创建客户端
var mqttClient = new MqttClientService(
    mqttClientFactory,
    options,
    handlerRegistry,
    logger);

// 连接并发布温湿度数据
await mqttClient.ConnectAsync();
await mqttClient.PublishAsync(
    "device/environmental/data",
    JsonSerializer.Serialize(new { 
        temperature = 25.5, 
        humidity = 60.2,
        timestamp = DateTime.Now
    }),
    MqttQualityOfServiceLevel.AtLeastOnce);
```

**注册MQTT消息处理器接收数据**:
```csharp
// 实现消息处理器
public class EnvironmentalDataHandler : IMessageHandler
{
    public bool CanHandle(string topic) => 
        topic.StartsWith("device/environmental/");
    
    public async Task HandleMessageAsync(string topic, string message)
    {
        var data = JsonSerializer.Deserialize<EnvironmentalData>(message);
        Console.WriteLine($"收到环境数据: 温度={data.temperature}, 湿度={data.humidity}");
        // 处理数据...
    }
}

// 注册处理器
handlerRegistry.RegisterHandler(new EnvironmentalDataHandler());
```

## 6. 典型业务流程示例

### 6.1 环境测试计划执行流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 界面层
    participant App as 应用服务层
    participant Device as 设备通信层
    participant DB as 数据存储
    
    User->>UI: 1. 创建测试计划
    UI->>App: 2. 保存测试计划
    App->>DB: 3. 存储测试计划
    User->>UI: 4. 启动测试
    UI->>App: 5. 执行测试请求
    App->>Device: 6. 发送设备命令
    Device->>Device: 7. 设备执行测试步骤
    Device->>App: 8. 上报实时数据
    App->>DB: 9. 记录测试数据
    App->>UI: 10. 更新测试状态
    UI->>User: 11. 显示实时数据和进度
    Device->>App: 12. 测试完成信号
    App->>DB: 13. 更新测试状态
    App->>UI: 14. 显示测试完成
    UI->>User: 15. 测试报告生成选项
```

**详细步骤说明**:

1. 用户通过UI创建测试计划，设置温度、湿度等参数和持续时间
2. UI层将测试计划传递给应用服务层
3. 应用服务层将测试计划存储到数据库
4. 用户点击"启动测试"按钮
5. UI层请求应用服务层执行测试
6. 应用服务层通过设备通信层向环境测试设备发送命令
7. 设备按照指令执行各个测试步骤（如升温、保持恒温等）
8. 设备通过Modbus或MQTT协议上报实时数据
9. 应用服务层记录测试数据到数据库
10. 应用服务层将最新状态发送给UI层
11. UI层实时更新图表和状态指示器
12. 测试完成后设备发送完成信号
13. 应用服务层更新数据库中测试状态为"已完成"
14. 应用服务层通知UI层测试完成
15. UI层显示测试报告选项给用户

### 6.2 多平台协同监控流程

```mermaid
sequenceDiagram
    participant Desktop as Desktop应用
    participant Server as 服务器
    participant Mobile as 移动应用
    participant Browser as 浏览器
    participant Device as 测试设备
    
    Desktop->>Device: 1. 直接通信(Modbus)
    Desktop->>Server: 2. 推送数据(MQTT)
    Server->>Mobile: 3. 转发数据(MQTT)
    Server->>Browser: 4. 提供数据(WebAPI)
    Mobile->>Server: 5. 发送控制命令
    Server->>Desktop: 6. 转发控制命令
    Desktop->>Device: 7. 执行控制命令
    Device->>Desktop: 8. 返回执行结果
    Desktop->>Server: 9. 更新状态
    Server->>Mobile: 10. 推送状态更新
    Server->>Browser: 11. 更新Web界面
```

**详细步骤说明**:

1. Desktop应用通过Modbus直接与测试设备通信获取数据
2. Desktop应用将数据推送到服务器(MQTT)
3. 服务器将数据转发给移动应用(Android/iOS)
4. 服务器通过Web API向浏览器提供数据访问
5. 移动应用发送控制命令到服务器
6. 服务器将控制命令转发给Desktop应用
7. Desktop应用通过Modbus执行设备控制命令
8. 设备返回命令执行结果给Desktop应用
9. Desktop应用将最新状态更新到服务器
10. 服务器将状态更新推送给移动应用
11. 服务器更新Web界面显示最新状态

## 7. 项目未来扩展性和可维护性分析

### 7.1 扩展性设计

EnvizonController项目在以下方面具有良好的扩展性:

1. **通信协议扩展**
   - 基于接口的通信抽象层设计
   - 可轻松添加新的通信协议实现(如MQTT、OPC UA等)
   - 统一的消息处理机制

2. **平台支持扩展**
   - 平台特定代码完全隔离在适配器层
   - 跨平台UI基于Avalonia
   - 共享核心业务逻辑

3. **功能模块扩展**
   - 松耦合的组件设计
   - 基于依赖注入的服务注册
   - 事件驱动架构支持新功能集成

4. **设备支持扩展**
   - 可配置的设备描述
   - 基于接口的设备抽象
   - 可扩展的寄存器映射机制

### 7.2 可维护性分析

#### 7.2.1 优势

1. **清晰的分层架构**
   - 关注点分离，每层职责明确
   - 降低变更的影响范围
   - 便于单元测试和集成测试

2. **统一的编码规范**
   - 一致的命名约定
   - 标准化的错误处理
   - 完善的注释和文档

3. **自动化测试支持**
   - 单元测试项目(如Modbus.Tests)
   - 可测试的架构设计
   - 模拟对象支持

4. **模块化设计**
   - 高内聚、低耦合的组件
   - 独立部署和更新能力
   - 基于契约的接口设计

#### 7.2.2 挑战与改进空间

1. **复杂的跨平台适配**
   - 平台特定Bug难以跟踪
   - 需要更多自动化跨平台测试
   - 建议: 增强平台适配层的测试覆盖率

2. **多协议集成的复杂性**
   - 通信协议间的交互逻辑复杂
   - 异步操作协调难度高
   - 建议: 实现统一的通信调度器

3. **数据一致性保证**
   - 多平台下的数据同步挑战
   - 离线操作与冲突解决
   - 建议: 引入更严格的数据版本控制

4. **配置管理**
   - 配置项分散在多个文件中
   - 缺乏集中的配置验证
   - 建议: 实现统一的配置管理服务

## 8. 架构评估

### 8.1 优势

1. **高度模块化**: 系统组件高内聚、低耦合，便于独立开发和测试
2. **跨平台能力**: 支持多种操作系统和设备类型，满足不同使用场景
3. **协议抽象**: 通过抽象层隔离协议细节，简化应用层开发
4. **可扩展性**: 接口驱动设计便于添加新功能和支持新设备
5. **统一数据模型**: 共享的DTO和领域模型确保系统一致性

### 8.2 局限性

1. **复杂性**: 高度抽象的架构增加了理解和学习成本
2. **性能权衡**: 多层架构可能引入性能开销
3. **分布式挑战**: 多平台数据同步和一致性保证复杂
4. **测试覆盖**: 需要广泛的跨平台测试场景
5. **依赖管理**: 需要谨慎管理第三方库的依赖和版本

### 8.3 改进建议

1. **统一配置管理**: 实现集中式配置服务，减少配置分散
2. **增强监控能力**: 添加更多内部监控点和诊断功能
3. **改进错误处理**: 统一的错误处理和恢复策略
4. **优化跨平台UI**: 更多平台特定UI优化以提升用户体验
5. **强化安全机制**: 增加数据传输加密和访问控制

## 9. 结论

EnvizonController是一个设计精良的跨平台环境控制与监测系统，采用现代软件架构原则，实现了高度的模块化和可扩展性。系统通过分层设计和接口抽象，成功解决了跨平台通信和多协议集成的复杂挑战。

该系统的架构适合于需要同时满足实验室环境测试和工业生产环境监控的综合应用场景，提供了从设备控制到数据分析的完整解决方案。随着物联网技术的发展，系统具有良好的扩展性基础，可以不断集成新的通信协议和设备类型。

未来发展应着重于增强分布式数据同步、简化配置管理以及提升跨平台用户体验，以应对物联网设备日益增长的复杂性和多样性。