# Decision Log

此文件使用列表格式记录架构和实现决策。
2025-05-19 14:42:08 - 日志更新。

*

## 决策

* 采用分层架构设计
* 选择领域驱动设计(DDD)作为主要设计方法
* 使用MVVM模式实现表示层
* 实现多协议支持（Modbus、MQTT）
* 采用API客户端架构模式访问服务端接口
* 在API交互中采用Result模式进行统一的错误处理
* 使用工厂模式创建API服务实例
* 预留缓存扩展设计支持后续优化

## 理由

* 分层架构确保关注点分离，每层职责明确，减少变更的影响范围
* DDD适合复杂业务逻辑的项目，能够更好地反映业务需求
* MVVM模式便于实现UI和业务逻辑分离，提高代码可测试性
* 多协议支持可以适应不同的设备通信需求
* API客户端架构模式使客户端代码与API实现完全解耦，便于维护和测试
* Result模式统一处理API错误，降低错误处理的复杂性
* 工厂模式集中管理实例创建逻辑，提高扩展性
* 预留缓存扩展点为后续性能优化提供支持

## 实现细节

* 分层架构包括表示层、应用层、领域层和基础设施层
* Modbus实现采用严格的分层设计，包括协议核心层、通信抽象层、平台适配层和应用服务层
* MQTT实现分为客户端和服务器两部分，客户端提供消息处理器注册机制
* API客户端架构包括API接口抽象层、API实现层和HTTP通信层
* Result模式封装了请求结果、错误信息和状态码
* 工厂模式通过IApiClientFactory接口和ApiClientFactory实现类实现
* 预留IApiCache接口和CachedApiServiceBase基类为缓存实现提供基础
[2025-05-23 14:50:25] - 服务器端启动初始化架构决策

## 启动时初始化策略
**决策**: 在应用构建完成后、启动前执行系统初始化逻辑
**理由**: 确保所有依赖服务已注册完成，数据库连接可用，避免运行时初始化错误
**实现**: 使用 `using (var scope = app.Services.CreateScope())` 创建作用域，通过依赖注入获取服务实例

## 数据库初始化决策  
**决策**: 使用 `EnsureCreated()` 方法进行数据库初始化
**理由**: 适合开发和测试环境快速启动，生产环境可切换为 `Migrate()` 方法
**影响**: 开发阶段简化了数据库管理，但生产部署需要考虑迁移策略

## 默认数据初始化顺序
**决策**: 按照协议→设备→测试项的顺序进行初始化
**理由**: 设备依赖协议配置，测试项可能依赖设备配置，确保依赖关系正确
**实现**: 顺序调用各个AppService的初始化方法

## 后台服务启动策略
**决策**: 使用ASP.NET Core的托管服务机制启动数据采集服务
**理由**: 利用框架的生命周期管理，自动处理服务启动和停止
**实现**: `services.AddHostedService<QuartzDeviceCollectionBackgroundService>()`
[2025-05-24 16:24:30] - Modbus指令发送框架架构决策

## Modbus指令管理框架设计决策
**决策**: 采用Command Pattern + Factory Pattern + Strategy Pattern的组合设计
**理由**: 
- Command Pattern封装设备指令，支持撤销、重试和批量操作
- Factory Pattern统一创建不同类型的Modbus指令，便于扩展
- Strategy Pattern处理不同设备类型的指令格式差异
**实现**: 创建IModbusCommandService作为主要服务接口，IModbusCommandExecutor作为执行器

## 异步和错误处理策略
**决策**: 使用现有的Result模式扩展到Modbus指令操作
**理由**: 保持与项目现有错误处理机制的一致性，统一异步操作模式
**实现**: 创建ModbusCommandResult和ModbusCommandResult<T>类型

## 分层集成策略
**决策**: 在DeviceTestService和Modbus通信层之间插入Modbus指令抽象层
**理由**: 
- 不破坏现有架构，保持层次清晰
- 为设备操作提供高级抽象接口
- 支持指令的组合和编排
**影响**: 需要修改DeviceTestService.PauseDeviceTestAsync方法以调用Modbus指令服务