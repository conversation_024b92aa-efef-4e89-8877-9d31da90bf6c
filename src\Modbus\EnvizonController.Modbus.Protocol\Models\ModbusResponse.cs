using EnvizonController.Modbus.Protocol.Enums;

namespace EnvizonController.Modbus.Protocol.Models
{
    /// <summary>
    /// Modbus 响应基类
    /// </summary>
    public abstract class ModbusResponse
    {
        /// <summary>
        /// 从站地址
        /// </summary>
        public byte SlaveAddress { get; set; }

        /// <summary>
        /// 功能码
        /// </summary>
        public ModbusFunction FunctionCode { get; set; }

        /// <summary>
        /// 是否为异常响应
        /// </summary>
        public bool IsException { get; set; }

        /// <summary>
        /// 异常码
        /// </summary>
        public ModbusExceptionCode ExceptionCode { get; set; }

        /// <summary>
        /// 从响应帧中解析数据
        /// </summary>
        /// <param name="frame">响应帧</param>
        public virtual void ParseResponse(byte[] frame)
        {
            if (frame == null || frame.Length < 2)
                throw new ArgumentException("响应帧长度不足", nameof(frame));

            SlaveAddress = frame[0];
            
            // 检查是否为异常响应（功能码最高位为1）
            if ((frame[1] & 0x80) != 0)
            {
                IsException = true;
                FunctionCode = (ModbusFunction)(frame[1] & 0x7F); // 移除最高位
                
                if (frame.Length >= 3)
                    ExceptionCode = (ModbusExceptionCode)frame[2];
            }
            else
            {
                IsException = false;
                FunctionCode = (ModbusFunction)frame[1];
            }
        }
    }
}
