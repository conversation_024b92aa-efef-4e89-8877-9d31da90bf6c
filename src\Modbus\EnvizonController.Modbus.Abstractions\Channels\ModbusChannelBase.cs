using EnvizonController.Modbus.Abstractions.Interfaces;

namespace EnvizonController.Modbus.Abstractions.Channels
{
    /// <summary>
    /// Modbus通道基类
    /// </summary>
    public abstract class ModbusChannelBase : IModbusChannel
    {
        private bool _isDisposed;

        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        public abstract bool IsConnected { get; }

        /// <summary>
        /// 连接到通道
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public abstract Task ConnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 断开通道连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public abstract Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public abstract Task SendAsync(byte[] data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收到的数据</returns>
        public abstract Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default);

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        /// <returns>异步任务</returns>
        public abstract Task ClearReceiveBufferAsync();

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                // 释放托管资源
                if (IsConnected)
                {
                    DisconnectAsync().GetAwaiter().GetResult();
                }
            }

            // 释放非托管资源

            _isDisposed = true;
        }
    }
}
