using System;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Mqtt
{
    /// <summary>
    /// MQTT连接状态变更事件参数
    /// </summary>
    public class MqttConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧连接状态
        /// </summary>
        public ConnectionStatus OldStatus { get; set; }

        /// <summary>
        /// 新连接状态
        /// </summary>
        public ConnectionStatus NewStatus { get; set; }

        /// <summary>
        /// 状态变更描述信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 如果状态变更由错误引起，包含相关异常
        /// </summary>
        public Exception Error { get; set; }
    }
}