<ResourceDictionary
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:EnvizonController.Presentation.Controls">

    <Design.PreviewWith>
        <StackPanel Width="400" Spacing="10">
            <StackPanel Background="#0B0A1A">
                <controls:PaginationControl />
            </StackPanel>
            <StackPanel Background="#0B0A1A">
                <controls:PaginationControl Size="Small" />
            </StackPanel>
        </StackPanel>
    </Design.PreviewWith>

    <!--  分页控件主题  -->
    <ControlTheme x:Key="{x:Type controls:PaginationControl}" TargetType="controls:PaginationControl">
        <Setter Property="Background" Value="#0B0A1A" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="6" />
        <Setter Property="Margin" Value="10" />
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="8"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.5"
                Color="#0DF0FF" />
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}"
                    Effect="{TemplateBinding Effect}">
                    <StackPanel
                        Margin="15"
                        HorizontalAlignment="Center"
                        Orientation="Horizontal"
                        Spacing="10">

                        <!--  页面大小选择器  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowPageSizeSelector}"
                            Orientation="Horizontal"
                            Spacing="5">
                            <TextBlock
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                Text="每页显示:" />
                            <ComboBox
                                Name="PART_PageSizeComboBox"
                                Width="70"
                                Background="#14131C"
                                BorderBrush="#224F5F"
                                Foreground="White"
                                ItemsSource="{TemplateBinding PageSizeOptions}"
                                SelectedItem="{Binding PaginationInfo.PageSize, RelativeSource={RelativeSource TemplatedParent}}" />
                            <TextBlock
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                Text="条" />
                        </StackPanel>

                        <!--  分隔线  -->
                        <Border
                            Width="1"
                            Height="20"
                            Background="#224F5F"
                            IsVisible="{TemplateBinding ShowPageSizeSelector}" />

                        <!--  导航按钮  -->
                        <StackPanel Orientation="Horizontal" Spacing="5">
                            <Button
                                Name="PART_FirstPageButton"
                                Content="首页"
                                IsVisible="{TemplateBinding ShowFirstLastButtons}"
                                Theme="{StaticResource CyberPaginationNavButtonTheme}" />
                            <Button
                                Name="PART_PrevPageButton"
                                Content="上一页"
                                Theme="{StaticResource CyberPaginationNavButtonTheme}" />

                            <!--  页码按钮容器  -->
                            <StackPanel
                                Name="PART_PageNumbersPanel"
                                IsVisible="{TemplateBinding ShowNumericButtons}"
                                Orientation="Horizontal"
                                Spacing="2" />

                            <Button
                                Name="PART_NextPageButton"
                                Content="下一页"
                                Theme="{StaticResource CyberPaginationNavButtonTheme}" />
                            <Button
                                Name="PART_LastPageButton"
                                Content="末页"
                                IsVisible="{TemplateBinding ShowFirstLastButtons}"
                                Theme="{StaticResource CyberPaginationNavButtonTheme}" />
                        </StackPanel>

                        <!--  快速跳转  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowQuickJump}"
                            Orientation="Horizontal"
                            Spacing="5">
                            <Border
                                Width="1"
                                Height="20"
                                Background="#224F5F" />
                            <TextBlock
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                Text="跳至" />
                            <TextBox
                                Name="PART_JumpPageTextBox"
                                Width="50"
                                Background="#14131C"
                                BorderBrush="#224F5F"
                                Foreground="White"
                                Watermark="页码" />
                            <Button
                                Name="PART_JumpButton"
                                Content="确定"
                                Theme="{StaticResource CyberPaginationJumpButtonTheme}" />
                        </StackPanel>

                        <!--  当前页码信息  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowCurrentPageInfo}"
                            Orientation="Horizontal"
                            Spacing="5">
                            <Border
                                Width="1"
                                Height="20"
                                Background="#224F5F" />
                            <TextBlock
                                Margin="10,0"
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="第 {0} 页 / 共 {1} 页">
                                        <Binding Path="PaginationInfo.CurrentPage" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.TotalPages" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </StackPanel>

                        <!--  页面信息  -->
                        <StackPanel IsVisible="{TemplateBinding ShowPageInfo}" Orientation="Horizontal">
                            <Border
                                Width="1"
                                Height="20"
                                Background="#224F5F" />
                            <TextBlock
                                Margin="10,0"
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="第 {0}-{1} 条，共 {2} 条">
                                        <Binding Path="PaginationInfo.StartIndex" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.EndIndex" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.TotalCount" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <!--  小尺寸分页控件主题  -->
    <ControlTheme x:Key="SmallPaginationControlTheme" TargetType="controls:PaginationControl">
        <Setter Property="Background" Value="#0B0A1A" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Margin" Value="5" />
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="6"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.4"
                Color="#0DF0FF" />
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}"
                    Effect="{TemplateBinding Effect}">
                    <StackPanel
                        Margin="8"
                        HorizontalAlignment="Center"
                        Orientation="Horizontal"
                        Spacing="6">

                        <!--  页面大小选择器（小尺寸版本）  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowPageSizeSelector}"
                            Orientation="Horizontal"
                            Spacing="3">
                            <TextBlock
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                FontSize="11"
                                Text="每页:" />
                            <ComboBox
                                Name="PART_PageSizeComboBox"
                                Width="55"
                                Height="24"
                                Background="#14131C"
                                BorderBrush="#224F5F"
                                FontSize="11"
                                Foreground="White"
                                ItemsSource="{TemplateBinding PageSizeOptions}"
                                SelectedItem="{Binding PaginationInfo.PageSize, RelativeSource={RelativeSource TemplatedParent}}" />
                        </StackPanel>

                        <!--  分隔线  -->
                        <Border
                            Width="1"
                            Height="16"
                            Background="#224F5F"
                            IsVisible="{TemplateBinding ShowPageSizeSelector}" />

                        <!--  导航按钮（小尺寸版本）  -->
                        <StackPanel Orientation="Horizontal" Spacing="3">
                            <Button
                                Name="PART_FirstPageButton"
                                Content="首"
                                IsVisible="{TemplateBinding ShowFirstLastButtons}"
                                Theme="{StaticResource CyberPaginationNavButtonSmallTheme}" />
                            <Button
                                Name="PART_PrevPageButton"
                                Content="上"
                                Theme="{StaticResource CyberPaginationNavButtonSmallTheme}" />

                            <!--  页码按钮容器  -->
                            <StackPanel
                                Name="PART_PageNumbersPanel"
                                IsVisible="{TemplateBinding ShowNumericButtons}"
                                Orientation="Horizontal"
                                Spacing="1" />

                            <Button
                                Name="PART_NextPageButton"
                                Content="下"
                                Theme="{StaticResource CyberPaginationNavButtonSmallTheme}" />
                            <Button
                                Name="PART_LastPageButton"
                                Content="末"
                                IsVisible="{TemplateBinding ShowFirstLastButtons}"
                                Theme="{StaticResource CyberPaginationNavButtonSmallTheme}" />
                        </StackPanel>

                        <!--  快速跳转（小尺寸版本）  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowQuickJump}"
                            Orientation="Horizontal"
                            Spacing="3">
                            <Border
                                Width="1"
                                Height="16"
                                Background="#224F5F" />
                            <TextBox
                                Name="PART_JumpPageTextBox"
                                Width="35"
                                Height="24"
                                Background="#14131C"
                                BorderBrush="#224F5F"
                                FontSize="11"
                                Foreground="White"
                                Watermark="页" />
                            <Button
                                Name="PART_JumpButton"
                                Content="跳"
                                Theme="{StaticResource CyberPaginationJumpButtonSmallTheme}" />
                        </StackPanel>

                        <!--  当前页码信息（小尺寸版本）  -->
                        <StackPanel
                            IsVisible="{TemplateBinding ShowCurrentPageInfo}"
                            Orientation="Horizontal"
                            Spacing="3">
                            <Border
                                Width="1"
                                Height="16"
                                Background="#224F5F" />
                            <TextBlock
                                Margin="6,0"
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                FontSize="11">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0}/{1}">
                                        <Binding Path="PaginationInfo.CurrentPage" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.TotalPages" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </StackPanel>

                        <!--  页面信息（小尺寸版本）  -->
                        <StackPanel IsVisible="{TemplateBinding ShowPageInfo}" Orientation="Horizontal">
                            <Border
                                Width="1"
                                Height="16"
                                Background="#224F5F" />
                            <TextBlock
                                Margin="6,0"
                                VerticalAlignment="Center"
                                Classes="gray"
                                FontFamily="{DynamicResource SansSerif}"
                                FontSize="11">
                                <TextBlock.Text>
                                    <MultiBinding StringFormat="{}{0}-{1}/{2}">
                                        <Binding Path="PaginationInfo.StartIndex" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.EndIndex" RelativeSource="{RelativeSource TemplatedParent}" />
                                        <Binding Path="PaginationInfo.TotalCount" RelativeSource="{RelativeSource TemplatedParent}" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <!--  赛博朋克导航按钮主题  -->
    <ControlTheme x:Key="CyberPaginationNavButtonTheme" TargetType="Button">
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="MinWidth" Value="60" />
        <Setter Property="Background" Value="#14131C" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Foreground" Value="#9DA3AF" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                <BrushTransition Property="Foreground" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    x:Name="border"
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  禁用状态  -->
        <Style Selector="^:disabled">
            <Setter Property="Background" Value="#0B0A1A" />
            <Setter Property="Foreground" Value="#6b7280" />
            <Setter Property="BorderBrush" Value="#1A1927" />
            <Setter Property="Opacity" Value="0.5" />
        </Style>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#1A1927" />
            <Setter Property="BorderBrush" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="8"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.6"
                    Color="#56EFFE" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

    <!--  赛博朋克页码按钮主题  -->
    <ControlTheme x:Key="CyberPaginationPageButtonTheme" TargetType="Button">
        <Setter Property="Background" Value="#14131C" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="10,6" />
        <Setter Property="MinWidth" Value="35" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Foreground" Value="#9DA3AF" />
        <Setter Property="FontFamily" Value="{DynamicResource Rajdhani}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                <BrushTransition Property="Foreground" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  当前页状态  -->
        <Style Selector="^.current">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="10"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.8"
                    Color="#0DF0FF" />
            </Setter>
        </Style>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#1A1927" />
            <Setter Property="BorderBrush" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#56EFFE" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="6"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.6"
                    Color="#56EFFE" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

    <!--  赛博朋克跳转按钮主题  -->
    <ControlTheme x:Key="CyberPaginationJumpButtonTheme" TargetType="Button">
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Background" Value="#252335" />
        <Setter Property="Foreground" Value="#0DF0FF" />
        <Setter Property="BorderBrush" Value="#0DF0FF" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="8"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.6"
                Color="#0DF0FF" />
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#0DF0FF" />
            <Setter Property="Foreground" Value="#0B0A1A" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="15"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.8"
                    Color="#0DF0FF" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#0B0A1A" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

    <!--  小尺寸赛博朋克导航按钮主题  -->
    <ControlTheme x:Key="CyberPaginationNavButtonSmallTheme" TargetType="Button">
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="MinWidth" Value="32" />
        <Setter Property="Height" Value="24" />
        <Setter Property="Background" Value="#14131C" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="3" />
        <Setter Property="Foreground" Value="#9DA3AF" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                <BrushTransition Property="Foreground" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    x:Name="border"
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  禁用状态  -->
        <Style Selector="^:disabled">
            <Setter Property="Background" Value="#0B0A1A" />
            <Setter Property="Foreground" Value="#6b7280" />
            <Setter Property="BorderBrush" Value="#1A1927" />
            <Setter Property="Opacity" Value="0.5" />
        </Style>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#1A1927" />
            <Setter Property="BorderBrush" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="6"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.5"
                    Color="#56EFFE" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

    <!--  小尺寸赛博朋克页码按钮主题  -->
    <ControlTheme x:Key="CyberPaginationPageButtonSmallTheme" TargetType="Button">
        <Setter Property="Background" Value="#14131C" />
        <Setter Property="BorderBrush" Value="#224F5F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="6,4" />
        <Setter Property="MinWidth" Value="24" />
        <Setter Property="Height" Value="24" />
        <Setter Property="CornerRadius" Value="3" />
        <Setter Property="Foreground" Value="#9DA3AF" />
        <Setter Property="FontFamily" Value="{DynamicResource Rajdhani}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                <BrushTransition Property="Foreground" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  当前页状态  -->
        <Style Selector="^.current">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="Foreground" Value="#0DF0FF" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="8"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.7"
                    Color="#0DF0FF" />
            </Setter>
        </Style>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#1A1927" />
            <Setter Property="BorderBrush" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#56EFFE" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="4"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.5"
                    Color="#56EFFE" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#252335" />
            <Setter Property="BorderBrush" Value="#0DF0FF" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

    <!--  小尺寸赛博朋克跳转按钮主题  -->
    <ControlTheme x:Key="CyberPaginationJumpButtonSmallTheme" TargetType="Button">
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Height" Value="24" />
        <Setter Property="Background" Value="#252335" />
        <Setter Property="Foreground" Value="#0DF0FF" />
        <Setter Property="BorderBrush" Value="#0DF0FF" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="3" />
        <Setter Property="FontFamily" Value="{DynamicResource SansSerif}" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="FontSize" Value="11" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <BrushTransition Property="Background" Duration="0:0:0.2" />
                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
            </Transitions>
        </Setter>
        <Setter Property="Effect">
            <DropShadowEffect
                BlurRadius="6"
                OffsetX="0"
                OffsetY="0"
                Opacity="0.5"
                Color="#0DF0FF" />
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ContentPresenter
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        Content="{TemplateBinding Content}"
                        Foreground="{TemplateBinding Foreground}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!--  悬停状态  -->
        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="#0DF0FF" />
            <Setter Property="Foreground" Value="#0B0A1A" />
            <Setter Property="Effect">
                <DropShadowEffect
                    BlurRadius="10"
                    OffsetX="0"
                    OffsetY="0"
                    Opacity="0.7"
                    Color="#0DF0FF" />
            </Setter>
        </Style>

        <!--  按下状态  -->
        <Style Selector="^:pressed">
            <Setter Property="Background" Value="#56EFFE" />
            <Setter Property="Foreground" Value="#0B0A1A" />
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>
    </ControlTheme>

</ResourceDictionary>
