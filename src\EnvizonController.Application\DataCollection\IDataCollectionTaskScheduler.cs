namespace EnvizonController.Application.DataCollection
{
    /// <summary>
    /// 数据采集任务调度器接口
    /// 负责调度数据采集任务的执行
    /// </summary>
    public interface IDataCollectionTaskScheduler
    {
        /// <summary>
        /// 获取采集间隔（毫秒）
        /// </summary>
        int CollectionIntervalMs { get; }

        /// <summary>
        /// 获取调度器是否正在运行
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 启动调度器
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止调度器
        /// </summary>
        /// <returns>异步任务</returns>
        Task StopAsync();

        /// <summary>
        /// 设置采集间隔
        /// </summary>
        /// <param name="intervalMs">间隔（毫秒）</param>
        void SetCollectionInterval(int intervalMs);
    }
}
