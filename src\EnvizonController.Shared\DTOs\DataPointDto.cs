namespace EnvizonController.Shared.DTOs
{
    /// <summary>
    /// 数据点DTO - 用于API响应
    /// </summary>
    public class DataPointDto
    {
        public long Id { get; set; }
        public long TestId { get; set; }
        public DateTime Timestamp { get; set; }
        public List<ValueDataDto> Values { get; set; } = new List<ValueDataDto>();
    }

    /// <summary>
    /// 值数据DTO - 用于API响应
    /// </summary>
    public class ValueDataDto
    {
        public int ProtocolItemIndex { get; set; }
        public string Value { get; set; } = String.Empty;
        public string ProtocolName { get; set; } = String.Empty;
        public string Unit { get; set; } = String.Empty;
    }

    /// <summary>
    /// 数据点查询参数DTO - 用于API请求过滤
    /// </summary>
    public class DataPointQueryParams
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 数据聚合参数DTO - 用于API请求
    /// </summary>
    public class DataAggregateParams
    {
        public long DeviceId { get; set; }
        public long? TestId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Interval { get; set; } = "hour"; // minute, hour, day, month
        public string AggregateFunc { get; set; } = "avg"; // avg, min, max, sum
    }

    /// <summary>
    /// 聚合数据点DTO - 用于API响应
    /// </summary>
    public class AggregateDataPointDto
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<int, object> Values { get; set; } = new Dictionary<int, object>();
    }
}