using EnvizonController.Domain.Aggregates;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EnvizonController.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 程式链接实体配置
    /// </summary>
    public class ProgramLinkConfiguration : IEntityTypeConfiguration<ProgramLink>
    {
        public void Configure(EntityTypeBuilder<ProgramLink> builder)
        {
            // 表名
            builder.ToTable("ProgramLinks");

            // 主键
            builder.HasKey(e => e.Id);

            // 配置自增长主键
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            // 必填属性
            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.CycleCount)
                .IsRequired();

            builder.Property(e => e.CreatedAt)
                .IsRequired();

            builder.Property(e => e.UpdatedAt)
                .IsRequired();

            // 索引
            builder.HasIndex(e => e.Name);

            // 关系配置 - 一对多关系
            builder.HasMany(e => e.Items)
                .WithOne(e => e.ProgramLink)
                .HasForeignKey(e => e.ProgramLinkId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}