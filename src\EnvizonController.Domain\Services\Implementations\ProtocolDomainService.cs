﻿using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;

namespace EnvizonController.Domain.Services.Implementations;

/// <summary>
///     协议服务实现
/// </summary>
public class ProtocolDomainService : IProtocolService
{
    private readonly IProtocolRepository _protocolRepository;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="protocolRepository">协议仓储</param>
    public ProtocolDomainService(IProtocolRepository protocolRepository)
    {
        _protocolRepository = protocolRepository;
    }

    /// <summary>
    ///     添加协议
    /// </summary>
    public async Task<Protocol> AddProtocolAsync(Protocol protocol)
    {
        return protocol;
    }

    /// <summary>
    ///     更新协议
    /// </summary>
    public async Task<Protocol> UpdateProtocolAsync(Protocol protocol)
    {
        return protocol;
    }
}