<UserControl
    x:Class="EnvizonController.Modbus.SerialTester.Views.ModbusTestView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:EnvizonController.Modbus.SerialTester.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Modbus.SerialTester.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ModbusTestViewModel}"
    d:DesignHeight="650"
    d:DesignWidth="650"
    DataContext="{Binding ModbusTestViewModel}"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  通用参数设置  -->
        <GroupBox
            Grid.Row="0"
            Margin="10,5"
            Header="通用参数设置">
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <Label
                    Grid.Row="0"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="从站地址:" />
                <TextBox
                    Grid.Row="0"
                    Grid.Column="1"
                    Margin="5"
                    Text="{Binding MainViewModel.SlaveAddress}" />

                <Label
                    Grid.Row="0"
                    Grid.Column="2"
                    Margin="10,5,0,5"
                    Content="响应超时(ms):" />
                <TextBox
                    Grid.Row="0"
                    Grid.Column="3"
                    Margin="5"
                    Text="{Binding MainViewModel.ResponseTimeout}" />

                <Label
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="0,5"
                    Content="重试次数:" />
                <TextBox
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="5"
                    Text="{Binding MainViewModel.RetryCount}" />

                <Label
                    Grid.Row="1"
                    Grid.Column="2"
                    Margin="10,5,0,5"
                    Content="重试延迟(ms):" />
                <TextBox
                    Grid.Row="1"
                    Grid.Column="3"
                    Margin="5"
                    Text="{Binding MainViewModel.RetryDelay}" />
            </Grid>
        </GroupBox>

        <TabControl Grid.Row="1">
            <TabItem Header="读取操作">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="功能码:" />
                    <ComboBox
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="5"
                        ItemsSource="{Binding ReadFunctions}"
                        SelectedItem="{Binding SelectedReadFunction}" />

                    <Label
                        Grid.Row="0"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="从站地址:" />
                    <TextBox
                        Grid.Row="0"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.SlaveAddress}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="起始地址:" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding ReadStartAddress}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="超时设置(ms):" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.ResponseTimeout}" />

                    <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="读取数量:" />
                    <TextBox
                        Grid.Row="2"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding ReadCount}" />

                    <Label
                        Grid.Row="2"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试次数:" />
                    <TextBox
                        Grid.Row="2"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryCount}" />

                    <Label
                        Grid.Row="3"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="字节顺序:" />
                    <ComboBox
                        Grid.Row="3"
                        Grid.Column="1"
                        Margin="5"
                        ItemsSource="{Binding ByteOrders}"
                        SelectedItem="{Binding SelectedByteOrder}" />

                    <Label
                        Grid.Row="3"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试延迟(ms):" />
                    <TextBox
                        Grid.Row="3"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryDelay}" />

                    <CheckBox
                        Grid.Row="4"
                        Grid.Column="1"
                        Margin="5,10"
                        Content="十六进制显示"
                        IsChecked="{Binding IsHexDisplay}" />

                    <CheckBox
                        Grid.Row="4"
                        Grid.Column="3"
                        Margin="5,10"
                        Content="使用扩展功能(支持浮点数)"
                        IsChecked="{Binding UseExtensions}" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="3"
                        Margin="25,30,5,0"
                        FontSize="10"
                        Foreground="Gray"
                        Text="启用后可读写IEEE-754浮点数" />

                    <Button
                        Grid.Row="5"
                        Grid.Column="1"
                        Grid.ColumnSpan="3"
                        Height="30"
                        Margin="5,10"
                        Command="{Binding ExecuteReadCommand}"
                        Content="读取"
                        IsEnabled="{Binding IsConnected}" />

                    <GroupBox
                        Grid.Row="6"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="0,10"
                        Header="读取结果">
                        <TextBox
                            Margin="5"
                            FontFamily="Consolas"
                            HorizontalScrollBarVisibility="Auto"
                            IsReadOnly="True"
                            Text="{Binding ReadResult, Mode=OneWay}"
                            VerticalScrollBarVisibility="Auto" />
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="写入操作">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="功能码:" />
                    <ComboBox
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="5"
                        ItemsSource="{Binding WriteFunctions}"
                        SelectedItem="{Binding SelectedWriteFunction}" />

                    <Label
                        Grid.Row="0"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="从站地址:" />
                    <TextBox
                        Grid.Row="0"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.SlaveAddress}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="地址:" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding WriteAddress}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="超时设置(ms):" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.ResponseTimeout}" />

                    <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="值:" />
                    <TextBox
                        Grid.Row="2"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding WriteValues}" />

                    <Label
                        Grid.Row="2"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试次数:" />
                    <TextBox
                        Grid.Row="2"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryCount}" />

                    <Label
                        Grid.Row="3"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="字节顺序:" />
                    <ComboBox
                        Grid.Row="3"
                        Grid.Column="1"
                        Margin="5"
                        ItemsSource="{Binding ByteOrders}"
                        SelectedItem="{Binding SelectedByteOrder}" />

                    <Label
                        Grid.Row="3"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试延迟(ms):" />
                    <TextBox
                        Grid.Row="3"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryDelay}" />

                    <CheckBox
                        Grid.Row="4"
                        Grid.Column="3"
                        Margin="5,10"
                        Content="使用扩展功能(支持浮点数)"
                        IsChecked="{Binding UseExtensions}" />
                    <TextBlock
                        Grid.Row="4"
                        Grid.Column="3"
                        Margin="25,30,5,0"
                        FontSize="10"
                        Foreground="Gray"
                        Text="启用后可读写IEEE-754浮点数" />

                    <Button
                        Grid.Row="5"
                        Grid.Column="1"
                        Grid.ColumnSpan="3"
                        Height="30"
                        Margin="5,10"
                        Command="{Binding ExecuteWriteCommand}"
                        Content="写入"
                        IsEnabled="{Binding IsConnected}" />

                    <GroupBox
                        Grid.Row="4"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="0,10"
                        Header="写入结果">
                        <TextBox
                            Margin="5"
                            FontFamily="Consolas"
                            HorizontalScrollBarVisibility="Auto"
                            IsReadOnly="True"
                            Text="{Binding WriteResult, Mode=OneWay}"
                            VerticalScrollBarVisibility="Auto" />
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="自定义命令">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="功能码(十六进制):" />
                    <TextBox
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding CustomFunctionCode, StringFormat=X2}" />

                    <Label
                        Grid.Row="0"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="从站地址:" />
                    <TextBox
                        Grid.Row="0"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.SlaveAddress}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="数据(十六进制):" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="1"
                        Margin="5"
                        Text="{Binding CustomData}" />

                    <Label
                        Grid.Row="1"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="超时设置(ms):" />
                    <TextBox
                        Grid.Row="1"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.ResponseTimeout}" />

                    <Label
                        Grid.Row="2"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试次数:" />
                    <TextBox
                        Grid.Row="2"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryCount}" />

                    <Label
                        Grid.Row="3"
                        Grid.Column="2"
                        Margin="10,5,0,5"
                        Content="重试延迟(ms):" />
                    <TextBox
                        Grid.Row="3"
                        Grid.Column="3"
                        Margin="5"
                        Text="{Binding MainViewModel.RetryDelay}" />

                    <CheckBox
                        Grid.Row="3"
                        Grid.Column="1"
                        Margin="5,10"
                        Content="使用扩展功能(支持浮点数)"
                        IsChecked="{Binding UseExtensions}" />
                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="1"
                        Margin="25,30,5,0"
                        FontSize="10"
                        Foreground="Gray"
                        Text="启用后可读写IEEE-754浮点数" />

                    <Button
                        Grid.Row="4"
                        Grid.Column="1"
                        Grid.ColumnSpan="3"
                        Height="30"
                        Margin="5,10"
                        Command="{Binding ExecuteCustomCommandCommand}"
                        Content="执行"
                        IsEnabled="{Binding IsConnected}" />

                    <GroupBox
                        Grid.Row="3"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="0,10"
                        Header="执行结果">
                        <TextBox
                            Margin="5"
                            FontFamily="Consolas"
                            HorizontalScrollBarVisibility="Auto"
                            IsReadOnly="True"
                            Text="{Binding CustomResult, Mode=OneWay}"
                            VerticalScrollBarVisibility="Auto" />
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
