﻿﻿using CommunityToolkit.Mvvm.Messaging.Messages;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 对话框结果消息
/// </summary>
public class DialogResultMessage : ValueChangedMessage<(object DialogViewModel, object Result, bool IsCancelled)>
{
    /// <summary>
    /// 对话框视图模型
    /// </summary>
    public object DialogViewModel => Value.DialogViewModel;

    /// <summary>
    /// 结果
    /// </summary>
    public object Result => Value.Result;

    /// <summary>
    /// 是否已取消
    /// </summary>
    public bool IsCancelled => Value.IsCancelled;

    /// <summary>
    /// 创建对话框结果消息
    /// </summary>
    /// <param name="dialogViewModel">对话框视图模型</param>
    /// <param name="result">结果</param>
    /// <param name="isCancelled">是否已取消</param>
    public DialogResultMessage(object dialogViewModel, object result, bool isCancelled = false) 
        : base((dialogViewModel, result, isCancelled))
    {
    }
}
