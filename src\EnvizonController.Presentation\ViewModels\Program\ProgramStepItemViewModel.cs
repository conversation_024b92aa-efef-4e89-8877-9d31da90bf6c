using CommunityToolkit.Mvvm.ComponentModel;
using EnvizonController.Shared.DTOs;
using System;

namespace EnvizonController.Presentation.ViewModels.Program
{
    /// <summary>
    /// 程式步骤项视图模型
    /// </summary>
    public class ProgramStepItemViewModel : ObservableObject
    {
        private long _id;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public long Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private long _programId;
        /// <summary>
        /// 所属程式表ID
        /// </summary>
        public long ProgramId
        {
            get => _programId;
            set => SetProperty(ref _programId, value);
        }

        private double _humidity;
        /// <summary>
        /// 湿度（百分比）
        /// </summary>
        public double Humidity
        {
            get => _humidity;
            set => SetProperty(ref _humidity, value);
        }

        private double _temperature;
        /// <summary>
        /// 温度（摄氏度）
        /// </summary>
        public double Temperature
        {
            get => _temperature;
            set => SetProperty(ref _temperature, value);
        }

        private bool _isLinear;
        /// <summary>
        /// 是否线性变化
        /// </summary>
        public bool IsLinear
        {
            get => _isLinear;
            set => SetProperty(ref _isLinear, value);
        }

        private int _duration;
        /// <summary>
        /// 持续时间（秒）
        /// </summary>
        public int Duration
        {
            get => _duration;
            set => SetProperty(ref _duration, value);
        }

        private int _index;
        /// <summary>
        /// 排序索引 1 开始
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 从DTO创建视图模型
        /// </summary>
        public static ProgramStepItemViewModel FromDto(ProgramStepDTO dto)
        {
            return new ProgramStepItemViewModel
            {
                Id = dto.Id,
                ProgramId = dto.ProgramId,
                Humidity = dto.Humidity,
                Temperature = dto.Temperature,
                IsLinear = dto.IsLinear,
                Duration = dto.Duration,
                Index = dto.Index,
                CreatedAt = dto.CreatedAt,
                UpdatedAt = dto.UpdatedAt
            };
        }

        /// <summary>
        /// 转换为DTO
        /// </summary>
        public ProgramStepDTO ToDto()
        {
            return new ProgramStepDTO
            {
                Id = Id,
                ProgramId = ProgramId,
                Humidity = Humidity,
                Temperature = Temperature,
                IsLinear = IsLinear,
                Duration = Duration,
                Index = Index,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}