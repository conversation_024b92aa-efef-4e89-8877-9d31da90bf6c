namespace EnvizonController.Application.Devices
{
    /// <summary>
    /// 设备模型
    /// </summary>
    public class DeviceModel
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号
        /// </summary>
        public string ModelName { get; set; } = string.Empty;

        /// <summary>
        /// 固件版本
        /// </summary>
        public string FirmwareVersion { get; set; } = string.Empty;

        /// <summary>
        /// 连接状态
        /// </summary>
        public ConnectionStatus ConnectionStatus { get; set; } = ConnectionStatus.Disconnected;

        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus DeviceStatus { get; set; } = DeviceStatus.Normal;

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 设备参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 连接状态
    /// </summary>
    public enum ConnectionStatus
    {
        /// <summary>
        /// 已断开连接
        /// </summary>
        Disconnected = 0,

        /// <summary>
        /// 正在连接
        /// </summary>
        Connecting = 1,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 2,

        /// <summary>
        /// 连接错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 正在重新连接
        /// </summary>
        Reconnecting = 4
    }

    /// <summary>
    /// 设备状态
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 故障
        /// </summary>
        Fault = 2
    }
}
