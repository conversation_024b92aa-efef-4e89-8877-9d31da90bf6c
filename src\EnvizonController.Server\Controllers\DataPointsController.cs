using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DataPointsController : ControllerBase
    {
        private readonly IDataCollectionAppService _dataCollectionService;
        private readonly ILogger<DataPointsController> _logger;

        public DataPointsController(IDataCollectionAppService dataCollectionService, ILogger<DataPointsController> logger)
        {
            _dataCollectionService = dataCollectionService;
            _logger = logger;
        }

        /// <summary>
        /// 获取测试运行的数据点
        /// </summary>
        /// <param name="testId">测试ID</param>
        /// <param name="queryParams">查询参数</param>
        /// <returns>数据点列表</returns>
        [HttpGet("by-test/{testId}")]
        [ProducesResponseType(typeof(PagedResultDto<DataPointDto>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<DataPointDto>>> GetTestRunDataPoints(
            long testId,
            [FromQuery] DataPointQueryParams queryParams)
        {
            try
            {
                var pagedResultDto = await _dataCollectionService.GetTestRunDataPointsPagedAsync(
                    testId, 
                    queryParams.Page, 
                    queryParams.PageSize);

                return Ok(pagedResultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试运行 {TestId} 的数据点时出错", testId);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取数据点时发生错误");
            }
        }

        /// <summary>
        /// 获取设备的数据点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="queryParams">查询参数</param>
        /// <returns>数据点列表</returns>
        [HttpGet("by-device/{deviceId}")]
        [ProducesResponseType(typeof(PagedResultDto<DataPointDto>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<DataPointDto>>> GetDeviceDataPoints(
            long deviceId,
            [FromQuery] DataPointQueryParams queryParams)
        {
            try
            {
                var dataPoints = await _dataCollectionService.GetDeviceDataPointsAsync(
                    deviceId, 
                    queryParams.StartTime, 
                    queryParams.EndTime, 
                    queryParams.Page, 
                    queryParams.PageSize);
                
                var dataPointsList = dataPoints.ToList();
                
                // 手动映射到DTO
                var dataPointDtos = dataPointsList.Select(d => new DataPointDto
                {
                    Id = d.Id,
                    TestId = d.TestId,
                    Timestamp = d.Timestamp,
                    Values = d.Values.Select(v => new ValueDataDto
                    {
                        ProtocolItemIndex = v.ProtocolIndex,
                        Value = v.Value,

                    }).ToList()
                }).ToList();

                return Ok(new PagedResultDto<DataPointDto>
                {
                    Items = dataPointDtos,
                    TotalCount = dataPointsList.Count,
                    Page = queryParams.Page,
                    PageSize = queryParams.PageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 的数据点时出错", deviceId);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取数据点时发生错误");
            }
        }

        /// <summary>
        /// 获取数据点详情
        /// </summary>
        /// <param name="id">数据点ID</param>
        /// <returns>数据点详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(DataPointDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<DataPointDto>> GetDataPoint(long id)
        {
            try
            {
                var dataPoint = await _dataCollectionService.GetDataPointDetailAsync(id);
                if (dataPoint == null)
                {
                    return NotFound($"ID为{id}的数据点不存在");
                }

                // 手动映射到DTO
                var dataPointDto = new DataPointDto
                {
                    Id = dataPoint.Id,
                    TestId = dataPoint.TestId,
                    Timestamp = dataPoint.Timestamp,
                    Values = dataPoint.Values.Select(v => new ValueDataDto
                    {
                        ProtocolItemIndex = v.ProtocolIndex,
                        Value = v.Value
                    }).ToList()
                };

                return Ok(dataPointDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据点 {DataPointId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取数据点详情时发生错误");
            }
        }

        /// <summary>
        /// 获取数据点聚合统计
        /// </summary>
        /// <param name="aggregateParams">聚合参数</param>
        /// <returns>聚合数据</returns>
        [HttpGet("aggregate")]
        [ProducesResponseType(typeof(IEnumerable<AggregateDataPointDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public ActionResult<IEnumerable<AggregateDataPointDto>> GetAggregateData([FromQuery] DataAggregateParams aggregateParams)
        {
            try
            {
                // 注意：目前应用服务层可能没有实现数据聚合的功能
                // 这里是一个示例实现，实际应用中需要扩展IDataCollectionAppService

                // 验证参数
                if (aggregateParams.EndTime <= aggregateParams.StartTime)
                {
                    return BadRequest("结束时间必须晚于开始时间");
                }

                // 返回未实现的消息
                return StatusCode(StatusCodes.Status501NotImplemented, 
                    "数据聚合功能尚未实现，这是API的设计预留");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "聚合数据时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取聚合数据时发生错误");
            }
        }
    }
}