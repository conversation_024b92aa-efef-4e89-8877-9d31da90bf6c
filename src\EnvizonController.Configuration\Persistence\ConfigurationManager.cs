﻿using System.Text.Json;
using EnvizonController.Configuration.Models;

namespace EnvizonController.Configuration.Persistence;

public class ConfigurationManager<T> : IConfigurationManager<T> where T : DefaultConfig, new()
{
    private static readonly JsonSerializerOptions SerializerOptions = new()
    {
        WriteIndented = true
        // PropertyNameCaseInsensitive = true, // 如需更宽松可打开
    };

    public async Task<T> LoadAsync(string filePath)
    {
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"未找到配置文件: {filePath}，返回默认配置。");
            return new T();
        }

        try
        {
            using var openStream = File.OpenRead(filePath);
            var loadedConfig = await JsonSerializer.DeserializeAsync<T>(openStream, SerializerOptions);
            if (loadedConfig == null)
            {
                Console.WriteLine($"配置文件为空或无效: {filePath}，返回默认配置。");
                return new T();
            }

            // 可选择性触发迁移
            // loadedConfig.MigrateIfNecessary();
            return loadedConfig;
        }
        catch (JsonException jsonEx)
        {
            Console.WriteLine($"反序列化出错: {filePath} - {jsonEx.Message}，返回默认配置。");
            return new T();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载配置出错: {filePath} - {ex.Message}，返回默认配置。");
            return new T();
        }
    }

    public async Task SaveAsync(T configuration, string filePath)
    {
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            using var createStream = File.Create(filePath);
            await JsonSerializer.SerializeAsync(createStream, configuration, SerializerOptions);
            await createStream.FlushAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存配置出错: {filePath} - {ex.Message}");
            throw;
        }
    }

    // 简化同步版本（推荐优先用异步）
    public T Load(string filePath)
    {
        if (!File.Exists(filePath)) return new T();
        try
        {
            var json = File.ReadAllText(filePath);
            var loadedConfig = JsonSerializer.Deserialize<T>(json, SerializerOptions);
            if (loadedConfig == null) return new T();
            // loadedConfig.MigrateIfNecessary(); // 可选
            return loadedConfig;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"同步加载/反序列化配置出错: {filePath} - {ex.Message}，返回默认配置。");
            return new T();
        }
    }

    public void Save(T configuration, string filePath)
    {
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            var json = JsonSerializer.Serialize(configuration, SerializerOptions);
            File.WriteAllText(filePath, json);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存配置出错: {filePath} - {ex.Message}");
            throw;
        }
    }
}