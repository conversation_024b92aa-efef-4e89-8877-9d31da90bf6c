namespace EnvizonController.Domain.Enums
{
    /// <summary>
    /// 设备状态
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 正常
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 故障
        /// </summary>
        Fault = 2
    }

    /// <summary>
    /// 连接状态
    /// </summary>
    public enum ConnectionStatus
    {
        /// <summary>
        /// 已断开连接
        /// </summary>
        Disconnected = 0,

        /// <summary>
        /// 正在连接
        /// </summary>
        Connecting = 1,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 2,

        /// <summary>
        /// 连接错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 正在重新连接
        /// </summary>
        Reconnecting = 4
    }

    /// <summary>
    /// 腔体状态
    /// </summary>
    public enum ChamberStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized = 0,

        /// <summary>
        /// 运行中
        /// </summary>
        Running = 1,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 2,

        /// <summary>
        /// 已停止
        /// </summary>
        Stopped = 3,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 4
    }
}
