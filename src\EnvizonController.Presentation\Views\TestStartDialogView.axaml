<UserControl
    x:Class="EnvizonController.Presentation.Views.TestStartDialogView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="using:EnvizonController.Presentation.Models"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="600"
    d:DesignWidth="600"
    x:DataType="vm:TestStartDialogViewModel"
    mc:Ignorable="d">

    <Border
        Width="600"
        Margin="15"
        Padding="20"
        Background="#1A2A3A"
        BorderBrush="#0DF0FF"
        BorderThickness="1"
        CornerRadius="8">

        <Grid RowDefinitions="Auto,*,Auto">
            <!--  标题  -->
            <TextBlock
                Grid.Row="0"
                Margin="0,0,0,20"
                HorizontalAlignment="Center"
                Classes="h2 glow-primary font-cyber"
                Text="启动测试" />

            <!--  内容区域  -->
            <Grid Grid.Row="1">
                <!--  第一步：基本信息  -->
                <StackPanel IsVisible="{Binding IsBasicInfoStepVisible}" Spacing="15">
                    <!--  步骤标题  -->
                    <TextBlock
                        Margin="0,0,0,10"
                        Classes="h3 glow-primary font-cyber"
                        Text="第一步：测试基本信息" />

                    <!--  测试名称  -->
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,5"
                            Classes="gray"
                            Text="测试名称 *" />
                        <TextBox
                            Height="35"
                            Background="#252335"
                            BorderBrush="#0DF0FF"
                            BorderThickness="1"
                            CornerRadius="4"
                            Foreground="White"
                            Text="{Binding TestConfiguration.TestName}"
                            Watermark="请输入测试名称" />
                    </StackPanel>

                    <!--  测试备注  -->
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,5"
                            Classes="gray"
                            Text="测试备注" />
                        <TextBox
                            Height="80"
                            AcceptsReturn="True"
                            Background="#252335"
                            BorderBrush="#0DF0FF"
                            BorderThickness="1"
                            CornerRadius="4"
                            Foreground="White"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            Text="{Binding TestConfiguration.TestRemark}"
                            TextWrapping="Wrap"
                            Watermark="请输入测试备注（可选）" />
                    </StackPanel>

                    <!--  错误消息  -->
                    <TextBlock
                        Foreground="#FF4556"
                        IsVisible="{Binding ErrorMessage, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                        Text="{Binding ErrorMessage}"
                        TextWrapping="Wrap" />
                </StackPanel>

                <!--  第二步：测试模式配置  -->
                <ScrollViewer IsVisible="{Binding IsConfigStepVisible}" VerticalScrollBarVisibility="Auto">
                    <StackPanel Spacing="15">
                        <!--  步骤标题  -->
                        <TextBlock
                            Margin="0,0,0,10"
                            Classes="h3 glow-primary font-cyber"
                            Text="第二步：测试模式配置" />

                        <!--  测试模式选择  -->
                        <StackPanel>
                            <TextBlock
                                Margin="0,0,0,10"
                                Classes="gray"
                                Text="测试模式" />
                            <TabControl Background="Transparent" SelectedIndex="{Binding TestConfiguration.TestMode}">

                                <!--  定值模式  -->
                                <TabItem Header="定值模式">
                                    <StackPanel Margin="10" Spacing="15">
                                        <!--  启动模式  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="启动模式" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                DisplayMemberBinding="{Binding DisplayName}"
                                                ItemsSource="{Binding StartupModes}"
                                                SelectedValue="{Binding TestConfiguration.FixedValueConfig.StartupMode}"
                                                SelectedValueBinding="{Binding Mode}" />
                                        </StackPanel>

                                        <!--  温度设定值  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="温度设定值 (°C)" />
                                            <NumericUpDown
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                FormatString="F1"
                                                Increment="0.1"
                                                Maximum="200"
                                                Minimum="-100"
                                                Value="{Binding TestConfiguration.FixedValueConfig.Temperature}" />
                                        </StackPanel>

                                        <!--  湿度设定值  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="湿度设定值 (%RH)" />
                                            <NumericUpDown
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                FormatString="F0"
                                                Increment="1"
                                                Maximum="100"
                                                Minimum="0"
                                                Value="{Binding TestConfiguration.FixedValueConfig.Humidity}" />
                                        </StackPanel>
                                    </StackPanel>
                                </TabItem>

                                <!--  定时模式  -->
                                <TabItem Header="定时模式">
                                    <StackPanel Margin="10" Spacing="15">
                                        <!--  启动模式  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="启动模式" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                DisplayMemberBinding="{Binding DisplayName}"
                                                ItemsSource="{Binding StartupModes}"
                                                SelectedValue="{Binding TestConfiguration.TimerConfig.StartupMode}"
                                                SelectedValueBinding="{Binding Mode}" />
                                        </StackPanel>

                                        <!--  温度设定值  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="温度设定值 (°C)" />
                                            <NumericUpDown
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                FormatString="F1"
                                                Increment="0.1"
                                                Maximum="200"
                                                Minimum="-100"
                                                Value="{Binding TestConfiguration.TimerConfig.Temperature}" />
                                        </StackPanel>

                                        <!--  湿度设定值  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="湿度设定值 (%RH)" />
                                            <NumericUpDown
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                FormatString="F0"
                                                Increment="1"
                                                Maximum="100"
                                                Minimum="0"
                                                Value="{Binding TestConfiguration.TimerConfig.Humidity}" />
                                        </StackPanel>

                                        <!--  开始计时范围  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="开始计时范围" />
                                            <TextBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Foreground="White"
                                                Text="{Binding TestConfiguration.TimerConfig.TimingRange}"
                                                Watermark="请输入计时范围描述" />
                                        </StackPanel>

                                        <!--  定时设置  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,10"
                                                Classes="gray"
                                                Text="定时设置" />
                                            <Grid ColumnDefinitions="*,Auto,*,Auto,*,Auto,*">
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock
                                                        Margin="0,0,0,5"
                                                        Classes="gray"
                                                        Text="天" />
                                                    <NumericUpDown
                                                        Height="35"
                                                        Background="#252335"
                                                        BorderBrush="#0DF0FF"
                                                        BorderThickness="1"
                                                        CornerRadius="4"
                                                        FormatString="F0"
                                                        Maximum="365"
                                                        Minimum="0"
                                                        Value="{Binding TestConfiguration.TimerConfig.Days}" />
                                                </StackPanel>
                                                <TextBlock
                                                    Grid.Column="1"
                                                    Margin="10,25,10,0"
                                                    VerticalAlignment="Center"
                                                    Classes="gray"
                                                    Text="天" />
                                                <StackPanel Grid.Column="2">
                                                    <TextBlock
                                                        Margin="0,0,0,5"
                                                        Classes="gray"
                                                        Text="时" />
                                                    <NumericUpDown
                                                        Height="35"
                                                        Background="#252335"
                                                        BorderBrush="#0DF0FF"
                                                        BorderThickness="1"
                                                        CornerRadius="4"
                                                        FormatString="F0"
                                                        Maximum="23"
                                                        Minimum="0"
                                                        Value="{Binding TestConfiguration.TimerConfig.Hours}" />
                                                </StackPanel>
                                                <TextBlock
                                                    Grid.Column="3"
                                                    Margin="10,25,10,0"
                                                    VerticalAlignment="Center"
                                                    Classes="gray"
                                                    Text="时" />
                                                <StackPanel Grid.Column="4">
                                                    <TextBlock
                                                        Margin="0,0,0,5"
                                                        Classes="gray"
                                                        Text="分" />
                                                    <NumericUpDown
                                                        Height="35"
                                                        Background="#252335"
                                                        BorderBrush="#0DF0FF"
                                                        BorderThickness="1"
                                                        CornerRadius="4"
                                                        FormatString="F0"
                                                        Maximum="59"
                                                        Minimum="0"
                                                        Value="{Binding TestConfiguration.TimerConfig.Minutes}" />
                                                </StackPanel>
                                                <TextBlock
                                                    Grid.Column="5"
                                                    Margin="10,25,10,0"
                                                    VerticalAlignment="Center"
                                                    Classes="gray"
                                                    Text="分" />
                                            </Grid>
                                        </StackPanel>
                                    </StackPanel>
                                </TabItem>

                                <!--  程式模式  -->
                                <TabItem Header="程式模式">
                                    <StackPanel Margin="10" Spacing="15">
                                        <!--  启动模式  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="启动模式" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                DisplayMemberBinding="{Binding DisplayName}"
                                                ItemsSource="{Binding StartupModes}"
                                                SelectedValue="{Binding TestConfiguration.ProgramConfig.StartupMode}"
                                                SelectedValueBinding="{Binding Mode}" />
                                        </StackPanel>

                                        <!--  开始计时范围  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="开始计时范围" />
                                            <TextBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Foreground="White"
                                                Text="{Binding TestConfiguration.ProgramConfig.TimingRange}"
                                                Watermark="请输入计时范围描述" />
                                        </StackPanel>

                                        <!--  当前程式  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="当前程式 *" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                ItemsSource="{Binding Programs}"
                                                SelectedValue="{Binding TestConfiguration.ProgramConfig.SelectedProgramId}"
                                                SelectedValueBinding="{Binding Id}">
                                                <ComboBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <StackPanel Orientation="Horizontal">
                                                            <TextBlock Text="{Binding Name}" />
                                                            <TextBlock
                                                                Margin="10,0,0,0"
                                                                Classes="gray"
                                                                Text="{Binding Id, StringFormat='(ID: {0})'}" />
                                                        </StackPanel>
                                                    </DataTemplate>
                                                </ComboBox.ItemTemplate>
                                            </ComboBox>
                                            <!--  加载状态指示器  -->
                                            <ProgressBar
                                                Height="4"
                                                Margin="0,5,0,0"
                                                IsIndeterminate="True"
                                                IsVisible="{Binding IsLoading}" />
                                        </StackPanel>
                                    </StackPanel>
                                </TabItem>

                                <!--  链接模式  -->
                                <TabItem Header="链接模式">
                                    <StackPanel Margin="10" Spacing="15">
                                        <!--  启动模式  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="启动模式" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                DisplayMemberBinding="{Binding DisplayName}"
                                                ItemsSource="{Binding StartupModes}"
                                                SelectedValue="{Binding TestConfiguration.LinkConfig.StartupMode}"
                                                SelectedValueBinding="{Binding Mode}" />
                                        </StackPanel>

                                        <!--  开始计时范围  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="开始计时范围" />
                                            <TextBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Foreground="White"
                                                Text="{Binding TestConfiguration.LinkConfig.TimingRange}"
                                                Watermark="请输入计时范围描述" />
                                        </StackPanel>

                                        <!--  当前链接  -->
                                        <StackPanel>
                                            <TextBlock
                                                Margin="0,0,0,5"
                                                Classes="gray"
                                                Text="当前链接 *" />
                                            <ComboBox
                                                Height="35"
                                                Background="#252335"
                                                BorderBrush="#0DF0FF"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                ItemsSource="{Binding ProgramLinks}"
                                                SelectedValue="{Binding TestConfiguration.LinkConfig.SelectedLinkId}"
                                                SelectedValueBinding="{Binding Id}">
                                                <ComboBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <StackPanel Orientation="Horizontal">
                                                            <TextBlock Text="{Binding Name}" />
                                                            <TextBlock
                                                                Margin="10,0,0,0"
                                                                Classes="gray"
                                                                Text="{Binding Id, StringFormat='(ID: {0})'}" />
                                                        </StackPanel>
                                                    </DataTemplate>
                                                </ComboBox.ItemTemplate>
                                            </ComboBox>
                                            <!--  加载状态指示器  -->
                                            <ProgressBar
                                                Height="4"
                                                Margin="0,5,0,0"
                                                IsIndeterminate="True"
                                                IsVisible="{Binding IsLoading}" />
                                        </StackPanel>
                                    </StackPanel>
                                </TabItem>
                            </TabControl>
                        </StackPanel>

                        <!--  错误消息  -->
                        <TextBlock
                            Foreground="#FF4556"
                            IsVisible="{Binding ErrorMessage, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                            Text="{Binding ErrorMessage}"
                            TextWrapping="Wrap" />
                    </StackPanel>
                </ScrollViewer>
            </Grid>

            <!--  按钮区域  -->
            <StackPanel
                Grid.Row="2"
                Margin="0,20,0,0"
                HorizontalAlignment="Right"
                Orientation="Horizontal"
                Spacing="10">

                <!--  上一步按钮  -->
                <Button
                    Width="80"
                    Height="35"
                    Background="Transparent"
                    BorderBrush="#FFA500"
                    BorderThickness="1"
                    Classes="glow2"
                    Command="{Binding PreviousStepCommand}"
                    CornerRadius="4"
                    IsVisible="{Binding IsConfigStepVisible}">
                    <TextBlock
                        Classes="font-cyber"
                        Foreground="#FFA500"
                        Text="上一步" />
                </Button>

                <!--  取消按钮  -->
                <Button
                    Width="80"
                    Height="35"
                    Background="Transparent"
                    BorderBrush="#FF4556"
                    BorderThickness="1"
                    Classes="glow2"
                    Command="{Binding CancelCommand}"
                    CornerRadius="4">
                    <TextBlock
                        Classes="font-cyber"
                        Foreground="#FF4556"
                        Text="取消" />
                </Button>

                <!--  下一步按钮  -->
                <Button
                    Width="80"
                    Height="35"
                    Background="Transparent"
                    BorderBrush="#0DF0FF"
                    BorderThickness="1"
                    Classes="glow2"
                    Command="{Binding NextStepCommand}"
                    CornerRadius="4"
                    IsVisible="{Binding IsBasicInfoStepVisible}">
                    <TextBlock
                        Classes="font-cyber"
                        Foreground="#0DF0FF"
                        Text="下一步" />
                </Button>

                <!--  开始测试按钮  -->
                <Button
                    Width="100"
                    Height="35"
                    Background="Transparent"
                    BorderBrush="#00FF00"
                    BorderThickness="1"
                    Classes="glow2"
                    Command="{Binding StartTestCommand}"
                    CornerRadius="4"
                    IsVisible="{Binding IsConfigStepVisible}">
                    <TextBlock
                        Classes="font-cyber"
                        Foreground="#00FF00"
                        Text="开始测试" />
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>