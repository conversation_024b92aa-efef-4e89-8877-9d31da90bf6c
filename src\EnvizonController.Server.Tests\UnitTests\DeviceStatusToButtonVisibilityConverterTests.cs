using System.Globalization;
using EnvizonController.Presentation.Converters;
using EnvizonController.Shared.Enums;
using Xunit;

namespace EnvizonController.Server.Tests.UnitTests
{
    /// <summary>
    /// DeviceStatusToButtonVisibilityConverter 单元测试
    /// </summary>
    public class DeviceStatusToButtonVisibilityConverterTests
    {
        private readonly DeviceStatusToButtonVisibilityConverter _converter;

        public DeviceStatusToButtonVisibilityConverterTests()
        {
            _converter = new DeviceStatusToButtonVisibilityConverter();
        }

        [Theory]
        [InlineData(DeviceOperatingStatus.Stopped, "start", true)]
        [InlineData(DeviceOperatingStatus.Running, "start", false)]
        [InlineData(DeviceOperatingStatus.Paused, "start", false)]
        public void Convert_StartButton_ReturnsCorrectVisibility(DeviceOperatingStatus status, string parameter, bool expected)
        {
            // Act
            var result = _converter.Convert(status, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(DeviceOperatingStatus.Stopped, "pause", false)]
        [InlineData(DeviceOperatingStatus.Running, "pause", true)]
        [InlineData(DeviceOperatingStatus.Paused, "pause", false)]
        public void Convert_PauseButton_ReturnsCorrectVisibility(DeviceOperatingStatus status, string parameter, bool expected)
        {
            // Act
            var result = _converter.Convert(status, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(DeviceOperatingStatus.Stopped, "resume", false)]
        [InlineData(DeviceOperatingStatus.Running, "resume", false)]
        [InlineData(DeviceOperatingStatus.Paused, "resume", true)]
        public void Convert_ResumeButton_ReturnsCorrectVisibility(DeviceOperatingStatus status, string parameter, bool expected)
        {
            // Act
            var result = _converter.Convert(status, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(DeviceOperatingStatus.Stopped, "stop", false)]
        [InlineData(DeviceOperatingStatus.Running, "stop", true)]
        [InlineData(DeviceOperatingStatus.Paused, "stop", true)]
        public void Convert_StopButton_ReturnsCorrectVisibility(DeviceOperatingStatus status, string parameter, bool expected)
        {
            // Act
            var result = _converter.Convert(status, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void Convert_InvalidStatus_ReturnsTrue()
        {
            // Act
            var result = _converter.Convert("invalid", typeof(bool), "start", CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(true, result);
        }

        [Fact]
        public void Convert_NullParameter_ReturnsTrue()
        {
            // Act
            var result = _converter.Convert(DeviceOperatingStatus.Running, typeof(bool), null, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(true, result);
        }

        [Fact]
        public void Convert_UnknownParameter_ReturnsTrue()
        {
            // Act
            var result = _converter.Convert(DeviceOperatingStatus.Running, typeof(bool), "unknown", CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(true, result);
        }

        [Theory]
        [InlineData("START")]
        [InlineData("Start")]
        [InlineData("start")]
        public void Convert_CaseInsensitiveParameter_WorksCorrectly(string parameter)
        {
            // Act
            var result = _converter.Convert(DeviceOperatingStatus.Stopped, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(true, result);
        }

        [Theory]
        [InlineData("RESUME")]
        [InlineData("Resume")]
        [InlineData("resume")]
        public void Convert_CaseInsensitiveResumeParameter_WorksCorrectly(string parameter)
        {
            // Act
            var result = _converter.Convert(DeviceOperatingStatus.Paused, typeof(bool), parameter, CultureInfo.InvariantCulture);

            // Assert
            Assert.Equal(true, result);
        }
    }
}
