<Window x:Class="EnvizonController.Modbus.SerialTester.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EnvizonController.Modbus.SerialTester"
        xmlns:vm="clr-namespace:EnvizonController.Modbus.SerialTester.ViewModels"
        xmlns:views="clr-namespace:EnvizonController.Modbus.SerialTester.Views"
        mc:Ignorable="d"
        Title="Modbus 串口测试工具" Height="700" Width="1000"
        d:DataContext="{d:DesignInstance Type=vm:MainViewModel}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <ToolBar Grid.Row="0">
            <Button Content="保存配置" Command="{Binding SaveConfigCommand}" Margin="5"/>
            <Button Content="加载配置" Command="{Binding LoadConfigCommand}" Margin="5"/>
            <Separator/>
            <Button Content="清空日志" Command="{Binding ClearLogCommand}" Margin="5"/>
            <Separator/>
            <Button Content="关于" Command="{Binding ShowAboutCommand}" Margin="5"/>
        </ToolBar>

        <!-- 主内容区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧配置面板 -->
            <TabControl Grid.Column="0" Margin="5">
                <TabItem Header="串口配置">
                    <views:SerialConfigView x:Name="SerialConfigView"/>
                </TabItem>
                <TabItem Header="Modbus配置">
                    <StackPanel Margin="10">
                        <GroupBox Header="Modbus设置" Margin="0,5">
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Label Grid.Row="0" Grid.Column="0" Content="传输类型:" Margin="0,5"/>
                                <ComboBox Grid.Row="0" Grid.Column="1"
                                          ItemsSource="{Binding TransportTypes}"
                                          SelectedItem="{Binding SelectedTransportType}"
                                          Margin="5"/>

                                <Label Grid.Row="1" Grid.Column="0" Content="从站地址:" Margin="0,5"/>
                                <TextBox Grid.Row="1" Grid.Column="1"
                                         Text="{Binding SlaveAddress}"
                                         Margin="5"/>

                                <Label Grid.Row="2" Grid.Column="0" Content="响应超时(ms):" Margin="0,5"/>
                                <TextBox Grid.Row="2" Grid.Column="1"
                                         Text="{Binding ResponseTimeout}"
                                         Margin="5"/>

                                <Label Grid.Row="3" Grid.Column="0" Content="重试次数:" Margin="0,5"/>
                                <TextBox Grid.Row="3" Grid.Column="1"
                                         Text="{Binding RetryCount}"
                                         Margin="5"/>

                                <Label Grid.Row="4" Grid.Column="0" Content="重试延迟(ms):" Margin="0,5"/>
                                <TextBox Grid.Row="4" Grid.Column="1"
                                         Text="{Binding RetryDelay}"
                                         Margin="5"/>
                            </Grid>
                        </GroupBox>

                        <Button Content="{Binding ConnectionButtonText}"
                                Command="{Binding ToggleConnectionCommand}"
                                Background="{Binding ConnectionButtonBackground}"
                                Foreground="White"
                                Height="40" Margin="0,10"/>
                    </StackPanel>
                </TabItem>
            </TabControl>

            <!-- 右侧测试面板 -->
            <TabControl Grid.Column="1" Margin="5">
                <TabItem Header="Modbus测试">
                    <views:ModbusTestView x:Name="ModbusTestView"/>
                </TabItem>
                <TabItem Header="通信日志">
                    <views:LogView x:Name="LogView"/>
                </TabItem>
            </TabControl>
        </Grid>

        <!-- 底部状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ConnectionStatus}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
