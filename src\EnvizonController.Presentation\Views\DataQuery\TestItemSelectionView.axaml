<UserControl
    x:Class="EnvizonController.Presentation.Views.DataQuery.TestItemSelectionView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="800"
    d:DesignWidth="320"
    x:DataType="vm:DataQueryViewModel"
    mc:Ignorable="d">
    
    <Border
        Margin="5,5,3,5"
        BorderThickness="1"
        Classes="cyber-border">

        <Grid RowDefinitions="Auto,*">
            <!--  顶部搜索与筛选区  -->
            <Border
                Grid.Row="0"
                Padding="10"
                Background="#0B0A1A"
                BorderBrush="#224F5F"
                BorderThickness="0,0,0,1">
                <Grid RowDefinitions="Auto,Auto">
                    <!--  标题区  -->
                    <TextBlock
                        Grid.Row="0"
                        Margin="0,0,0,10"
                        Classes="primary"
                        FontSize="18"
                        FontWeight="Bold"
                        Text="测试项" />

                    <!--  搜索与筛选区  -->
                    <Grid Grid.Row="1" RowDefinitions="Auto,Auto">
                        <!--  主搜索框  -->
                        <DockPanel Grid.Row="0">
                            <Button
                                Margin="5,0,0,5"
                                VerticalAlignment="Stretch"
                                Classes="glow2"
                                Command="{Binding ApplyTestItemFiltersCommand}"
                                DockPanel.Dock="Right"
                                ToolTip.Tip="精确搜索">
                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                    <TextBlock
                                        Width="16"
                                        Height="16"
                                        VerticalAlignment="Center"
                                        Classes="font-icon"
                                        Text="&#xf002;" />
                                    <TextBlock
                                        Margin="3,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="搜索" />
                                </StackPanel>
                            </Button>
                            <TextBox
                                Grid.Row="0"
                                Margin="0,0,0,5"
                                DockPanel.Dock="Left"
                                Text="{Binding TestItemSearchText}"
                                Watermark="搜索测试项名称..." />
                        </DockPanel>


                        <!--  测试项高级筛选按钮  -->
                        <Grid Grid.Row="1" ColumnDefinitions="*,Auto">
                            <Button
                                Grid.Column="0"
                                HorizontalAlignment="Left"
                                Classes="glow2"
                                Command="{Binding ToggleTestItemFilterPanelCommand}">
                                <Button.Flyout>
                                    <Flyout
                                        Flyout.FlyoutPresenterClasses="wider"
                                        IsOpen="{Binding IsTestItemFilterPanelOpen}"
                                        Placement="BottomEdgeAlignedLeft"
                                        ShowMode="Transient">
                                        <Border
                                            Padding="10"
                                            Background="#0B0A1A"
                                            BorderBrush="#224F5F"
                                            BorderThickness="1"
                                            CornerRadius="4">
                                            <StackPanel>
                                                <!--  测试项名称精确筛选  -->
                                                <TextBlock
                                                    Margin="0,0,0,5"
                                                    Foreground="White"
                                                    Text="测试项名称" />
                                                <TextBox
                                                    Margin="0,0,0,10"
                                                    Text="{Binding TestItemNameFilter}"
                                                    Watermark="输入精确名称..." />

                                                <!--  执行时间范围  -->
                                                <TextBlock
                                                    Margin="0,0,0,5"
                                                    Foreground="White"
                                                    Text="执行时间范围" />
                                                <Grid ColumnDefinitions="*,Auto,*">
                                                    <DatePicker
                                                        Grid.Column="0"
                                                        Background="#1A2A3A"
                                                        BorderBrush="#0DF0FF"
                                                        Foreground="White"
                                                        SelectedDate="{Binding TestItemStartDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                    <TextBlock
                                                        Grid.Column="1"
                                                        Margin="5,0"
                                                        VerticalAlignment="Center"
                                                        Foreground="White"
                                                        Text="至" />
                                                    <DatePicker
                                                        Grid.Column="2"
                                                        Background="#1A2A3A"
                                                        BorderBrush="#0DF0FF"
                                                        Foreground="White"
                                                        SelectedDate="{Binding TestItemEndDate, Converter={StaticResource DateTimeOffsetToDateTimeConverter}}" />
                                                </Grid>

                                                <!--  筛选按钮区  -->
                                                <StackPanel
                                                    Margin="0,10,0,0"
                                                    HorizontalAlignment="Right"
                                                    Orientation="Horizontal">
                                                    <Button
                                                        Margin="0,0,10,0"
                                                        Classes="accent"
                                                        Command="{Binding ApplyTestItemFiltersCommand}"
                                                        Content="应用筛选" />
                                                    <Button
                                                        Classes="secondary"
                                                        Command="{Binding ClearTestItemFiltersCommand}"
                                                        Content="清除筛选" />
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Flyout>
                                </Button.Flyout>
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Width="16"
                                            Height="16"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            Classes="font-icon"
                                            Text="&#xf0b0;" />
                                        <TextBlock Text="高级筛选" />
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button
                                Grid.Column="1"
                                Padding="6"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Classes="clear"
                                Command="{Binding ClearTestItemFiltersCommand}"
                                IsVisible="{Binding HasActiveTestItemFilters}"
                                ToolTip.Tip="清除所有筛选">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Classes="font-icon"
                                    FontSize="21"
                                    Foreground="Red"
                                    Text="&#xf00d;" />
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </Border>

            <!--  测试项列表与高级筛选面板  -->
            <Grid Grid.Row="1" RowDefinitions="Auto,*">
                <!--  测试项列表  -->
                <ListBox
                    Grid.Row="1"
                    Margin="5,5,5,0"
                    Classes="cyber"
                    ItemsSource="{Binding TestItems}"
                    SelectedItem="{Binding SelectedTestItem}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5" ColumnDefinitions="*,Auto">
                                <TextBlock
                                    Grid.Column="0"
                                    VerticalAlignment="Center"
                                    Foreground="White"
                                    Text="{Binding Name}" />
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Grid>
        </Grid>
    </Border>
</UserControl> 