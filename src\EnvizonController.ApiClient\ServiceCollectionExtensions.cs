using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Services;
using EnvizonController.ApiClient.Services.Implementations;
using EnvizonController.Configuration.Models;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using Polly.Extensions.Http;
using System;

namespace EnvizonController.ApiClient
{
    /// <summary>
    /// 扩展方法，用于API客户端服务的依赖注入配置
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加API客户端服务
        /// </summary>
        public static IServiceCollection AddApiClient(this IServiceCollection services, ApiClientSettings settings = null)
        {
            // 使用默认设置如果未提供
            settings ??= new ApiClientSettings();

            // 配置HttpClient工厂
            services.AddHttpClient("ApiClient", client =>
            {
                client.BaseAddress = new Uri(settings.BaseUrl);
                client.Timeout = TimeSpan.FromMilliseconds(settings.TimeoutMs);
            })
            // 添加重试策略
            .AddPolicyHandler(GetRetryPolicy(settings.RetryCount, settings.RetryDelayMs));

            // 注册HTTP客户端适配器
            services.AddScoped<IHttpClient, HttpClientAdapter>();
            
            // 注册API服务实现
            services.AddScoped<IDeviceApiService, DeviceApiService>();
            services.AddScoped<IAlarmApiService, AlarmApiService>();
            services.AddScoped<IDataPointApiService, DataPointApiService>();
            services.AddScoped<ITestItemApiService, TestItemApiService>();
            services.AddScoped<IDeviceTestApiService, DeviceTestApiService>();

            return services;
        }

        /// <summary>
        /// 创建HTTP请求重试策略
        /// </summary>
        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int retryCount, int retryDelayMs)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(
                    retryCount,
                    retryAttempt => TimeSpan.FromMilliseconds(retryDelayMs * Math.Pow(2, retryAttempt - 1))
                );
        }
    }
}