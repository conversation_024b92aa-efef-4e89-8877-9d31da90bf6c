# EnvizonController Modbus 测试项目

本项目包含 EnvizonController Modbus 通信框架的单元测试和集成测试。

## 测试范围

测试覆盖以下主要组件：

1. **协议测试**
   - CRC/LRC 校验算法
   - 功能码实现
   - 异常处理

2. **帧构建测试**
   - RTU 帧构建与解析
   - ASCII 帧构建与解析
   - TCP 帧构建与解析

3. **通道测试**
   - 通道连接与断开
   - 数据发送与接收
   - 超时处理

4. **客户端测试**
   - 读写操作
   - 重试机制
   - 异常处理

## 运行测试

### 使用 Visual Studio

1. 在解决方案资源管理器中右键点击 `EnvizonController.Modbus.Tests` 项目
2. 选择 "运行测试"

### 使用命令行

```bash
# 进入测试项目目录
cd Modbus\EnvizonController.Modbus.Tests

# 运行所有测试
dotnet test

# 运行特定测试类
dotnet test --filter "FullyQualifiedName~ModbusCrcTests"

# 运行特定测试方法
dotnet test --filter "Name=CalculateCrc_WithValidData_ReturnsCorrectCrc"

# 生成测试覆盖率报告
dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=cobertura
```

## 测试结构

- **Mocks/** - 包含用于测试的模拟实现
  - `MockModbusChannel.cs` - 模拟 Modbus 通道实现

- **Protocol/Utils/** - 协议工具类测试
  - `ModbusCrcTests.cs` - CRC 校验算法测试
  - `ModbusLrcTests.cs` - LRC 校验算法测试

- **Protocol/Frames/** - 帧构建器测试
  - `ModbusRtuFrameBuilderTests.cs` - RTU 帧构建器测试
  - `ModbusAsciiFrameBuilderTests.cs` - ASCII 帧构建器测试
  - `ModbusTcpFrameBuilderTests.cs` - TCP 帧构建器测试
  - `ModbusFrameBuilderFactoryTests.cs` - 帧构建器工厂测试

- **Channels/** - 通道测试
  - `ModbusChannelTests.cs` - 通道基础功能测试

- **Client/** - 客户端测试
  - `ModbusClientTests.cs` - 客户端功能测试

## 添加新测试

1. 在适当的目录下创建新的测试类
2. 使用 xUnit 的 `[Fact]` 或 `[Theory]` 特性标记测试方法
3. 按照 AAA (Arrange-Act-Assert) 模式编写测试

示例：

```csharp
[Fact]
public void MyTest_WithSpecificCondition_ExpectedBehavior()
{
    // Arrange - 准备测试数据和环境
    var sut = new SystemUnderTest();
    
    // Act - 执行被测试的操作
    var result = sut.DoSomething();
    
    // Assert - 验证结果
    Assert.Equal(expectedValue, result);
}
```

## 测试最佳实践

1. 每个测试方法只测试一个行为或功能点
2. 测试方法名称应清晰描述测试场景和预期结果
3. 使用 AAA 模式组织测试代码
4. 避免测试之间的依赖
5. 使用模拟对象隔离被测试组件
6. 测试边界条件和异常情况
7. 保持测试简单、可读和可维护
