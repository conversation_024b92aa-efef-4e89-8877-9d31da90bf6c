<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.72" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnvizonController.Modbus.Protocol\EnvizonController.Modbus.Protocol.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Abstractions\EnvizonController.Modbus.Abstractions.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Adapters.Desktop\EnvizonController.Modbus.Adapters.Desktop.csproj" />
    <ProjectReference Include="..\EnvizonController.Modbus.Client\EnvizonController.Modbus.Client.csproj" />
  </ItemGroup>

</Project>
