using System.Collections.Concurrent;
using EnvizonController.Application.DataCollection.Protocol;
using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces.Cache;
using EnvizonController.Application.Services;
using EnvizonController.Configuration;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Domain.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace EnvizonController.Application.DataCollection;

/// <summary>
/// 数据采集协调器的实现。
/// </summary>
public class DataCollectionCoordinator : IDataCollectionCoordinator
{
    private readonly DataBufferService _dataBufferService;
    private readonly ILogger _logger;
    private readonly IModbusDeviceServiceCache _modbusDeviceServiceCache;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    /// <summary>
    /// 初始化 <see cref="DataCollectionCoordinator" /> 类的新实例。
    /// </summary>
    /// <param name="serviceScopeFactory">服务范围工厂。</param>
    /// <param name="logger">日志记录器。</param>
    /// <param name="modbusDeviceServiceCache">Modbus设备服务缓存。</param>
    /// <param name="dataBufferService">数据缓冲服务。</param>
    /// <param name="configuration">配置对象。</param>
    public DataCollectionCoordinator(
        IServiceScopeFactory serviceScopeFactory,
        ILogger logger,
        IModbusDeviceServiceCache modbusDeviceServiceCache,
        DataBufferService dataBufferService,
        IConfiguration configuration)
    {
        _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _modbusDeviceServiceCache = modbusDeviceServiceCache ?? throw new ArgumentNullException(nameof(modbusDeviceServiceCache));
        _dataBufferService = dataBufferService ?? throw new ArgumentNullException(nameof(dataBufferService));
    }

    /// <inheritdoc />
    public ConcurrentDictionary<long, Device> ActiveCollectingDevices { get; } = new();

    /// <inheritdoc />
    public Task<bool> StartTestCollectionAsync(Device device)
    {
        if (device == null)
        {
            _logger.Warning("尝试为 null 设备启动测试采集。");
            return Task.FromResult(false);
        }

        var deviceId = device.Id;
        try
        {
            if (ActiveCollectingDevices.TryAdd(deviceId, device))
                _logger.Information("已启动设备 {DeviceId} 的测试数据采集。", deviceId);
            else
                _logger.Information("设备 {DeviceId} 的测试数据采集已处于活动状态。", deviceId);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "为设备 {DeviceId} 启动测试数据采集时出错：{Message}", deviceId, ex.Message);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public async Task<bool> StopTestCollectionAsync(long deviceId)
    {
        try
        {
            if (ActiveCollectingDevices.TryRemove(deviceId, out _))
                _logger.Information("已停止设备 {DeviceId} 的测试数据采集。", deviceId);
            else
                _logger.Warning("尝试停止采集时，设备 {DeviceId} 不在活动测试采集列表中。", deviceId);

            using var scope = _serviceScopeFactory.CreateScope();
            var testService = scope.ServiceProvider.GetRequiredService<ITestRunDomainService>();
            await testService.StopTestAsync(deviceId);
            _logger.Information("设备 {DeviceId} 的测试运行状态已更新为已停止。", deviceId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "为设备 {DeviceId} 停止测试数据采集时出错：{Message}", deviceId, ex.Message);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> CollectDataAsync(Device device)
    {
        if (device == null)
        {
            _logger.Warning("尝试为 null 设备采集数据。");
            return false;
        }

        var deviceId = device.Id;
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dataCollectionService = scope.ServiceProvider.GetRequiredService<IDataCollectionDomainService>();
            var protocolRepository = scope.ServiceProvider.GetRequiredService<IProtocolRepository>();

            var modbusDeviceService = _modbusDeviceServiceCache.GetOrCreate(device);
            if (!modbusDeviceService.IsConnected)
            {
                _logger.Information("设备 {DeviceId} 的 Modbus 服务未连接。正在尝试连接。", deviceId);
                await modbusDeviceService.ConnectAsync();
            }

            if (device.Protocol == null && device.ProtocolId > 0)
                device.Protocol = await protocolRepository.GetByIdAsync(device.ProtocolId);

            if (device.Protocol == null)
            {
                _logger.Error("无法为设备 {DeviceId} 采集数据：未关联或加载协议。", deviceId);
                await dataCollectionService.CreateCollectionAlarmAsync(
                    deviceId,
                    "设备未关联或加载协议，无法采集数据。"
                );
                return false;
            }

            try
            {
                var protocolDataCollector = new ProtocolDataCollector(scope.ServiceProvider);
                var groupedData = await protocolDataCollector.CollectGroupedDataAsync(
                    device.Protocol,
                    device,
                    modbusDeviceService
                );

                var collectionDetailsDTO = groupedData.CreateDeviceCollectionDetailsDTO();

                if (device.TestId.HasValue)
                {
                    _dataBufferService.BufferTestDataPoint(deviceId, groupedData.TestDataPoint);
                    _logger.Debug("已为设备 {DeviceId} (TestId: {TestId}) 缓冲 TestDataPoint。", deviceId,
                        device.TestId.Value);
                }

                _dataBufferService.BufferCollectionDetails(deviceId, collectionDetailsDTO);
                
                // 处理报警数据
                if (collectionDetailsDTO.Alarms != null && collectionDetailsDTO.Alarms.Any())
                {
                    _dataBufferService.BufferAlarms(deviceId, collectionDetailsDTO.Alarms);
                    _logger.Debug("已为设备 {DeviceId} 缓冲 {Count} 个报警数据。", deviceId, collectionDetailsDTO.Alarms.Count);
                }
                else
                {
                    _dataBufferService.ClearDeviceAlarms(deviceId);
                }
                _logger.Debug("已成功为设备 {DeviceId} 采集并缓冲数据。", deviceId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "为设备 {DeviceId} 进行基于协议的数据采集时出错：{Message}", deviceId, ex.Message);
                await dataCollectionService.CreateCollectionAlarmByDeviceIdAsync(
                    deviceId,
                    $"协议数据采集错误：{ex.Message}"
                );
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "为设备 {DeviceId} 执行数据采集过程中发生一般错误：{Message}", deviceId, ex.Message);
            try
            {
                using var errorScope = _serviceScopeFactory.CreateScope();
                var dataCollectionService = errorScope.ServiceProvider.GetRequiredService<IDataCollectionDomainService>();
                await dataCollectionService.CreateCollectionAlarmByDeviceIdAsync(
                    deviceId,
                    $"常规数据采集错误：{ex.Message}"
                );
            }
            catch (Exception alarmEx)
            {
                _logger.Error(alarmEx, "数据采集错误后，为设备 {DeviceId} 创建采集告警失败。", deviceId);
            }

            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> StartDeviceCollectionAsync(long deviceId)
    {
        try
        {
            Device device;
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();
                device = await deviceService.GetDeviceByIdAsync(deviceId);
            }

            if (device == null)
            {
                _logger.Warning("无法启动数据采集：未找到 ID 为 {DeviceId} 的设备。", deviceId);
                return false;
            }

            if (ActiveCollectingDevices.TryAdd(deviceId, device))
                _logger.Information("已启动设备 {DeviceId} 的常规数据采集。", deviceId);
            else
                _logger.Information("设备 {DeviceId} 的常规数据采集已处于活动状态。", deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "为设备 {DeviceId} 启动常规数据采集时出错：{Message}", deviceId, ex.Message);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> StopDeviceCollectionAsync(long deviceId)
    {
        try
        {
            if (ActiveCollectingDevices.TryRemove(deviceId, out _))
            {
                _logger.Information("已停止设备 {DeviceId} 的常规数据采集。", deviceId);
                await _dataBufferService.ForceProcessAllAsync();
                _logger.Information("停止设备 {DeviceId} 的采集后，已强制处理缓冲数据。", deviceId);
            }
            else
            {
                _logger.Warning("尝试停止采集时，设备 {DeviceId} 不在活动常规采集列表中。", deviceId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "为设备 {DeviceId} 停止常规数据采集时出错：{Message}", deviceId, ex.Message);
            return false;
        }
    }
}