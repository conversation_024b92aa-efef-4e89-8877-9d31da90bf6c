using System;
using System.Collections.Generic;

namespace EnvizonController.DataProcessing.Implementation
{
    /// <summary>
    /// 数据处理上下文实现
    /// </summary>
    public class DataProcessingContext : IDataProcessingContext
    {
        /// <summary>
        /// 获取或设置原始数据
        /// </summary>
        public object RawData { get; set; }
        
        /// <summary>
        /// 获取或设置处理后的数据
        /// </summary>
        public object ProcessedData { get; set; }
        
        /// <summary>
        /// 获取或设置数据源标识
        /// </summary>
        public string SourceId { get; set; }
        
        /// <summary>
        /// 获取或设置数据类型
        /// </summary>
        public string DataType { get; set; }
        
        /// <summary>
        /// 获取或设置时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 获取或设置元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; }
        
        /// <summary>
        /// 获取处理状态
        /// </summary>
        public ProcessingStatus Status { get; set; }
        
        /// <summary>
        /// 获取处理错误信息
        /// </summary>
        public List<string> Errors { get; private set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public DataProcessingContext()
        {
            Timestamp = DateTime.Now;
            Metadata = new Dictionary<string, object>();
            Errors = new List<string>();
            Status = ProcessingStatus.NotProcessed;
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="rawData">原始数据</param>
        /// <param name="sourceId">数据源标识</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="metadata">元数据</param>
        public DataProcessingContext(object rawData, string sourceId = null, string dataType = null, Dictionary<string, object> metadata = null)
            : this()
        {
            RawData = rawData;
            ProcessedData = rawData;
            SourceId = sourceId;
            DataType = dataType;
            
            if (metadata != null)
            {
                foreach (var item in metadata)
                {
                    Metadata[item.Key] = item.Value;
                }
            }
        }
        
        /// <summary>
        /// 添加错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error))
            {
                Errors.Add(error);
            }
        }
        
        /// <summary>
        /// 创建上下文的副本
        /// </summary>
        /// <returns>上下文副本</returns>
        public IDataProcessingContext Clone()
        {
            var clone = new DataProcessingContext
            {
                RawData = RawData,
                ProcessedData = ProcessedData,
                SourceId = SourceId,
                DataType = DataType,
                Timestamp = Timestamp,
                Status = Status,
                Metadata = new Dictionary<string, object>(Metadata),
                Errors = new List<string>(Errors)
            };
            
            return clone;
        }
    }
}
