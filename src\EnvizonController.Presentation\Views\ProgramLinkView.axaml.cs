using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using EnvizonController.Presentation.ViewModels;
using EnvizonController.Presentation.ViewModels.Program;
using CommunityToolkit.Mvvm.DependencyInjection;

namespace EnvizonController.Presentation.Views
{
    public partial class ProgramLinkView : UserControl
    {
        public ProgramLinkView()
        {
            InitializeComponent();
            // 绑定ViewModel
            
        }

    
        /// <summary>
        /// 处理错误消息点击事件
        /// </summary>
        private void OnErrorMessageTapped(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProgramLinkViewModel viewModel)
            {
                viewModel.ToggleErrorExpandCommand.Execute(null);
            }
        }
    }
}