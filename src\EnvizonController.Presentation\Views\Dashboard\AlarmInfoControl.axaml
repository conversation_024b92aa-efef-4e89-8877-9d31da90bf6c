<UserControl
    x:Class="EnvizonController.Presentation.Views.Dashboard.AlarmInfoControl"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dashboard="clr-namespace:EnvizonController.Presentation.ViewModels.Dashboard"
    xmlns:dataTemplates="clr-namespace:EnvizonController.Presentation.DataTemplates"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="200"
    d:DesignWidth="300"
    x:DataType="viewModels:DashboardViewModel"
    mc:Ignorable="d">
    <Border Classes="cyber-noGlow-border card">
        <Grid RowDefinitions="Auto,Auto,Auto,*">
            <Grid.Resources>
                <!--  错误报警项模板  -->
                <DataTemplate x:Key="ErrorAlarmTemplate" DataType="{x:Type dashboard:AlarmItemViewModel}">
                    <Border
                        Margin="0,3"
                        Padding="6"
                        Background="#2A0E17"
                        BorderBrush="#5000EFFF"
                        BorderThickness="1"
                        CornerRadius="4"
                        Effect="{DynamicResource GlowEffect2}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="18" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <!--  报警类型  -->
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Classes="font-icon"
                                FontSize="13"
                                Foreground="#FF3333"
                                Text="&#xf06a;" />
                            <TextBlock
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                FontSize="14"
                                Foreground="#FF3333"
                                Text="{Binding Message}" />
                            <!--  时间  -->
                            <TextBlock
                                Grid.Column="2"
                                Foreground="#CCCCCC"
                                Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" />
                        </Grid>
                    </Border>
                </DataTemplate>

                <!--  警告报警项模板  -->
                <DataTemplate x:Key="WarningAlarmTemplate" DataType="{x:Type dashboard:AlarmItemViewModel}">
                    <Border
                        Margin="0,3"
                        Padding="6"
                        Background="#251713"
                        BorderBrush="#5000EFFF"
                        BorderThickness="1"
                        CornerRadius="4"
                        Effect="{DynamicResource GlowEffect2}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="18" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <!--  报警类型  -->
                            <TextBlock
                                Grid.Column="0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Classes="font-icon"
                                FontSize="13"
                                Foreground="#FFAA33"
                                Text="&#x21;" />
                            <TextBlock
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                FontSize="14"
                                Foreground="#FFAA33"
                                Text="{Binding Message}" />
                            <!--  时间  -->
                            <TextBlock
                                Grid.Column="2"
                                Foreground="#CCCCCC"
                                Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" />
                        </Grid>
                    </Border>
                </DataTemplate>

                <!--  信息报警项模板 (调整为更紧凑的样式)  -->
                <DataTemplate x:Key="InfoAlarmTemplate" DataType="{x:Type dashboard:AlarmItemViewModel}">
                    <Border
                        Margin="0,3"
                        Padding="10"
                        Background="{StaticResource CyberpunkInfo}"
                        CornerRadius="4">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  报警类型图标  -->
                            <Border
                                Grid.Column="0"
                                Width="24"
                                Height="24"
                                Margin="0,0,8,0"
                                Background="{StaticResource CyberpunkBackground}">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="14"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource CyberpunkForeground}"
                                    Text="ⓘ" />
                            </Border>

                            <!--  报警类型  -->
                            <TextBlock
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                FontSize="11"
                                Foreground="{StaticResource CyberpunkWarning}"
                                Text="{Binding Message}" />

                            <!--  时间  -->
                            <TextBlock
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Foreground="{StaticResource CyberpunkInfo}"
                                Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" />
                        </Grid>
                    </Border>
                </DataTemplate>
                <!--  已解除报警项模板  -->
                <DataTemplate x:Key="ResolvedAlarmTemplate" DataType="{x:Type dashboard:AlarmItemViewModel}">
                    <Border
                        Margin="0,3"
                        Padding="6"
                        Background="#333333"
                        BorderBrush="#555555"
                        BorderThickness="1"
                        CornerRadius="4">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <!--  报警类型  -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="13"
                                    Foreground="#00DD88"
                                    Text="✓" />
                                <TextBlock
                                    Margin="4,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="14"
                                    Foreground="#DDDDDD"
                                    Text="{Binding Message}" />
                            </StackPanel>
                            <!--  时间  -->
                            <TextBlock
                                Grid.Column="1"
                                Foreground="#CCCCCC"
                                Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" />
                        </Grid>
                    </Border>
                </DataTemplate>
            </Grid.Resources>

            <StackPanel Grid.Row="0" Orientation="Horizontal">
                <TextBlock
                    Margin="0,0,5,15"
                    VerticalAlignment="Center"
                    Classes="h4 primary font-icon"
                    Text="&#xf071;" />
                <TextBlock
                    VerticalAlignment="Center"
                    Classes="h4 primary bottom"
                    Text="报警信息" />
            </StackPanel>

            <!--  报警信息列表  -->
            <ScrollViewer Grid.Row="3" IsVisible="{Binding CurrentDevice.Alarms.Count}">
                <ItemsControl ItemsSource="{Binding CurrentDevice.Alarms}">
                    <ItemsControl.DataTemplates>
                        <dataTemplates:AlarmItemViewModelTemplateSelector
                            ErrorTemplate="{StaticResource ErrorAlarmTemplate}"
                            InfoTemplate="{StaticResource InfoAlarmTemplate}"
                            ResolvedTemplate="{StaticResource ResolvedAlarmTemplate}"
                            WarningTemplate="{StaticResource WarningAlarmTemplate}" />
                    </ItemsControl.DataTemplates>
                </ItemsControl>
            </ScrollViewer>
            <DockPanel
                Grid.Row="3"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                IsVisible="{Binding !CurrentDevice.Alarms.Count}">
                <TextBlock
                    Margin="0,6,0,0"
                    HorizontalAlignment="Center"
                    Classes="gray2"
                    DockPanel.Dock="Bottom"
                    FontWeight="Black">
                    无报警
                </TextBlock>
                <TextBlock
                    Classes="font-icon gray2"
                    DockPanel.Dock="Top"
                    FontSize="91"
                    Text="&#xf132;" />
            </DockPanel>
        </Grid>
    </Border>

</UserControl>
