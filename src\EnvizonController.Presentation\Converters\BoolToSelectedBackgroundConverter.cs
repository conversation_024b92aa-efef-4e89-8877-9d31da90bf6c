using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将布尔值转换为选中/非选中状态的背景色
    /// </summary>
    public class BoolToSelectedBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return isSelected
                    ? new SolidColorBrush(Color.Parse("#0F1E2C")) // 选中状态的背景色（深蓝色）
                    : new SolidColorBrush(Color.Parse("Transparent")); // 非选中状态透明背景
            }

            return new SolidColorBrush(Color.Parse("Transparent"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}