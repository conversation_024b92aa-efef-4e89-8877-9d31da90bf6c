using EnvizonController.Modbus.Abstractions.Interfaces;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Application.DeviceCommands.Interfaces;
using EnvizonController.Application.DeviceCommands.Models;
using EnvizonController.Shared.Enums;
using EnvizonController.Modbus.Client.Extensions;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace EnvizonController.Application.DeviceCommands.Services
{
    /// <summary>
    /// Modbus指令执行器实现
    /// </summary>
    public class ModbusCommandExecutor : ICommandExecutor
    {
        private readonly ILogger<ModbusCommandExecutor> _logger;

        public ModbusCommandExecutor(ILogger<ModbusCommandExecutor> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 执行读取指令
        /// </summary>
        public async Task<CommandResult<T>> ExecuteReadCommandAsync<T>(IModbusClient client, ProtocolItem protocolItem, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult<T>();

            try
            {
                _logger.LogDebug("执行读取指令，协议项: {ItemName}, 地址: {Address}, 类型: {DataType}", 
                    protocolItem.DisplayName, protocolItem.Address, protocolItem.DataType);

                if (!IsCommandTypeSupported(protocolItem, CommandType.Read))
                {
                    result.IsSuccess = false;
                    result.Message = $"协议项 '{protocolItem.DisplayName}' 不支持读取操作";
                    result.ErrorCode = "UNSUPPORTED_OPERATION";
                    return result;
                }

                object rawValue = await ReadProtocolItemValueAsync(client, protocolItem, cancellationToken);
                
                // 应用缩放因子和偏移量
                if (rawValue is double doubleValue)
                {
                    doubleValue = doubleValue * protocolItem.ScaleFactor + protocolItem.Offset;
                    rawValue = doubleValue;
                }

                T convertedValue = ConvertValue<T>(rawValue, protocolItem);

                result.IsSuccess = true;
                result.Data = convertedValue;
                result.Message = "读取成功";
                result.Metadata.Add("原始值", rawValue);
                result.Metadata.Add("单位", protocolItem.Unit);
                result.Metadata.Add("地址", protocolItem.Address);

                _logger.LogDebug("读取成功，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, convertedValue);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"读取失败: {ex.Message}";
                result.ErrorCode = "READ_ERROR";
                _logger.LogError(ex, "读取协议项失败，协议项: {ItemName}", protocolItem.DisplayName);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }
        /// <summary>
        /// 格式化要写入的数据
        /// </summary>
        /// <param name="value">原始值</param>
        /// <param name="protocolItem">协议项</param>
        /// <returns>格式化后的寄存器值数组</returns>
        private ushort[] FormatDataForWriting(object value, ProtocolItem protocolItem)
        {
            // 应用逆向缩放因子和偏移量
            object writeValue = value;
            if (writeValue is double doubleValue && protocolItem.ScaleFactor != 0)
            {
                doubleValue = (doubleValue - protocolItem.Offset) / protocolItem.ScaleFactor;
                writeValue = doubleValue;
            }

            switch (protocolItem.DataType)
            {
                case DataType.UInt16:
                    return new ushort[] { Convert.ToUInt16(writeValue) };

                case DataType.Int16:
                    return new ushort[] { (ushort)Convert.ToInt16(writeValue) }; // 转为ushort用于Modbus传输

                case DataType.Bool:
                    return new ushort[] { Convert.ToBoolean(writeValue) ? (ushort)1 : (ushort)0 };

                case DataType.Enum:
                    string stringValue = writeValue.ToString()!;
                    var reverseMappings = protocolItem.GetReverseValueMappings();
                    if (reverseMappings != null && reverseMappings.TryGetValue(stringValue, out int rawValue))
                    {
                        return new ushort[] { (ushort)rawValue };
                    }
                    throw new ArgumentException($"无法将枚举字符串 '{stringValue}' 映射到原始值");

                case DataType.BitField16:
                    // 值应该是ushort直接值或Dictionary<string, bool>
                    if (writeValue is ushort directUshort)
                        return new ushort[] { directUshort };

                    if (writeValue is Dictionary<string, bool> bitValues && protocolItem.BitMappings != null)
                    {
                        ushort resultRegister = 0;
                        var reverseBitMappings = protocolItem.BitMappings.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);
                        foreach (var bitName in bitValues.Keys)
                        {
                            if (reverseBitMappings.TryGetValue(bitName, out int bitIndex) && bitValues[bitName])
                            {
                                resultRegister |= (ushort)(1 << bitIndex);
                            }
                        }
                        return new ushort[] { resultRegister };
                    }
                    throw new ArgumentException("对于BitField16类型，请提供ushort值或Dictionary<string, bool>");

                // 多寄存器类型（需要处理字节顺序）
                case DataType.UInt32:
                    return EndianHelper.SplitUInt32(Convert.ToUInt32(writeValue),
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Int32:
                    return EndianHelper.SplitInt32(Convert.ToInt32(writeValue),
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Float:
                    return EndianHelper.SplitFloat(Convert.ToSingle(writeValue),
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Double:
                    return EndianHelper.SplitDouble(Convert.ToDouble(writeValue),
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.BitField32:
                    // BitField32处理类似于UInt32，但可能需要进一步的位操作
                    if (writeValue is uint directUint)
                        return EndianHelper.SplitUInt32(directUint,
                            ConvertWordOrderType(protocolItem.WordOrderType));

                    if (writeValue is Dictionary<string, bool> bitField32Values && protocolItem.BitMappings != null)
                    {
                        uint resultValue = 0;
                        var reverseBitMappings = protocolItem.BitMappings.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);
                        foreach (var bitName in bitField32Values.Keys)
                        {
                            if (reverseBitMappings.TryGetValue(bitName, out int bitIndex) && bitField32Values[bitName])
                            {
                                resultValue |= (uint)(1 << bitIndex);
                            }
                        }
                        return EndianHelper.SplitUInt32(resultValue,
                            ConvertWordOrderType(protocolItem.WordOrderType));
                    }
                    throw new ArgumentException("对于BitField32类型，请提供uint值或Dictionary<string, bool>");

                case DataType.String:
                    return EndianHelper.StringToRegisters(writeValue.ToString()!,
                        protocolItem.WordOrderType == WordOrderType.BigEndian ||
                        protocolItem.WordOrderType == WordOrderType.BigEndianWordSwap ?
                        EndianHelper.ByteOrder.BigEndian : EndianHelper.ByteOrder.LittleEndian);

                default:
                    throw new NotSupportedException($"不支持写入数据类型 {protocolItem.DataType}");
            }
        }



        /// <summary>
        /// 执行自定义指令
        /// </summary>
        public async Task<CommandResult> ExecuteCustomCommandAsync(IModbusClient client, CustomCommand command, 
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult();

            try
            {
                _logger.LogDebug("执行自定义指令，名称: {CommandName}, 功能码: {FunctionCode}, 地址: {Address}", 
                    command.Name, command.FunctionCode, command.Address);

                // 这里可以根据功能码执行相应的Modbus操作
                // 暂时返回成功状态，具体实现根据需求来定
                result.IsSuccess = true;
                result.Message = "自定义指令执行成功";
                result.Metadata.Add("指令名称", command.Name);
                result.Metadata.Add("功能码", command.FunctionCode);
                result.Metadata.Add("地址", command.Address);

                await Task.Delay(100, cancellationToken); // 模拟执行时间
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"自定义指令执行失败: {ex.Message}";
                result.ErrorCode = "CUSTOM_COMMAND_ERROR";
                _logger.LogError(ex, "自定义指令执行失败，指令: {CommandName}", command.Name);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 验证并转换数据类型
        /// </summary>
        public T ConvertValue<T>(object value, ProtocolItem protocolItem)
        {
            try
            {
                if (value == null)
                    return default(T)!;

                Type targetType = typeof(T);
                Type? nullableType = Nullable.GetUnderlyingType(targetType);
                if (nullableType != null)
                    targetType = nullableType;

                // 如果类型已经匹配，直接返回
                if (value.GetType() == targetType || targetType.IsAssignableFrom(value.GetType()))
                    return (T)value;

                // 尝试类型转换
                if (targetType == typeof(string))
                    return (T)(object)value.ToString()!;

                if (targetType == typeof(bool))
                {
                    if (value is double doubleVal)
                        return (T)(object)(doubleVal != 0);
                    if (value is int intVal)
                        return (T)(object)(intVal != 0);
                    return (T)Convert.ChangeType(value, targetType);
                }

                return (T)Convert.ChangeType(value, targetType);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "类型转换失败，值: {Value}, 目标类型: {TargetType}", value, typeof(T));
                return default(T)!;
            }
        }

        /// <summary>
        /// 检查协议项是否支持指定操作
        /// </summary>
        public bool IsCommandTypeSupported(ProtocolItem protocolItem, CommandType commandType)
        {
            return commandType switch
            {
                CommandType.Read => true, // 所有协议项都支持读取
                CommandType.Write => protocolItem.IsWritable,
                CommandType.Custom => true, // 自定义指令暂时都支持
                _ => false
            };
        }

        /// <summary>
        /// 读取协议项值（根据数据类型）
        /// </summary>
        private async Task<object> ReadProtocolItemValueAsync(IModbusClient client, ProtocolItem protocolItem, 
            CancellationToken cancellationToken)
        {
            byte slaveId = protocolItem.UnitId;
            ushort address = protocolItem.Address;

            return protocolItem.DataType switch
            {
                DataType.Float => await client.ReadFloatAsync(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Double => await client.ReadDoubleAsync(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Int16 => await client.ReadInt16Async(slaveId, address, cancellationToken),
                DataType.UInt16 => await client.ReadUInt16Async(slaveId, address, cancellationToken),
                DataType.Int32 => await client.ReadInt32Async(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.UInt32 => await client.ReadUInt32Async(slaveId, address, 
                    EndianHelper.WordOrder.BigEndian, cancellationToken),
                DataType.Bool => (await client.ReadCoilsAsync(slaveId, address, 1, cancellationToken))[0],
                DataType.String => await client.ReadStringAsync(slaveId, address, 
                    protocolItem.RegisterCount, EndianHelper.ByteOrder.BigEndian, cancellationToken),
                _ => throw new NotSupportedException($"不支持的数据类型: {protocolItem.DataType}")
            };
        }
        /// <summary>
        /// 解析原始寄存器数据
        /// </summary>
        /// <param name="rawRegisters">原始寄存器数据</param>
        /// <param name="protocolItem">协议项</param>
        /// <returns>解析后的数据</returns>
        private object ParseRawData(ushort[] rawRegisters, ProtocolItem protocolItem)
        {
            if (rawRegisters == null || rawRegisters.Length == 0) return null;

            switch (protocolItem.DataType)
            {
                case DataType.UInt16:
                    return rawRegisters[0];
                case DataType.Int16:
                    return (short)rawRegisters[0];

                case DataType.Enum:
                    int rawEnumValue = rawRegisters[0];
                    if (protocolItem.ValueMappings != null && protocolItem.ValueMappings.TryGetValue(rawEnumValue, out string enumString))
                    {
                        return enumString; // 返回 "启动", "暂停", "停止" 等映射值
                    }
                    return $"Unknown ({rawEnumValue})"; // 未知值处理

                case DataType.BitField16:
                    var bitField16Values = new Dictionary<string, bool>();
                    ushort val16 = rawRegisters[0];
                    if (protocolItem.BitMappings != null)
                    {
                        foreach (var mapping in protocolItem.BitMappings)
                        {
                            bitField16Values[mapping.Value] = (val16 & (1 << mapping.Key)) != 0;
                        }
                    }
                    return bitField16Values; // 返回如 {"手动": true, "故障": false} 的字典

                // --- 多寄存器类型（处理字节顺序） ---
                case DataType.UInt32:
                    if (rawRegisters.Length < 2) throw new ArgumentException("32位无符号整数需要至少2个寄存器");
                    return EndianHelper.CombineToUInt32(rawRegisters[0], rawRegisters[1],
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Int32:
                    if (rawRegisters.Length < 2) throw new ArgumentException("32位有符号整数需要至少2个寄存器");
                    return EndianHelper.CombineToInt32(rawRegisters[0], rawRegisters[1],
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Float:
                    if (rawRegisters.Length < 2) throw new ArgumentException("32位浮点数需要至少2个寄存器");
                    return EndianHelper.CombineToFloat(rawRegisters[0], rawRegisters[1],
                        ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.Double:
                    if (rawRegisters.Length < 4) throw new ArgumentException("64位浮点数需要至少4个寄存器");
                    return EndianHelper.CombineToDouble(rawRegisters, ConvertWordOrderType(protocolItem.WordOrderType));

                case DataType.BitField32:
                    if (rawRegisters.Length < 2) throw new ArgumentException("32位位域需要至少2个寄存器");
                    uint val32 = EndianHelper.CombineToUInt32(rawRegisters[0], rawRegisters[1],
                        ConvertWordOrderType(protocolItem.WordOrderType));
                    var bitField32Values = new Dictionary<string, bool>();
                    if (protocolItem.BitMappings != null)
                    {
                        foreach (var mapping in protocolItem.BitMappings)
                        {
                            bitField32Values[mapping.Value] = (val32 & (1U << mapping.Key)) != 0;
                        }
                    }
                    return bitField32Values;

                case DataType.String:
                    return EndianHelper.RegistersToString(rawRegisters,
                        protocolItem.WordOrderType == WordOrderType.BigEndian || protocolItem.WordOrderType == WordOrderType.BigEndianWordSwap ?
                        EndianHelper.ByteOrder.BigEndian : EndianHelper.ByteOrder.LittleEndian);

                case DataType.Bool:
                    return rawRegisters[0] != 0;

                default:
                    return $"不支持的数据类型: {protocolItem.DataType}";
            }
        }

        /// <summary>
        /// 将ProtocolItem的WordOrderType转换为EndianHelper的WordOrder
        /// </summary>
        private EndianHelper.WordOrder ConvertWordOrderType(WordOrderType wordOrderType)
        {
            return wordOrderType switch
            {
                WordOrderType.BigEndian => EndianHelper.WordOrder.BigEndian,
                WordOrderType.LittleEndian => EndianHelper.WordOrder.LittleEndian,
                WordOrderType.BigEndianWordSwap => EndianHelper.WordOrder.BigEndianWordSwap,
                WordOrderType.LittleEndianWordSwap => EndianHelper.WordOrder.LittleEndianWordSwap,
                _ => EndianHelper.WordOrder.BigEndian // 默认使用大端序
            };
        }

        /// <summary>
        /// 执行写入指令
        /// </summary>
        public async Task<CommandResult> ExecuteWriteCommandAsync<T>(IModbusClient client,byte slaveId, ProtocolItem protocolItem,
            T value, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CommandResult();

            try
            {
                _logger.LogDebug("执行写入指令，协议项: {ItemName}, 地址: {Address}, 值: {Value}",
                    protocolItem.DisplayName, protocolItem.Address, value);

                if (!IsCommandTypeSupported(protocolItem, CommandType.Write))
                {
                    result.IsSuccess = false;
                    result.Message = $"协议项 '{protocolItem.DisplayName}' 不支持写入操作";
                    result.ErrorCode = "UNSUPPORTED_OPERATION";
                    return result;
                }

                // 格式化要写入的数据
                ushort[] formattedData = FormatDataForWriting(value!, protocolItem);

                // 使用格式化后的数据执行写入操作
                bool success = await WriteProtocolItemValueAsync(client, slaveId, protocolItem, formattedData, cancellationToken);

                if (success)
                {
                    result.IsSuccess = true;
                    result.Message = "写入成功";
                    result.Metadata.Add("写入值", value!);
                    result.Metadata.Add("格式化后的值", formattedData);
                    result.Metadata.Add("地址", protocolItem.Address);

                    _logger.LogDebug("写入成功，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, value);
                }
                else
                {
                    result.IsSuccess = false;
                    result.Message = "写入操作返回失败";
                    result.ErrorCode = "WRITE_FAILED";
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"写入失败: {ex.Message}";
                result.ErrorCode = "WRITE_ERROR";
                _logger.LogError(ex, "写入协议项失败，协议项: {ItemName}, 值: {Value}", protocolItem.DisplayName, value);
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
                result.ExecutedAt = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// 写入协议项值
        /// </summary>
        private async Task<bool> WriteProtocolItemValueAsync(IModbusClient client, byte slaveId, ProtocolItem protocolItem,
            ushort[] values, CancellationToken cancellationToken)
        {
            ushort address = protocolItem.Address;

            // 对于单个寄存器的情况，使用WriteSingleRegister可能更高效
            if (values.Length == 1 && protocolItem.DataType != DataType.Bool)
            {
                return await client.WriteInt16Async(slaveId, address, (short)values[0], cancellationToken);
            }
            // 对于布尔值，使用WriteSingleCoil
            else if (protocolItem.DataType == DataType.Bool)
            {
                return await client.WriteSingleCoilAsync(slaveId, address, values[0] != 0, cancellationToken);
            }
            // 对于多个寄存器，使用WriteMultipleRegisters
            else
            {
                return await client.WriteMultipleRegistersAsync(slaveId, address, values, cancellationToken);
            }
        }
        /// <summary>
        /// 写入协议项值（根据数据类型）- 兼容原有方法
        /// </summary>
        private async Task<bool> WriteProtocolItemValueAsync(IModbusClient client, byte slaveId, ProtocolItem protocolItem,
            object value, CancellationToken cancellationToken)
        {
            // 使用新的格式化方法
            ushort[] formattedData = FormatDataForWriting(value, protocolItem);
            return await WriteProtocolItemValueAsync(client, slaveId ,protocolItem, formattedData, cancellationToken);
        }

    }
} 