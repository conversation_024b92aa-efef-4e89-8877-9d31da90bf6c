using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Converters
{
    public class AlarmLevelToForeground : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is AlarmLevel level)
            {
                return level switch
                {
                    AlarmLevel.Critical => new SolidColorBrush(Color.Parse("#FF3333")),  // 红色
                    AlarmLevel.Warning => new SolidColorBrush(Color.Parse("#FFAA33")),   // 琥珀色
                    AlarmLevel.Notice => new SolidColorBrush(Color.Parse("#33AAFF")),    // 蓝色
                    _ => new SolidColorBrush(Color.Parse("#AAAAAA")),                   // 灰色
                };
            }

            return new SolidColorBrush(Color.Parse("#FFFFFF"));
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 