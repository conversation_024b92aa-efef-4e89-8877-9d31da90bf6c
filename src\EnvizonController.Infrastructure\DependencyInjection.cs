using EnvizonController.Application.DataCollection;
using EnvizonController.Application.Devices;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Cache;
using EnvizonController.Domain.Repositories;
using EnvizonController.Infrastructure.Persistence;
using EnvizonController.Infrastructure.Persistence.Repositories;
using EnvizonController.Infrastructure.Services.DataCollection;
using EnvizonController.Infrastructure.Services.Devices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using EnvizonController.Domain.Aggregates;
using Serilog;

namespace EnvizonController.Infrastructure
{
    /// <summary>
    /// 依赖注入扩展方法
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// 添加基础设施服务
        /// </summary>
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册数据库上下文
            services.AddDbContext<AppDbContext>(options =>
            {
                // 从配置中获取连接字符串
                var connectionString = configuration.GetConnectionString("DefaultConnection");

                // 如果未指定连接字符串，则使用默认路径
                if (string.IsNullOrEmpty(connectionString))
                {
                    connectionString = System.IO.Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "envizon.db");
                }

                // 配置SQLite
                // 检查连接字符串是否已包含Data Source前缀
                if (connectionString.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase))
                {
                    options.UseSqlite(connectionString);
                }
                else
                {
                    options.UseSqlite($"Data Source={connectionString}");
                }
            });
            
            // 注册仓储              
            services.AddScoped<IAlarmRepository, AlarmRepository>();
            services.AddScoped<ITestItemRepository, TestItemRepository>();
            services.AddScoped<IDataPointRepository, TestDataPointRepository>();
            services.AddScoped<IDeviceRepository, DeviceRepository>();
            services.AddScoped<IProtocolRepository, ProtocolRepository>();
            services.AddScoped<IProgramRepository, ProgramRepository>();
            services.AddScoped<IProgramStepRepository, ProgramStepRepository>();
            services.AddScoped<IProgramLinkRepository, ProgramLinkRepository>();
            
            // 注册工作单元
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // 注册设备服务
            services.AddScoped<IDeviceService, DeviceService>();

            // 注册Modbus相关服务
            services.AddSingleton<IModbusChannelFactory>(provider => 
            {
                var logger = Log.ForContext<ModbusChannelFactory>();
                return new ModbusChannelFactory(logger, provider);
            });
            
            services.AddSingleton<ModbusDeviceServiceFactory>(provider => 
            {
                var logger = Log.ForContext<ModbusDeviceServiceFactory>();
                var channelFactory = provider.GetRequiredService<IModbusChannelFactory>();
                return new ModbusDeviceServiceFactory(logger, channelFactory, provider);
            });
            
            services.AddSingleton<IModbusDeviceServiceCache, ModbusDeviceServiceCache>();
            
            // 注册数据采集后台服务
            //services.AddHostedService<DataCollectionBackgroundService>();
            // services.AddSingleton<DataCollectionAppBackgroundService>();
            
            return services;
        }
    }
}
