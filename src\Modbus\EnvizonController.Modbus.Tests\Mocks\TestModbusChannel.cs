using EnvizonController.Modbus.Abstractions.Interfaces;

namespace EnvizonController.Modbus.Tests.Mocks
{
    /// <summary>
    /// 用于测试的特殊Modbus通道，不会清空响应队列
    /// </summary>
    public class TestModbusChannel : IModbusChannel
    {
        private bool _isConnected;
        private readonly Queue<byte[]> _responseQueue = new();
        private readonly List<byte[]> _sentData = new();

        /// <summary>
        /// 获取通道是否已连接
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 获取已发送的数据
        /// </summary>
        public IReadOnlyList<byte[]> SentData => _sentData;

        /// <summary>
        /// 添加模拟响应
        /// </summary>
        /// <param name="response">响应数据</param>
        public void EnqueueResponse(byte[] response)
        {
            _responseQueue.Enqueue(response);
        }

        /// <summary>
        /// 连接到通道
        /// </summary>
        public Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            _isConnected = true;
            return Task.CompletedTask;
        }

        /// <summary>
        /// 断开通道连接
        /// </summary>
        public Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            _isConnected = false;
            return Task.CompletedTask;
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        public Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (!_isConnected)
                throw new InvalidOperationException("通道未连接");

            _sentData.Add(data);
            return Task.CompletedTask;
        }

        /// <summary>
        /// 接收数据
        /// </summary>
        public Task<byte[]> ReceiveAsync(int timeout = 1000, CancellationToken cancellationToken = default)
        {
            if (!_isConnected)
                throw new InvalidOperationException("通道未连接");

            if (_responseQueue.Count == 0)
                throw new TimeoutException("接收超时");

            return Task.FromResult(_responseQueue.Dequeue());
        }

        /// <summary>
        /// 清空接收缓冲区 - 在测试中不执行任何操作
        /// </summary>
        public Task ClearReceiveBufferAsync()
        {
            // 在测试中不清空接收缓冲区
            return Task.CompletedTask;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 清理资源
            _responseQueue.Clear();
            _sentData.Clear();
            _isConnected = false;
        }
    }
}
