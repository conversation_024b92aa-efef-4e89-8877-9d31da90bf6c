# EnvizonController系统维护教程

![EnvizonController <PERSON><PERSON>](https://placeholder.com/logo)

## 目录

- [1. 系统概述](#1-系统概述)
  - [1.1 系统架构](#11-系统架构)
  - [1.2 核心功能模块](#12-核心功能模块)
  - [1.3 技术栈概览](#13-技术栈概览)
- [2. 环境搭建指南](#2-环境搭建指南)
  - [2.1 开发环境准备](#21-开发环境准备)
  - [2.2 开发工具安装](#22-开发工具安装)
  - [2.3 项目依赖配置](#23-项目依赖配置)
  - [2.4 环境验证](#24-环境验证)
- [3. 构建和部署流程](#3-构建和部署流程)
  - [3.1 构建过程](#31-构建过程)
  - [3.2 发布配置](#32-发布配置)
  - [3.3 部署步骤](#33-部署步骤)
  - [3.4 多平台部署说明](#34-多平台部署说明)
- [4. 模块维护指南](#4-模块维护指南)
  - [4.1 Modbus通信模块](#41-modbus通信模块)
  - [4.2 MQTT通信模块](#42-mqtt通信模块)
  - [4.3 数据采集与存储](#43-数据采集与存储)
  - [4.4 前端界面维护](#44-前端界面维护)
- [5. 常见问题排查与解决](#5-常见问题排查与解决)
  - [5.1 通信问题诊断](#51-通信问题诊断)
  - [5.2 数据异常处理](#52-数据异常处理)
  - [5.3 性能问题优化](#53-性能问题优化)
  - [5.4 日志分析与故障排除](#54-日志分析与故障排除)
  - [5.5 故障诊断决策树](#55-故障诊断决策树)
- [6. 系统扩展指南](#6-系统扩展指南)
  - [6.1 添加新设备类型](#61-添加新设备类型)
  - [6.2 集成新通信协议](#62-集成新通信协议)
  - [6.3 扩展数据分析功能](#63-扩展数据分析功能)
  - [6.4 UI定制与主题开发](#64-ui定制与主题开发)
- [7. 维护最佳实践](#7-维护最佳实践)
  - [7.1 定期维护计划](#71-定期维护计划)
  - [7.2 性能监控策略](#72-性能监控策略)
  - [7.3 安全最佳实践](#73-安全最佳实践)
  - [7.4 数据备份与恢复](#74-数据备份与恢复)
- [8. 附录](#8-附录)
  - [8.1 配置文件参考](#81-配置文件参考)
  - [8.2 API接口文档](#82-api接口文档)
  - [8.3 常用命令参考](#83-常用命令参考)
  - [8.4 重要依赖版本说明](#84-重要依赖版本说明)

## 1. 系统概述

### 1.1 系统架构

EnvizonController是一个基于.NET 9.0的跨平台监控与控制系统，采用模块化、分层设计，主要由以下组件构成：

- **表示层**：基于Avalonia UI框架的跨平台用户界面
- **应用服务层**：实现业务逻辑和协调各组件的交互
- **域模型层**：定义核心业务实体和业务规则
- **基础设施层**：提供数据访问、通信协议实现和第三方服务集成

系统采用Clean Architecture架构风格，确保核心业务逻辑不依赖于外部框架和技术，便于维护和扩展。

### 系统架构图

```mermaid
graph LR
    A[用户界面] --> B(应用服务层);
    B --> C(域模型层);
    B --> D(基础设施层);
    D --> E[Modbus通信];
    D --> F[MQTT通信];
    D --> G[数据存储];
    G --> H[SQLite数据库];
    D --> I[第三方服务];
```

### 数据流图

```mermaid
graph LR
    A[设备] --> B[Modbus/MQTT通信模块];
    B --> C[数据采集服务];
    C --> D[数据预处理];
    D --> E[数据存储];
    E --> F[SQLite数据库];
    E --> G[报警模块];
    G --> H[通知服务];
    E --> I[API服务器];
    I --> J[用户界面];
    J --> I;
    I --> E;
```

### 1.2 核心功能模块

EnvizonController系统包含以下核心功能模块：

1. **设备管理**：设备注册、配置、状态监控和生命周期管理
2. **通信协议**：支持Modbus（RTU/ASCII/TCP）和MQTT协议
3. **数据采集**：从各类设备采集数据，支持定时采集和事件触发采集
4. **数据存储**：使用SQLite数据库本地存储设备数据和历史记录
5. **报警管理**：阈值设置、报警触发、通知推送和报警记录
6. **用户界面**：仪表盘、设备监控、数据可视化和系统配置

### 1.3 技术栈概览

- **开发语言**: C# 12.0
- **框架**: .NET 9.0
- **UI框架**: Avalonia UI 11.0
- **数据访问**: Entity Framework Core 9.0
- **数据库**: SQLite
- **通信组件**: 
  - NModbus (Modbus实现)
  - MQTTnet (MQTT客户端/服务器)
- **日志框架**: Serilog
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **单元测试**: xUnit

## 2. 环境搭建指南

### 2.1 开发环境准备

#### Windows开发环境

1. **安装.NET 9.0 SDK**
   ```bash
   # 验证安装
   dotnet --version
   # 应显示9.x.x版本
   ```

2. **安装SQLite**
   - 下载并安装SQLite (https://www.sqlite.org/download.html)
   - 确保将SQLite添加到系统PATH环境变量

3. **配置环境变量**
   - DOTNET_ROOT: 指向.NET SDK安装路径
   - PATH: 包含.NET SDK和SQLite可执行文件路径

#### Linux开发环境

```bash
# 安装.NET 9.0 SDK
wget https://dot.net/v1/dotnet-install.sh
chmod +x dotnet-install.sh
./dotnet-install.sh --channel 9.0

# 安装SQLite
sudo apt-get update
sudo apt-get install sqlite3 libsqlite3-dev

# 配置环境变量
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$PATH:$DOTNET_ROOT:$DOTNET_ROOT/tools
```

### 2.2 开发工具安装

#### 推荐IDE

- **Visual Studio 2025** (Windows)
  - 安装.NET开发工作负载
  - 安装Avalonia扩展

- **Visual Studio Code** (跨平台)
  - 安装C#扩展
  - 安装Avalonia扩展
  - 安装SQLite扩展

- **JetBrains Rider** (跨平台)
  - 安装Avalonia插件

#### 版本控制工具

- Git 2.40.0或更高版本

### 2.3 项目依赖配置

克隆项目后，需要还原NuGet包依赖：

```bash
# 在项目根目录执行
dotnet restore
```

主要依赖包括：

- **通用依赖**:
  - Microsoft.Extensions.DependencyInjection
  - Microsoft.Extensions.Configuration
  - Serilog
  - AutoMapper

- **Modbus相关**:
  - NModbus
  - System.IO.Ports
  - System.IO.Pipelines

- **MQTT相关**:
  - MQTTnet
  - MQTTnet.Extensions.ManagedClient

- **UI相关**:
  - Avalonia
  - Avalonia.Desktop
  - CommunityToolkit.Mvvm

### 2.4 环境验证

执行以下命令验证开发环境是否正确配置：

```bash
# 编译解决方案
dotnet build

# 运行单元测试
dotnet test
```

如果编译和测试都成功，说明环境已正确配置。

## 3. 构建和部署流程

### 3.1 构建过程

#### 调试构建

```bash
# 在项目根目录执行
dotnet build
```

#### 发布构建

```bash
# 在项目根目录执行
dotnet publish -c Release
```

### 3.2 发布配置

EnvizonController支持多种发布配置，可在`src/Directory.Build.props`中设置全局属性：

```xml
<PropertyGroup>
  <Version>1.0.0</Version>
  <Authors>EnvizonTeam</Authors>
  <Company>EnvizonTech</Company>
  <Product>EnvizonController</Product>
  <!-- 其他属性 -->
</PropertyGroup>
```

#### 平台特定发布配置

**Windows Desktop**:
```bash
dotnet publish src/Platforms/EnvizonController.Desktop/EnvizonController.Desktop.csproj -c Release -r win-x64 --self-contained true
```

**Linux**:
```bash
dotnet publish src/Platforms/EnvizonController.Desktop/EnvizonController.Desktop.csproj -c Release -r linux-x64 --self-contained true
```

**Android**:
```bash
dotnet publish src/Platforms/EnvizonController.Android/EnvizonController.Android.csproj -c Release
```

### 3.3 部署步骤

#### Windows部署流程

1. 生成发布包
   ```bash
   dotnet publish src/Platforms/EnvizonController.Desktop/EnvizonController.Desktop.csproj -c Release -r win-x64 --self-contained true -o ./publish
   ```

2. 创建配置文件
   - 在发布目录创建`appsettings.json`文件
   - 配置数据库连接、日志设置和系统参数

3. 设置Windows服务（可选）
   ```powershell
   # 以管理员身份运行PowerShell
   New-Service -Name "EnvizonController" -BinaryPathName "D:\path\to\publish\EnvizonController.Desktop.exe" -DisplayName "EnvizonController Service" -Description "EnvizonController监控与控制系统" -StartupType Automatic
   Start-Service -Name "EnvizonController"
   ```

#### 服务器部署流程

1. 准备服务器环境
   - 安装.NET 9.0 Runtime
   - 配置防火墙规则

2. 复制发布文件
   ```bash
   scp -r ./publish user@server:/opt/envizoncontroller/
   ```

3. 配置systemd服务
   ```bash
   # 创建服务文件
   sudo nano /etc/systemd/system/envizoncontroller.service

   # 添加以下内容
   [Unit]
   Description=EnvizonController Service
   After=network.target

   [Service]
   WorkingDirectory=/opt/envizoncontroller
   ExecStart=/opt/envizoncontroller/EnvizonController.Desktop
   Restart=always
   RestartSec=10
   KillSignal=SIGINT
   SyslogIdentifier=envizoncontroller
   User=envizon
   Environment=ASPNETCORE_ENVIRONMENT=Production

   [Install]
   WantedBy=multi-user.target
   ```

4. 启动服务
   ```bash
   sudo systemctl enable envizoncontroller.service
   sudo systemctl start envizoncontroller.service
   ```

### 3.4 系统启动流程

```mermaid
graph TD
    A[应用程序启动] --> B{初始化配置};
    B --> C[配置日志 Serilog];
    C --> D[注册依赖注入服务];
    D --> E[配置API服务 Swagger/CORS];
    D --> F[添加应用/基础设施/域服务];
    D --> G[添加MQTT服务];
    D --> H[添加Quartz数据采集服务];
    H --> I[注册Quartz后台服务];
    I --> J[构建应用程序];
    J --> K{初始化默认数据};
    K --> L[确保数据库创建];
    K --> M[初始化默认协议];
    K --> N[初始化默认设备];
    K --> O[初始化默认测试项];
    O --> P[配置HTTP请求管道];
    P --> Q[启动应用程序];
```

### 3.5 多平台部署差异

EnvizonController支持多种平台部署，包括Windows Desktop、Linux、Android和可选的Docker容器。不同平台的部署存在一些差异：

#### Windows Desktop

- **发布**: 使用`-r win-x64 --self-contained true`参数发布自包含应用，无需安装.NET Runtime。
- **部署**: 直接运行可执行文件，或配置为Windows服务。
- **串口访问**: 直接使用System.IO.Ports访问本地串口。

#### Linux

- **发布**: 使用`-r linux-x64 --self-contained true`参数发布自包含应用。
- **部署**: 配置为systemd服务，确保用户有访问串口或网络的权限。
- **串口访问**: 需要确保用户属于`dialout`或`uucp`组以访问串口设备。

#### Android

- **发布**: 发布为APK文件。
- **部署**: 安装APK到Android设备。
- **串口访问**: 使用EnvizonController.Modbus.Adapters.Android模块，通过Android USB API访问USB转串口设备，需要相应的权限。
- **后台运行**: Android应用的后台运行和电源管理需要特别注意，可能需要配置服务或使用WorkManager。

#### Docker容器

- **发布**: 构建Docker镜像。
- **部署**: 运行Docker容器，通过端口映射访问API服务。
- **通信**: Modbus TCP和MQTT通信依赖于容器的网络配置和宿主机或外部网络的连通性。串口设备通常无法直接在容器中访问，需要通过其他方式（如串口转TCP服务器）桥接。

#### Android部署

1. 从Visual Studio或命令行生成APK
   ```bash
   dotnet publish src/Platforms/EnvizonController.Android/EnvizonController.Android.csproj -c Release
   ```

2. 安装APK到Android设备
   - 复制APK文件到设备
   - 允许来自未知来源的应用安装
   - 打开APK文件进行安装

#### Docker容器部署（可选）

1. 创建Dockerfile
   ```dockerfile
   FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
   WORKDIR /app
   EXPOSE 80
   EXPOSE 443

   FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
   WORKDIR /src
   COPY ["src/EnvizonController.Server/EnvizonController.Server.csproj", "EnvizonController.Server/"]
   RUN dotnet restore "EnvizonController.Server/EnvizonController.Server.csproj"
   COPY . .
   WORKDIR "/src/EnvizonController.Server"
   RUN dotnet build "EnvizonController.Server.csproj" -c Release -o /app/build

   FROM build AS publish
   RUN dotnet publish "EnvizonController.Server.csproj" -c Release -o /app/publish

   FROM base AS final
   WORKDIR /app
   COPY --from=publish /app/publish .
   ENTRYPOINT ["dotnet", "EnvizonController.Server.dll"]
   ```

2. 构建并运行容器
   ```bash
   docker build -t envizoncontroller .
   docker run -d -p 8080:80 --name envizoncontroller envizoncontroller
   ```

## 4. 模块维护指南

### 4.1 Modbus通信模块

EnvizonController的Modbus通信模块采用分层设计，支持RTU、ASCII和TCP变体。

#### 4.1.1 模块架构

Modbus通信框架分为四个核心层：

1. **协议核心层** (EnvizonController.Modbus.Protocol)
   - 实现纯Modbus协议逻辑
   - 包含帧构建、报文解析、功能码处理
   - 实现CRC/LRC校验算法

2. **通信抽象层** (EnvizonController.Modbus.Abstractions)
   - 定义统一的通信通道接口
   - 处理通信生命周期、超时和重试逻辑

3. **平台适配层**
   - Desktop适配器：基于System.IO.Ports实现
   - Android适配器：基于Android USB API实现
   - 网络适配器：跨平台TCP/IP实现

4. **应用服务层** (EnvizonController.Modbus.Client)
   - 提供高级ModbusClient API
   - 实现设备配置与状态管理

#### 4.1.2 配置说明

Modbus配置存储在`appsettings.Modbus.json`文件中：

```json
{
  "ModbusDevices": [
    {
      "Id": "device1",
      "Name": "温湿度控制器",
      "SlaveAddress": 1,
      "TransportType": "Rtu",
      "ConnectionType": "Serial",
      "SerialSettings": {
        "PortName": "COM1",
        "BaudRate": 9600,
        "DataBits": 8,
        "Parity": 0,
        "StopBits": 1
      },
      "RetrySettings": {
        "RetryCount": 3,
        "RetryDelay": 1000,
        "ResponseTimeout": 2000
      },
      "AutoReconnect": {
        "Enabled": true,
        "ReconnectDelay": 5000,
        "MaxAttempts": 10
      },
      "Parameters": [
        {
          "Name": "温度",
          "Address": 0,
          "Type": "HoldingRegister",
          "DataType": "Int16",
          "ScaleFactor": 0.1,
          "Offset": 0,
          "Unit": "°C",
          "Description": "当前温度",
          "Writable": false
        },
        {
          "Name": "湿度",
          "Address": 1,
          "Type": "HoldingRegister",
          "DataType": "Int16",
          "ScaleFactor": 0.1,
          "Offset": 0,
          "Unit": "%RH",
          "Description": "当前湿度",
          "Writable": false
        },
        {
          "Name": "温度设定值",
          "Address": 100,
          "Type": "HoldingRegister",
          "DataType": "Int16",
          "ScaleFactor": 0.1,
          "Offset": 0,
          "Unit": "°C",
          "Description": "温度设定值",
          "Writable": true,
          "MinValue": 10,
          "MaxValue": 35
        }
      ]
    }
  ]
}
```

#### 4.1.3 常见维护任务

1. **添加新设备**

   修改`appsettings.Modbus.json`文件，添加新的设备配置，然后重启应用。

2. **修改通信参数**

   编辑现有设备的通信参数（如波特率、数据位等），保存配置并重启应用。

3. **添加新参数映射**

   在设备的`Parameters`数组中添加新的参数定义，指定寄存器地址、数据类型和转换因子。

4. **实现自定义功能码**

   ```csharp
   // 创建自定义请求类
   public class CustomFunctionRequest : ModbusRequest
   {
       public CustomFunctionRequest(byte slaveAddress, byte[] data)
           : base(slaveAddress, ModbusFunction.Custom)
       {
           Data = data;
       }
       
       public byte[] Data { get; }
       
       public override byte[] ToBytes()
       {
           // 实现序列化逻辑
       }
   }
   
   // 创建自定义响应类
   public class CustomFunctionResponse : ModbusResponse
   {
       public CustomFunctionResponse()
           : base(ModbusFunction.Custom)
       {
       }
       
       public override void FromBytes(byte[] bytes)
       {
           // 实现反序列化逻辑
       }
   }
   ```

5. **调试Modbus通信**

   使用`EnvizonController.Modbus.SerialTester`工具进行通信测试和诊断，它提供直观的界面用于：
   - 配置串口参数
   - 发送Modbus请求
   - 查看响应数据
   - 记录通信日志

### 4.2 MQTT通信模块

EnvizonController的MQTT组件分为客户端和服务器两部分，支持标准的MQTT v3.1.1和v5.0协议。

#### 4.2.1 MQTT客户端

##### 初始化与配置

```csharp
// 创建MQTT客户端配置
var options = new MqttClientOptions
{
    BrokerUrl = "localhost",
    Port = 1883,
    ClientId = $"client-{Guid.NewGuid()}",
    Username = "username", // 可选
    Password = "password", // 可选
    UseTls = false
};

// 依赖注入方式
services.AddMqttClient(options);

// 或手动创建客户端
var mqttClientFactory = new MqttClientFactory();
var mqttClient = new MqttClientService(
    mqttClientFactory,
    options,
    handlerRegistry,
    logger);
```

##### 连接管理与消息处理

```csharp
// 连接到MQTT服务器
await mqttClient.ConnectAsync();

// 订阅主题
await mqttClient.SubscribeAsync(
    "device/commands", 
    MqttQualityOfServiceLevel.ExactlyOnce);

// 发布消息
await mqttClient.PublishAsync(
    "device/status", 
    "{\"status\":\"online\"}", 
    MqttQualityOfServiceLevel.AtLeastOnce);

// 实现消息处理器
public class DeviceCommandHandler : IMessageHandler
{
    public bool CanHandle(string topic) => 
        topic.StartsWith("device/commands/");
    
    public async Task HandleMessageAsync(string topic, string message)
    {
        // 处理消息
        Console.WriteLine($"收到命令: {message}");
    }
}

// 注册处理器
handlerRegistry.RegisterHandler(new DeviceCommandHandler());
```

##### 错误处理与重连策略

MQTT客户端内置自动重连机制，当连接断开时会自动尝试重连。默认设置：
- 断开连接时自动触发
- 默认延迟5秒后尝试重连
- 最大重试次数为10次

可以自定义重连策略：

```csharp
var options = new MqttClientOptions
{
    // 基本连接设置
    BrokerUrl = "localhost",
    Port = 1883,
    
    // 重连策略
    AutoReconnect = true,
    ReconnectDelay = TimeSpan.FromSeconds(5),
    MaxReconnectAttempts = 10
};
```

#### 4.2.2 MQTT服务器

##### 服务器配置与启动

```csharp
// 依赖注入方式
services.AddMqttServer();
services.AddHostedService<MqttServerBackgroundService>();

// 或手动创建服务器
var messageProcessor = new MessageProcessor();
var logger = LoggerFactory.Create(builder => builder.AddConsole())
    .CreateLogger<MqttServerService>();
var mqttServer = new MqttServerService(messageProcessor, logger);

// 启动服务器
await mqttServer.StartAsync(CancellationToken.None);
```

##### 自定义认证与授权

```csharp
// 实现自定义授权服务
public class MqttAuthorizationService : 
    IMqttServerConnectionValidator, 
    IMqttTopicPermissions
{
    public Task ValidateConnectionAsync(ValidatingConnectionEventArgs args)
    {
        // 验证连接凭据
        if (args.Username == "admin" && args.Password == "password")
        {
            args.ReasonCode = MqttConnectReasonCode.Success;
        }
        else
        {
            args.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
        }
        return Task.CompletedTask;
    }
    
    public Task ValidatePublishAsync(ValidatingPublishEventArgs args)
    {
        // 只允许特定客户端发布到特定主题
        if (args.ClientId == "device1" && 
            !args.Topic.StartsWith("devices/device1/"))
        {
            args.ReasonCode = MqttPublishReasonCode.NotAuthorized;
        }
        return Task.CompletedTask;
    }
}

// 注册授权服务
services.AddSingleton<IMqttServerConnectionValidator, MqttAuthorizationService>();
```

##### 性能优化与监控

```csharp
// 配置服务器性能选项
var optionsBuilder = new MqttServerOptionsBuilder()
    .WithDefaultEndpoint()
    .WithDefaultEndpointPort(1883)
    .WithMaxPendingMessagesPerClient(100) // 限制每个客户端的待处理消息数
    .WithConnectionBacklog(100)           // 调整连接积压队列大小
    .WithMaximumQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce);

// 添加监控
services.AddMqttServerMetrics();
services.AddHealthChecks().AddCheck<MqttServerHealthCheck>("mqtt_server");
```

#### 4.2.3 故障排除

1. **连接问题**
   - 检查网络连接是否正常
   - 验证服务器地址和端口是否正确
   - 确认凭据（用户名/密码）是否正确
   - 检查TLS设置是否正确配置

2. **消息丢失**
   - 确保使用适当的QoS级别
   - 检查主题订阅是否正确
   - 查看服务器日志中的错误信息

3. **性能问题**
   - 减少不必要的发布/订阅操作
   - 优化消息大小和频率
   - 调整QoS级别
   - 监控服务器资源使用情况

### 4.3 数据采集与存储

EnvizonController使用Entity Framework Core访问SQLite数据库，并通过Quartz.NET实现定时数据采集。

#### 4.3.1 数据库架构

系统主要数据表包括：

- **Devices**: 存储设备基本信息
- **Parameters**: 存储设备参数定义
- **DataPoints**: 存储采集的数据点
- **Alarms**: 存储报警记录
- **Protocols**: 存储通信协议定义
- **TestItems**: 存储测试项定义
- **Programs**: 存储测试程序定义

数据表关系如下：

```
Devices 1--* Parameters
Devices 1--* DataPoints
Parameters 1--* DataPoints
Devices 1--* Alarms
Devices *--1 Protocols
TestItems *--* Programs
```

#### 4.3.2 数据采集配置

数据采集由`QuartzDeviceCollectionBackgroundService`负责，配置存储在`appsettings.json`的`DeviceCollection`部分：

```json
{
  "DeviceCollection": {
    "Enabled": true,
    "DefaultCollectionInterval": 1000,
    "MaxPointsPerDevice": 10000,
    "DataRetentionDays": 30,
    "AutoPurgeEnabled": true,
    "PurgeInterval": "0 0 * * *",
    "CollectionJobs": [
      {
        "DeviceId": "device1",
        "Parameters": ["温度", "湿度"],
        "Interval": 5000,
        "Enabled": true
      }
    ]
  }
}
```

#### 4.3.3 数据管理任务

1. **数据备份**

   ```bash
   # 备份SQLite数据库
   sqlite3 /path/to/envizon.db ".backup '/path/to/backup/envizon_backup.db'"
   ```

2. **数据恢复**

   ```bash
   # 停止应用
   sudo systemctl stop envizoncontroller.service
   
   # 恢复数据库
   cp /path/to/backup/envizon_backup.db /path/to/envizon.db
   
   # 重启应用
   sudo systemctl start envizoncontroller.service
   ```

3. **数据清理**

   系统默认配置了自动数据清理，根据`DataRetentionDays`设置的保留天数自动清理过期数据。也可以手动清理：

   ```sql
   -- 清理30天以前的数据点
   DELETE FROM DataPoints WHERE Timestamp < datetime('now', '-30 days');
   
   -- 清理已确认的报警
   DELETE FROM Alarms WHERE Status = 'Acknowledged' AND Timestamp < datetime('now', '-90 days');
   ```

4. **数据导出**

   ```sql
   -- 导出CSV格式数据
   sqlite3 -header -csv /path/to/envizon.db "SELECT * FROM DataPoints WHERE DeviceId = 1;" > data_export.csv
   ```

### 4.4 前端界面维护

EnvizonController前端基于Avalonia UI框架，采用MVVM架构模式。

#### 4.4.1 界面结构

UI组件主要位于`EnvizonController.Presentation`项目中：

- **Views/**: 包含所有界面视图
  - MainView/MainWindow: 主窗口
  - DashboardView: 仪表板视图
  - TestPlanView: 测试计划视图
  - Controls/: 自定义控件

- **ViewModels/**: 包含所有视图模型
  - 每个视图有对应的ViewModel类
  - 支持数据绑定和命令模式

- **Models/**: 包含UI模型类
  - 表示层数据模型
  - 数据转换和验证逻辑

#### 4.4.2 UI主题定制

EnvizonController支持自定义主题：

1. 创建主题文件`Themes/CustomTheme.axaml`：

   ```xml
   <ResourceDictionary xmlns="https://github.com/avaloniaui"
                      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
     <!-- 定义颜色 -->
     <Color x:Key="PrimaryColor">#0DF0FF</Color>
     <Color x:Key="SecondaryColor">#1A2A3A</Color>
     <Color x:Key="BackgroundColor">#121212</Color>
     
     <!-- 样式覆盖 -->
     <Style Selector="Button">
       <Setter Property="Background" Value="{DynamicResource SecondaryColor}" />
       <Setter Property="Foreground" Value="{DynamicResource PrimaryColor}" />
     </Style>
     
     <!-- 更多样式定义 -->
   </ResourceDictionary>
   ```

2. 加载主题在`App.axaml.cs`中：

   ```csharp
   public override void OnFrameworkInitializationCompleted()
   {
       // 加载自定义主题
       if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
       {
           var theme = new ResourceDictionary
           {
               Source = new Uri("avares://EnvizonController.Presentation/Themes/CustomTheme.axaml")
           };
           Application.Current.Resources.MergedDictionaries.Add(theme);
           
           desktop.MainWindow = new MainWindow();
       }
       
       base.OnFrameworkInitializationCompleted();
   }
   ```

#### 4.4.3 本地化与国际化

EnvizonController支持多语言本地化：

1. 添加资源文件：
   - `Resources/Strings.zh-CN.resx`（中文）
   - `Resources/Strings.en-US.resx`（英文）

2. 在XAML中使用本地化资源：

   ```xml
   <TextBlock Text="{x:Static resources:Strings.Dashboard_Title}" />
   ```

3. 在代码中使用本地化资源：

   ```csharp
   var title = EnvizonController.Presentation.Resources.Strings.Dashboard_Title;
   ```

4. 切换语言：

   ```csharp
   public void ChangeLanguage(string cultureName)
   {
       var culture = new CultureInfo(cultureName);
       Thread.CurrentThread.CurrentCulture = culture;
       Thread.CurrentThread.CurrentUICulture = culture;
       
       // 通知UI刷新
       Application.Current.Resources.MergedDictionaries.Clear();
       LoadResourceDictionaries();
   }
   ```

## 5. 常见问题排查与解决

### 5.1 通信问题诊断

#### 5.1.1 Modbus通信问题

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 无法连接到设备 | 通信参数错误 | 验证端口、波特率、数据位、校验位和停止位设置 |
| | 设备地址错误 | 确认从站地址配置正确 |
| | 硬件连接问题 | 检查物理连接、电缆和电源 |
| 通信超时 | 响应超时设置过短 | 增加超时时间 |
| | 设备响应缓慢 | 检查设备负载，可能需要降低查询频率 |
| | 网络延迟（TCP） | 检查网络质量，减少数据包大小 |
| 数据读取错误 | 寄存器地址错误 | 验证寄存器地址与设备文档一致 |
| | 数据类型不匹配 | 检查数据类型和字节序 |
| | 功能码不支持 | 确认设备支持请求的功能码 |

**诊断步骤**：

1. 使用日志分析：
   - 启用调试级别日志：
     ```json
     "Serilog": {
       "MinimumLevel": {
         "Default": "Debug",
         "Override": {
           "EnvizonController.Modbus": "Verbose"
         }
       }
     }
     ```
   - 分析日志中的通信帧和错误消息

2. 使用ModbusSerialTester工具：
   - 尝试手动建立连接
   - 发送简单的读取请求
   - 验证响应是否正确

3. 检查硬件连接：
   - 使用万用表测量信号线电压
   - 检查接线是否松动
   - 验证接线极性是否正确

#### 5.1.2 MQTT通信问题

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 客户端连接失败 | 服务器地址错误 | 验证MQTT服务器地址和端口 |
| | 认证失败 | 检查用户名和密码 |
| | TLS配置错误 | 验证TLS证书和设置 |
| 消息未收到 | 主题订阅错误 | 确认订阅的主题与发布主题匹配 |
| | QoS设置不当 | 使用更高级别的QoS保证传递 |
| | 网络不稳定 | 实现断线重连和消息重发机制 |
| 消息乱码 | 编码不一致 | 确保发送和接收使用相同的编码 |
| | JSON格式错误 | 验证JSON结构是否正确 |

**诊断步骤**：

1. 使用MQTT客户端工具（如MQTT Explorer）：
   - 测试与服务器的连接
   - 订阅和发布测试消息
   - 分析消息内容

2. 检查网络连接：
   - 测试与MQTT服务器的网络连通性
   - 检查防火墙设置
   - 验证MQTT端口是否开放

3. 分析客户端日志：
   - 查找连接和消息处理的错误
   - 跟踪消息流向
   - 识别断开连接的原因

### 5.2 数据异常处理

#### 5.2.1 数据采集异常

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 数据点缺失 | 采集任务未执行 | 检查Quartz任务调度是否正常 |
| | 通信中断 | 排查通信问题，确保稳定连接 |
| | 权限问题 | 检查数据库访问权限 |
| 数据值异常 | 缩放因子错误 | 验证数据转换参数设置 |
| | 传感器故障 | 检查硬件传感器状态 |
| | 配置错误 | 检查参数类型和地址配置 |
| 数据采集延迟 | 系统负载过高 | 监控CPU和内存使用情况 |
| | 数据库性能问题 | 优化数据库索引和查询 |
| | 采集间隔设置不当 | 调整采集频率与系统能力匹配 |

**处理步骤**：

1. **识别异常数据**：
   ```sql
   -- 查找超出合理范围的数据
   SELECT * FROM DataPoints 
   WHERE ParameterId = 1 AND (Value < 10 OR Value > 50);
   
   -- 查找异常增长的数据点
   SELECT dp1.* 
   FROM DataPoints dp1
   JOIN DataPoints dp2 ON dp1.DeviceId = dp2.DeviceId AND dp1.ParameterId = dp2.ParameterId
   WHERE dp1.Timestamp > dp2.Timestamp
   AND ABS(dp1.Value - dp2.Value) / (JULIANDAY(dp1.Timestamp) - JULIANDAY(dp2.Timestamp)) > 10;
   ```

2. **数据修正**：
   ```sql
   -- 标记异常数据
   UPDATE DataPoints SET Quality = 'Bad' 
   WHERE ParameterId = 1 AND (Value < 10 OR Value > 50);
   
   -- 删除异常数据（谨慎使用）
   DELETE FROM DataPoints WHERE Quality = 'Bad';
   
   -- 数据平滑处理
   -- 使用代码实现或通过存储过程
   ```

3. **预防措施**：
   - 设置数据合理性检查
   - 实现数据过滤和验证逻辑
   - 配置自动数据修正规则

#### 5.2.2 数据库一致性问题

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 外键约束失败 | 删除父记录时未处理子记录 | 使用级联删除或先删除子记录 |
| | 数据迁移不完整 | 检查数据迁移脚本，确保完整性 |
| 唯一约束冲突 | 重复插入相同数据 | 实现重复检查或使用UPSERT操作 |
| | 并发操作 | 使用事务和锁机制 |
| 数据库损坏 | 非正常关闭 | 恢复备份，使用WAL模式增强稳定性 |
| | 磁盘故障 | 检查磁盘健康状态，更换损坏磁盘 |

**修复步骤**：

1. **备份数据**：
   ```bash
   sqlite3 /path/to/envizon.db ".backup '/path/to/backup/envizon_before_fix.db'"
   ```

2. **检查并修复一致性**：
   ```bash
   # 运行自动检查
   sqlite3 /path/to/envizon.db "PRAGMA integrity_check;"
   
   # 修复孤立记录
   sqlite3 /path/to/envizon.db << EOF
   BEGIN TRANSACTION;
   DELETE FROM DataPoints WHERE DeviceId NOT IN (SELECT Id FROM Devices);
   DELETE FROM Parameters WHERE DeviceId NOT IN (SELECT Id FROM Devices);
   COMMIT;
   EOF
   ```

3. **数据库维护**：
   ```bash
   # 压缩数据库
   sqlite3 /path/to/envizon.db "VACUUM;"
   
   # 重建索引
   sqlite3 /path/to/envizon.db "REINDEX;"
   ```

### 5.3 性能问题优化

#### 5.3.1 内存使用优化

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 内存持续增长 | 内存泄漏 | 使用内存分析工具查找泄漏点 |
| | 大对象缓存未释放 | 实现缓存过期和自动清理机制 |
| | 长时间运行累积效应 | 定期重启服务或实现内存管理 |
| GC频繁触发 | 短生命周期对象过多 | 减少临时对象创建，使用对象池 |
| | 大内存分配 | 优化数据结构，避免大数组分配 |
| | 内存压力 | 监控并调整内存限制 |

**优化步骤**：

1. **分析内存使用**：
   - 使用dotnet-counters监控内存：
     ```bash
     dotnet-counters monitor --process-id <PID> System.Runtime
     ```
   - 使用dotnet-dump收集分析：
     ```bash
     dotnet-dump collect --process-id <PID>
     dotnet-dump analyze dump_file.dmp
     ```

2. **实现优化**：
   - 使用`ArrayPool<T>`减少数组分配：
     ```csharp
     var buffer = ArrayPool<byte>.Shared.Rent(1024);
     try {
         // 使用buffer
     } finally {
         ArrayPool<byte>.Shared.Return(buffer);
     }
     ```
   - 使用`StringBuilder`替代字符串拼接：
     ```csharp
     var sb = new StringBuilder();
     for (int i = 0; i < 1000; i++) {
         sb.Append(i);
     }
     var result = sb.ToString();
     ```
   - 实现对象池：
     ```csharp
     services.AddSingleton<ObjectPoolProvider>(new DefaultObjectPoolProvider());
     services.AddSingleton(s => {
         var provider = s.GetRequiredService<ObjectPoolProvider>();
         return provider.Create(new DefaultPooledObjectPolicy<MyExpensiveObject>());
     });
     ```

3. **配置GC**：
   - 在服务器环境优化GC：
     ```xml
     <PropertyGroup>
       <ServerGarbageCollection>true</ServerGarbageCollection>
       <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
     </PropertyGroup>
     ```

#### 5.3.2 数据库性能优化

| 问题描述 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 查询执行缓慢 | 缺少索引 | 在常用查询字段上创建索引 |
| | 低效查询 | 优化SQL查询，避免全表扫描 |
| | 数据库碎片化 | 定期执行VACUUM操作 |
| 写入性能下降 | 频繁单条插入 | 使用批量插入操作 |
| | 索引过多 | 仅在必要字段上创建索引 |
| | 事务管理不当 | 优化事务范围和隔离级别 |
| 数据库锁定 | 长时间事务 | 缩短事务持续时间 |
| | 并发写入冲突 | 实现并发控制策略 |

**优化步骤**：

1. **分析慢查询**：
   ```sql
   -- 启用查询计划分析
   EXPLAIN QUERY PLAN 
   SELECT * FROM DataPoints WHERE DeviceId = 1 AND Timestamp BETWEEN '2025-01-01' AND '2025-01-31';
   ```

2. **创建合适的索引**：
   ```sql
   -- 为常用查询创建索引
   CREATE INDEX idx_datapoints_device_time ON DataPoints(DeviceId, Timestamp);
   
   -- 为范围查询创建索引
   CREATE INDEX idx_datapoints_parameter_time ON DataPoints(ParameterId, Timestamp);
   ```

3. **优化数据库设置**：
   ```csharp
   // 配置EF Core性能选项
   services.AddDbContext<AppDbContext>(options => {
       options.UseSqlite("Data Source=envizon.db", sqliteOptions => {
           sqliteOptions.CommandTimeout(60);
       });
       
       // 禁用不必要的变更跟踪
       options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
       
       // 启用批量操作
       options.UseBulkExtensions();
   });
   ```

4. **实现数据分区**：
   - 创建按时间分区的数据表
   - 实现自动数据迁移策略
   - 使用视图统一访问分区数据

### 5.4 日志分析与故障排除

EnvizonController使用Serilog进行日志记录，支持多种日志汇总目标和结构化日志。

#### 5.4.1 日志配置

```json
{
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "EnvizonController.Modbus": "Debug",
        "EnvizonController.Mqtt": "Debug"
      }
    },
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "logs/envizon-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 14,
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/envizon-errors-.log",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Error",
          "retainedFileCountLimit": 30
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ]
  }
}
```

#### 5.4.2 常见日志模式分析

| 日志模式 | 含义 | 处理方法 |
|---------|------|----------|
| `[ERROR] 连接失败: 端口COM1不存在` | 串口不存在或被占用 | 检查设备连接，更新端口配置 |
| `[ERROR] Modbus异常: 非法功能码(01)` | 设备不支持请求的功能码 | 修改功能码或确认设备支持功能 |
| `[ERROR] 数据库操作失败: 表'XYZ'不存在` | 数据库结构不匹配 | 运行数据库迁移或更新 |
| `[WARN] 连接超时，尝试重连(第3次)` | 通信超时，执行自动重连 | 监控重连进度，检查连接稳定性 |
| `[INFO] 设备'温控器'状态变为'Online'` | 设备状态变化通知 | 无需处理的正常状态变化 |

#### 5.4.3 自定义日志提取和分析

1. **提取特定类型的错误**：
   ```bash
   grep "\[ERROR\]" logs/envizon-2025-05-15.log > errors.log
   ```

2. **分析连接问题**：
   ```bash
   grep -E "连接|connection" logs/envizon-2025-05-15.log > connection_issues.log
   ```

3. **统计错误频率**：
   ```bash
   grep "\[ERROR\]" logs/envizon-2025-05-15.log | cut -d':' -f3 | sort | uniq -c | sort -nr
   ```

4. **创建故障时间线**：
   ```bash
   grep -E "\[ERROR\]|\[WARN\]" logs/envizon-2025-05-15.log | awk '{print $1,$2,$3,$4,$5,$6,$7,$8,$9,$10}' > timeline.log
   ```

#### 5.4.4 实时日志监控

1. **使用PowerShell实时监控日志**：
   ```powershell
   Get-Content -Path "logs/envi<thinking>
这份Modbus测试项目的README提供了关于如何测试Modbus组件的有用信息。它包括测试的范围、如何运行测试、测试结构，以及添加新测试的指南。这对于维护教程中测试部分的编写非常有帮助。

现在我已经收集了足够的信息，可以开始编写完整的维护教程了。我会按照用户的要求，包含以下内容：

1. 环境搭建指南
2. 构建和部署流程
3. 各模块的维护方法和注意事项（特别是Modbus和MQTT）
4. 常见问题排查和解决方案
5. 系统扩展指南
6. 系统维护最佳实践

让我开始编写这份维护教程。
</thinking>

让我开始编写EnvizonController项目的维护教程草稿。我会基于收集到的信息，创建一个全面的文档：

<write_to_file>
<path>EnvizonController维护教程.md</path>
<content>
# EnvizonController系统维护教程

## 目录

1. [系统概述](#1-系统概述)
2. [环境搭建指南](#2-环境搭建指南)
3. [构建和部署流程](#3-构建和部署流程)
4. [模块维护指南](#4-模块维护指南)
   - [4.1 Modbus通信模块](#41-modbus通信模块)
   - [4.2 MQTT通信模块](#42-mqtt通信模块)
   - [4.3 数据采集与存储](#43-数据采集与存储)
   - [4.4 UI界面](#44-ui界面)
5. [常见问题排查与解决](#5-常见问题排查与解决)
6. [系统扩展指南](#6-系统扩展指南)
   - [6.1 添加新设备类型](#61-添加新设备类型)
   - [6.2 集成新通信协议](#62-集成新通信协议)
   - [6.3 扩展API功能](#63-扩展api功能)
7. [系统维护最佳实践](#7-系统维护最佳实践)
8. [附录](#8-附录)

## 1. 系统概述

EnvizonController是一个基于.NET的跨平台监控和控制系统，主要用于设备监控、数据采集和环境控制。系统采用模块化架构设计，具有良好的扩展性和可维护性。

### 1.1 系统架构

EnvizonController系统由以下主要组件构成：

- **前端界面**：基于Avalonia UI框架的跨平台用户界面
- **设备通信层**：包括Modbus和MQTT通信模块
- **业务逻辑层**：处理设备数据、测试计划和报警管理
- **数据存储层**：基于Entity Framework Core和SQLite的数据持久化

### 1.2 目录结构

```
src/
├── EnvizonController.sln                  # 解决方案文件
├── EnvizonController.Mqtt.Client/         # MQTT客户端模块
├── EnvizonController.Mqtt.Server/         # MQTT服务器模块
├── EnvizonController.Presentation/        # 用户界面组件
├── EnvizonController.Server/              # API服务器
├── EnvizonController.Shared/              # 共享组件
├── Modbus/                                # Modbus通信框架
│   ├── EnvizonController.Modbus.Abstractions/
│   ├── EnvizonController.Modbus.Adapters.Android/
│   ├── EnvizonController.Modbus.Adapters.Desktop/
│   ├── EnvizonController.Modbus.Adapters.Network/
│   ├── EnvizonController.Modbus.Client/
│   ├── EnvizonController.Modbus.Protocol/
│   └── EnvizonController.Modbus.Tests/
└── Platforms/                             # 跨平台适配层
    ├── EnvizonController.Android/
    ├── EnvizonController.Browser/
    ├── EnvizonController.Desktop/
    └── EnvizonController.iOS/
```

## 2. 环境搭建指南

本节详细说明如何搭建EnvizonController的开发和测试环境。

### 2.1 开发环境要求

- **操作系统**：Windows 10/11（推荐）、macOS 或 Linux
- **.NET SDK**：.NET 9.0 SDK [下载链接](https://dotnet.microsoft.com/download/dotnet/9.0)
- **IDE**：
  - Visual Studio 2022或更高版本（推荐）
  - JetBrains Rider
  - Visual Studio Code + C#扩展
- **数据库**：SQLite（已集成，无需单独安装）
- **版本控制**：Git

### 2.2 安装开发工具

#### 2.2.1 安装.NET 9.0 SDK

1. 访问[Microsoft .NET 下载页面](https://dotnet.microsoft.com/download/dotnet/9.0)
2. 下载并安装适合您操作系统的.NET 9.0 SDK
3. 安装完成后，打开命令行终端，验证安装：

```bash
dotnet --version
# 应显示9.0.x版本
```

#### 2.2.2 安装Visual Studio 2022（Windows环境）

1. 下载并安装[Visual Studio 2022](https://visualstudio.microsoft.com/downloads/)
2. 在安装过程中，选择以下工作负载：
   - .NET桌面开发
   - ASP.NET和Web开发
   - 移动开发（如需支持Android/iOS）
3. 在单个组件中，确保选择：
   - .NET 9.0运行时
   - Avalonia扩展（可在VS市场中安装）

#### 2.2.3 配置开发环境变量

在Windows系统中，设置以下环境变量：

1. 打开系统属性 > 高级 > 环境变量
2. 在系统变量中，添加/更新以下变量：
   - `DOTNET_ROOT`：指向.NET SDK安装目录
   - 确保`Path`变量包含.NET SDK的bin目录

### 2.3 获取源代码

```bash
# 克隆仓库
git clone https://your-repository-url/envizon-controller.git
cd envizon-controller

# 更新子模块（如有）
git submodule update --init --recursive
```

### 2.4 还原依赖项和构建项目

```bash
# 还原NuGet包
dotnet restore src/EnvizonController.sln

# 构建解决方案
dotnet build src/EnvizonController.sln
```

### 2.5 设置数据库

EnvizonController使用SQLite数据库，数据库文件自动创建在`src/EnvizonController.Server`目录中。首次运行时，系统会自动创建所需的表结构。

### 2.6 启动开发服务器

```bash
# 启动API服务器
cd src/EnvizonController.Server
dotnet run

# 在另一个终端启动桌面应用
cd src/Platforms/EnvizonController.Desktop
dotnet run
```

## 3. 构建和部署流程

本节介绍如何构建和部署EnvizonController系统到生产环境。

### 3.1 版本号管理

EnvizonController采用语义化版本号（SemVer）：`主版本.次版本.修订号`

1. 在发布新版本前，更新以下文件中的版本号：
   - `src/Directory.Build.props`
   - 各项目的`.csproj`文件

### 3.2 创建发布构建

#### 3.2.1 服务器组件发布

```bash
# 发布API服务器
cd src/EnvizonController.Server
dotnet publish -c Release -o ../../publish/server

# 创建自包含部署（包含运行时）
dotnet publish -c Release -r win-x64 --self-contained -o ../../publish/server-win64
```

#### 3.2.2 客户端组件发布

```bash
# Windows桌面客户端
cd src/Platforms/EnvizonController.Desktop
dotnet publish -c Release -r win-x64 --self-contained -o ../../../publish/desktop-win64

# Android客户端
cd src/Platforms/EnvizonController.Android
dotnet publish -c Release -o ../../../publish/android
```

### 3.3 部署流程

#### 3.3.1 服务器部署

1. 停止正在运行的服务
2. 备份数据库文件（`envizon.db`）
3. 复制发布的服务器文件到目标服务器
4. 恢复数据库文件（如需要）
5. 配置`appsettings.json`
6. 启动服务：

```bash
# Windows环境
EnvizonController.Server.exe

# Linux环境
./EnvizonController.Server
```

#### 3.3.2 客户端部署

1. 卸载旧版本（如果存在）
2. 复制发布的客户端文件到目标设备
3. 配置客户端连接服务器的地址：
   - 修改`appsettings.json`中的`ApiEndpoint`设置
4. 启动客户端应用程序

### 3.4 CI/CD流程

1. **构建触发**：推送到主分支或创建发布标签
2. **构建流程**：
   - 还原依赖项
   - 运行单元测试
   - 构建发布版本
   - 打包安装程序
3. **部署流程**：
   - 发布构建版本到指定服务器
   - 运行数据库迁移（如需要）
   - 更新服务配置
   - 重启服务

## 4. 模块维护指南

### 4.1 Modbus通信模块

Modbus模块是系统的核心组件，用于与支持Modbus协议的设备通信。

#### 4.1.1 架构介绍

Modbus通信框架采用分层设计：

1. **协议核心层**：实现Modbus协议逻辑
2. **通信抽象层**：定义统一的通信通道接口
3. **平台适配层**：为不同平台提供具体实现
4. **应用服务层**：提供高级ModbusClient API

#### 4.1.2 配置与使用

Modbus设备配置存储在`appsettings.Modbus.json`文件中：

```json
{
  "ModbusDevices": [
    {
      "Id": "device1",
      "Name": "温控设备",
      "SlaveAddress": 1,
      "TransportType": "Rtu",
      "ConnectionType": "Serial",
      "SerialSettings": {
        "PortName": "COM1",
        "BaudRate": 9600,
        "DataBits": 8,
        "Parity": 0,
        "StopBits": 1
      },
      "RetryPolicy": {
        "RetryCount": 3,
        "RetryDelay": 1000,
        "ResponseTimeout": 1000
      },
      "Parameters": [
        {
          "Name": "Temperature",
          "Address": 0,
          "Type": "HoldingRegister",
          "DataType": "Int16",
          "ScaleFactor": 0.1,
          "Unit": "°C"
        }
      ]
    }
  ]
}
```

#### 4.1.3 常见维护任务

**修改通信参数**：
1. 在`appsettings.Modbus.json`中更新设备的通信参数
2. 重启应用程序或使用API刷新设备配置

**添加新的寄存器映射**：
1. 在设备配置中添加新的参数定义
2. 指定正确的地址、数据类型和转换参数

**监控通信状态**：
1. 使用仪表盘的设备状态面板
2. 查看日志文件中的通信错误

**故障排除**：
1. 检查物理连接（串口/网络）
2. 验证设备地址和通信参数
3. 使用Modbus测试工具验证设备响应

### 4.2 MQTT通信模块

MQTT模块用于与MQTT协议兼容的设备和服务器通信，支持发布/订阅模式。

#### 4.2.1 MQTT客户端配置

客户端配置示例：

```csharp
var options = new MqttClientOptions
{
    BrokerUrl = "localhost",
    Port = 1883,
    ClientId = $"client-{Guid.NewGuid()}",
    Username = "username", // 可选
    Password = "password", // 可选
    UseTls = false
};

// 依赖注入方式
services.AddMqttClient(options);
```

#### 4.2.2 MQTT服务器配置

服务器配置示例：

```csharp
services.AddMqttServer();
services.AddHostedService<MqttServerBackgroundService>();

// 配置服务器选项
var optionsBuilder = new MqttServerOptionsBuilder()
    .WithDefaultEndpoint()
    .WithDefaultEndpointPort(1883)
    .WithPersistentSessions()
    .WithMaxPendingMessagesPerClient(100);
```

#### 4.2.3 消息处理和处理器注册

注册消息处理器：

```csharp
// 实现消息处理器
public class DeviceStatusHandler : IMessageHandler
{
    public bool CanHandle(string topic) => topic.StartsWith("device/status/");
    
    public async Task HandleMessageAsync(string topic, string message)
    {
        // 处理消息
        Console.WriteLine($"收到状态更新: {message}");
    }
}

// 注册处理器
handlerRegistry.RegisterHandler(new DeviceStatusHandler());
```

#### 4.2.4 常见维护任务

**监控MQTT连接**：
1. 检查连接状态和日志
2. 查看MQTT服务器控制台

**修改主题结构**：
1. 更新消息处理器中的主题模式
2. 更新发布消息的主题路径

**调整QoS级别**：
根据消息重要性和网络环境调整服务质量级别：
- QoS 0：最多一次（无保证）
- QoS 1：至少一次（可能重复）
- QoS 2：恰好一次（保证送达且不重复）

### 4.3 数据采集与存储

#### 4.3.1 数据采集流程

系统使用Quartz.NET调度框架进行数据采集，主要步骤：

1. 定时从设备读取数据
2. 数据预处理（单位转换、异常检测）
3. 存储到数据库
4. 发送通知（如超限报警）

#### 4.3.2 数据库维护

**备份数据库**：

```bash
# 备份SQLite数据库
sqlite3 src/EnvizonController.Server/envizon.db ".backup 'backup/envizon-backup-$(date +%Y%m%d).db'"
```

**优化数据库**：

```bash
# 优化SQLite数据库
sqlite3 src/EnvizonController.Server/envizon.db "VACUUM;"
```

**管理历史数据**：

系统默认保留90天的历史数据，可通过修改配置调整保留期限：

```json
{
  "DataRetention": {
    "HistoricalDataDays": 90,
    "AlarmHistoryDays": 180
  }
}
```

#### 4.3.3 数据导出

系统支持以CSV和JSON格式导出数据：

1. 使用API导出：`GET /api/DataPoints/export?format=csv&startDate=2025-01-01&endDate=2025-05-01`
2. 使用UI界面的导出功能

### 4.4 UI界面

#### 4.4.1 UI架构

EnvizonController使用Avalonia UI框架实现跨平台用户界面，采用MVVM架构模式：

- **Views**：显示界面元素
- **ViewModels**：管理界面状态和业务逻辑
- **Models**：表示业务实体

#### 4.4.2 UI主题和样式

系统使用自定义主题，主题定义在以下文件中：

- `src/EnvizonController.Presentation/Styles/Colors.xaml`
- `src/EnvizonController.Presentation/Styles/Controls.xaml`

修改主题：

1. 编辑相应的样式文件
2. 重新构建应用程序

#### 4.4.3 添加新视图

添加新界面步骤：

1. 在`Views`目录创建新的视图文件（.axaml）
2. 创建对应的ViewModel类
3. 在`ViewLocator.cs`中注册视图
4. 在导航系统中添加新视图

## 5. 常见问题排查与解决

### 5.1 通信故障

#### 5.1.1 Modbus通信问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 连接超时 | 物理连接问题、设备地址错误 | 检查线缆连接、验证设备地址 |
| CRC校验错误 | 通信干扰、参数不匹配 | 检查波特率和通信参数、改用屏蔽线缆 |
| 异常响应 | 非法功能码、非法数据地址 | 检查访问的寄存器地址是否有效 |
| 间歇性连接丢失 | 网络不稳定、设备过热 | 增加重试次数、检查设备散热 |

**诊断步骤**：

1. 启用Modbus调试日志：
   ```json
   {
     "Logging": {
       "LogLevel": {
         "EnvizonController.Modbus": "Debug"
       }
     }
   }
   ```

2. 使用SerialTester工具验证设备通信

#### 5.1.2 MQTT连接问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 无法连接到代理 | 网络问题、认证失败 | 检查网络连接、验证凭据 |
| 消息未送达 | QoS级别不足、主题不匹配 | 提高QoS级别、检查主题名称 |
| 客户端断开 | 网络不稳定、心跳超时 | 调整KeepAlive时间、实现自动重连 |
| 性能下降 | 消息积压、内存不足 | 优化处理逻辑、增加资源分配 |

**诊断步骤**：

1. 启用MQTT调试日志
2. 使用MQTT.fx等客户端工具测试连接和消息发布

### 5.2 数据库问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 数据库锁定 | 并发访问冲突 | 增加超时设置、优化事务 |
| 查询性能下降 | 索引缺失、表碎片化 | 添加索引、执行VACUUM操作 |
| 数据文件过大 | 历史数据累积 | 配置数据清理策略、归档旧数据 |

### 5.3 UI响应问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 界面卡顿 | 数据绑定过多、UI线程阻塞 | 减少绑定数量、使用异步操作 |
| 内存泄漏 | 事件订阅未注销、大对象未释放 | 正确注销事件、使用弱引用 |
| 图表渲染慢 | 数据点过多 | 限制显示点数、使用数据抽样 |

### 5.4 系统性能优化

**内存使用优化**：
- 使用对象池减少GC压力
- 避免大对象堆上的分配

**CPU使用优化**：
- 使用异步IO操作
- 避免不必要的线程切换

**网络优化**：
- 批量读取数据而非频繁单次请求
- 使用数据缓存减少网络传输

## 6. 系统扩展指南

### 6.1 添加新设备类型

#### 6.1.1 添加设备定义

1. 在`Domain`层创建新设备类型
2. 在数据库中添加设备记录
3. 实现设备通信接口

**示例代码**：

```csharp
// 1. 创建设备类型
public class NewDeviceType : Device
{
    public string SpecificParameter { get; set; }
    
    // 设备特有属性和方法
}

// 2. 实现通信服务
public class NewDeviceTypeService : IDeviceService
{
    // 实现通信逻辑
}
```

#### 6.1.2 注册设备服务

在依赖注入容器中注册新设备服务：

```csharp
services.AddScoped<INewDeviceTypeService, NewDeviceTypeService>();
```

#### 6.1.3 添加UI组件

1. 创建设备特定的ViewModel
2. 创建设备特定的View
3. 更新设备选择逻辑

### 6.2 集成新通信协议

#### 6.2.1 创建协议适配器

1. 定义协议接口
2. 实现协议适配器
3. 注册协议服务

**示例代码**：

```csharp
// 1. 定义协议接口
public interface INewProtocolAdapter
{
    Task<bool> ConnectAsync();
    Task<byte[]> ReadDataAsync(string address);
    Task WriteDataAsync(string address, byte[] data);
}

// 2. 实现协议适配器
public class NewProtocolAdapter : INewProtocolAdapter
{
    // 实现协议通信逻辑
}
```

#### 6.2.2 添加协议配置

在`appsettings.json`中添加新协议配置：

```json
{
  "Protocols": {
    "NewProtocol": {
      "Enabled": true,
      "DefaultSettings": {
        // 协议特定配置
      }
    }
  }
}
```

### 6.3 扩展API功能

#### 6.3.1 添加新API端点

1. 创建新的Controller类
2. 定义API路由和动作方法
3. 实现认证和授权（如需要）

**示例代码**：

```csharp
[ApiController]
[Route("api/[controller]")]
public class NewFeatureController : ControllerBase
{
    private readonly INewFeatureService _service;
    
    public NewFeatureController(INewFeatureService service)
    {
        _service = service;
    }
    
    [HttpGet]
    public async Task<ActionResult<IEnumerable<NewFeatureDto>>> GetAll()
    {
        var items = await _service.GetAllAsync();
        return Ok(items);
    }
    
    [HttpPost]
    public async Task<ActionResult<NewFeatureDto>> Create(CreateNewFeatureDto dto)
    {
        var result = await _service.CreateAsync(dto);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }
    
    [HttpGet("{id}")]
    public async Task<ActionResult<NewFeatureDto>> GetById(long id)
    {
        var item = await _service.GetByIdAsync(id);
        if (item == null) return NotFound();
        return Ok(item);
    }
}
```

## 7. 系统维护最佳实践

### 7.1 日志管理

#### 7.1.1 日志配置

EnvizonController使用Serilog进行日志记录，配置在`appsettings.json`中：

```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "EnvizonController.Modbus": "Debug",
        "EnvizonController.Mqtt": "Debug"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "logs/log-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      },
      {
        "Name": "Console"
      }
    ]
  }
}
```

#### 7.1.2 日志轮转

日志文件每天轮转，保留最近30天的日志文件。可根据需要调整`retainedFileCountLimit`参数。

#### 7.1.3 日志监控

1. 使用日志管理工具（如Seq、ELK）集中管理日志
2. 设置关键错误的报警通知

### 7.2 备份策略

#### 7.2.1 数据库备份

1. 定期备份SQLite数据库文件
2. 自动化备份脚本示例：

```bash
# 备份脚本示例 (backup.sh)
#!/bin/bash
BACKUP_DIR="/path/to/backups"
SOURCE_DB="/path/to/envizon.db"
DATE=$(date +%Y%m%d%H%M)

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份数据库
sqlite3 "$SOURCE_DB" ".backup '$BACKUP_DIR/envizon-$DATE.db'"

# 压缩备份
gzip "$BACKUP_DIR/envizon-$DATE.db"

# 删除30天前的备份
find "$BACKUP_DIR" -name "envizon-*.db.gz" -mtime +30 -delete
```

#### 7.2.2 配置备份

定期备份以下配置文件：

- `appsettings.json`
- `appsettings.Modbus.json`
- 自定义设备配置文件

### 7.3 性能监控

#### 7.3.1 系统指标监控

监控以下关键指标：

- CPU使用率
- 内存使用情况
- 磁盘空间
- 网络连接状态

#### 7.3.2 应用程序指标

监控以下应用程序指标：

- API响应时间
- 设备连接成功率
- 数据采集完成率
- 异常和错误频率

### 7.4 安全最佳实践

#### 7.4.1 认证与授权

1. 启用API认证：

```csharp
// 配置认证
services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options => {
        // JWT配置
    });

services.AddAuthorization(options => {
    // 授权策略配置
});
```

2. 使用HTTPS保护API通信

#### 7.4.2 敏感数据保护

1. 加密储存敏感配置（如密码、API密钥）
2. 使用.NET数据保护API：

```csharp
services.AddDataProtection()
    .SetApplicationName("EnvizonController")
    .PersistKeysToFileSystem(new DirectoryInfo("keys"));
```

#### 7.4.3 定期更新和补丁

1. 监控.NET安全公告
2. 定期更新NuGet包依赖
3. 应用安全补丁

### 7.5 代码管理最佳实践

#### 7.5.1 版本控制

1. 使用Git进行版本控制
2. 遵循GitFlow工作流：
   - `main`分支：稳定发布版本
   - `develop`分支：开发集成
   - 功能分支：新功能开发
   - 发布分支：版本准备
   - 热修复分支：生产环境修复

#### 7.5.2 代码审查

1. 所有合并请求必须经过代码审查
2. 使用静态代码分析工具检查代码质量

#### 7.5.3 测试实践

1. 编写单元测试覆盖核心功能
2. 实施集成测试验证组件交互
3. 使用自动化UI测试验证用户界面

## 8. 附录

### 8.1 参考文档

- [.NET 9.0 文档](https://docs.microsoft.com/dotnet)
- [Avalonia UI 文档](https://docs.avaloniaui.net/)
- [Entity Framework Core 文档](https://docs.microsoft.com/ef/core/)
- [Modbus 协议规范](https://modbus.org/specs.php)
- [MQTT 协议规范](https://mqtt.org/mqtt-specification/)

### 8.2 常用命令参考

```bash
# 构建项目
dotnet build src/EnvizonController.sln

# 运行测试
dotnet test src/EnvizonController.sln

# 发布项目
dotnet publish src/Platforms/EnvizonController.Desktop -c Release -o publish

# 运行服务器
cd src/EnvizonController.Server
dotnet run
```

### 8.3 故障排除步骤

1. 检查日志文件获取错误信息
2. 验证网络连接和设备通信
3. 检查数据库连接和完整性
4. 验证配置文件设置
5. 检查系统资源使用情况

### 8.4 联系与支持

- 技术支持邮箱：<EMAIL>
- 问题跟踪系统：https://github.com/your-org/envizon-controller/issues
- 文档网站：https://docs.envizon-controller.com