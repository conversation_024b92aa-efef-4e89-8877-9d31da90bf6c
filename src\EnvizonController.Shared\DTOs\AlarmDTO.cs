﻿using EnvizonController.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EnvizonController.Shared.DTOs
{
    public class AlarmDTO
    {
        public long Id { get; set; }
        public long DeviceId { get; set; }
        public long? ProtocolId { get; set; }
        public int? ProtocolItemIndex { get; set; }

        
        public long? TestId { get; set; }
        /// <summary>
        ///     父报警记录的ID，用于构建报警层次结构
        ///     如果为null或空字符串，则表示这是顶级报警
        /// </summary>
        public string? ParentId { get; set; }

        public string Name { get; set; } = null!;

        public string Message { get; set; } = null!;
        public AlarmSeverity Level { get; set; } = AlarmSeverity.High;

        public AlarmStatus Status { get; set; } = AlarmStatus.Active; // 默认为活动状态
        public string? ProcessedBy { get; set; }

        #region Time

        /// <summary>
        ///     报警事件发生时的时间戳。
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        ///     此报警记录最后更新的时间戳。
        ///     可用于跟踪记录的修改。
        /// </summary>
        public DateTime? LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        ///     报警被用户手动清除的时间戳。
        ///     这通常在报警已解决或不再相关时发生。
        ///     如果尚未清除，则为 Null。
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        #endregion
    }

    /// <summary>
    /// 创建报警DTO - 用于API请求
    /// </summary>
    public class CreateAlarmDto
    {
        public long? TestId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public AlarmSeverity Level { get; set; } = AlarmSeverity.Medium;
    }

    /// <summary>
    /// 处理报警DTO - 用于API请求
    /// </summary>
    public class ProcessAlarmDto
    {
        public string ProcessedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 报警查询参数DTO - 用于API请求过滤
    /// </summary>
    public class AlarmQueryParams
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public AlarmStatus? Status { get; set; }
        public AlarmSeverity? Level { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public long? TestId { get; set; }
        public long? DeviceId { get; set; }
    }
}
