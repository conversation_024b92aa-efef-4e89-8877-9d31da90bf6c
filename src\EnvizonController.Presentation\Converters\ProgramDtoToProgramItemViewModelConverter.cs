using Avalonia.Data.Converters;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Shared.DTOs;
using System;
using System.Globalization;
using System.Linq;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将ProgramDTO转换为ProgramItemViewModel的转换器，用于DataGrid绑定
    /// </summary>
    public class ProgramDtoToProgramItemViewModelConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 如果值为空，返回null
            if (value == null)
                return null;

            // 如果值已经是ProgramItemViewModel，直接返回
            if (value is ProgramItemViewModel programItemViewModel)
                return programItemViewModel;

            // 如果值是ProgramDTO，尝试转换为ProgramItemViewModel
            if (value is ProgramDTO programDto)
            {
                // 这里应该获取ViewModel实例，但简单起见，我们从DTO创建一个新的ViewModel
                return ProgramItemViewModel.FromDto(programDto);
            }

            // 如果不能转换，返回原值
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 如果值为空，返回null
            if (value == null)
                return null;

            // 如果值是ProgramItemViewModel，返回其Program属性
            if (value is ProgramItemViewModel programItemViewModel)
                return programItemViewModel.ToDto();

            // 如果值已经是ProgramDTO，直接返回
            if (value is ProgramDTO programDto)
                return programDto;

            // 如果不能转换，返回null
            return null;
        }
    }
}