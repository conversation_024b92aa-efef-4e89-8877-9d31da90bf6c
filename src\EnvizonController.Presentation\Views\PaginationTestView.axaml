<UserControl
    x:Class="EnvizonController.Presentation.Views.PaginationTestView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:EnvizonController.Presentation.Controls"
    xmlns:models="using:EnvizonController.Presentation.Controls.Models">

    <Design.DataContext>
        <models:PaginationInfo CurrentPage="5" TotalCount="1000" PageSize="20" />
    </Design.DataContext>

    <ScrollViewer Background="#0B0A1A">
        <StackPanel Margin="20" Spacing="30">
            
            <!-- 标题 -->
            <TextBlock 
                Text="分页控件尺寸对比测试" 
                FontSize="24" 
                FontWeight="Bold" 
                Foreground="#0DF0FF" 
                HorizontalAlignment="Center" />

            <!-- 默认尺寸分页控件 -->
            <StackPanel Spacing="10">
                <TextBlock 
                    Text="默认尺寸 (Default Size)" 
                    FontSize="16" 
                    FontWeight="SemiBold" 
                    Foreground="White" />
                <controls:PaginationControl 
                    Size="Default"
                    ShowPageSizeSelector="True"
                    ShowQuickJump="True"
                    ShowCurrentPageInfo="True"
                    ShowPageInfo="True"
                    ShowFirstLastButtons="True"
                    MaxVisiblePages="7">
                    <controls:PaginationControl.PaginationInfo>
                        <models:PaginationInfo CurrentPage="5" TotalCount="1000" PageSize="20" />
                    </controls:PaginationControl.PaginationInfo>
                </controls:PaginationControl>
            </StackPanel>

            <!-- 小尺寸分页控件 -->
            <StackPanel Spacing="10">
                <TextBlock 
                    Text="小尺寸 (Small Size)" 
                    FontSize="16" 
                    FontWeight="SemiBold" 
                    Foreground="White" />
                <controls:PaginationControl 
                    Size="Small"
                    ShowPageSizeSelector="True"
                    ShowQuickJump="True"
                    ShowCurrentPageInfo="True"
                    ShowPageInfo="True"
                    ShowFirstLastButtons="True"
                    MaxVisiblePages="7">
                    <controls:PaginationControl.PaginationInfo>
                        <models:PaginationInfo CurrentPage="5" TotalCount="1000" PageSize="20" />
                    </controls:PaginationControl.PaginationInfo>
                </controls:PaginationControl>
            </StackPanel>

            <!-- 小尺寸简化版本 -->
            <StackPanel Spacing="10">
                <TextBlock 
                    Text="小尺寸简化版 (Small Size - Simplified)" 
                    FontSize="16" 
                    FontWeight="SemiBold" 
                    Foreground="White" />
                <controls:PaginationControl 
                    Size="Small"
                    ShowPageSizeSelector="False"
                    ShowQuickJump="False"
                    ShowCurrentPageInfo="True"
                    ShowPageInfo="False"
                    ShowFirstLastButtons="True"
                    MaxVisiblePages="5">
                    <controls:PaginationControl.PaginationInfo>
                        <models:PaginationInfo CurrentPage="3" TotalCount="500" PageSize="10" />
                    </controls:PaginationControl.PaginationInfo>
                </controls:PaginationControl>
            </StackPanel>

            <!-- 小尺寸最简版本 -->
            <StackPanel Spacing="10">
                <TextBlock 
                    Text="小尺寸最简版 (Small Size - Minimal)" 
                    FontSize="16" 
                    FontWeight="SemiBold" 
                    Foreground="White" />
                <controls:PaginationControl 
                    Size="Small"
                    ShowPageSizeSelector="False"
                    ShowQuickJump="False"
                    ShowCurrentPageInfo="False"
                    ShowPageInfo="False"
                    ShowFirstLastButtons="False"
                    MaxVisiblePages="5">
                    <controls:PaginationControl.PaginationInfo>
                        <models:PaginationInfo CurrentPage="2" TotalCount="100" PageSize="10" />
                    </controls:PaginationControl.PaginationInfo>
                </controls:PaginationControl>
            </StackPanel>

            <!-- 对比展示 -->
            <StackPanel Spacing="10">
                <TextBlock 
                    Text="并排对比" 
                    FontSize="16" 
                    FontWeight="SemiBold" 
                    Foreground="White" />
                <StackPanel Orientation="Horizontal" Spacing="20" HorizontalAlignment="Center">
                    <StackPanel Spacing="5">
                        <TextBlock Text="默认" FontSize="12" Foreground="#9DA3AF" HorizontalAlignment="Center" />
                        <controls:PaginationControl 
                            Size="Default"
                            ShowPageSizeSelector="False"
                            ShowQuickJump="False"
                            ShowCurrentPageInfo="False"
                            ShowPageInfo="False"
                            ShowFirstLastButtons="True"
                            MaxVisiblePages="5">
                            <controls:PaginationControl.PaginationInfo>
                                <models:PaginationInfo CurrentPage="3" TotalCount="100" PageSize="10" />
                            </controls:PaginationControl.PaginationInfo>
                        </controls:PaginationControl>
                    </StackPanel>
                    
                    <StackPanel Spacing="5">
                        <TextBlock Text="小尺寸" FontSize="12" Foreground="#9DA3AF" HorizontalAlignment="Center" />
                        <controls:PaginationControl 
                            Size="Small"
                            ShowPageSizeSelector="False"
                            ShowQuickJump="False"
                            ShowCurrentPageInfo="False"
                            ShowPageInfo="False"
                            ShowFirstLastButtons="True"
                            MaxVisiblePages="5">
                            <controls:PaginationControl.PaginationInfo>
                                <models:PaginationInfo CurrentPage="3" TotalCount="100" PageSize="10" />
                            </controls:PaginationControl.PaginationInfo>
                        </controls:PaginationControl>
                    </StackPanel>
                </StackPanel>
            </StackPanel>

        </StackPanel>
    </ScrollViewer>
</UserControl> 