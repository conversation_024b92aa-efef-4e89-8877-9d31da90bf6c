using CommunityToolkit.Mvvm.Messaging.Messages;
using EnvizonController.Presentation.ViewModels.Program;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Presentation.Messages;

/// <summary>
/// 程序变更消息，用于通知程序的添加、更新或删除操作
/// </summary>
public class ProgramChangedMessage : ValueChangedMessage<(ProgramItemViewModel Program, ChangeType Type)>
{
    /// <summary>
    /// 被变更的程序
    /// </summary>
    public ProgramItemViewModel Program => Value.Program;

    /// <summary>
    /// 变更类型（添加、更新、删除）
    /// </summary>
    public ChangeType ChangeType => Value.Type;

    /// <summary>
    /// 创建一个程序变更消息
    /// </summary>
    /// <param name="program">变更的程序</param>
    /// <param name="type">变更类型</param>
    public ProgramChangedMessage(ProgramItemViewModel program, ChangeType type)
        : base((program, type))
    {
    }
}

/// <summary>
/// 变更类型枚举
/// </summary>
public enum ChangeType
{
    /// <summary>
    /// 添加
    /// </summary>
    Added,

    /// <summary>
    /// 更新
    /// </summary>
    Updated,

    /// <summary>
    /// 删除
    /// </summary>
    Deleted
} 