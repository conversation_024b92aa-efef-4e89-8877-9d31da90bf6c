using Microsoft.AspNetCore.Mvc;
using EnvizonController.Application.Interfaces;
using EnvizonController.Application.Interfaces.Services;
using AutoMapper;
using EnvizonController.Shared.DTOs;
using EnvizonController.Shared.DTOs.Common;

namespace EnvizonController.Server.Controllers
{
    /// <summary>
    /// 程式表控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ProgramsController : ControllerBase
    {
        private readonly IProgramAppService _programService;
        private readonly ILogger<ProgramsController> _logger;
        private readonly IMapper _mapper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="programService">程式表应用服务</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="mapper">对象映射器</param>
        public ProgramsController(
            IProgramAppService programService,
            ILogger<ProgramsController> logger,
            IMapper mapper)
        {
            _programService = programService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// 获取所有程式表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>程式表列表</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<ProgramDTO>), StatusCodes.Status200OK)]
        public async Task<ActionResult<PagedResultDto<ProgramDTO>>> GetPrograms(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var programs = await _programService.GetAllProgramsAsync();
                var programsList = programs.ToList();

                var pagedPrograms = programsList
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return Ok(new PagedResultDto<ProgramDTO>
                {
                    Items = pagedPrograms,
                    TotalCount = programsList.Count,
                    Page = page,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式表列表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式表列表时发生错误");
            }
        }

        /// <summary>
        /// 根据ID获取程式表
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>程式表详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(ProgramDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ProgramDTO>> GetProgram(long id)
        {
            try
            {
                var program = await _programService.GetProgramByIdAsync(id);
                if (program == null)
                {
                    return NotFound($"ID为{id}的程式表不存在");
                }

                return Ok(program);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式表 {ProgramId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式表详情时发生错误");
            }
        }

        /// <summary>
        /// 按名称获取程式表
        /// </summary>
        /// <param name="name">程式表名称</param>
        /// <returns>程式表详情</returns>
        [HttpGet("name/{name}")]
        [ProducesResponseType(typeof(ProgramDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ProgramDTO>> GetProgramByName(string name)
        {
            try
            {
                var program = await _programService.GetProgramByNameAsync(name);
                if (program == null)
                {
                    return NotFound($"名称为{name}的程式表不存在");
                }

                return Ok(program);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按名称获取程式表 {ProgramName} 时出错", name);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式表详情时发生错误");
            }
        }

        /// <summary>
        /// 创建程式表
        /// </summary>
        /// <param name="programDto">程式表信息</param>
        /// <returns>创建的程式表</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ProgramDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramDTO>> CreateProgram(ProgramDTO programDto)
        {
            try
            {
                var createdProgram = await _programService.CreateProgramAsync(programDto);
                return CreatedAtAction(nameof(GetProgram), new { id = createdProgram.Id }, createdProgram);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "创建程式表参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建程式表时出错");
                return StatusCode(StatusCodes.Status500InternalServerError, "创建程式表时发生错误");
            }
        }

        /// <summary>
        /// 更新程式表
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <param name="programDto">更新的程式表信息</param>
        /// <returns>更新后的程式表</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(ProgramDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramDTO>> UpdateProgram(long id, ProgramDTO programDto)
        {
            try
            {
                if (id != programDto.Id)
                {
                    return BadRequest("路径ID与请求体ID不匹配");
                }

                var updatedProgram = await _programService.UpdateProgramAsync(programDto);
                return Ok(updatedProgram);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "更新程式表参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新程式表 {ProgramId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "更新程式表时发生错误");
            }
        }

        /// <summary>
        /// 删除程式表
        /// </summary>
        /// <param name="id">程式表ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteProgram(long id)
        {
            try
            {
                var program = await _programService.GetProgramByIdAsync(id);
                if (program == null)
                {
                    return NotFound($"ID为{id}的程式表不存在");
                }

                bool result = await _programService.DeleteProgramAsync(id);
                if (result)
                {
                    return NoContent();
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "删除程式表失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除程式表 {ProgramId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "删除程式表时发生错误");
            }
        }

        /// <summary>
        /// 获取程式表的所有步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <returns>步骤列表</returns>
        [HttpGet("{programId}/steps")]
        [ProducesResponseType(typeof(IEnumerable<ProgramStepDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<ProgramStepDTO>>> GetProgramSteps(long programId)
        {
            try
            {
                var program = await _programService.GetProgramByIdAsync(programId);
                if (program == null)
                {
                    return NotFound($"ID为{programId}的程式表不存在");
                }

                var steps = await _programService.GetProgramStepsAsync(programId);
                return Ok(steps);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程式表 {ProgramId} 的步骤时出错", programId);
                return StatusCode(StatusCodes.Status500InternalServerError, "获取程式步骤时发生错误");
            }
        }

        /// <summary>
        /// 创建程式步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <param name="stepDto">步骤信息</param>
        /// <returns>创建的步骤</returns>
        [HttpPost("{programId}/steps")]
        [ProducesResponseType(typeof(ProgramStepDTO), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramStepDTO>> CreateProgramStep(long programId, ProgramStepDTO stepDto)
        {
            try
            {
                if (programId != stepDto.ProgramId)
                {
                    return BadRequest("路径ID与请求体ID不匹配");
                }

                var program = await _programService.GetProgramByIdAsync(programId);
                if (program == null)
                {
                    return NotFound($"ID为{programId}的程式表不存在");
                }

                var createdStep = await _programService.CreateProgramStepAsync(stepDto);
                return CreatedAtAction(nameof(GetProgramSteps), new { programId = createdStep.ProgramId }, createdStep);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "创建程式步骤参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为程式表 {ProgramId} 创建步骤时出错", programId);
                return StatusCode(StatusCodes.Status500InternalServerError, "创建程式步骤时发生错误");
            }
        }

        /// <summary>
        /// 更新程式步骤
        /// </summary>
        /// <param name="id">步骤ID</param>
        /// <param name="stepDto">更新的步骤信息</param>
        /// <returns>更新后的步骤</returns>
        [HttpPut("steps/{id}")]
        [ProducesResponseType(typeof(ProgramStepDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ProgramStepDTO>> UpdateProgramStep(long id, ProgramStepDTO stepDto)
        {
            try
            {
                if (id != stepDto.Id)
                {
                    return BadRequest("路径ID与请求体ID不匹配");
                }

                var updatedStep = await _programService.UpdateProgramStepAsync(stepDto);
                return Ok(updatedStep);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "更新程式步骤参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新程式步骤 {StepId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "更新程式步骤时发生错误");
            }
        }

        /// <summary>
        /// 删除程式步骤
        /// </summary>
        /// <param name="id">步骤ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("steps/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteProgramStep(long id)
        {
            try
            {
                bool result = await _programService.DeleteProgramStepAsync(id);
                if (result)
                {
                    return NoContent();
                }
                else
                {
                    return NotFound($"ID为{id}的程式步骤不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除程式步骤 {StepId} 时出错", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "删除程式步骤时发生错误");
            }
        }

        /// <summary>
        /// 批量更新程式步骤
        /// </summary>
        /// <param name="programId">程式表ID</param>
        /// <param name="stepDtos">步骤信息列表</param>
        /// <returns>更新后的步骤列表</returns>
        [HttpPut("{programId}/steps/batch")]
        [ProducesResponseType(typeof(IEnumerable<ProgramStepDTO>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<IEnumerable<ProgramStepDTO>>> BatchUpdateProgramSteps(long programId, [FromBody] IEnumerable<ProgramStepDTO> stepDtos)
        {
            try
            {
                // 检查程式表是否存在
                var program = await _programService.GetProgramByIdAsync(programId);
                if (program == null)
                {
                    return NotFound($"ID为{programId}的程式表不存在");
                }

                // 验证步骤是否属于该程式表
                if (stepDtos.Any(s => s.ProgramId != programId))
                {
                    return BadRequest("所有步骤必须属于同一个程式表");
                }

                var updatedSteps = await _programService.BatchUpdateProgramStepsAsync(programId, stepDtos);
                return Ok(updatedSteps);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "批量更新程式步骤参数验证失败");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新程式表 {ProgramId} 的步骤时出错", programId);
                return StatusCode(StatusCodes.Status500InternalServerError, "批量更新程式步骤时发生错误");
            }
        }
    }
}