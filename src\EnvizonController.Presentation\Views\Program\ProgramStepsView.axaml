<UserControl
    x:Class="EnvizonController.Presentation.Views.ProgramStepsView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="600"
    x:DataType="vm:ProgramViewModel"
    mc:Ignorable="d">

    <Grid Margin="15" RowDefinitions="Auto,*,Auto">
        <!--  顶部标题和刷新按钮  -->
        <Grid
            Grid.Row="0"
            Margin="0,0,0,15"
            ColumnDefinitions="*,Auto">
            <TextBlock
                Grid.Column="0"
                Classes="h3 glow-primary font-cyber"
                Text="程式步骤管理" />

            <Button
                Grid.Column="1"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#0DF0FF"
                BorderThickness="1"
                Command="{Binding RefreshProgramStepsCommand}"
                CornerRadius="4">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon primary"
                        Text="&#xf021;" />
                    <TextBlock Classes="primary font-cyber" Text="刷新步骤" />
                </StackPanel>
            </Button>
        </Grid>

        <!--  步骤列表  -->
        <DataGrid
            Grid.Row="1"
            Margin="0,0,0,10"
            AutoGenerateColumns="False"
            Background="Transparent"
            BorderBrush="#2A2942"
            BorderThickness="1"
            CanUserReorderColumns="False"
            CanUserResizeColumns="True"
            CanUserSortColumns="False"
            GridLinesVisibility="All"
            ItemsSource="{Binding ProgramSteps}"
            SelectedItem="{Binding SelectedProgramStep}"
            SelectionMode="Single">
            <DataGrid.Styles>
                <Style Selector="DataGridRow:selected">
                    <Setter Property="Background" Value="#2A2942" />
                    <Setter Property="BorderBrush" Value="#0DF0FF" />
                    <Setter Property="BorderThickness" Value="1" />
                </Style>
                <Style Selector="DataGridCell">
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="Padding" Value="10,8" />
                </Style>
                <Style Selector="DataGridColumnHeader">
                    <Setter Property="Background" Value="#14131C" />
                    <Setter Property="Foreground" Value="#FFFFFF" />
                    <Setter Property="Padding" Value="10,8" />
                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                    <Setter Property="BorderBrush" Value="#2A2942" />
                </Style>
            </DataGrid.Styles>
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Index}"
                    Header="NO"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Width="*" Header="温度">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Temperature, StringFormat={}{0}°C}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F1"
                                Increment="0.5"
                                Maximum="150"
                                Minimum="-40"
                                Value="{Binding Temperature, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="湿度">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Humidity, StringFormat={}{0}%}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F0"
                                Increment="1"
                                Maximum="100"
                                Minimum="0"
                                Value="{Binding Humidity, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="持续时间">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock VerticalAlignment="Center" Text="{Binding Duration, StringFormat={}{0}秒}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <NumericUpDown
                                VerticalAlignment="Center"
                                FormatString="F0"
                                Increment="60"
                                Maximum="86400"
                                Minimum="0"
                                Value="{Binding Duration, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="*" Header="线性变化">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox
                                HorizontalAlignment="Center"
                                IsChecked="{Binding IsLinear}"
                                IsEnabled="False" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <CheckBox HorizontalAlignment="Center" IsChecked="{Binding IsLinear, Mode=TwoWay}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Width="180" Header="操作">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Panel>
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    IsVisible="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).EditingProgramStep, Converter={x:Static ObjectConverters.IsNull}}"
                                    Orientation="Horizontal">
                                    <!--  删除按钮  -->
                                    <Button
                                        Margin="2,0"
                                        Padding="5"
                                        Background="Transparent"
                                        BorderBrush="#FF5252"
                                        BorderThickness="1"
                                        Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).ConfirmDeleteStepCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4"
                                        ToolTip.Tip="删除">
                                        <TextBlock
                                            Classes="font-icon"
                                            Foreground="#FF5252"
                                            Text="&#xf1f8;" />
                                    </Button>
                                </StackPanel>

                                <StackPanel
                                    HorizontalAlignment="Center"
                                    IsVisible="{Binding !$parent[UserControl].((vm:ProgramViewModel)DataContext).EditingProgramStep, Converter={x:Static ObjectConverters.IsNull}}"
                                    Orientation="Horizontal">
                                    <!--  保存按钮  -->
                                    <Button
                                        Margin="2,0"
                                        Padding="5"
                                        Background="Transparent"
                                        BorderBrush="#4CAF50"
                                        BorderThickness="1"
                                        Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).SaveStepEditCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4"
                                        ToolTip.Tip="保存">
                                        <TextBlock
                                            Classes="font-icon"
                                            Foreground="#4CAF50"
                                            Text="&#xf00c;" />
                                    </Button>

                                    <!--  取消按钮  -->
                                    <Button
                                        Margin="2,0"
                                        Padding="5"
                                        Background="Transparent"
                                        BorderBrush="#FF5252"
                                        BorderThickness="1"
                                        Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).CancelStepEditCommand}"
                                        CommandParameter="{Binding}"
                                        CornerRadius="4"
                                        ToolTip.Tip="取消">
                                        <TextBlock
                                            Classes="font-icon"
                                            Foreground="#FF5252"
                                            Text="&#xf00d;" />
                                    </Button>
                                </StackPanel>

                            </Panel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>

                    <!--  编辑状态模板  -->
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <!--  保存按钮  -->
                                <Button
                                    Margin="2,0"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderBrush="#4CAF50"
                                    BorderThickness="1"
                                    Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).SaveStepEditCommand}"
                                    CommandParameter="{Binding}"
                                    CornerRadius="4"
                                    IsVisible="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).EditingProgramStep, Converter={x:Static ObjectConverters.IsNotNull}}"
                                    ToolTip.Tip="保存">
                                    <TextBlock
                                        Classes="font-icon"
                                        Foreground="#4CAF50"
                                        Text="&#xf00c;" />
                                </Button>

                                <!--  取消按钮  -->
                                <Button
                                    Margin="2,0"
                                    Padding="5"
                                    Background="Transparent"
                                    BorderBrush="#FF5252"
                                    BorderThickness="1"
                                    Command="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).CancelStepEditCommand}"
                                    CommandParameter="{Binding}"
                                    CornerRadius="4"
                                    IsVisible="{Binding $parent[UserControl].((vm:ProgramViewModel)DataContext).EditingProgramStep, Converter={x:Static ObjectConverters.IsNotNull}}"
                                    ToolTip.Tip="取消">
                                    <TextBlock
                                        Classes="font-icon"
                                        Foreground="#FF5252"
                                        Text="&#xf00d;" />
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!--  操作按钮  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <Button
                Margin="5,0"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#4CAF50"
                BorderThickness="1"
                Command="{Binding SaveAllStepsCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#4CAF50"
                        Text="&#xf0c7;" />
                    <TextBlock Foreground="#4CAF50" Text="保存所有更改" />
                </StackPanel>
            </Button>
            <Button
                Margin="5,0"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#4CAF50"
                BorderThickness="1"
                Command="{Binding AddProgramStepCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#4CAF50"
                        Text="&#xf067;" />
                    <TextBlock Foreground="#4CAF50" Text="添加步骤" />
                </StackPanel>
            </Button>

            <Button
                Margin="5,0"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#FFC107"
                BorderThickness="1"
                Command="{Binding MoveProgramStepUpCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#FFC107"
                        Text="&#xf062;" />
                    <TextBlock Foreground="#FFC107" Text="上移" />
                </StackPanel>
            </Button>
            <Button
                Margin="5,0"
                Padding="12,6"
                Background="Transparent"
                BorderBrush="#FF9800"
                BorderThickness="1"
                Command="{Binding MoveProgramStepDownCommand}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        Classes="font-icon"
                        Foreground="#FF9800"
                        Text="&#xf063;" />
                    <TextBlock Foreground="#FF9800" Text="下移" />
                </StackPanel>
            </Button>
        </StackPanel>

        <!--  删除确认弹窗  -->
        <Panel
            Name="DeleteConfirmPanel"
            IsVisible="{Binding IsShowingDeleteConfirm}"
            ZIndex="100">
            <Rectangle Fill="#80000000" />
            <Border
                Width="300"
                Padding="15"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Background="#14131C"
                BorderBrush="#FF5252"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,15"
                        HorizontalAlignment="Center"
                        Classes="h4 font-cyber"
                        Text="确认删除" />
                    <TextBlock
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        Text="确定要删除此步骤吗？此操作无法撤销。"
                        TextAlignment="Center" />
                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                        <Button
                            Margin="5,0"
                            Padding="15,8"
                            Background="#FF5252"
                            BorderThickness="0"
                            Command="{Binding ConfirmDeleteCommand}"
                            CornerRadius="4">
                            <TextBlock Text="确认删除" />
                        </Button>
                        <Button
                            Margin="5,0"
                            Padding="15,8"
                            Background="Transparent"
                            BorderBrush="#CCCCCC"
                            BorderThickness="1"
                            Command="{Binding CancelDeleteCommand}"
                            CornerRadius="4">
                            <TextBlock Text="取消" />
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Panel>
    </Grid>
</UserControl> 