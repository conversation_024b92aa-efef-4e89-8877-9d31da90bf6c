<ResourceDictionary
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:program="clr-namespace:EnvizonController.Presentation.ViewModels.Program"
    xmlns:uikit="https://github.com/avaloniaui/avaloniauikit"
    xmlns:views="clr-namespace:EnvizonController.Presentation.Views"
    xmlns:vm="using:EnvizonController.Presentation.ViewModels">


    <!--  高级设置对话框模板  -->
    <!--
    <DataTemplate x:Key="AdvancedSettingsViewModel" DataType="vm:AdvancedSettingsViewModel">
        <uikit:Dialog Header="{Binding Title}" ShowCloseButton="{Binding ShowCloseButton}">
            <uikit:Dialog.Content>
                <views:AdvancedSettingsView/>
            </uikit:Dialog.Content>

        </uikit:Dialog>
    </DataTemplate>

    -->
    <!--  报警处理对话框模板  -->
    <!--
    <DataTemplate x:Key="AlarmProcessViewModel" DataType="vm:AlarmProcessViewModel">
        <uikit:Dialog Header="{Binding Title}" ShowCloseButton="{Binding ShowCloseButton}">
            <uikit:Dialog.Content>
                <views:AlarmProcessView/>
            </uikit:Dialog.Content>
        </uikit:Dialog>
    </DataTemplate>-->
    <!--  设置视图  -->
   
    <!--<DataTemplate x:Key="ProgramItemViewModel" DataType="program:ProgramItemViewModel">
        <uikit:Dialog Header="{x:Null}">
            <uikit:Dialog.Content>
                <Grid>
                    <Grid Margin="15" RowDefinitions="Auto,*">
                        <TextBlock
                            Grid.Row="0"
                            Margin="0,0,0,15"
                            Classes="h3 glow-primary font-cyber"
                            Text="程式设置" />
                        <Border
                            Grid.Row="1"
                            Padding="20"
                            Background="#0F0E1D"
                            Classes="cyber-noGlow-border">
                            <StackPanel Spacing="15">
                                <TextBlock Classes="h4 primary font-cyber" Text="基本设置" />

                                <Grid
                                    Margin="0,10,0,20"
                                    ColumnDefinitions="120,*"
                                    RowDefinitions="Auto,Auto,Auto,Auto">
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        Classes="gray"
                                        Text="程式名称:" />
                                    <TextBox
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Text="{Binding Name}"
                                        Watermark="请输入程式名称" />

                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="0,10,0,0"
                                        VerticalAlignment="Center"
                                        Classes="gray"
                                        Text="循环次数:" />
                                    <NumericUpDown
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Width="200"
                                        Margin="0,10,0,0"
                                        HorizontalAlignment="Left"
                                        Maximum="999"
                                        Minimum="1"
                                        Value="{Binding CycleCount}" />

                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        Margin="0,10,0,0"
                                        VerticalAlignment="Center"
                                        Classes="gray"
                                        Text="循环开始:" />
                                    <NumericUpDown
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        Width="200"
                                        Margin="0,10,0,0"
                                        HorizontalAlignment="Left"
                                        Maximum="100"
                                        Minimum="1"
                                        Value="{Binding CycleStart}" />

                                    <TextBlock
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Margin="0,10,0,0"
                                        VerticalAlignment="Center"
                                        Classes="gray"
                                        Text="循环结束:" />
                                    <NumericUpDown
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        Width="200"
                                        Margin="0,10,0,0"
                                        HorizontalAlignment="Left"
                                        Maximum="100"
                                        Minimum="1"
                                        Value="{Binding CycleEnd}" />
                                </Grid>

                                <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                                    <Button
                                        Margin="5,0"
                                        Padding="15,8"
                                        Background="Transparent"
                                        BorderBrush="#FF4556"
                                        BorderThickness="1"
                                        Command="{Binding CancelCommand}"
                                        CornerRadius="4">
                                        <TextBlock
                                            Classes="font-cyber"
                                            Foreground="#FF4556"
                                            Text="取消" />
                                    </Button>
                                    <Button
                                        Margin="5,0"
                                        Padding="15,8"
                                        Background="Transparent"
                                        BorderBrush="#0DF0FF"
                                        BorderThickness="1"
                                        Command="{Binding SaveCommand}"
                                        CornerRadius="4">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock
                                                Margin="0,0,5,0"
                                                Classes="font-icon primary"
                                                Text="&#xf0c7;" />
                                            <TextBlock Classes="primary font-cyber" Text="保存" />
                                        </StackPanel>
                                        <Button.Effect>
                                            <DropShadowEffect BlurRadius="10" Color="#0DF0FF" />
                                        </Button.Effect>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Grid>

            </uikit:Dialog.Content>
        </uikit:Dialog>
    </DataTemplate>-->

</ResourceDictionary>