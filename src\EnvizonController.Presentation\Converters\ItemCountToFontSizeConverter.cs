﻿// YourApp/Converters/ItemCountToFontSizeConverter.cs

using System.Globalization;
using Avalonia;
using Avalonia.Data.Converters;

// 如果需要处理 IEnumerable 而不是直接的 Count

namespace EnvizonController.Presentation.Converters // 确保命名空间正确
{
    public class ItemCountToFontSizeConverter : IValueConverter
    {
        // 当集合只有一个项时应用的字体大小
        public double SingleItemFontSize { get; set; } = 60.0;

        // 默认的字体大小（当集合项数不为1时）
        public double DefaultFontSize { get; set; } = 45.0; // 假设你原来的大小是45

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                return count == 1 ? SingleItemFontSize : DefaultFontSize;
            }
            // 你也可以处理 IEnumerable<T> 如果绑定路径直接是集合本身
            // else if (value is System.Collections.IEnumerable enumerable)
            // {
            //     var itemCount = enumerable.Cast<object>().Count();
            //     return itemCount == 1 ? SingleItemFontSize : DefaultFontSize;
            // }
            return DefaultFontSize; // 或者 AvaloniaProperty.UnsetValue;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            // 通常不需要反向转换字体大小
            return AvaloniaProperty.UnsetValue; // 或者 throw new NotSupportedException();
        }
    }
}