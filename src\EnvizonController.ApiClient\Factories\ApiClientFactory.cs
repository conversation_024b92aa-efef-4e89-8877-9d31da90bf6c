using EnvizonController.ApiClient.Http;
using EnvizonController.ApiClient.Services;
using EnvizonController.ApiClient.Services.Implementations;
using EnvizonController.Configuration.Models;
using Microsoft.Extensions.Logging;

namespace EnvizonController.ApiClient.Factories
{
    /// <summary>
    /// API客户端工厂实现
    /// </summary>
    public class ApiClientFactory : IApiClientFactory
    {
        private readonly IHttpClient _httpClient;
        private readonly DefaultConfig _config;
        private readonly ILogger<ApiClientFactory> _logger;

        public ApiClientFactory(IHttpClient httpClient, DefaultConfig config, ILogger<ApiClientFactory> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;
        }

        public IDeviceApiService CreateDeviceApiService()
        {
            return new DeviceApiService(_httpClient, _config);
        }

        public IAlarmApiService CreateAlarmApiService()
        {
            return new AlarmApiService(_httpClient);
        }

        public IDataPointApiService CreateDataPointApiService()
        {
            return new DataPointApiService(_httpClient);
        }
        
        public ITestItemApiService CreateTestItemApiService()
        {
            return new TestItemApiService(_httpClient);
        }

        public IDeviceTestApiService CreateDeviceTestApiService()
        {
            return new DeviceTestApiService(_httpClient);
        }
    }
}