Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Protocol", "EnvizonController.Modbus.Protocol\EnvizonController.Modbus.Protocol.csproj", "{09148D15-3631-405B-BF01-CB551A12CA6F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Abstractions", "EnvizonController.Modbus.Abstractions\EnvizonController.Modbus.Abstractions.csproj", "{8CD255E7-9D57-4AEF-A853-6B20022962BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Adapters.Desktop", "EnvizonController.Modbus.Adapters.Desktop\EnvizonController.Modbus.Adapters.Desktop.csproj", "{4DE68D24-7A90-4383-AD3B-5EE54DB70B9A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Adapters.Network", "EnvizonController.Modbus.Adapters.Network\EnvizonController.Modbus.Adapters.Network.csproj", "{65D0867A-FDDC-4A94-9A2B-6D950342710E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Adapters.Android", "EnvizonController.Modbus.Adapters.Android\EnvizonController.Modbus.Adapters.Android.csproj", "{B0FDB0D7-92A4-486A-AEA7-621614A9D03D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnvizonController.Modbus.Client", "EnvizonController.Modbus.Client\EnvizonController.Modbus.Client.csproj", "{B3ACFAAD-7741-45B5-8C70-18184AC72517}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{09148D15-3631-405B-BF01-CB551A12CA6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09148D15-3631-405B-BF01-CB551A12CA6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09148D15-3631-405B-BF01-CB551A12CA6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09148D15-3631-405B-BF01-CB551A12CA6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CD255E7-9D57-4AEF-A853-6B20022962BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CD255E7-9D57-4AEF-A853-6B20022962BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CD255E7-9D57-4AEF-A853-6B20022962BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CD255E7-9D57-4AEF-A853-6B20022962BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DE68D24-7A90-4383-AD3B-5EE54DB70B9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DE68D24-7A90-4383-AD3B-5EE54DB70B9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DE68D24-7A90-4383-AD3B-5EE54DB70B9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DE68D24-7A90-4383-AD3B-5EE54DB70B9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{65D0867A-FDDC-4A94-9A2B-6D950342710E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65D0867A-FDDC-4A94-9A2B-6D950342710E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65D0867A-FDDC-4A94-9A2B-6D950342710E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65D0867A-FDDC-4A94-9A2B-6D950342710E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0FDB0D7-92A4-486A-AEA7-621614A9D03D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0FDB0D7-92A4-486A-AEA7-621614A9D03D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0FDB0D7-92A4-486A-AEA7-621614A9D03D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0FDB0D7-92A4-486A-AEA7-621614A9D03D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3ACFAAD-7741-45B5-8C70-18184AC72517}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3ACFAAD-7741-45B5-8C70-18184AC72517}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3ACFAAD-7741-45B5-8C70-18184AC72517}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3ACFAAD-7741-45B5-8C70-18184AC72517}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A0B1C2D3-E4F5-G6H7-I8J9-K0L1M2N3O4P5}
	EndGlobalSection
EndGlobal
