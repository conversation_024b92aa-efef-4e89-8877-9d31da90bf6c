using Avalonia.Data.Converters;
using EnvizonController.Shared.Enums;
using System;
using System.Globalization;
using Avalonia;
using Avalonia.Data;

namespace EnvizonController.Presentation.Converters
{
    /// <summary>
    /// 将报警状态转换为对应的字符串
    /// </summary>
    public class StatusToStringConverter : IValueConverter
    {
        /// <summary>
        /// 将报警状态转换为对应的字符串
        /// </summary>
        /// <param name="value">报警状态</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>对应的字符串表示</returns>
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not AlarmStatus status) return "未知状态";

            return status switch
            {
                AlarmStatus.Active => "活动",
                AlarmStatus.Processed => "已处理",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 将字符串转换回报警状态（不支持）
        /// </summary>
        /// <param name="value">字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>始终返回 <see cref="BindingNotification.UnsetValue"/></returns>
        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return AvaloniaProperty.UnsetValue;
        }
    }
} 