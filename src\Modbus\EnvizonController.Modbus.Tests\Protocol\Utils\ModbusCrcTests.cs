using EnvizonController.Modbus.Protocol.Utils;
using Xunit;

namespace EnvizonController.Modbus.Tests.Protocol.Utils
{
    public class ModbusCrcTests
    {
        [Fact]
        public void CalculateCrc_WithValidData_ReturnsCorrectCrc()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act
            byte[] crc = ModbusCrc.CalculateCrc(data);
            
            // Assert
            Assert.Equal(2, crc.Length);
            Assert.Equal(0xC5, crc[0]);
            Assert.Equal(0xCD, crc[1]);
        }
        
        [Fact]
        public void AppendCrc_WithValidData_ReturnsByteArrayWithCrc()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            
            // Act
            byte[] result = ModbusCrc.AppendCrc(data);
            
            // Assert
            Assert.Equal(8, result.Length);
            Assert.Equal(data, result.Take(data.Length).ToArray());
            Assert.Equal(0xC5, result[6]);
            Assert.Equal(0xCD, result[7]);
        }
        
        [Fact]
        public void ValidateCrc_WithValidCrc_ReturnsTrue()
        {
            // Arrange
            byte[] data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
            byte[] dataWithCrc = ModbusCrc.AppendCrc(data);
            
            // Act
            bool isValid = ModbusCrc.ValidateCrc(dataWithCrc);
            
            // Assert
            Assert.True(isValid);
        }
        
        [Fact]
        public void ValidateCrc_WithInvalidCrc_ReturnsFalse()
        {
            // Arrange
            byte[] dataWithInvalidCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00 };
            
            // Act
            bool isValid = ModbusCrc.ValidateCrc(dataWithInvalidCrc);
            
            // Assert
            Assert.False(isValid);
        }
        
        [Fact]
        public void ValidateCrc_WithTooShortData_ReturnsFalse()
        {
            // Arrange
            byte[] tooShortData = new byte[] { 0x01 };
            
            // Act
            bool isValid = ModbusCrc.ValidateCrc(tooShortData);
            
            // Assert
            Assert.False(isValid);
        }
        
        [Fact]
        public void ValidateCrc_WithNullData_ReturnsFalse()
        {
            // Act
            bool isValid = ModbusCrc.ValidateCrc(null);
            
            // Assert
            Assert.False(isValid);
        }
    }
}
