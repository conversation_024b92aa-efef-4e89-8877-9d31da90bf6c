using System;

namespace EnvizonController.Mqtt.Client.Events
{
    /// <summary>
    /// MQTT连接状态变化事件参数
    /// </summary>
    public class MqttConnectionStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 获取或设置连接状态
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 获取或设置状态信息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 获取或设置相关的异常（如果有）
        /// </summary>
        public Exception Exception { get; set; }
    }
} 