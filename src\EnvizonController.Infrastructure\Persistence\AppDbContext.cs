﻿using EnvizonController.Domain.Aggregates;
using EnvizonController.Infrastructure.Persistence.Extensions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

namespace EnvizonController.Infrastructure.Persistence
{
    /// <summary>
    /// 应用程序数据库上下文
    /// </summary>
    public class AppDbContext : DbContext
    {
        private readonly string? _connectionString;
        private readonly bool _useSqlite;

        #region DbSets

        public DbSet<TestRun> TestItems { get; set; }
        public DbSet<Alarm> Alarms { get; set; }
        public DbSet<TestDataPoint> TestDataPoints { get; set; }
        public DbSet<Device> Devices { get; set; }
        public DbSet<Protocol> Protocols { get; set; }
        public DbSet<Program> Programs { get; set; }
        //public DbSet<ProgramStep> ProgramSteps { get; set; }
        public DbSet<ProgramLink> ProgramLinks { get; set; }
        public DbSet<ProgramLinkStep> ProgramLinkItems { get; set; }

        #endregion

        /// <summary>
        /// 构造函数 - 用于依赖注入
        /// </summary>
        /// <param name="options">DbContext选项</param>
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
            _useSqlite = true; // 默认使用SQLite
        }

        /// <summary>
        /// 构造函数 - 用于手动创建实例
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="useSqlite">是否使用SQLite（如果为false，则需要在派生类中重写OnConfiguring方法）</param>
        public AppDbContext(string connectionString, bool useSqlite = true)
        {
            _connectionString = connectionString;
            _useSqlite = useSqlite;
        }

        /// <summary>
        /// 配置数据库连接
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                if (_useSqlite)
                {
                    // 如果未指定连接字符串，则使用默认路径
                    var connectionString = _connectionString ??
                        System.IO.Path.Combine(
                            AppDomain.CurrentDomain.BaseDirectory,
                            "envizon.db");

                    // 检查连接字符串是否已包含Data Source前缀
                    if (connectionString.StartsWith("Data Source=", StringComparison.OrdinalIgnoreCase))
                    {
                        optionsBuilder.UseSqlite(connectionString);
                    }
                    else
                    {
                        optionsBuilder.UseSqlite($"Data Source={connectionString}");
                    }
                }
                // 这里可以添加其他数据库提供程序的支持
                // 例如：if (_useCustomBinaryStorage) { ... }
            }
        }

        /// <summary>
        /// 配置实体映射
        /// </summary>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 应用实体配置
            modelBuilder.ApplyConfiguration(new Configurations.ProgramConfiguration());
            modelBuilder.ApplyConfiguration(new Configurations.ProgramStepConfiguration());
            modelBuilder.ApplyConfiguration(new Configurations.ProgramLinkConfiguration());
            modelBuilder.ApplyConfiguration(new Configurations.ProgramLinkItemConfiguration());

            // TestRun 配置
            modelBuilder.Entity<TestRun>(entity =>
            {
                entity.ToTable("TestRuns");
                entity.HasKey(e => e.Id);
                
                // 确保StepCount和TotalDurationSeconds被映射到数据库
                entity.Property(e => e.StepCount);
                entity.Property(e => e.EstimatedDurationSeconds);
            });


            // Alarm 配置
            modelBuilder.Entity<Alarm>(entity =>
            {
                entity.ToTable("Alarms");
                entity.HasKey(e => e.Id);

                // 配置属性
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Message).IsRequired();
                entity.Property(e => e.Level).IsRequired();
                entity.Property(e => e.Status).IsRequired();
                entity.Property(e => e.Timestamp).IsRequired();
            });
      
            // TestDataPoint 配置
            modelBuilder.Entity<TestDataPoint>(entity =>
            {
                entity.ToTable("TestDataPoints");
                entity.HasKey(e => e.Id);

                // 配置属性
                entity.Property(e => e.ValuesJson).IsRequired();
            });

            // Device 配置
            modelBuilder.Entity<Device>(entity =>
            {
                entity.ToTable("Devices");
                entity.HasKey(e => e.Id);

                // 配置属性
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.ProtocolId).IsRequired();
                entity.Property(e => e.ConnectionType).IsRequired();
                entity.Property(e => e.TransportType).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
            });

            // Protocol 配置
            modelBuilder.Entity<Protocol>(entity =>
            {
                entity.ToTable("Protocols");
                entity.HasKey(e => e.Id);
                
                // 配置Id为自增
                entity.Property(e => e.Id)
                    .ValueGeneratedOnAdd();

                // 配置属性
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.DisplayName).IsRequired();
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();

                // 配置协议项集合（复杂类型）
                entity.Property(e => e.Items).HasJsonConversion<Protocol, List<ProtocolItem>>();

            });
        }
    }
}
